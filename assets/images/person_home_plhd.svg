<?xml version="1.0" encoding="UTF-8"?>
<svg width="112px" height="112px" viewBox="0 0 112 112" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组</title>
    <g id="社交（活动/好友）" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="A活动-好友-个人主页（没有任何动态）" transform="translate(-358.000000, -872.000000)" fill="#52555C">
            <g id="编组" transform="translate(358.000000, 872.000000)">
                <path d="M32,34 L80,34 C81.1045695,34 82,34.8954305 82,36 C82,37.1045695 81.1045695,38 80,38 L32,38 C30.8954305,38 30,37.1045695 30,36 C30,34.8954305 30.8954305,34 32,34 Z M32,54 L48,54 C49.1045695,54 50,54.8954305 50,56 C50,57.1045695 49.1045695,58 48,58 L32,58 C30.8954305,58 30,57.1045695 30,56 C30,54.8954305 30.8954305,54 32,54 Z M32,74 L48,74 C49.1045695,74 50,74.8954305 50,76 C50,77.1045695 49.1045695,78 48,78 L32,78 C30.8954305,78 30,77.1045695 30,76 C30,74.8954305 30.8954305,74 32,74 Z" id="形状结合" fill-rule="nonzero"></path>
                <path d="M76,54 C79.3137085,54 82,56.6862915 82,60 L82,72 C82,75.3137085 79.3137085,78 76,78 L64,78 C60.6862915,78 58,75.3137085 58,72 L58,60 C58,56.6862915 60.6862915,54 64,54 L76,54 Z M76,58 L64,58 C62.8954305,58 62,58.8954305 62,60 L62,60 L62,72 C62,73.1045695 62.8954305,74 64,74 L64,74 L76,74 C77.1045695,74 78,73.1045695 78,72 L78,72 L78,60 C78,58.8954305 77.1045695,58 76,58 L76,58 Z" id="形状结合"></path>
                <path d="M82,5 C90.836556,5 98,12.163444 98,21 L98,91 C98,99.836556 90.836556,107 82,107 L30,107 C21.163444,107 14,99.836556 14,91 L14,21 C14,12.163444 21.163444,5 30,5 L82,5 Z M82,9 L30,9 C23.372583,9 18,14.372583 18,21 L18,21 L18,91 C18,97.627417 23.372583,103 30,103 L30,103 L82,103 C88.627417,103 94,97.627417 94,91 L94,91 L94,21 C94,14.372583 88.627417,9 82,9 L82,9 Z" id="形状结合"></path>
            </g>
        </g>
    </g>
</svg>