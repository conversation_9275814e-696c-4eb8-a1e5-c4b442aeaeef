<?xml version="1.0" encoding="UTF-8"?>
<svg width="828px" height="380px" viewBox="0 0 828 380" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组</title>
    <defs>
        <linearGradient x1="53.7637436%" y1="60.5311676%" x2="52.0480253%" y2="39.4688324%" id="linearGradient-1">
            <stop stop-color="#4E535F" offset="0%"></stop>
            <stop stop-color="#21262D" offset="100%"></stop>
        </linearGradient>
        <rect id="path-2" x="0" y="0" width="828" height="380"></rect>
        <linearGradient x1="12.3570775%" y1="68.4693351%" x2="12.3570775%" y2="0%" id="linearGradient-4">
            <stop stop-color="#22272F" offset="0%"></stop>
            <stop stop-color="#414651" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100%" y1="18.1073315%" x2="0%" y2="18.1073315%" id="linearGradient-5">
            <stop stop-color="#2E343D" offset="0%"></stop>
            <stop stop-color="#282C35" offset="5.21387484%"></stop>
            <stop stop-color="#494F59" offset="74.0224904%"></stop>
            <stop stop-color="#666D7D" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="50%" x2="100%" y2="50%" id="linearGradient-6">
            <stop stop-color="#606B7F" offset="0%"></stop>
            <stop stop-color="#464D5B" offset="100%"></stop>
        </linearGradient>
        <path d="M0,95.1468059 C0,52.227843 0,26.8850577 0,19.1184501 C0,7.46853867 4.25162883,0 15.7247223,0 C27.1978158,0 44,13.534704 44,29.7734146 C44,40.5992217 44,64.4573957 44,101.347936 L9,93.4045942 L0,95.1468059 Z" id="path-7"></path>
        <path d="M1.13686838e-13,93.4045942 C1.13686838e-13,51.6471058 1.13686838e-13,26.8850577 1.13686838e-13,19.1184501 C1.13686838e-13,7.46853867 4.25162883,0 15.7247223,0 C27.1978158,0 44,13.534704 44,29.7734146 C44,40.5992217 44,66.0080835 44,106 L1.13686838e-13,93.4045942 Z" id="path-9"></path>
        <filter x="-61.4%" y="-31.1%" width="222.7%" height="150.9%" filterUnits="objectBoundingBox" id="filter-11">
            <feOffset dx="0" dy="-6" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="8" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.160620629 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <polygon id="path-12" points="0.273960455 24.3040823 4.46450487 31.2550713 10.2739605 16.2550713"></polygon>
        <filter x="-60.0%" y="-40.0%" width="220.0%" height="180.0%" filterUnits="objectBoundingBox" id="filter-13">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.187308785 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <polygon id="path-14" points="41 33.1183511 42.1463254 37 47 35.0591755 47 31"></polygon>
        <filter x="-100.0%" y="-100.0%" width="300.0%" height="300.0%" filterUnits="objectBoundingBox" id="filter-15">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.187308785 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <polygon id="path-16" points="21.4344095 23.8813845 25.4344095 25.35924 23.4344095 21.35924"></polygon>
        <filter x="-150.0%" y="-150.0%" width="400.0%" height="400.0%" filterUnits="objectBoundingBox" id="filter-17">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.187308785 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <polygon id="path-18" points="50 0 56 8 56 0"></polygon>
        <filter x="-100.0%" y="-75.0%" width="300.0%" height="250.0%" filterUnits="objectBoundingBox" id="filter-19">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.187308785 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <polygon id="path-20" points="75.494712 17.7272235 78.0606593 19.8980914 80.494712 17.7272235 80.494712 13.8980914 77.994712 16.8980914"></polygon>
        <filter x="-120.0%" y="-100.0%" width="340.0%" height="300.0%" filterUnits="objectBoundingBox" id="filter-21">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.187308785 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <polygon id="path-22" points="34.1532725 17.3196966 40.1532725 17.3196966 40.1532725 11.3196966"></polygon>
        <filter x="-100.0%" y="-100.0%" width="300.0%" height="300.0%" filterUnits="objectBoundingBox" id="filter-23">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.187308785 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="76.5003026%" y1="77.0440654%" x2="-27.7621136%" y2="31.5029951%" id="linearGradient-24">
            <stop stop-color="#414651" offset="0%"></stop>
            <stop stop-color="#22272F" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="55.9677592%" y1="72.698648%" x2="-31.4660985%" y2="17.3511183%" id="linearGradient-25">
            <stop stop-color="#2D333C" offset="0%"></stop>
            <stop stop-color="#3A414C" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="58.0645445%" y1="51.2002261%" x2="81.4725107%" y2="48.5019143%" id="linearGradient-26">
            <stop stop-color="#606B7F" offset="0%"></stop>
            <stop stop-color="#697386" offset="100%"></stop>
        </linearGradient>
        <path d="M572,178.510315 L606.850364,186.453657 L575.808105,202.09623 C541.535117,201.892128 511.805581,198.413189 495.731454,193.320446 L572,178.510315 Z" id="path-27"></path>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图" transform="translate(-55.000000, -63.000000)">
            <g id="编组" transform="translate(55.000000, 63.000000)">
                <mask id="mask-3" fill="white">
                    <use xlink:href="#path-2"></use>
                </mask>
                <use id="蒙版" fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                <polygon id="路径-10" fill="url(#linearGradient-4)" mask="url(#mask-3)" points="606.850364 183.137464 761.853601 91.57971 862.737928 178.105721 862.737928 380 606.850364 380"></polygon>
                <polygon id="矩形" fill="url(#linearGradient-5)" mask="url(#mask-3)" points="483 185.105721 675 185.105721 675 380 483 380"></polygon>
                <ellipse id="椭圆形" fill="" mask="url(#mask-3)" cx="579" cy="184.605721" rx="96" ry="17.5"></ellipse>
                <ellipse id="椭圆形" fill="url(#linearGradient-6)" mask="url(#mask-3)" cx="579" cy="184.605721" rx="96" ry="17.5"></ellipse>
                <g id="路径-9" mask="url(#mask-3)">
                    <g transform="translate(207.000000, 175.000000)" id="路径-19">
                        <g transform="translate(0.000000, 0.049217)"></g>
                    </g>
                </g>
                <g id="编组-4" mask="url(#mask-3)">
                    <g transform="translate(519.000000, 85.105721)">
                        <g id="路径-7备份" transform="translate(44.000000, 0.000000)">
                            <mask id="mask-8" fill="white">
                                <use xlink:href="#path-7"></use>
                            </mask>
                            <use id="蒙版" fill="#484E5B" xlink:href="#path-7"></use>
                            <g id="路径-8" mask="url(#mask-8)">
                                <g transform="translate(9.000000, 0.000000)">
                                    <mask id="mask-10" fill="white">
                                        <use xlink:href="#path-9"></use>
                                    </mask>
                                    <g id="蒙版" stroke="none" fill="none">
                                        <use fill="black" fill-opacity="1" filter="url(#filter-11)" xlink:href="#path-9"></use>
                                        <use fill="#646B7A" fill-rule="evenodd" xlink:href="#path-9"></use>
                                    </g>
                                    <path d="M-2,92.9134058 L-2,82 C4.67216881,81.8197085 11.0107819,81.7844683 17.0158393,81.8942793 C23.0208967,82.0040904 29.0156169,82.2648632 35,82.6765979 L34.8503644,101.347936 L-2,92.9134058 Z" id="路径-8" stroke="none" fill="#7A869B" fill-rule="evenodd" mask="url(#mask-10)"></path>
                                </g>
                            </g>
                        </g>
                        <g id="编组-3" transform="translate(0.000000, 28.000000)">
                            <g id="路径-13">
                                <use fill="black" fill-opacity="1" filter="url(#filter-13)" xlink:href="#path-12"></use>
                                <use fill="#99C0FF" fill-rule="evenodd" xlink:href="#path-12"></use>
                            </g>
                            <g id="路径-14">
                                <use fill="black" fill-opacity="1" filter="url(#filter-15)" xlink:href="#path-14"></use>
                                <use fill="#7DA5E7" fill-rule="evenodd" xlink:href="#path-14"></use>
                            </g>
                            <g id="路径-15">
                                <use fill="black" fill-opacity="1" filter="url(#filter-17)" xlink:href="#path-16"></use>
                                <use fill="#7DA5E7" fill-rule="evenodd" xlink:href="#path-16"></use>
                            </g>
                            <g id="路径-16">
                                <use fill="black" fill-opacity="1" filter="url(#filter-19)" xlink:href="#path-18"></use>
                                <use fill="#AECDFF" fill-rule="evenodd" xlink:href="#path-18"></use>
                            </g>
                            <g id="路径-17">
                                <use fill="black" fill-opacity="1" filter="url(#filter-21)" xlink:href="#path-20"></use>
                                <use fill="#7DA5E7" fill-rule="evenodd" xlink:href="#path-20"></use>
                            </g>
                            <g id="路径-18">
                                <use fill="black" fill-opacity="1" filter="url(#filter-23)" xlink:href="#path-22"></use>
                                <use fill="#7DA5E7" fill-rule="evenodd" xlink:href="#path-22"></use>
                            </g>
                        </g>
                    </g>
                </g>
                <polygon id="路径-11" fill="url(#linearGradient-24)" mask="url(#mask-3)" points="619.601885 305.610387 2.27373675e-13 -44.5285584 2.27373675e-13 380 619.601885 380"></polygon>
                <polygon id="路径-12" fill="url(#linearGradient-25)" mask="url(#mask-3)" points="538.164747 331.871415 -2.95585778e-12 -35.62864 8.68032736e-13 380 538.164747 380"></polygon>
                <mask id="mask-28" fill="white">
                    <use xlink:href="#path-27"></use>
                </mask>
                <use id="形状结合" fill="url(#linearGradient-26)" xlink:href="#path-27"></use>
            </g>
        </g>
    </g>
</svg>