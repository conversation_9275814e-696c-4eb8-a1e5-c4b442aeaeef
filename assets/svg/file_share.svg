<?xml version="1.0" encoding="UTF-8"?>
<svg width="44px" height="44px" viewBox="0 0 44 44" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>形状结合</title>
    <g id="1.4_获取VR截图文件" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="详情-视频" transform="translate(-726.000000, -1680.000000)" fill="#B0B3B8">
            <g id="fenxiang-(2)" transform="translate(726.000000, 1680.000000)">
                <path d="M30.9998077,1.13686838e-13 C35.9498673,1.13686838e-13 40,4.05005576 40,8.99988464 C40,13.9499442 35.9498673,18 30.9998077,18 C28.4769923,18 26.187899,16.9478804 24.549653,15.2607256 L17.5789258,19.2847921 C17.8523315,20.1423958 18,21.0547373 18,21.9998846 C18,23.2539975 17.7400309,24.4503412 17.271322,25.5376888 L26.8969369,31.0948433 C28.3576917,28.0866087 31.4469858,26 34.9998077,26 C39.9498673,26 44,30.0500558 44,34.9998846 C44,39.9499442 39.9498673,44 34.9998077,44 C30.1164305,44 26.1087819,40.0578361 26.0021772,35.198528 L14.8956866,28.7859461 C13.313451,30.1634044 11.2495756,31 8.99980774,31 C4.05005576,31 0,26.9499442 0,21.9998846 C0,17.0500558 4.05005576,13 8.99980774,13 C11.5724594,13 13.9020315,14.0939617 15.5462917,15.8396419 L22.4638896,11.845923 C22.1631177,10.9504657 22,9.99329918 22,8.99988464 C22,4.05005576 26.0500558,1.13686838e-13 30.9998077,1.13686838e-13 Z M34.9997864,30 C31.9997095,30 30,31.9997095 30,34.9998718 C30,37.9999487 31.9997095,40 34.9997864,40 C37.9999487,40 40,37.9999487 40,34.9998718 C40,31.9997095 37.9999487,30 34.9997864,30 Z M8.99978637,17 C5.99970947,17 4,18.9997095 4,21.9998718 C4,24.9999487 5.99970947,27 8.99978637,27 C11.9999487,27 14,24.9999487 14,21.9998718 C14,18.9997095 11.9999487,17 8.99978637,17 Z M30.9997864,4 C27.9997095,4 26,5.99970947 26,8.99987182 C26,11.9999487 27.9997095,14 30.9997864,14 C33.9999487,14 36,11.9999487 36,8.99987182 C36,5.99970947 33.9999487,4 30.9997864,4 Z" id="形状结合"></path>
            </g>
        </g>
    </g>
</svg>