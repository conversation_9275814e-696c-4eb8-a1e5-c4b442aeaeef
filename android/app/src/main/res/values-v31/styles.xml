<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Theme applied to the Android Window as soon as the process has started.
         This theme determines the color of the Android Window while your
         Flutter UI initializes, as well as behind your Flutter UI while its
         running.
         
         This Theme is only used starting with V2 of Flutter's Android embedding. -->
    <style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
     <!-- <item name="android:windowBackground">@drawable/launch_background</item> -->
    </style>

    <style name="Theme.App.Starting" parent="Theme.SplashScreen">
          <!-- Set the splash screen background, animated icon, and animation
          duration. -->
          <item name="windowSplashScreenBackground">@color/launch_color</item>

          <!-- Use windowSplashScreenAnimatedIcon to add a drawable or an animated
               drawable. One of these is required. -->
          <item name="windowSplashScreenAnimatedIcon">@mipmap/ic_launcher</item>
          <!-- Required for animated icons. -->
          <item name="windowSplashScreenAnimationDuration">200</item>

          <!-- Set the theme of the Activity that directly follows your splash
          screen. This is required. -->
          <item name="postSplashScreenTheme">@style/NormalTheme</item>     
     </style>
</resources>
