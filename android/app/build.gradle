def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new FileNotFoundException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

apply plugin: 'com.android.application'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"

android {
    ndkVersion "23.2.8568313"
    namespace "com.yvr.fmoba"
    compileSdkVersion 35

    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false// 有关详细信息, 请使用 -Xlint:deprecation 重新编译。
        abortOnError false
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.yvr.fmoba"
        minSdkVersion 24
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true

        ndk {
            abiFilters 'arm64-v8a'
        }
    }

    signingConfigs {
        release {
            storeFile file("../releasekey.jks")
            storePassword "123456"
            keyAlias "yvr"
            keyPassword "123456"
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            /// 修复 D/BluetoothLeScanner(17439): could not find callback wrapper
            /// https://github.com/pauldemarco/flutter_blue/issues/772
            shrinkResources false
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            ndk {
                abiFilters "arm64-v8a"
            }
        }

        debug {
            signingConfig signingConfigs.debug
            /// 修复 D/BluetoothLeScanner(17439): could not find callback wrapper
            /// https://github.com/pauldemarco/flutter_blue/issues/772
            shrinkResources false
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            ndk {
                abiFilters "arm64-v8a", "x86_64"
            }
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    // implementation files('libs/ffmpeg-kit.aar')
    implementation files('libs/logan-release.aar')
    implementation 'androidx.work:work-runtime:2.7.1'
    implementation 'androidx.work:work-runtime-ktx:2.7.1'
    implementation 'androidx.core:core-splashscreen:1.0.0-beta02'
}
