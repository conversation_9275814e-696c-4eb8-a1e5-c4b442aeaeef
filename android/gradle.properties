org.gradle.jvmargs=-Xmx5120M --add-exports=java.base/sun.nio.ch=ALL-UNNAMED \
--add-opens=java.base/java.lang=ALL-UNNAMED \
--add-opens=java.base/java.lang.reflect=ALL-UNNAMED \
--add-opens=java.base/java.io=ALL-UNNAMED \
--add-exports=jdk.unsupported/sun.misc=ALL-UNNAMED
android.useAndroidX=true
android.enableJetifier=true
MobSDK.spEdition=FP
MobSDK.mobEnv=x
android.enableAapt2=true
android.debug.obsoleteApiUsage=false
# E/gralloc4(21406): Empty SMPTE 2094-40 data
# 关闭 GPU 调试日志

# 如果是 Android 11+ 设备，可以通过 开发者选项 禁用 GPU 调试：
# 	1.	进入开发者模式（多次点击“关于手机”中的 版本号）。
# 	2.	进入“开发者选项”。
# 	3.	找到 “Enable GPU debug layers”（启用 GPU 调试层），关闭它。

