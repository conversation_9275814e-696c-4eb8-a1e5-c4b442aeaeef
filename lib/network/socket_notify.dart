abstract class OnFriendNotifyListener {
//  收到好友邀请通知
  void onReceiveFriendInvited();

  //收到请求好友后的反馈通知
  /*1同意 2忽略 3拒绝 4拉黑*/
  void onReceiveFriendFeedback(int status);

  //收到对方处理好友通知
  // /*4拉黑好友 5删除好友 6恢复好友*/
  void onReceiveFriendProcess(int status);

  void onReceiveFriendOnOffline(bool online);
}

abstract class OnEventNotifyListener
{
  void onReceiveEventRemove(int eventId);

  void onReceiveEventInvited(int eventId);

  void onReceiveEventToCome(int eventId);

  void onReceiveEventComing(int eventId);
}