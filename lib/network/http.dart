import 'package:yvr_assistant/manager/navigation_serive.dart';

import 'api.dart';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:cookie_jar/cookie_jar.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/pages/home/<USER>';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/manager/environment.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:dio_cookie_manager/dio_cookie_manager.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';

Http http;
String appVersion;

Future<void> prepareHttp() async {
  http = Http();
  appVersion = await PlatformUtils.getAppVersion();
  http.interceptors
    ..add(RefreshTokenQueuedInterceptor(enabled: true))
    ..add(ApiInterceptor())
    ..add(CacheInterceptor())
    // cookie持久化 异步
    ..add(CookieManager(CookieJar()));
  return true;
}

const String _kAutoProcessError = "AutoProcessError";
const int _kPlatform = 1;

///first
const Map<String, dynamic> _generalHeaders = {
  'Authorization': "**",
  'Content-Type': 'application/json',
  'Accept': 'application/json, text/plain, */*',
};

///second
Map<String, dynamic> _dynamicHeaders() {
  // MR设备默认美国渠道 其他设备始终为国内渠道不允许修改
  String mr = Global.kDevModelOptions.first;
  String device = DBUtil.instance.userBox.get(Global.kDevModel) ?? mr;
  String model = (device == mr ? 'Play For Dream MR' : device);
  return {
    'token': DBUtil.instance.userBox.get(kToken) ?? "",
    'ver': 'mbsuite_' + appVersion,
    'platForm': _kPlatform,
    'lang': 'en_US',
    'group': (device == mr) ? 'PFDMUSA' : 'Customer',
    'model': model
  };
}

class Http extends BaseHttp {
  static Options buildNoAutoProcessErrorOptions() {
    return Options(extra: <String, dynamic>{_kAutoProcessError: false});
  }

  @override
  void init() {
    options
      ..baseUrl = Environment().config.apiHost
      ..receiveTimeout = 5000
      ..connectTimeout = 10000
      ..headers = _generalHeaders;
  }
}

class RefreshTokenQueuedInterceptor extends QueuedInterceptor {
  bool enabled;
  RefreshTokenQueuedInterceptor({this.enabled = false});
  final List<String> kNoNeedLoginPathsFor10021 = [
    "getUserInfoFromMobile",
    "getUnreadNtyNumScd",
    "searchAppOnStore",
  ];
  final List<String> kNoNeedLoginPathsFor10020 = [
    "getAssociativeApps",
    "getUserInfoFromMobile",
    "getAddOnDetail",
    "getAppCfgInfo",
    "getAppTagInfo",
    "getAllNtyList",
    "getLoginDevInfos",
    "getPurchasedAppId",
    "getLoginDevIds",
    "logoutSd",
    "queryUserPointsInfo",
  ];

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // 检查用户信息 登出接口
    if (response.data is String) {
      Map<String, dynamic> map = json.decode(response.data);
      int errCode = map["errCode"];
      String uriPath = response.realUri.path?.split('/')?.last;

      if (errCode == 10021) {
        if (enabled && !kNoNeedLoginPathsFor10021.contains(uriPath)) {
          refreshToken(
              errCode: errCode, preResponse: response, handler: handler);
          return;
        }
      } else if (errCode == 10020) {
        // 等价接口取消token验证
        if (kNoNeedLoginPathsFor10020.contains(uriPath)) {
          // Do nothing
        } else if (response.headers["token"] == null) {
          YvrToast.showToast(YLocal.current.toast_need_login);
        } else if (enabled) {
          refreshToken(
              errCode: errCode, preResponse: response, handler: handler);
          return;
        }
      }
      handleReject(errCode, response, handler);
    } else {
      handler.next(response);
    }
  }

  static void handleReject(
      int errCode, Response response, ResponseInterceptorHandler handler) {
    handler.reject(
      DioError(
          type: DioErrorType.other,
          error: YLocal.current.jiazaishibaiqingchon +
              (errCode == null ? '' : '[$errCode]'),
          requestOptions: response.requestOptions),
    );
  }

  static List<int> tokenErrCodes = [10002, 10005, 10009, 10010];
  Future<void> refreshToken(
      {@required int errCode,
      @required Response preResponse,
      @required ResponseInterceptorHandler handler}) async {
    Log.i('开始请求Token');
    final tokenDio = Dio();
    Options options = Options();
    options
      ..headers = reqHeader()
      ..receiveTimeout = 10000;
    Map param = {'refresh_token': DBUtil.instance.userBox.get(kRefreshToken)};

    await tokenDio
        .post(
      Environment().config.apiHost + 'vrmcsys/account/refreshToken',
      data: param,
      options: options,
    )
        .then((response) async {
      var data = response.data;
      int code = data["errCode"];

      if (code == 0) {
        String token = data['data']['token'];
        String refreshToken = data['data']['refresh_token'];
        await DBUtil.instance.userBox.put(kToken, token);
        await DBUtil.instance.userBox.put(kRefreshToken, refreshToken);
        Log.i("Token获取成功\nrefreshToken:\n$refreshToken\ntoken:\n$token\n");
        requestAgain(response: preResponse, handler: handler, token: token);
      } else {
        // 11002 登录过于频繁（同一账号1分钟只能登录一次）
        // 10021 一月未登录，需要重新登录
        if (code == 10021 || tokenErrCodes.contains(code)) {
          Log.e('RefreshToken 失败 errCode: $code');
          signOut();
        }
        throw DioError(
          response: response,
          requestOptions: response.requestOptions,
        );
      }
    }).onError((error, stackTrace) async {
      Log.e('请求RefreshToken失败:$errCode');
      handleReject(errCode, error.response, handler);
      if (tokenErrCodes.contains(errCode)) {
        Log.i("退出登录 - $errCode");
        signOut();
      }
    });
  }

  Future<void> signOut() async {
    bool isInLoginPage = DBUtil.instance.userBox.get("isLoginPage") ?? false;
    if (!isInLoginPage) {
      // 处理已打开个人页面情形
      eventBus.fire(EventFn({Global.kLogout: true}));

      // 处理未打开个人页面情形
      await StorageManager.localStorage.deleteItem(kUser);
      await DBUtil.instance.userBox.delete(kRefreshToken);
      await DBUtil.instance.userBox.delete(kToken);
      await DBUtil.instance.userBox.delete(kActId);

      NavigationService.pushLoginAndRemoveUntilFirst();
    }
  }

  requestAgain(
      {@required Response response,
      @required ResponseInterceptorHandler handler,
      @required String token}) async {
    try {
      var requestOptions = response.requestOptions;
      // 修改类型
      Options options = Options(
          method: requestOptions.method,
          sendTimeout: requestOptions.sendTimeout,
          receiveTimeout: requestOptions.receiveTimeout,
          extra: requestOptions.extra,
          headers: requestOptions.headers,
          responseType: requestOptions.responseType,
          contentType: requestOptions.contentType,
          validateStatus: requestOptions.validateStatus,
          receiveDataWhenStatusError: requestOptions.receiveDataWhenStatusError,
          followRedirects: requestOptions.followRedirects,
          maxRedirects: requestOptions.maxRedirects,
          requestEncoder: requestOptions.requestEncoder,
          responseDecoder: requestOptions.responseDecoder,
          listFormat: requestOptions.listFormat);
      // options.headers = reqHeader(token: token);
      options.headers['token'] = token;
      final Dio dio = Dio(BaseOptions(baseUrl: Environment().config.apiHost));
      dio.interceptors.add(RefreshTokenQueuedInterceptor(enabled: false));
      var newResponses = await dio.request(
        requestOptions.path,
        data: requestOptions.data,
        queryParameters: requestOptions.queryParameters,
        cancelToken: requestOptions.cancelToken,
        options: options,
        onReceiveProgress: requestOptions.onReceiveProgress,
      );
      handler.next(newResponses);
      return;
    } on DioError catch (e) {
      handler.reject(e);
    }
  }

  Map<String, dynamic> reqHeader({String token = ""}) {
    return Map<String, dynamic>.from(_generalHeaders)
      ..addAll(_dynamicHeaders());
  }
}

class ApiInterceptor extends InterceptorsWrapper {
  // HACK: 打印响应数据 isLog设置 false
  // 防止 Log.d 先解析内容，后决定是否打印 true
  bool isLog = false;

  void logResponse(Response response) {
    if (isLog) {
      bool isLogAll;
      String responseStr = "";
      RequestOptions options = response.requestOptions;
      isLogAll = options.path.contains('getAppPriceRanges');
      isLogAll = false;

      if (isLogAll) {
        responseStr = '''
  Response:
  ${JsonEncoder.withIndent('    ').convert(response.data)} ''';
      }

      Log.d(''' 
  URL:  ${options.baseUrl}${options.path}${options.queryParameters} 
  Headers:  ${JsonEncoder.withIndent('    ').convert(options.headers)}
  Params:  ${options.data}\n$responseStr''');
    }
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    options.headers.addAll(_dynamicHeaders());
    if (!options.queryParameters.containsKey('platForm')) {
      options.queryParameters['platForm'] = _kPlatform;
    }

    // 解决数据获取失败问题：token 后端用于区分 海外版 或 国内版，默认国内版可不传token
    if (options.path.contains('getAppStoreTab') ||
        options.path.contains('getAppStorePage')) {
      // options.headers['token'] = '';
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    logResponse(response);
    ResponseData result;
    result = ResponseData.fromJson(
      response.data,
      response.headers.value('date'),
    );
    response.data = result.data;
    if (response.data == null) {
      response.data = {"errCode": result.errCode};
    } else {
      response.data["errCode"] = result.errCode;
    }
    // 获取全局context
    BuildContext context = Global.navigatorKey.currentState.overlay.context;
    if (response.requestOptions.extra == null ||
        !response.requestOptions.extra.containsKey(_kAutoProcessError) ||
        response.requestOptions.extra[_kAutoProcessError]) {
      switch (result.errCode) {
        case 0:
        case 10010:
          break;
        case 10001:
          YvrToast.showToast(YLocal.current.toast_no_regist);
          break;
        case 10002:
          YvrToast.showToast(YLocal.current.toast_pwd_error);
          break;
        case 10003:
          YvrToast.showToast(YLocal.current.toast_update_pwd);
          break;
        case 10005:
          YvrToast.showToast(YLocal.current.zhanghaoyishixiao);
          break;
        case 10006:
          YvrToast.showToast(YLocal.current.toast_regist_yet);
          break;
        case 10007:
          YvrToast.showToast(YLocal.current.toast_dev_limit);
          break;
        case 10009:
          YvrToast.showToast(YLocal.current.toast_login_fail);
          break;
        case 10009:
          YvrToast.showToast(YLocal.current.zhanghaoyishenqingzh);
          break;
        case 10041:
          if (response.realUri.path.contains("refund")) {
            YvrToast.showToast(YLocal.current.faqituikuanshibaifei);
          }
          break;
        case 10050:
          YvrToast.showToast(YLocal.current.toast_vfcodeJ_send_fail);
          break;
        case 10051:
          YvrToast.showToast(YLocal.current.toast_vfcode_error);
          break;
        case 10052:
          YvrToast.showToast(YLocal.current.toast_vfcode_frequently);
          break;
        case 10053:
          YvrToast.showToast(YLocal.current.toast_vfcode_day);
          break;
        case 10054:
          YvrToast.showToast(YLocal.current.toast_not_exist);
          break;
        case 10501:
          loganInfo(
              '(PopUntilFirst) response 10501', StackTrace.current.toString());
          Navigator.of(context).popUntil((route) => route.isFirst);
          YvrToast.showToast(YLocal.current.yingyongyixiajiahuob);
          break;
        case 10503:
          loganInfo(
              '(PopUntilFirst) response 10503', StackTrace.current.toString());
          Navigator.of(context).popUntil((route) => route.isFirst);
          YvrToast.showToast(YLocal.current.zhuantiyiguoqihuobuc);
          break;
        case 10504:
          loganInfo(
              '(PopUntilFirst) response 10504', StackTrace.current.toString());
          Navigator.of(context).popUntil((route) => route.isFirst);
          YvrToast.showToast(YLocal.current.zhutiyiguoqihuobucun);
          break;
        case 10540:
          break;
        case 10701:
          YvrToast.showToast(YLocal.current.toast_already_commented);
          break;
        case 12000:
          if (response.realUri.path.contains("commentApp")) {
            YvrToast.showToast(YLocal.current.toast_emoticon_icon);
          } else {
            YvrToast.showToast(YLocal.current.toast_unknown_error);
            Log.e(
                '''请求失败接口: ${response.requestOptions.baseUrl}${response.requestOptions.path}
                param: ${response.requestOptions.queryParameters}''');
          }
          break;
        case 11001:
          Log.d("参数解析错误");
          break;
        case 17000:
          Future.delayed(const Duration(milliseconds: 2), () async {
            if (response.realUri.path.contains("wxPay")) {
              YvrToast.showToast(YLocal.current.toast_order_fail);
            } else if (response.realUri.path.contains("recharge")) {
              YvrToast.showToast(YLocal.current.chongzhishibai);
            } else if (response.realUri.path.contains("subjectPay")) {
              YvrToast.showToast(YLocal.current.jiazaishibaiqingchon);
            } else {
              YvrToast.showToast(YLocal.current.tuikuanshibai);
            }
          });
          break;
        case 17001:
          YvrToast.showToast(YLocal.current.dangqianquanbuzhichi);
          break;
        case 19000:
          Future.delayed(const Duration(milliseconds: 2), () async {
            YvrToast.showToast(YLocal.current.tuikuanshibaiyichaog);
          });
          break;
        case 15000:
          Future.delayed(const Duration(milliseconds: 2), () async {
            if (response.realUri.path.contains("exchangeMobileGood")) {
              YvrToast.showToast('商品库存不足');
            } else {
              YvrToast.showToast(YLocal.current.toast_inventory_shortage);
            }
          });
          break;
        case 18000:
          Future.delayed(const Duration(milliseconds: 2), () async {
            YvrToast.showToast(YLocal.current.toast_app_purchased);
          });
          break;
        case 21000:
          YvrToast.showToast(YLocal.current.cuxiaohuodongfasheng);
          break;
        case 22000:
          YvrToast.showToast(YLocal.current.huodongyijieshuqings);
          break;
        case 23000:
          Future.delayed(const Duration(milliseconds: 2), () async {
            YvrToast.showToast(YLocal.current.chongzhishibai);
          });
          break;
        case 23300:
          // YvrToast.showToast(YLocal.current.PageNotFound);
          throw NotSuccessException.fromRespData(result);
          break;
        case 24000:
          Future.delayed(const Duration(milliseconds: 2), () async {
            YvrToast.showToast(YLocal.current.Ybiyuebuzu);
          });
          break;
        case 10013:
          Future.delayed(const Duration(milliseconds: 20), () async {
            YvrToast.showToast(YLocal.current.IncorrectEmailAddress);
          });
          break;
        case 10012:
          Future.delayed(const Duration(milliseconds: 20), () async {
            YvrToast.showToast(YLocal.current.EmailAddressHasBeenRegistered);
          });
          break;
        case 10016:
          Future.delayed(const Duration(milliseconds: 20), () async {
            YvrToast.showToast(result.errMsg);
          });
          break;
        default:
          Log.e('''
接口：${response.realUri.path}
错误码：${result.errCode}
错误信息：${result.errMsg}''');
        // throw NotSuccessException.fromRespData(result);
      }
    }

    eventBus.fire(EventFn({'isHaveNetwork': true}));
    super.onResponse(response, handler);
  }
}

class CacheInterceptor extends Interceptor {
  CacheInterceptor();

  @override
  void onError(DioError err, ErrorInterceptorHandler handler) {
    String errReqPath = err.requestOptions.path;
    Log.e('apiName: $errReqPath \nonError: $err');
    logan("api: $errReqPath \nerror: $err'", 'network', 1);
    if (errReqPath.contains("commentApp") ||
        errReqPath.contains("wxPay") ||
        errReqPath.contains("loginSd") ||
        errReqPath.contains("punchAuditDetail") ||
        errReqPath.contains("recharge")) {
      YvrToast.showToast(YLocal.current.wanglaoyichangqingsh);
    } else if (errReqPath.contains("tryObtainPoints")) {
      YvrToast.showToast("积分获取失败");
    }
    super.onError(err, handler);
  }
}

class ResponseData<T> extends BaseResponseData<T> {
  bool get success => 0 == errCode;
  String timestamp;

  ResponseData.fromJson(Map<String, dynamic> json, String t) {
    errCode = json['errCode'];
    errMsg = json['errMsg'];
    data = json['data'];
    timestamp = t;
  }

  ResponseData.convertFromJson(Response response,
      {T Function(dynamic json) dataConvert}) {
    Map<String, dynamic> json = response.data;
    errCode = json['errCode'];
    data = dataConvert?.call(json);
    timestamp = response.headers.value('date');
  }
}

bool isNetworkError(DioError error) {
  if (error.type == DioErrorType.other) {
    return error.error is SocketException || error.error is HttpException;
  }
  return false;
}

String getDioErrorMessage(DioError error) {
  if (isNetworkError(error)) {
    return YLocal.current.wanglaoyichangqingsh;
  } else {
    switch (error.type) {
      case DioErrorType.connectTimeout:
      case DioErrorType.sendTimeout:
      case DioErrorType.receiveTimeout:
        return YLocal.current.qingqiuchaoshi;
      case DioErrorType.other:
        return error.error?.toString() ?? YLocal.current.jiazaishibaiqingchon;
        break;
      case DioErrorType.response:
        return '${YLocal.current.fuwuqiqingqiushibai}[${error.response.statusCode}]';
      case DioErrorType.cancel:
        return "";
      default:
        return null;
    }
  }
}

bool checkSuccess(int errCode) {
  if (errCode == 0) {
    return true;
  }
  return false;
}

String _getHttpErrorMessage(int errCode, {Map<int, String> errorMatcher}) {
  if (checkSuccess(errCode)) {
    return null;
  }
  switch (errCode) {
    case 11001:
      return YLocal.current.canshucuowucenshucuo;
    case 10041:
      return YLocal.current.jitongcuowuxitongcuo;
    default:
      if (errorMatcher != null && errorMatcher.containsKey(errCode)) {
        return errorMatcher[errCode] ?? "";
      }
      return "${YLocal.current.jitacuowuqitacuowu}[$errCode]";
  }
}

void checkAndThrowException(int errCode, {Map<int, String> errorMatcher}) {
  String errorMessage =
      _getHttpErrorMessage(errCode, errorMatcher: errorMatcher);
  if (errorMessage == null) {
    return;
  }
  throw Exception(errorMessage);
}

bool checkAndShowToast(int errCode, {Map<int, String> errorMatcher}) {
  String errorMessage =
      _getHttpErrorMessage(errCode, errorMatcher: errorMatcher);
  if (errorMessage == null) {
    return true;
  }
  YvrToast.showToast(errorMessage);
  return false;
}
