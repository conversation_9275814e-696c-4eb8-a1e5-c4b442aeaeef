import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/manager/environment.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/model/socket_model.dart';
import 'package:yvr_assistant/network/socket_connect.dart';
import 'package:yvr_assistant/network/socket_notify.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/utils/yvr_utils.dart';
import '../generated/l10n.dart';

/// WebSocket地址
// ignore: non_constant_identifier_names
String _SOCKET_URL = Environment().config.socketHost;

typedef NotifyCallback = void Function(Map data, String tag);

class SocketManager implements ConnectCallback {
  /// 单例对象
  static SocketManager _socket;

  /// 内部构造方法，可避免外部暴露构造函数，进行实例化
  SocketManager._();

  /// 获取单例内部方法 只能有一个实例
  factory SocketManager() {
    if (_socket == null) {
      _socket = new SocketManager._();
    }
    return _socket;
  }

  static SocketManager getInstance() {
    if (_socket == null) {
      _socket = new SocketManager._();
    }
    return _socket;
  }

  Function _onError; // 连接错误回调
  Function _onOpen; // 连接开启回调
  Function _onMessage; // 接收消息回调

  ///socket连接回调
  List<ConnectCallback> _connectCallbacks =
      List<ConnectCallback>.empty(growable: true);

  ///全局通知回调
  List<NotifyCallback> _notifyCallbacks =
      List<NotifyCallback>.empty(growable: true);

  ///朋友相关通知回调
  List<OnFriendNotifyListener> _friendNotifyCallbacks =
      List<OnFriendNotifyListener>.empty(growable: true);

  ///活动相关通知回调
  List<OnEventNotifyListener> _eventNotifyCallbacks =
      List<OnEventNotifyListener>.empty(growable: true);

  SocketConnect socketConnect;

  /// 初始化WebSocket
  void initWebSocket(
      {Function onOpen, Function onMessage, Function onError}) async {
    if (socketConnect == null) {
      socketConnect =
          SocketConnect(await YvrUtils.getPlatformUid(), _SOCKET_URL);
      socketConnect.callback = this;
      socketConnect.onNotify = onNotify;
    }

    this._onOpen = onOpen;
    this._onMessage = onMessage;
    this._onError = onError;
    openSocket();
  }

  /// 开启WebSocket连接
  void openSocket() {
    socketConnect.openSocket();
  }

  /// WebSocket接收消息回调
  webSocketOnMessage(data) {
    _onMessage(data);
    SocketModel socketModel = socketModelFromJson(data);

    // JSON 美化格式
    // var logStr = JsonEncoder.withIndent('  ').convert(jsonDecode(data));
    // Log.v("\nSocket_接口：\n$logStr}");

    switch (socketModel.errCode) {
      case 11001:
        Log.i("参数错误");
        break;
      case 10008:
        YvrToast.showToast(YLocal.current.zhiyouhuodongchuangj);
        break;
      case 10511:
        BuildContext context = Global.navigatorKey.currentState.overlay.context;
        int prdId = socketModel.data["appId"];
        String prdName = socketModel.data["appName"];
        showDialog(
            context: context,
            barrierDismissible: false,
            builder: (_) {
              return CustomDialog(
                title: YLocal.of(context).xiazaiyingyong,
                content: YLocal.of(context).yaocanjiahuodongnixu(prdName),
                confirmText: YLocal.of(context).quxiazai,
                confirmCallback: () {
                  Navigator.pushNamed(context, '/prod',
                      arguments: {"id": prdId});
                },
              );
            });
        break;
      case 10760:
      case 10761:
        YvrToast.showToast(
            socketModel.errCode == 10760 ? YLocal.of(Global.context).weizhaodaoxiangguanh : YLocal.of(Global.context).huodongyijingguoqi);
        eventBus.fire(EventSocket(socketModel));
        break;
      default:
        // eventBus 发送event_bus.dart文件中的定义的事件，参数传递事件定义中变量的类型即可
        eventBus.fire(EventSocket(socketModel));
    }
  }

  /// 关闭WebSocket
  void closeSocket() {
    socketConnect.closeSocket();
  }

  /// 发送WebSocket消息
  void sendMessage(message, {ValueChanged<Exception> onError}) {
    socketConnect.sendMessageAsync(message).then((value) {
      webSocketOnMessage(value.data);
    }).catchError((e, s) {
      onError?.call(e);
    });
  }

  Future<WSResponseData<T>> sendMessageAsync<T>(
      {@required String cmd,
      Map<String, dynamic> params,
      T Function(dynamic json) dataConvert}) async {
    Map<String, dynamic> map = {
      "type": "request",
    };
    if (cmd != null) {
      map["cmd"] = cmd;
    }
    if (params != null) {
      map.addAll(params);
    }
    return sendOriginalMessageAsync(json.encode(map), dataConvert: dataConvert);
  }

  Future<WSResponseData<T>> sendOriginalMessageAsync<T>(String message,
      {T Function(dynamic json) dataConvert}) async {
    SocketData data = await socketConnect.sendMessageAsync(message);
    var result = WSResponseData.convertFromJson(data.result,
        dataConvert: dataConvert, original: data.data);
    return result;
  }

  void addNotifyCallback(NotifyCallback callback) {
    _notifyCallbacks.add(callback);
  }

  void removeNotifyCallback(NotifyCallback callback) {
    _notifyCallbacks.remove(callback);
  }

  void addFriendNotifyCallback(OnFriendNotifyListener callback) {
    _friendNotifyCallbacks.add(callback);
  }

  void removeFriendNotifyCallback(OnFriendNotifyListener callback) {
    _friendNotifyCallbacks.remove(callback);
  }

  void addEventNotifyCallback(OnEventNotifyListener callback) {
    _eventNotifyCallbacks.add(callback);
  }

  void removeEventNotifyCallback(OnEventNotifyListener callback) {
    _eventNotifyCallbacks.remove(callback);
  }

  void addConnectCallback(ConnectCallback callback) {
    _connectCallbacks.add(callback);
  }

  void removeConnectCallback(ConnectCallback callback) {
    _connectCallbacks.remove(callback);
  }

  void onNotify(SocketData data) {
    webSocketOnMessage(data.data);
    String tag = data.result["tag"];
    Map dt = data.result["data"];
    _notifyCallbacks.forEach((element) {
      element.call(dt, tag);
    });

    switch (tag) {
      case "recvFdInvite":
        _friendNotifyCallbacks.forEach((element) {
          element.onReceiveFriendInvited();
        });
        break;
      case "recvFdInviteFeedback":
        _friendNotifyCallbacks.forEach((element) {
          element.onReceiveFriendFeedback(data.result['status']);
        });
        break;
      case "recvFdRemove":
        _friendNotifyCallbacks.forEach((element) {
          element.onReceiveFriendProcess(data.result['status']);
        });
        break;
      case "recvFdOnOffline":
        _friendNotifyCallbacks.forEach((element) {
          element.onReceiveFriendOnOffline(data.result['status'] == 1);
        });
        break;
      case "recvEventRemove":
        _eventNotifyCallbacks.forEach((element) {
          element.onReceiveEventRemove(dt["eventId"]);
        });
        break;
      case "recvEventComing":
        _eventNotifyCallbacks.forEach((element) {
          element.onReceiveEventComing(dt["eventId"]);
        });
        break;
      case "recvEventInvite":
        _eventNotifyCallbacks.forEach((element) {
          element.onReceiveEventInvited(dt["eventId"]);
        });
        break;
      case "recvEventToCome":
        _eventNotifyCallbacks.forEach((element) {
          element.onReceiveEventToCome(dt["eventId"]);
        });
        break;
    }
  }

  @override
  void onConnectFailed(Exception e) {
    _onError(e.toString());
    _connectCallbacks.forEach((element) {
      element.onConnectFailed(e);
    });
  }

  @override
  void onConnectSuccess() {
    _onOpen();
    _connectCallbacks.forEach((element) {
      element.onConnectSuccess();
    });
  }

  @override
  void onConnectionClosed(int closeCode) {
    _connectCallbacks.forEach((element) {
      element.onConnectionClosed(closeCode);
    });
  }

  @override
  void onReconnectFailed(int time, Exception e) {
    _onError(e.toString());
    _connectCallbacks.forEach((element) {
      element.onReconnectFailed(time, e);
    });
  }

  @override
  void onReconnectSuccess(int time) {
    _onOpen();
    _connectCallbacks.forEach((element) {
      element.onReconnectSuccess(time);
    });
  }

  @override
  void onStartReconnect(int time) {
    _connectCallbacks.forEach((element) {
      element.onStartReconnect(time);
    });
  }
}

class WSResponseData<T> {
  String original;
  String cmd;
  int errCode;
  String errMsg;
  T data;

  WSResponseData.convertFromJson(
    Map response, {
    T Function(dynamic json) dataConvert,
    String original,
  }) {
    errCode = response['errCode'];
    errMsg = response['errMsg'];
    cmd = response['cmd'];
    data = dataConvert?.call(response["data"]);
    this.original = original;
  }
}

String getWSErrorMessage(WebSocketConnectException error) {
  return error.toString();
}

void checkAndThrowWSException(int errCode, {Map<int, String> errorMatcher}) {
  String errorMessage = _getWSErrorMessage(errCode, errorMatcher: errorMatcher);
  if (errorMessage == null) {
    return;
  }
  throw Exception(errorMessage);
}

bool checkWSSuccess(int errCode) {
  if (errCode == 0) {
    return true;
  }
  return false;
}

String _getWSErrorMessage(int errCode, {Map<int, String> errorMatcher}) {
  if (checkWSSuccess(errCode)) {
    return null;
  }
  switch (errCode) {
    case 11000:
      return YLocal.current.qingqiushibai;
    case 11001:
      return YLocal.current.canshucuowucenshucuo;
    case 12000:
      return YLocal.current.weizhicuowu;
    default:
      if (errorMatcher != null && errorMatcher.containsKey(errCode)) {
        return errorMatcher[errCode] ?? "";
      }
      return "${YLocal.current.qingqiushibai}[$errCode]";
  }
}

bool checkAndShowWSToast(int errCode, {Map<int, String> errorMatcher}) {
  String errorMessage = _getWSErrorMessage(errCode, errorMatcher: errorMatcher);
  if (errorMessage == null) {
    return true;
  }
  if (errorMessage.isNotEmpty) {
    YvrToast.showToast(errorMessage);
  }
  return false;
}
