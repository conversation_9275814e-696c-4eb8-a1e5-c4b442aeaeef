import 'package:yvr_assistant/model/hall_friends_model.dart';
import 'package:yvr_assistant/model/my_friends_model.dart';
import 'package:yvr_assistant/model/personal_home_model.dart';
import 'package:yvr_assistant/network/socket.dart';
import '../model/event_detl_model.dart';
import '../model/Join_personal_event_model.dart';
import '../model/event_model.dart';

class YvrWSRequests {
  ///appCount:初始展示共同兴趣应用的个数（设备端建议3个）
  static Future<WSResponseData<HallDataModel>> getPotentialFriends(
      int appCount) async {
    return SocketManager.getInstance().sendMessageAsync(
      cmd: 'reqGetPotentialFriends',
      params: {"appCount": appCount},
      dataConvert: (json) => HallDataModel.fromJson(json),
    );
  }

  ///flag: 1:只获取购买应用 2:获取购买应用或者在线人数较多的应用
  static Future<WSResponseData<AppDataModel>> getMutualAppInfos(
      int psize, int page, int flag) async {
    return SocketManager.getInstance().sendMessageAsync(
      cmd: 'reqGetMutualAppInfos',
      params: {
        "psize": psize,
        "page": page,
        "flag": flag,
      },
      dataConvert: (json) => AppDataModel.fromJson(json),
    );
  }

  static Future<WSResponseData<FriendDataModel>> searchFriend(
      String key) async {
    return SocketManager.getInstance().sendMessageAsync(
      cmd: 'reqSearchFriend',
      params: {"kword": key},
      dataConvert: (json) => FriendDataModel.fromJson(json),
    );
  }

  static Future<WSResponseData<AppFriendsDataModel>> getMutualAppUsers(
    int appId,
    int psize,
    int page,
  ) async {
    return SocketManager.getInstance().sendMessageAsync(
      cmd: 'reqGetMutualAppUsers',
      params: {
        "appId": appId,
        "page": page,
        "psize": psize,
      },
      dataConvert: (json) => AppFriendsDataModel.fromJson(json),
    );
  }

  /// 10740	和对方已经是好友关系
  static Future<WSResponseData<void>> inviteFriend(int actId) {
    return SocketManager.getInstance().sendMessageAsync(
      cmd: 'reqInviteFriend',
      params: {
        "actId": actId,
      },
    );
  }

  /// 10740	和对方已经是好友关系
  static Future<WSResponseData<MyFriendsDataModel>> getMyFriends() {
    return SocketManager.getInstance().sendMessageAsync(
      cmd: 'reqGetMyFriends',
      dataConvert: (json) => MyFriendsDataModel.fromJson(json),
    );
  }

  ///status:4拉黑好友 5删除好友 6恢复好友
  ///10741	未成为好友，不能处理好友关系
  static Future<WSResponseData<void>> disposeFdRelate(int actId, int status) {
    return SocketManager.getInstance().sendMessageAsync(
      cmd: 'reqDisposeFdRelate',
      params: {
        "actId": actId,
        "status": status,
      },
    );
  }

  /// 10740	和对方已经是好友关系
  static Future<WSResponseData<EventDataModel>> getSquareEvents(
      int page, int sort) {
    return SocketManager.getInstance().sendMessageAsync(
        cmd: 'reqGetAllEvents',
        dataConvert: (json) => EventDataModel.fromJson(json),
        params: {"page": page, "sort": sort});
  }

  ///用户加入活动
  /// process	int	活动进展 0未开始 1已开始
  /// appName	string	活动对应的应用名字，只在错误码10511时存在
  /// 10760	没有相关的活动
  /// 10761	活动已经过期
  /// 10511	应用没有购买, 暂只针对手机端
  static Future<WSResponseData<JoinPersonalEventModel>> joinEvent(int eventId) {
    return SocketManager.getInstance().sendMessageAsync(
        cmd: 'reqJoinEvent',
        dataConvert: (json) => JoinPersonalEventModel.fromJson(json),
        params: {"eventId": eventId});
  }

  ///用户退出活动
  /// 10760	没有相关的活动
  /// 10762	活动已开始，不能退出
  static Future<WSResponseData<void>> quitEvent(int eventId) {
    return SocketManager.getInstance()
        .sendMessageAsync(cmd: 'reqQuitEvent', params: {"eventId": eventId});
  }

  ///处理好友请求
  ///status:1同意 2拒绝 3忽略 4拉黑
  ///10730	非法的通知id（id不存在或者非邀请通知或者非自己的通知）
  ///10731  该通知id已处理过，不能再次处理
  static Future<WSResponseData<void>> disposeFdInvite(int ntyId, int status) {
    return SocketManager.getInstance().sendMessageAsync(
      cmd: 'reqDisposeFdInvite',
      params: {
        "ntyId": ntyId,
        "status": status,
      },
    );
  }

  ///获取活动详情
  ///10760	没有相关的活动
  static Future<WSResponseData<EventDetlModel>> getPersonalEventDetails(
      int eventId) {
    return SocketManager.getInstance().sendMessageAsync(
      cmd: 'reqGetEventDetail',
      params: {
        "eventId": eventId,
      },
      dataConvert: (json) => EventDetlModel.fromJson(json),
    );
  }

  ///创建活动
  /// 10762	活动的开始时间比现在的时间早
  static Future<WSResponseData<int>> createPersonalEvent(
    int appId,
    String scover,
    String rcover,
    String name,
    String startTime,
    int duration,
    int auth, {
    String detail,
  }) {
    return SocketManager.getInstance().sendMessageAsync(
      cmd: 'reqCreateEvent',
      dataConvert: (json) => json["eventId"],
      params: {
        "appId": appId,
        "scover": scover,
        "rcover": rcover,
        "name": name,
        "startTime": startTime,
        "duration": duration,
        "auth": auth,
        "detail": detail,
      },
    );
  }

  ///创建活动
  /// 10760	没有相关的活动
  /// 10008	没有权限（只能创建者修改）
  static Future<WSResponseData<void>> editPersonalEvent(
    int eventId,
    int appId,
    String scover,
    String rcover,
    String name,
    String startTime,
    int duration,
    int auth, {
    String detail,
  }) {
    return SocketManager.getInstance().sendMessageAsync(
      cmd: 'reqEditEvent',
      params: {
        "eventId": eventId,
        "appId": appId,
        "scover": scover,
        "rcover": rcover,
        "name": name,
        "startTime": startTime,
        "duration": duration,
        "auth": auth,
        "detail": detail,
      },
    );
  }

  ///删除活动
  /// 10008	没有权限
  /// 10760	没有相关的活动
  /// 10762	活动已开始，不能删除
  static Future<WSResponseData<void>> deletePersonalEvent(
    int eventId,
  ) {
    return SocketManager.getInstance()
        .sendMessageAsync(cmd: 'reqDeleteEvent', params: {
      "eventId": eventId,
    });
  }

  ///邀请好友加入活动
  ///10760	没有相关的活动
  ///10761	活动已经过期
  ///10008	没有权限（活动创建者设置了只能自己邀请好友）
  static Future<WSResponseData<void>> invitePersonalEvent(
    String eventId,
    List<int> fdIds,
  ) {
    return SocketManager.getInstance()
        .sendMessageAsync(cmd: 'rspInviteEvent', params: {
      "eventId": eventId,
      "fdIds": fdIds,
    });
  }


  ///appCount:初始展示共同兴趣应用的个数（设备端建议3个）
  static Future<WSResponseData<PersonalHomeModel>> getPersonalHome(
      int actId) async {
    return SocketManager.getInstance().sendMessageAsync(
      cmd: 'reqGetUserDetail',
      params: {"actId": actId},
      dataConvert: (json) => PersonalHomeModel.fromJson(json),
    );
  }
}
