import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'package:websocket_connector/websocket_connector.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';
import 'package:yvr_assistant/utils/string_utils.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';

import '../generated/l10n.dart';

class SocketData {
  Map result;
  String data;

  SocketData(this.result, this.data);
}

abstract class ConnectCallback {
  void onConnectFailed(Exception e);

  void onConnectSuccess();

  void onStartReconnect(int time);

  void onReconnectFailed(int time, Exception e);

  void onReconnectSuccess(int time);

  void onConnectionClosed(int closeCode);
}

class SocketConnect implements WebSocketCallback {
  static const int kInClosing = -1;
  static const int kInClosed = -2;
  static const int kInterrupted = -3;
  static const int kDisconnected = -4;
  static const int kConnectDown = -5;
  static const int kConnectFailed = -6;
  static const int kReConnectFailed = -7;
  static const int kInvalidConfig = -8;

  final String platformUid;
  final String url;
  final Queue<Completer<SocketData>> _queue = Queue<Completer<SocketData>>();
  final Map<Completer<SocketData>, String> _sendFutures = Map();
  ConnectCallback callback;
  void Function(SocketData) onNotify;

  static WSLogger logger = WSLogger("YvrSocketConnect");

  /// 内部构造方法，可避免外部暴露构造函数，进行实例化
  SocketConnect(this.platformUid, this.url) {
    _websocketConnector = SocketConnector(callback: this);
  }

  SocketConnector _websocketConnector;

  /// 开启WebSocket连接
  bool openSocket() {
    if (_websocketConnector.isConnected()) {
      assert(_sendFutures.length == 0);
      _websocketConnector.disconnect();
      _reportError(kInterrupted);
    } else if (_websocketConnector.isConnecting()) {
      assert(_queue.length == 0);
      _websocketConnector.disconnect();
      _retryFutures(kInterrupted);
    }

    String userId = DBUtil.instance.userBox.get(kActId).toString();
    if (userId == null || userId.isEmpty) {
      return false;
    }
    Future.delayed(const Duration(milliseconds: 200), () {
      _websocketConnector.connect(generateConfig(userId), autoReconnect: true);
    });
    return true;
  }

  /// 关闭WebSocket
  void closeSocket() {
    _websocketConnector.disconnect();
    _reportError(kDisconnected);
  }

  Future<SocketData> sendMessageAsync(String message) {
    Completer<SocketData> completer = new Completer<SocketData>();
    if (_websocketConnector.isConnected()) {
      assert(_sendFutures.length == 0);
      _websocketConnector.sendMessage(message);
      _queue.add(completer);
      logger.i("sendMessageAsync:isConnected");
    } else if (_websocketConnector.isConnecting()) {
      assert(_queue.length == 0);
      _sendFutures[completer] = message;
      logger.i("sendMessageAsync:isConnecting,insert to futures");
    } else if (_websocketConnector.isClosing()) {
      completer.completeError(WebSocketConnectException(kInClosing));
      logger.i("sendMessageAsync:isClosing,errors");
    } else {
      completer.completeError(WebSocketConnectException(kInClosed));
      logger.i("sendMessageAsync:isClosed,errors");
    }
    return completer.future;
  }

  void _reportError(int errorCode) {
    if (_queue.isNotEmpty) {
      _queue.forEach((element) {
        element.completeError(WebSocketConnectException(errorCode));
      });
      _queue.clear();
    }
  }

  void _retryFutures(int code) {
    if (code == null) {
      _sendFutures.removeWhere((key, value) {
        _queue.add(key);
        _websocketConnector.sendMessage(value);
        return true;
      });
    } else {
      _sendFutures.removeWhere((key, value) {
        key.completeError(WebSocketConnectException(code));
        return true;
      });
    }
  }

  ConnectConfig generateConfig(String userId) {
    String timestamp =
        (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString();
    String devType = Platform.isIOS ? "3" : "2";
    // 切换登录需要断开Socket 重新连接dynamic _variableName;
    String keyId = userId + "#$platformUid#" + devType;
    String ver = "1.0.0";
    String tempMD5 = StringUtils.toMD5(keyId + ver);
    String sign = StringUtils.toSHA256(tempMD5 + timestamp + "WCMX_YVR");
    // Log.i('Socked连接keyId: $keyId');

    Map<String, dynamic> headers = {
      "timestamp": timestamp,
      "keyId": keyId,
      "ver": ver,
      "sign": sign,
    };
    return ConnectConfig(url, headers, pingTimeSeconds: 50);
  }

  @override
  void onConnectFailed(Exception e) {
    logger.i("onConnectFailed:$e");
    assert(_queue.length == 0);
    _retryFutures(kConnectFailed);
    callback?.onConnectFailed(e);
  }

  @override
  void onConnectSuccess() {
    logger.i("onConnectSuccess");
    assert(_queue.length == 0);
    _retryFutures(null);
    callback?.onConnectSuccess();
  }

  @override
  void onConnectionClosed(int closeCode) {
    logger.i("onConnectionClosed:$closeCode");
    assert(_sendFutures.length == 0);
    _reportError(kConnectDown);
    callback?.onConnectionClosed(closeCode);
  }

  @override
  void onData(String data) {
    logger.i("onData:$data");

    Map result = jsonDecode(data);
    String type = result['type'];
    if (type == 'response') {
      Completer<SocketData> completer = _queue.removeFirst();
      completer.complete(SocketData(result, data));
    } else if (type == 'notify') {
      onNotify?.call(SocketData(result, data));
    }
  }

  @override
  void onReconnectFailed(int time, Exception e) {
    logger.i("onReconnectFailed:$time,$e");
    assert(_queue.length == 0);
    _retryFutures(kReConnectFailed);
    callback?.onReconnectFailed(time, e);
  }

  @override
  void onReconnectSuccess(int time) {
    logger.i("onReconnectSuccess:$time");
    assert(_queue.length == 0);
    _retryFutures(null);
    callback?.onReconnectSuccess(time);
  }

  @override
  ConnectConfig onStartReconnect(int time, ConnectConfig config) {
    logger.i("onStartReconnect:$time");
    String userId = DBUtil.instance.userBox.get(kActId).toString();
    assert(_queue.length == 0);
    if (userId == null || userId.isEmpty) {
      _retryFutures(kInvalidConfig);
      return null;
    }
    callback?.onStartReconnect(time);
    return generateConfig(userId);
  }
}

class WebSocketConnectException implements Exception {
  final int code;

  const WebSocketConnectException(
    this.code,
  );

  @override
  String toString() {
    return "${YLocal.current.qingqiushibai}:[$code]";
  }
}
