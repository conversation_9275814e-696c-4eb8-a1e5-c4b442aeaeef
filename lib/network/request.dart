import 'dart:convert';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/model/Upload_file_model.dart';
import 'package:yvr_assistant/model/app_price_model.dart';
import 'package:yvr_assistant/model/coupon_model.dart';
import 'package:yvr_assistant/model/event_comment_model.dart';
import 'package:yvr_assistant/model/official_event_model.dart';
import 'package:yvr_assistant/model/app_type_model.dart';
import 'package:yvr_assistant/model/clock_model.dart';
import 'package:yvr_assistant/model/consume_model.dart';
import 'package:yvr_assistant/model/dev_info_model.dart';
import 'package:yvr_assistant/model/noti_model.dart';
import 'package:yvr_assistant/model/page_list_model.dart';
import 'package:yvr_assistant/model/pay_history_model.dart';
import 'package:yvr_assistant/model/prod_model.dart';
import 'package:yvr_assistant/model/punch_form_model.dart';
import 'package:yvr_assistant/model/qa_model.dart';
import 'package:yvr_assistant/model/recharge.dart';
import 'package:yvr_assistant/model/res_file_model.dart';
import 'package:yvr_assistant/model/subject_model.dart';
import 'package:yvr_assistant/model/theme_data_model.dart';
import 'package:yvr_assistant/model/user_assets.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/model/home_model.dart';
import 'package:yvr_assistant/model/user_model.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';
import '../model/Event_square_num_model.dart';
import '../model/app_config_model.dart';
import '../model/bundled_info_model.dart';
import '../model/official_event_detail_model.dart';
import '../model/personal_home_model.dart';
import '../model/teen_model.dart';

class YvrRequests {
  /// 用户信息
  static Future getUserInfoFromMobile() async {
    var response =
        await http.post('vrmcsys/account/getUserInfoFromMobile', data: {});
    return UserModel.fromJson(response.data);
  }

  static Future uploadAvatar(FormData formData) async {
    var response =
        await http.post('vrmcsys/account/uploadAvatar', data: formData);
    return response.data["url"];
  }

  /// 首页
  static Future getAppsRecmd() async {
    var response = await http.post('vrmcsys/appstore/getAppsRecmd',
        data: {"source": Platform.isIOS ? "3" : "2"});
    return HomeModel.fromJson(response.data);
  }

  /// 应用详情页
  static Future getAppDetail(int appId) async {
    Map<String, dynamic> params = {
      "appId": appId,
    };
    var response =
        await http.post('vrmcsys/appstore/getAppDetail', data: params);

    return ProdModel.fromJson(response.data);
  }

  /// 单个主题
  static Future getThemeInfo(int themeId) async {
    Map<String, dynamic> params = {"themeId": themeId};
    var response =
        await http.post('vrmcsys/appstore/getThemeInfo', data: params);
    return ThemeDataModel.fromJson(response.data);
  }

  /// 单个专题
  static Future getAppsOnSubject(int subjectId) async {
    Map<String, dynamic> params = {"subjectId": subjectId};
    var response =
        await http.post('vrmcsys/appstore/getAppsOnSubject', data: params);
    return SubjectModel.fromJson(response.data);
  }

  /// 已购应用
  static Future getAppsPurchased() async {
    Map<String, dynamic> params = {};
    var response =
        await http.post('vrmcsys/appstore/getAppsPurchased', data: params);
    return SubjectModel.fromJson(response.data);
  }

  /// 全部应用
  static Future getAppsOnStore(
      {int cgy = 0,
      int tag = 0,
      int prg = 0,
      int sort = 0,
      int lprice = 0,
      int hprice = 0,
      int page = 0,
      int psize = 10}) async {
    Map<String, dynamic> params = {
      "cgy": cgy,
      "tag": tag,
      "prg": prg,
      "sort": sort,
      "lprice": lprice,
      "hprice": hprice,
      "page": page,
      "psize": psize,
      "source": Platform.isIOS ? "3" : "2"
    };
    var response =
        await http.post('vrmcsys/appstore/getAppsOnStore', data: params);
    return SubjectModel.fromJson(response.data);
  }

  /// 评论列表
  static Future getAppComments(
      {int appId, int cmtId = 0, int star = 0, int size = 20}) async {
    Map<String, dynamic> params = {
      "appId": appId,
      "cmtId": cmtId,
      "star": star,
      "size": size
    };
    var response =
        await http.post('vrmcsys/appstore/getAppComments', data: params);

    /// 保存用户是否已经评论过该应用
    /// StorageManager.localStorage
    ///     .setItem("comment_$appId", response.data["status"] ?? 0);
    /// return response.data["comments"]
    ///     .map<CommentModel>((item) => CommentModel.fromJson(item))
    ///     .toList();
    return response.data;
  }

  /// 问题列表<二级分类>
  static Future getAllFeedbackDesc() async {
    var response =
        await http.post('vrmcsys/appstore/getAllFeedbackDesc', data: {});
    return response.data["fdDesc"]
        .map<QaModel>((item) => QaModel.fromJson(item))
        .toList();
  }

  /// 获取个人登录设备列表
  static Future getLoginDevIds() async {
    Map<String, dynamic> params = {};
    var response =
        await http.post('vrmcsys/account/getLoginDevIds', data: params);
    return response.data["devIds"]
            .map<String>((item) => item.toString())
            .toList() ??
        [];
  }

  /// 获取未读消息数量
  static Future getUnreadNtyNum(String fromTime) async {
    /// 未传入时间 时间设置拨回到本地时间的前一个月

    Map<String, dynamic> params = {
      "fromTime": fromTime ?? Global.kPreMonthDate,
      "media": Platform.isIOS ? "3" : "2",
    };
    var response =
        await http.post('vrmcsys/ntyserver/getUnreadNtyNum', data: params);
    return response.data["num"];
  }

  /// 获取未读消息数量
  static Future getAllNtyList(String fromTime) async {
    Map<String, dynamic> params = {
      "fromTime": fromTime ?? Global.kPreMonthDate,
      "media": Platform.isIOS ? "3" : "2",
    };
    var response =
        await http.post('vrmcsys/ntyserver/getAllNtyList', data: params);

    return response.data["ntys"]
        .map<NotiModel>((item) => NotiModel.fromJson(item))
        .toList();
  }

  /// 获取未读消息数量
  static Future disposeNotify({int ntyId, String tag, int mark}) async {
    Map<String, dynamic> params = {
      "ntyId": ntyId,
      "tag": tag,
      "mark": mark,
    };
    var response =
        await http.post('vrmcsys/ntyserver/disposeNotify', data: params);
    return response.data["errCode"];
  }

  /// 查询购买记录列表
  static Future purchaseAppList() async {
    var response = await http.get('vrmcsys/appstore/purchase_app_list');
    return response.data["content"]
        .map<PayHistoryModel>((item) => PayHistoryModel.fromJson(item))
        .toList();
  }

  /// 获取已购买的应用列表
  static Future getPurchasedAppId() async {
    Map<String, dynamic> params = {
      "appId": 0,
    };
    var response =
        await http.post('vrmcsys/appstore/getPurchasedAppId', data: params);
    return response.data["appIds"] ?? [];
  }

  /// Y币充值查询
  static Future queryRechargeList() async {
    var response = await http.get('vrmcsys/appstore/queryRechargeList');
    return response.data["content"]
        .map<RechargeModel>((item) => RechargeModel.fromJson(item))
        .toList();
  }

  /// Y币充值查询
  static Future queryYconsumeList() async {
    var response = await http.get('vrmcsys/appstore/queryYconsumeList');
    return response.data["content"]
        .map<ConsumeModel>((item) => ConsumeModel.fromJson(item))
        .toList();
  }

  /// 判断账号预发布应用权限
  static Future quireAppPrePublishAuth() async {
    var response =
        await http.post('vrmcsys/appstore/quireAppPrePublishAuth', data: {});
    return response.data["auth"] ?? 0;
  }

  /// 获取个人登录设备信息列表
  static Future getLoginDevInfos() async {
    var response =
        await http.post('vrmcsys/account/getLoginDevInfos', data: {});
    return response.data["devs"]
            .map<DevInfoModel>((item) => DevInfoModel.fromJson(item))
            .toList() ??
        [];
  }

  /// 获取设备活动详情，供活动页面调用: 参数请求除打卡记录以外的数据，防止后端查询时间过长
  static Future getDeviceDetail({String devId, String today}) async {
    Map<String, dynamic> params = {
      "devId": devId,
      "fromDate": today,
      "toDate": today,
    };
    var response =
        await http.post('vrmcsys/account/punch/getDeviceDetail', data: params);

    return ClockInModel.fromJson(response.data);
  }

  /// 获取设备打卡记录，全部数据
  static Future getDeviceRecords({String devId}) async {
    Map<String, dynamic> params = {"devId": devId};
    var response =
        await http.post('vrmcsys/account/punch/getDeviceRecords', data: params);
    return response.data["records"] ?? [];
  }

  /// 获取应用标签
  static Future getAppTagInfo() async {
    var response = await http.post('vrmcsys/appstore/getAppTagInfo', data: {});
    return response.data["tags"]
            .map<AppTypeModel>((item) => AppTypeModel.fromJson(item))
            .toList() ??
        [];
  }

  /// 获取应用相关配置
  static Future getAppCfgInfo() async {
    var response = await http.post('vrmcsys/appstore/getAppCfgInfo', data: {});
    return response.data["cgys"]
            .map<AppTypeModel>((item) => AppTypeModel.fromJson(item))
            .toList() ??
        [];
  }

  /// 获取应用相关配置
  static Future getAppPriceRanges() async {
    var response =
        await http.post('vrmcsys/appstore/getAppPriceRanges', data: {});
    return response.data["ranges"]
            .map<AppPriceModel>((item) => AppPriceModel.fromJson(item))
            .toList() ??
        [];
  }

  /// 获取个人资产
  static Future getUserAssets() async {
    var response = await http.post('vrmcsys/account//getUserAssets', data: {});
    return UserAssetsModel.fromJson(response.data);
  }

  ///官方活动列表
  ///排序规则：默认：default；最近活动：newest；最多关注：most
  static Future<ResponseData<PageListModel<OfficialEventModel>>>
      getOfficialEvent(int size, int current, String sortType) async {
    var response = await http.get(
      'vrmcsys/fdserver/event/officialEventPage',
      queryParameters: <String, dynamic>{
        'size': size,
        'current': current,
        'sortType': sortType
      },
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<PageListModel<OfficialEventModel>>.convertFromJson(
      response,
      dataConvert: (json) => PageListModel<OfficialEventModel>.fromJson(
        json,
        (json) => OfficialEventModel.fromJson(json),
      ),
    );
  }

  ///官方活动列表
  ///排序规则：默认：default；最近活动：newest；最多关注：most
  static Future<ResponseData<PageListModel<OfficialEventModel>>> getMyEvent(
      int size, int current, String sortType) async {
    var response = await http.get(
      'vrmcsys/fdserver/event/myEventPage',
      queryParameters: <String, dynamic>{
        'size': size,
        'current': current,
        'sortType': sortType
      },
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<PageListModel<OfficialEventModel>>.convertFromJson(
      response,
      dataConvert: (json) => PageListModel<OfficialEventModel>.fromJson(
        json,
        (json) => OfficialEventModel.fromJson(json),
      ),
    );
  }

  ///官方活动详情页-官方活动和应用信息
  static Future<ResponseData<OfficialEventDetailModel>> getOfficialEventDetail(
      int eventId) async {
    var response = await http.get(
      'vrmcsys/fdserver/event/officialEventDetail',
      queryParameters: <String, dynamic>{
        'eventId': eventId,
      },
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<OfficialEventDetailModel>.convertFromJson(
      response,
      dataConvert: (json) => OfficialEventDetailModel.fromJson(
        json,
      ),
    );
  }

  ///官方活动列表
  ///排序规则：默认：default；最近活动：newest；最多关注：most
  static Future<ResponseData<PageListModel<EventCommentModel>>> getEventComment(
      int size, int current, int eventId) async {
    var response = await http.get(
      'vrmcsys/fdserver/event/eventCommentPage',
      queryParameters: <String, dynamic>{
        'size': size,
        'current': current,
        'eventId': eventId
      },
      options: Http.buildNoAutoProcessErrorOptions(),
    );

    return ResponseData<PageListModel<EventCommentModel>>.convertFromJson(
      response,
      dataConvert: (json) => PageListModel<EventCommentModel>.fromJson(
        json,
        (json) => EventCommentModel.fromJson(json),
      ),
    );
  }

  ///关注/取消关注-官方活动
  ///请求类型：关注：focus 取消关注：cancel_focus
  ///11001	参数错误
  /// 10041	系统错误
  /// 10760	官方活动不存在
  /// 10763	已经关注该活动，不可重复关注
  /// 10764	未关注该活动，不可取消关注
  static Future<ResponseData<void>> followOfficialEvent(
      int eventId, bool follow) async {
    var response = await http.post(
      'vrmcsys/fdserver/event/focusOfficialEvent',
      data: <String, dynamic>{
        'eventId': eventId,
        'requestType': follow ? "focus" : "cancel_focus",
      },
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<void>.convertFromJson(response);
  }

  ///新增评论
  ///11001	参数错误
  /// 10540	评论内容违规
  /// 10041	系统错误
  static Future<ResponseData<void>> insertComment(
    int eventOfficialId,
    int appId,
    int userId,
    String userPhone, {
    String words,
    List<String> images,
  }) async {
    Map<String, dynamic> contentMap = {
      'words': words ?? '',
      'images': images ?? [],
    };

    var response = await http.post(
      'vrmcsys/fdserver/event/insertComment',
      data: <String, dynamic>{
        'eventOfficialId': eventOfficialId,
        'appId': appId,
        'userId': userId,
        'userPhone': userPhone,
        'content': jsonEncode(contentMap),
      },
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<void>.convertFromJson(response);
  }

  ///移动端用户文件上传
  /// 11001	参数不对/文件为空
  /// 10540	上传文件未通过智能审计
  /// 10041	系统错误
  static Future<ResponseData<UploadFileModel>> uploadOfficialEventImage(
      Uint8List data, String fileName, int id) async {
    var file = MultipartFile.fromBytes(
      data,
      filename: fileName,
    );
    FormData formData = FormData.fromMap(
      {
        "businessType": "official_event",
        "file": file,
        "businessId": id,
      },
    );
    var response = await http.post(
      'vrmcsys/account/file/upload',
      data: formData,
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<UploadFileModel>.convertFromJson(
      response,
      dataConvert: (json) => UploadFileModel.fromJson(json),
    );
  }

  ///个人删除个人评论
  ///11001	参数错误
  ///10041	系统错误
  static Future<ResponseData<void>> deleteComment(
    int commentId,
  ) async {
    var response = await http.post(
      'vrmcsys/fdserver/event/delOwnComment',
      data: <String, dynamic>{
        'commentId': commentId,
      },
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<void>.convertFromJson(response);
  }

  ///移动端用户文件删除（未提交业务）
  ///11001	参数不对/文件为空
  ///10041	系统错误
  static Future<ResponseData<void>> deleteOfficialEventImage(
      String fileUrl) async {
    var response = await http.post(
      'vrmcsys/account/file/delete',
      data: <String, dynamic>{
        'fileUrl': fileUrl,
        'businessType': 'official_event',
      },
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<void>.convertFromJson(response);
  }

  ///获取活动广场数量
  ///11001	参数错误
  static Future<EventSquareNumModel> getEventSquareNum() async {
    var response = await http.get(
      'vrmcsys/fdserver/event/eventSquareNum',
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return EventSquareNumModel.fromJson(response.data);
  }

  /// 活动返现申请详情
  static Future punchAuditDetail(String deviceId) async {
    var response = await http
        .get('vrmcsys/account/punch/punchAuditDetail?deviceId=$deviceId');
    return PunchFormModel.fromJson(response.data);
  }

  /// 获取用户所拥有的优惠券信息
  static Future appCouponDetailListFromMobile(int appId) async {
    var response = await http
        .get('vrmcsys/account/appCouponDetailListFromMobile?appId=$appId');
    StorageManager.localStorage
        .setItem("kUserYcoin", response.data["ycount"] ?? 0);
    return response.data["content"]
            .map<AccountCouponDetailList>(
                (item) => AccountCouponDetailList.fromJson(item))
            .toList() ??
        [];
  }

  ///打开青少年模式
  /// 10035	设备和账号非绑定关系
  static Future<ResponseData<void>> openTeenagerMode(
      String code, String devId) async {
    var response = await http.post(
      'vrmcsys/account/teen/openTeenagerMode',
      data: <String, dynamic>{
        'code': code,
        'devId': devId,
      },
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<void>.convertFromJson(response);
  }

  ///关闭青少年模式
  /// 10035	设备和账号非绑定关系
  /// 10830	不存在对应的青少年模式
  /// 10831	密码错误
  static Future<ResponseData<void>> closeTeenagerMode(
      String code, String devId) async {
    var response = await http.post(
      'vrmcsys/account/teen/closeTeenagerMode',
      data: <String, dynamic>{
        'code': code,
        'devId': devId,
      },
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<void>.convertFromJson(response);
  }

  ///校验青少年密码
  /// status	int	1正确 2错误
  static Future<ResponseData<TeenVerifyModel>> checkTeenagerCode(
      String code, String devId) async {
    var response = await http.post(
      'vrmcsys/account/teen/checkTeenagerCode',
      data: <String, dynamic>{
        'code': code,
        'devId': devId,
      },
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<TeenVerifyModel>.convertFromJson(
      response,
      dataConvert: (json) => TeenVerifyModel.fromJson(json),
    );
  }

  ///修改默认时间段
  /// 10035	设备和账号非绑定关系
  /// 10830	不存在对应的青少年模式
  static Future<ResponseData<void>> changeTeenagerTime(
      String devId, String weekday, String weekend) async {
    var response = await http.post(
      'vrmcsys/account/teen/changeTeenagerTime',
      data: <String, dynamic>{
        'devId': devId,
        'weekday': weekday,
        'weekend': weekend,
      },
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<void>.convertFromJson(response);
  }

  ///设置当天时间段
  /// 10035	设备和账号非绑定关系
  /// 10830	不存在对应的青少年模式
  static Future<ResponseData<void>> setTeenagerDayTime(
      String devId, String daytime) async {
    var response = await http.post(
      'vrmcsys/account/teen/setTeenagerDayTime',
      data: <String, dynamic>{
        'devId': devId,
        'daytime': daytime,
      },
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<void>.convertFromJson(response);
  }

  ///获取青少年信息
  static Future<ResponseData<TeenModeModel>> getTeenagerInfo(
      String devId) async {
    var response = await http.post(
      'vrmcsys/account/teen/getTeenagerInfo',
      data: <String, dynamic>{
        'devId': devId,
      },
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<TeenModeModel>.convertFromJson(
      response,
      dataConvert: (json) => TeenModeModel.fromJson(json),
    );
  }

  ///修改青少年密码
  ///10035	设备和账号非绑定关系
  ///10830	不存在对应的青少年模式
  ///10001	账号不存在
  ///10051	验证码错误
  static Future<ResponseData<void>> changeTeenagerCode(
      String devId, String code, String smsCode) async {
    var response = await http.post(
      'vrmcsys/account/teen/changeTeenagerCode',
      data: <String, dynamic>{
        'devId': devId,
        'code': code,
        'smsCode': smsCode,
      },
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<TeenModeModel>.convertFromJson(response);
  }

  ///根据设备获取设备青少年模式
  static Future<ResponseData<int>> getDevTeenagerMode(String devId) async {
    var response = await http.post(
      'vrmcsys/account/teen/getDevTeenagerMode',
      data: <String, dynamic>{
        'devId': devId,
      },
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<int>.convertFromJson(
      response,
      dataConvert: (json) => json['mode'],
    );
  }

  ///查询数据库配置的宣传信息
  static Future<ResponseData<AppConfigModel>> getAppMessage(int appId) async {
    var response = await http.get(
      'vrmcsys/appstore/queryAppMessage',
      queryParameters: <String, dynamic>{
        'appId': appId,
      },
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<AppConfigModel>.convertFromJson(
      response,
      dataConvert: (json) => AppConfigModel.fromJson(json),
    );
  }

  ///上传打卡信息相关图片
  static Future<ResponseData<ResUploadFileModel>> uploadAuditFile(
      {Uint8List data, String fileName, String requestType}) async {
    var file = MultipartFile.fromBytes(
      data,
      filename: fileName,
    );
    FormData formData = FormData.fromMap(
      {
        "requestType": requestType,
        "file": file,
      },
    );
    var response = await http.post(
      'vrmcsys/account/punch/uploadAuditFile',
      data: formData,
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<ResUploadFileModel>.convertFromJson(
      response,
      dataConvert: (json) => ResUploadFileModel.fromJson(json),
    );
  }

  ///判断应用是否已购买
  ///0未购买 1已购买 2退款中
  static Future<ResponseData<int>> validateAppPurchased(int appId) async {
    var response = await http.post(
      'vrmcsys/appstore/validateAppPurchased',
      data: <String, dynamic>{
        'appId': appId,
      },
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<int>.convertFromJson(
      response,
      dataConvert: (json) => json['purchased'],
    );
  }

  //获取捆绑专题详情
  static Future<ResponseData<BundledInfoModel>> getBundledInfo(
      Map<String, dynamic> parameters) async {
    var response =
        await http.post('vrmcsys/appstore/getAppsOnSubject', data: parameters);
    return ResponseData<BundledInfoModel>.convertFromJson(response,
        dataConvert: (json) => BundledInfoModel.fromJson(json));
  }

  //获取捆绑介绍
  static Future<ResponseData<dynamic>> getBundledIntroduce() async {
    var response = await http.get('vrmcsys/appstore/querySubjectSaleIntroduce');
    return ResponseData<dynamic>.convertFromJson(response,
        dataConvert: (json) => json['msg']);
  }

  //解绑设备
  static Future<ResponseData<dynamic>> unBindDevice(
      Map<String, dynamic> parameters) async {
    var response =
        await http.post('vrmcsys/account/removeAccountDev', data: parameters);
    return ResponseData<dynamic>.convertFromJson(response);
  }

  ///上传个人背景图片
  /// 10540	图片验证不通过
  static Future<ResponseData<String>> uploadPersonalBackgroundImage(
      Uint8List data, String fileName) async {
    var file = MultipartFile.fromBytes(
      data,
      filename: fileName,
    );
    FormData formData = FormData.fromMap({"bgFile": file});
    var response = await http.post(
      'vrmcsys/account/uploadBgPic',
      data: formData,
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<String>.convertFromJson(
      response,
      dataConvert: (json) => json['url'],
    );
  }

  ///上传个人背景图片
  /// 10540	图片验证不通过
  static Future<ResponseData<String>> uploadPersonalAvatar(
      Uint8List data, String fileName) async {
    var file = MultipartFile.fromBytes(
      data,
      filename: fileName,
    );
    FormData formData = FormData.fromMap({"avatarFile": file});
    var response = await http.post(
      'vrmcsys/account/uploadAvatar',
      data: formData,
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<String>.convertFromJson(
      response,
      dataConvert: (json) => json['url'],
    );
  }

  ///获取个人主页，手机端调用
  static Future<ResponseData<PersonalHomeModel>> getPersonalHome() async {
    var response = await http.post(
      'vrmcsys/account/getUserHomePage',
      options: Http.buildNoAutoProcessErrorOptions(),
      data: {},
    );
    return ResponseData<PersonalHomeModel>.convertFromJson(
      response,
      dataConvert: (json) => PersonalHomeModel.fromJson(json),
    );
  }

  ///设置个人信息接口
  ///10540	昵称验证不通过
  static Future<ResponseData<void>> updateUserInfo(
      {String nick, String birth, int sex, String motto}) async {
    var response = await http.post(
      'vrmcsys/account/setUserInfo',
      data: <String, dynamic>{
        if (nick != null) "nick": nick,
        if (birth != null) "birth": birth,
        if (sex != null) "sex": sex,
        if (motto != null) "motto": motto
      },
      options: Http.buildNoAutoProcessErrorOptions(),
    );
    return ResponseData<void>.convertFromJson(response);
  }

  //邮箱注册
  static Future<ResponseData<dynamic>> registerEmail(
      Map<String, dynamic> parameters) async {
    var response =
        await http.post('vrmcsys/account/registerMail', data: parameters);
    return ResponseData<dynamic>.convertFromJson(response,
        dataConvert: (json) => json['data']);
  }

  /// 获取用户所拥有的优惠券信息
  static Future getCouponListFromMobile() async {
    var response =
        await http.post('vrmcsys/account/getCouponListFromMobile', data: {});
    return response.data["coupons"]
        .map<CouponModel>((item) => CouponModel.fromJson(item))
        .toList();
  }
}
