import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:crypto/crypto.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:common_utils/common_utils.dart';
import 'package:yvr_assistant/utils/string_utils.dart';
import 'package:yvr_assistant/model/vr_file_model.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';

import '../generated/l10n.dart';

class VrHttp {
  ///超时时间 https://juejin.cn/post/6844904190838325262
  static const int CONNECT_TIMEOUT = 10000;
  static const int RECEIVE_TIMEOUT = 30000;

  static VrHttp _instance = VrHttp._internal();
  factory VrHttp() => _instance;

  static Dio dio;

  VrHttp._internal() {
    if (dio == null) {
      // BaseOptions、Options、RequestOptions 都可以配置参数，优先级别依次递增，且可以根据优先级别覆盖参数
      BaseOptions options = new BaseOptions(
        connectTimeout: CONNECT_TIMEOUT,
        // 响应流上前后两次接受到数据的间隔，单位为毫秒。
        receiveTimeout: RECEIVE_TIMEOUT,
        // Http请求头.
        headers: {},
      );

      dio = new Dio(options);

      // 添加error拦截器
      dio.interceptors.add(ErrorInterceptor());
    }
  }

  ///初始化公共属性
  ///
  /// [baseUrl] 地址前缀
  /// [connectTimeout] 连接超时时间
  /// [receiveTimeout] 接收超时时间
  /// [interceptors] 基础拦截器
  void initVrHttp({
    String baseUrl,
    List<Interceptor> interceptors,
    String snCode,
  }) {
    dio.options = BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: CONNECT_TIMEOUT,
      receiveTimeout: RECEIVE_TIMEOUT,
    );
    if (interceptors != null && interceptors.isNotEmpty) {
      dio.interceptors..addAll(interceptors);
    }
  }

  static Future<List> doQueryFiles({int pages, String snCode}) async {
    dio.options.headers = configHeader(snCode);
    Map param = {"mimetype": 0, "pages": pages, "nums": 10, "date": 0};

    Log.d('网络信息：${dio.options.baseUrl} \n${dio.options.headers} \n$param');
    var response = await dio.post("api/doQueryFiles", data: param);
    if (response?.data == null || response.data["files"] == null) {
      Log.e("返回数据为空或格式不正确：${response?.data['errcode']}");
      if (response?.data['errcode'] == '-3') {
        YvrToast.showToast(YLocal.current.KeepTimeSynchronized);
      }
    }

    Log.d("VR 页码：$pages \n文件列表数据：${response.data['files']}");
    return response.data["files"]
        .map<VrFileModel>((item) => VrFileModel.fromJson(item))
        .toList();
  }

  static Future doDeleteFiles({List ids, String snCode}) async {
    dio.options.headers = configHeader(snCode);
    Map param = {"ids": ids};
    var response = await dio.post("api/doDeleteFiles", data: param);
    Log.d("VR 删除文件结果：${response.data}");
    return response.data["errcode"];
  }

  static Map configHeader(String snCode) {
    String timestamp = DateUtil.getNowDateMs().toString();
    String md5Sn =
        StringUtils.toMD5(StringUtils.toMD5(snCode).toUpperCase() + "WCMX_YVR");
    var bytes = utf8.encode(timestamp + md5Sn);
    var sign = sha256.convert(bytes);
    Map<String, dynamic> headers = {
      "timestamp": timestamp,
      "sign": sign,
    };
    return headers;
  }
}

/// 错误处理拦截器
class ErrorInterceptor extends Interceptor {
  @override
  void onError(DioError err, ErrorInterceptorHandler handler) {
    YvrToast.showToast(YLocal.current.qinglianjiezhixiangt);
    super.onError(err, handler);
  }
}
