import 'package:flutter/widgets.dart';

class KeepAliveStatefulBuilder extends StatefulWidget {
  const KeepAliveStatefulBuilder({
    Key key,
    @required this.builder,
  })  : assert(builder != null),
        super(key: key);

  final StatefulWidgetBuilder builder;

  @override
  _KeepAliveStatefulBuilderState createState() => _KeepAliveStatefulBuilderState();
}

class _KeepAliveStatefulBuilderState extends State<KeepAliveStatefulBuilder>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.builder(context, setState);
  }

  @override
  bool get wantKeepAlive => true;
}
