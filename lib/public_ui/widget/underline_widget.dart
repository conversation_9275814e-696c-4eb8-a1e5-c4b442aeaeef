import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';

class UnderlineWidget extends SingleChildRenderObjectWidget {
  const UnderlineWidget({
    Key key,
    Widget child,
  }) : super(key: key, child: child);

  @override
  UnderlineRenderObject createRenderObject(BuildContext context) {
    return UnderlineRenderObject();
  }

  @override
  void updateRenderObject(BuildContext context,
      UnderlineRenderObject renderObject) {}
}

class UnderlineRenderObject extends RenderProxyBox {

  Paint _linePaint = Paint()
    ..color = Color(0xff767880)
    ..strokeWidth = 0.5;

  @override
  void paint(PaintingContext context, Offset offset) {
    super.paint(context, offset);
    double y = size.height * 0.6;
    context.canvas.drawLine(
        offset.translate(0, y), offset.translate(size.width, y), _linePaint);
  }
}
