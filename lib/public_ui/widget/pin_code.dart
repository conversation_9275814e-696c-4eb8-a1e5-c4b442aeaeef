import 'dart:async';

import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

import '../../utils/textInput_formatter.dart';

class PinCode extends StatelessWidget {
  final ValueChanged<String> onCompleted;
  final TextEditingController controller;
  final bool enabled;
  final bool autoDismissKeyboard;
  final bool autoFocus;
  final bool autoUnfocus;
  final FocusNode focusNode;
  final bool readOnly;
  final StreamController<ErrorAnimationType> errorAnimationController;

  const PinCode({
    Key key,
    this.onCompleted,
    this.controller,
    this.enabled = true,
    this.autoDismissKeyboard = true,
    this.autoFocus = false,
    this.autoUnfocus = true,
    this.focusNode,
    this.readOnly = false,
    this.errorAnimationController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PinCodeTextField(
      focusNode: focusNode,
      inputFormatters: [NumberFormatter()],
      appContext: context,
      textStyle: TextStyle(
        fontSize: 26,
        color: Color(0xff6E7380),
      ),
      length: 4,
      obscuringWidget: Container(
        width: 10,
        height: 10,
        decoration: BoxDecoration(
          color: Color(0xff6E7380),
          shape: BoxShape.circle,
        ),
      ),
      blinkWhenObscuring: true,
      animationType: AnimationType.fade,
      errorAnimationController: this.errorAnimationController,
      pinTheme: PinTheme(
        shape: PinCodeFieldShape.box,
        borderRadius: BorderRadius.circular(5),
        fieldHeight: 54,
        fieldWidth: 54,
        borderWidth: 2,
        activeFillColor: Color(0xffF0F2F5),
        selectedColor: Color(0xff4F7FFE),
        selectedFillColor: Color(0xffF0F2F5),
        inactiveFillColor: Color(0xffF0F2F5),
        inactiveColor: Color(0xffF0F2F5),
        activeColor: Color(0xffF0F2F5),
        disabledColor: Color(0xffF0F2F5),
      ),
      showCursor: false,
      animationDuration: const Duration(milliseconds: 300),
      enableActiveFill: true,
      keyboardType: TextInputType.number,
      onCompleted: onCompleted,
      beforeTextPaste: (text) {
        return false;
      },
      onChanged: (String value) {},
      controller: controller,
      autoDismissKeyboard: false,
      autoFocus: true,
      autoUnfocus: false,
      enabled: enabled,
    );
  }
}
