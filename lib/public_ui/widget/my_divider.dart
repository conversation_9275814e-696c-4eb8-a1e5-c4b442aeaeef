import 'package:flutter/material.dart';

/// indent: 起点缩进距离
/// endIndent: 终点缩进距离
/// color: 分割线颜色
/// height: 分割线区域的高度，并非分割线的高度
/// thickness: 分割线的厚度，真正的分割线的高度
class MyDivider extends StatelessWidget {
  final color;
  final double indent;
  final double endIndent;
  final backgroundColor;
  const MyDivider(
      {Key key, this.color, this.backgroundColor, this.indent, this.endIndent})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Divider(
      height: 0.6,
      thickness: 0.6,
      indent: indent ?? 20.0,
      endIndent: endIndent ?? 20.0,
      color: color ?? Color(0xff393A3C),
    );
  }
}
