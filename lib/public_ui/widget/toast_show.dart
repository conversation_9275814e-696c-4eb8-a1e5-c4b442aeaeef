import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/network/socket.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:yvr_assistant/network/socket_connect.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class YvrToast {
  static void showExceptionMessage(Exception e,
      {position = EasyLoadingToastPosition.center, Duration duration}) {
    if (e is DioError) {
      checkAndShowHttpError(e, position: position, duration: duration);
    } else if (e is WebSocketConnectException) {
      String dioMessage = getWSErrorMessage(e);
      if (dioMessage != null) {
        YvrToast.showToast(dioMessage, position: position, duration: duration);
      }
    } else {
      YvrToast.showToast(e.toString(), position: position, duration: duration);
    }
  }

  static void showToast(String msg,
      {position = EasyLoadingToastPosition.center, Duration duration}) {
    dismiss();
    EasyLoading.showToast(msg, toastPosition: position, duration: duration);
  }

  static void showLoading({String message = ""}) {
    dismiss();
    if (message == null || message.isEmpty) {
      EasyLoading.show(
          indicator: SizedBox(
              width: 50,
              height: 50,
              child: SpinKitCircle(
                color: Colors.white,
              )));
    } else {
      EasyLoading.show(status: message);
    }
  }

  static void showSuccess(String msg) {
    dismiss();
    EasyLoading.showSuccess(msg);
  }

  static void showError(String msg) {
    dismiss();
    EasyLoading.showError(msg);
  }

  static void showWarn(String msg) {
    dismiss();
    EasyLoading.showInfo(msg);
  }

  static void showProgress(double value, {String status}) {
    if (status == null) {
      status = YLocal.current.zhengzaibaocun;
    }
    dismiss();
    EasyLoading.showProgress(value, status: status);
  }

  static void dismiss() {
    EasyLoading.dismiss();
  }

  static void setToastStyle() {
    EasyLoading.instance
      ..radius = 6
      ..fontSize = 14
      ..userInteractions = false
      ..maskColor = Colors.transparent
      ..textColor = const Color(0xffFFFFFF)
      ..progressColor = const Color(0xffE8E8E8)
      ..indicatorColor = const Color(0XFFFFFFFF)
      ..backgroundColor = const Color.fromRGBO(0, 0, 0, 0.6)
      ..displayDuration = const Duration(milliseconds: 1000)
      ..boxShadow = [const BoxShadow(color: Colors.transparent)]
      ..maskType = EasyLoadingMaskType.custom
      ..loadingStyle = EasyLoadingStyle.custom
      ..contentPadding = const EdgeInsets.all(12)
      ..indicatorType = EasyLoadingIndicatorType.circle
      ..successWidget = Padding(
        padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
        child: SvgPicture.asset(
          "assets/svg/yvr_done.svg",
          width: 72,
          height: 72,
          color: const Color(0XFFFFFFFF),
        ),
      )
      ..errorWidget = Padding(
        padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
        child: SvgPicture.asset(
          "assets/svg/yvr_error.svg",
          width: 72,
          height: 72,
          color: const Color(0XFFFFFFFF),
        ),
      );
  }

  static bool checkAndShowHttpError(DioError error,
      {position = EasyLoadingToastPosition.center, Duration duration}) {
    String dioMessage = getDioErrorMessage(error);
    if (dioMessage != null) {
      YvrToast.showToast(dioMessage, position: position, duration: duration);
      return true;
    }
    return false;
  }

  ///如果键盘弹出就居底部显示，否则就居中显示
  static EasyLoadingToastPosition positionWithViewInsetsBottom() {
    /// 键盘高度
    var viewInsets = EdgeInsets.fromWindowPadding(
        WidgetsBinding.instance.window.viewInsets,
        WidgetsBinding.instance.window.devicePixelRatio);

    EasyLoadingToastPosition position = viewInsets.bottom > 0
        ? EasyLoadingToastPosition.bottom
        : EasyLoadingToastPosition.center;
    return position;
  }
}
