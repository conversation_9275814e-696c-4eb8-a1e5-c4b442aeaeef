import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';
import 'dart:math' as math;

class SmartRow extends MultiChildRenderObjectWidget {
  SmartRow({
    Key key,
    this.spacing = 0.0,
    List<Widget> children = const <Widget>[],
  }) : super(key: key, children: children);

  final double spacing;

  @override
  RenderSmartRow createRenderObject(BuildContext context) {
    return RenderSmartRow(
      spacing: spacing,
    );
  }

  @override
  void updateRenderObject(BuildContext context, RenderSmartRow renderObject) {
    renderObject..spacing = spacing;
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DoubleProperty('spacing', spacing));
  }
}

class SmartRowParentData extends ContainerBoxParentData<RenderBox> {}

class RenderSmartRow extends RenderBox
    with
        ContainerRenderObjectMixin<RenderBox, SmartRowParentData>,
        RenderBoxContainerDefaultsMixin<RenderBox, SmartRowParentData> {
  RenderSmartRow({
    List<RenderBox> children,
    double spacing = 0.0,
  })  : assert(spacing != null),
        _spacing = spacing {
    addAll(children);
  }

  double get spacing => _spacing;
  double _spacing;

  set spacing(double value) {
    assert(value != null);
    if (_spacing == value) return;
    _spacing = value;
    markNeedsLayout();
  }

  @override
  void setupParentData(RenderBox child) {
    if (child.parentData is! SmartRowParentData)
      child.parentData = SmartRowParentData();
  }

  @override
  double computeMinIntrinsicWidth(double height) {
    return _computeSpace();
  }

  double _computeSpace() {
    return spacing * (childCount > 0 ? childCount - 1 : 0);
  }

  @override
  double computeMaxIntrinsicWidth(double height) {
    double width = 0.0;
    RenderBox child = firstChild;
    while (child != null) {
      width += child.getMaxIntrinsicWidth(height);
      child = childAfter(child);
    }
    return width + _computeSpace();
  }

  @override
  double computeMinIntrinsicHeight(double width) {
    RenderBox child = firstChild;
    double height = 0;
    while (child != null) {
      width = math.max(height, child.getMinIntrinsicHeight(width));
      child = childAfter(child);
    }
    return height;
  }

  @override
  double computeMaxIntrinsicHeight(double width) {
    RenderBox child = firstChild;
    double height = 0;
    while (child != null) {
      width = math.max(height, child.getMinIntrinsicHeight(width));
      child = childAfter(child);
    }
    return height;
  }

  @override
  double computeDistanceToActualBaseline(TextBaseline baseline) {
    return defaultComputeDistanceToHighestActualBaseline(baseline);
  }

  @override
  Size computeDryLayout(BoxConstraints constraints) {
    final BoxConstraints childConstraints =
        BoxConstraints(maxWidth: constraints.maxWidth);

    double mainAxisExtent = 0.0;
    double crossAxisExtent = 0.0;
    double runMainAxisExtent = 0.0;
    double runCrossAxisExtent = 0.0;
    RenderBox child = firstChild;
    while (child != null) {
      final Size childSize = child.getDryLayout(childConstraints);
      final double childMainAxisExtent = childSize.width;
      final double childCrossAxisExtent = childSize.height;
      runMainAxisExtent += childMainAxisExtent;
      runCrossAxisExtent = math.max(runCrossAxisExtent, childCrossAxisExtent);
      child = childAfter(child);
    }
    crossAxisExtent += runCrossAxisExtent + _computeSpace();
    mainAxisExtent = math.max(mainAxisExtent, runMainAxisExtent);

    return constraints.constrain(Size(mainAxisExtent, crossAxisExtent));
  }

  @override
  void performLayout() {
    final BoxConstraints constraints = this.constraints;

    if (firstChild == null) {
      size = constraints.smallest;
      return;
    }
    double runMainAxisExtent = 0.0;
    double runCrossAxisExtent = 0.0;

    {
      RenderBox child = firstChild;
      BoxConstraints childConstraints;
      double mainAxisLimit = 0.0;

      childConstraints = BoxConstraints(maxWidth: constraints.maxWidth);
      mainAxisLimit = constraints.maxWidth;

      assert(childConstraints != null);
      assert(mainAxisLimit != null);
      while (child != null) {
        child.layout(childConstraints, parentUsesSize: true);
        final double childMainAxisExtent = child.size.width;
        runMainAxisExtent += childMainAxisExtent;
        runCrossAxisExtent = math.max(runCrossAxisExtent, child.size.height);

        final SmartRowParentData childParentData =
            child.parentData as SmartRowParentData;
        child = childParentData.nextSibling;
      }
    }

    double spaces = _computeSpace();
    runMainAxisExtent += spaces;
    double maxWidth = constraints.constrainWidth(runMainAxisExtent);
    if (runMainAxisExtent > maxWidth) {
      runMainAxisExtent = 0;
      final double p = maxWidth - spaces;
      double scale = p / maxWidth;
      RenderBox child = firstChild;

      while (child != null) {
        BoxConstraints childConstraints =
            BoxConstraints(maxWidth: child.size.width * scale);

        child.layout(childConstraints, parentUsesSize: true);
        final double childMainAxisExtent = child.size.width;
        runMainAxisExtent += childMainAxisExtent;
        final SmartRowParentData childParentData =
            child.parentData as SmartRowParentData;
        child = childParentData.nextSibling;
      }
    }

    RenderBox child = firstChild;
    {
      double childMainPosition = 0;
      while (child != null) {
        final SmartRowParentData childParentData =
            child.parentData as SmartRowParentData;
        childParentData.offset = Offset(childMainPosition, 0);
        childMainPosition += child.size.width;
        childMainPosition += spacing;
        child = childParentData.nextSibling;
      }
    }
    size = constraints.constrain(Size(runMainAxisExtent, runCrossAxisExtent));
  }

  @override
  bool hitTestChildren(BoxHitTestResult result, {@required Offset position}) {
    return defaultHitTestChildren(result, position: position);
  }

  @override
  void paint(PaintingContext context, Offset offset) {
    defaultPaint(context, offset);
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DoubleProperty('spacing', spacing));
  }
}
