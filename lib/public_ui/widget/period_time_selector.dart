import 'dart:math';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/styles/app_divider.dart';

import '../../generated/l10n.dart';
import '../../styles/app_color.dart';

class PeriodTime {
  final int start;
  final int end;

  const PeriodTime(this.start, this.end);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PeriodTime &&
          runtimeType == other.runtimeType &&
          start == other.start &&
          end == other.end;

  @override
  int get hashCode => start.hashCode ^ end.hashCode;

  String toTimeText() {
    return "$start:00-$end:00";
  }

  bool isAllDay() {
    return start <= 0 && end >= 24;
  }

  factory PeriodTime.parseTimeDuring(String t) {
    List<String> s = t.split('-');

    PeriodTime ret = PeriodTime(
        int.parse(s[0].split(":")[0]), int.parse(s[1].split(":")[0]));
    return ret;
  }

  @override
  String toString() {
    return toTimeText();
  }
}

int periodTimeSortFun(PeriodTime a, PeriodTime b) {
  return a.start - b.end;
}

class PeriodTimeSelector extends StatefulWidget {
  final List<PeriodTime> selected;
  final ValueChanged<List<PeriodTime>> onConfirm;
  final VoidCallback onCancel;
  final int editIndex;

  const PeriodTimeSelector({
    this.selected,
    this.onConfirm,
    this.onCancel,
    this.editIndex,
  });

  @override
  State<StatefulWidget> createState() {
    return _PeriodTimeSelectorState();
  }
}

class _PeriodTimeSelectorState extends State<PeriodTimeSelector> {
  PeriodTimeSelectController controller;

  @override
  void initState() {
    super.initState();
    if (widget.editIndex != null) {
      controller =
          PeriodTimeSelectController.edit(widget.selected, widget.editIndex);
    } else {
      controller = PeriodTimeSelectController(selected: widget.selected);
    }
    controller.addListener(onDragChanged);
  }

  void onDragChanged() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          // padding: EdgeInsets.symmetric(horizontal: 18, vertical: 18),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  widget.onCancel?.call();
                },
                child: Container(
                  padding: EdgeInsets.all(18),
                  child: Text(
                    YLocal.of(context).quxiao,
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 16,
                      height: 1.40625,
                      color: AppColors.textWeak,
                    ),
                  ),
                ),
              ),
              Text(
                YLocal.of(context).keyongshijianduan,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                  height: 1,
                  color: AppColors.textTitle,
                ),
              ),
              Builder(builder: (context) {
                final bool hasSelected = controller.hasSelected();
                return GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: !hasSelected
                      ? null
                      : () {
                          widget.onConfirm?.call(controller.generateSelected());
                        },
                  child: Container(
                    padding: EdgeInsets.all(18),
                    child: Text(
                      YLocal.of(context).queding,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 16,
                        height: 1,
                        color: hasSelected
                            ? Color(0xff4F7FFE)
                            : Color(0xff4F7FFE).withOpacity(0.5),
                      ),
                    ),
                  ),
                );
              }),
            ],
          ),
        ),
        AppDivider.backgroundDivider,
        Expanded(
          child: Container(
            padding: EdgeInsets.all(7),
            child: TimeSelector(
              onOccupied: (index) {
                YvrToast.showToast(YLocal.of(context).shiduanyizhanyongqin);
              },
              onSingle: (index) {
                YvrToast.showToast(YLocal.of(context).mofazuoweikaishishij);
              },
              controller: controller,
            ),
          ),
        ),
        SizedBox(
          height: MediaQuery.of(context).padding.bottom,
        ),
      ],
    );
  }

  @override
  void didUpdateWidget(covariant PeriodTimeSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.editIndex != null && widget.editIndex != oldWidget.editIndex) {
      controller.removeListener(onDragChanged);
      controller =
          PeriodTimeSelectController.edit(widget.selected, widget.editIndex);
      controller.addListener(onDragChanged);
    } else if (widget.selected != oldWidget.selected) {
      controller.removeListener(onDragChanged);
      controller = PeriodTimeSelectController(selected: widget.selected);
      controller.addListener(onDragChanged);
    }
  }

  @override
  void dispose() {
    controller.removeListener(onDragChanged);
    super.dispose();
  }
}

class PeriodTimeSelectController extends ChangeNotifier {
  List<_SelectStatus> _status = List<_SelectStatus>.generate(
      _kHorizontalCount * _kVerticalCount, (index) => _SelectStatus.free);

  List<PeriodTime> _selected;
  int _start = -1;
  int _end = -1;

  PeriodTimeSelectController({List<PeriodTime> selected})
      : _selected = (selected ?? List.empty(growable: true)) {
    _resetStatus();
  }

  void _resetStatus() {
    for (int i = 0; i < _status.length; ++i) {
      _status[i] = _SelectStatus.free;
    }

    _selected?.forEach((element) {
      for (int i = element.start; i <= element.end; ++i) {
        _status[i] = _SelectStatus.occupied;
      }
    });
  }

  set dragStart(int value) {
    if (_start != value) {
      _start = value;
      notifyListeners();
    }
  }

  set dragEnd(int value) {
    if (_end != value) {
      _end = value;
      notifyListeners();
    }
  }

  int get dragStart => _start;

  int get dragEnd => _end;

  List<PeriodTime> generateSelected() {
    var result = _selected.toList(growable: true);
    if (hasSelected()) {
      int start = _start;
      int end = _end;
      if (PeriodTimeUtils.needSwap(start, end)) {
        int t = start;
        start = end;
        end = t;
      }
      result.add(PeriodTime(start, end));
      result.sort(periodTimeSortFun);
    }
    return result;
  }

  PeriodTimeSelectController.edit(List<PeriodTime> selected, int index) {
    var editItem = selected[index];
    var d = selected.toList(growable: true);
    d.removeAt(index);
    _selected = d;
    _resetStatus();
    _start = editItem.start;
    _end = editItem.end;
  }

  bool hasSelected() {
    return _start >= 0 && _end >= 0 && _start != _end;
  }
}

class TimeSelector extends LeafRenderObjectWidget {
  final PeriodTimeSelectController controller;

  // final ValueChanged<int> onStartDisallowed;
  final ValueChanged<int> onOccupied;
  final ValueChanged<int> onSingle;

  const TimeSelector({
    Key key,
    this.onOccupied,
    this.onSingle,
    @required this.controller,
  }) : super(key: key);

  @override
  RenderObject createRenderObject(BuildContext context) {
    return TimeSelectorBoxRenderObject(
      onOccupied: onOccupied,
      onSingle: onSingle,
      controller: this.controller,
    );
  }

  @override
  void updateRenderObject(
      BuildContext context, TimeSelectorBoxRenderObject renderObject) {
    renderObject.onOccupied = onOccupied;
    renderObject.controller = controller;
    renderObject.onSingle = onSingle;
  }
}

enum _SelectStatus {
  ///空闲
  free,

  ///占用
  occupied,
}

const int _kHorizontalCount = 5;
const int _kVerticalCount = 5;

class TimeSelectorBoxRenderObject extends RenderProxyBoxWithHitTestBehavior {
  ValueChanged<int> onOccupied;
  ValueChanged<int> onSingle;

  Paint bgPaint = Paint()..color = Colors.black;

  PeriodTimeSelectController _controller;

  double _itemWidth;
  double _itemHeight;

  TimeSelectorBoxRenderObject({
    List<PeriodTime> selected,
    this.onOccupied,
    this.onSingle,
    RenderBox child,
    PeriodTimeSelectController controller,
  })  : _controller = controller,
        super(behavior: HitTestBehavior.opaque, child: child);

  set controller(PeriodTimeSelectController controller) {
    _controller = controller;
    markNeedsLayout();
    markNeedsPaint();
  }

  @override
  void performLayout() {
    size = constraints.biggest;
    _itemWidth = size.width / _kHorizontalCount;
    _itemHeight = size.height / _kVerticalCount;
  }

  @override
  void paint(PaintingContext context, Offset offset) {
    for (int i = 0; i < _kHorizontalCount; ++i) {
      for (int j = 0; j < _kVerticalCount; ++j) {
        int index = j * _kVerticalCount + i;
        drawItem(index, i, j, context.canvas, offset);
      }
    }
  }

  void drawItem(
      int index, int hIndex, int vIndex, Canvas canvas, Offset offset) {
    String text = timeTextOf(index);
    Offset itemOffset = Offset(
        offset.dx + _itemWidth * hIndex, offset.dy + _itemHeight * vIndex);
    _SelectStatus status = _controller._status[index];
    bool start = false;
    bool end = false;
    Paint bgPaint;
    double bgRadius = 0;
    Color textColor = Color(0xff2C2E33);

    int dragStartIndex = _controller.dragStart;
    int dragEndIndex = _controller.dragEnd;

    ///_controller.dragEnd >= 0保证必须选取了end，才允许做swap操作
    if (_controller.dragEnd >= 0 &&
        PeriodTimeUtils.needSwap(_controller.dragStart, _controller.dragEnd)) {
      dragStartIndex = _controller.dragEnd;
      dragEndIndex = _controller.dragStart;
    }

    if (dragStartIndex == index) {
      bgPaint = Paint()..color = Color(0xff4F7FFE);
      textColor = Colors.white;
      start = true;
      bgRadius = 6;
    } else if (dragEndIndex == index) {
      bgPaint = Paint()..color = Color(0xff4F7FFE);
      textColor = Colors.white;
      end = true;
      bgRadius = 6;
    } else if (index > dragStartIndex && index < dragEndIndex) {
      bgPaint = Paint()..color = Color.fromRGBO(79, 127, 254, 0.2);
    } else {
      switch (status) {
        case _SelectStatus.free:
          break;
        case _SelectStatus.occupied:
          textColor = Color(0xffAFB6CC);
          break;
      }
    }

    if (bgPaint != null) {
      drawBackground(canvas, itemOffset, bgPaint, radius: bgRadius);
    }
    drawText(canvas, itemOffset, text, textColor, start, end);
  }

  void drawBackground(Canvas canvas, Offset offset, Paint paint,
      {double radius}) {
    if (radius == null) {
      canvas.drawRect(
          Rect.fromLTWH(offset.dx, offset.dy, _itemWidth, _itemHeight), paint);
    } else {
      canvas.drawRRect(
          RRect.fromLTRBR(offset.dx, offset.dy, offset.dx + _itemWidth,
              offset.dy + _itemHeight, Radius.circular(radius)),
          paint);
    }
  }

  void drawText(Canvas canvas, Offset offset, String text, Color textColor,
      bool start, bool end) {
    TextPainter timeTextPainter = TextPainter(
      textAlign: TextAlign.start,
      textDirection: TextDirection.ltr,
      text: TextSpan(
        text: text,
        style: TextStyle(
            fontSize: 16,
            color: textColor,
            height: 44.8 / 32,
            fontWeight: FontWeight.w500),
      ),
    );
    timeTextPainter.layout();
    const double flatMargin = 5;

    if (start == true || end == true) {
      String fText;
      if (start) {
        fText = YLocal.current.kaishishijian;
        _drawFlag(
          canvas,
          offset.translate(flatMargin, (_itemHeight - kFlagAreaHeight) * 0.5),
        );
      }
      if (end) {
        fText = YLocal.current.jieshushijian;
        _drawFlag(
          canvas,
          offset.translate(_itemWidth - flatMargin - kFlagAreaWidth,
              (_itemHeight - kFlagAreaHeight) * 0.5),
        );
      }
      TextPainter timeFlagTextPainter = TextPainter(
        textAlign: TextAlign.start,
        textDirection: TextDirection.ltr,
        text: TextSpan(
          text: fText,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 10,
            height: 1.4,
            color: Color(0xf2ffffff),
          ),
        ),
      );
      timeFlagTextPainter.layout();
      const double y = 13;
      timeFlagTextPainter.paint(
        canvas,
        offset.translate((_itemWidth - timeFlagTextPainter.width) * 0.5, y),
      );

      timeTextPainter.paint(
        canvas,
        offset.translate((_itemWidth - timeTextPainter.width) * 0.5,
            y + timeFlagTextPainter.height),
      );
    } else {
      timeTextPainter.paint(
        canvas,
        offset.translate((_itemWidth - timeTextPainter.width) * 0.5,
            (_itemHeight - timeTextPainter.height) * 0.5),
      );
    }
  }

  Paint _flagPaint = Paint()..color = Color.fromRGBO(255, 255, 255, 0.5);
  static double kFlagAreaWidth = 6;
  static double kFlagAreaHeight = 20;

  void _drawFlag(Canvas canvas, Offset offset) {
    const int fWidth = 2;
    canvas.drawRRect(
        RRect.fromLTRBR(
          offset.dx,
          offset.dy,
          offset.dx + fWidth,
          offset.dy + kFlagAreaHeight,
          Radius.circular(13),
        ),
        _flagPaint);

    double secondX = offset.dx + kFlagAreaWidth - fWidth;
    canvas.drawRRect(
        RRect.fromLTRBR(
          secondX,
          offset.dy,
          secondX + fWidth,
          offset.dy + kFlagAreaHeight,
          Radius.circular(13),
        ),
        _flagPaint);
  }

  int findIndex(Offset offset) {
    int hIndex = min(max(offset.dx ~/ _itemWidth, 0), _kHorizontalCount - 1);
    int vIndex = min(max(offset.dy ~/ _itemHeight, 0), _kVerticalCount - 1);
    return vIndex * _kHorizontalCount + hIndex;
  }

  _EditType editType;

  void _onTapStart(Offset offset) {
    int index = findIndex(offset);
    if (_controller._status[index] != _SelectStatus.free) {
      onOccupied?.call(index);
    } else if (!hasFreeOnPrevOrNext(index)) {
      onSingle?.call(index);
    } else {
      if (_controller.dragStart >= 0 && _controller.dragEnd >= 0) {
        ///既有开始时间又有结束时间，需要判断是否是否拖动状态
        if (_controller.dragStart == index) {
          editType = _EditType.start;
        } else if (_controller.dragEnd == index) {
          editType = _EditType.end;
        } else {
          _controller.dragStart = index;
          _controller.dragEnd = -1;
          editType = null;
        }
      } else {
        if (_controller.dragStart < 0) {
          ///没有开始时间就先选择开始时间
          _controller.dragStart = index;
          _controller.dragEnd = -1;
        } else {
          ///选了开始时间后，就开始确定结束时间
          int start = _controller.dragStart;
          int end = index;
          bool swap = PeriodTimeUtils.needSwap(start, end);
          if (swap) {
            int t = start;
            start = end;
            end = t;
          }
          if (canTapUpdate(start, end)) {
            _controller.dragEnd = index;
          } else {
            _controller.dragStart = index;
            _controller.dragEnd = -1;
          }
        }
        editType = null;
      }
      markNeedsPaint();
    }
  }

  void _onTapUpdate(Offset offset) {
    if (_controller.dragStart >= 0) {
      int index = findIndex(offset);
      int start = -1;
      int end = -1;

      switch (editType) {
        case _EditType.start:
          start = index;
          end = _controller.dragEnd;
          break;
        case _EditType.end:
          start = _controller.dragStart;
          end = index;
          break;
        default:
          start = _controller.dragStart;
          end = index;
          break;
      }

      bool swap = PeriodTimeUtils.needSwap(start, end);
      if (swap) {
        int t = start;
        start = end;
        end = t;
      }
      if (canTapUpdate(start, end)) {
        switch (editType) {
          case _EditType.start:
            _controller.dragStart = index;
            break;
          case _EditType.end:
            _controller.dragEnd = index;
            break;
          default:
            _controller.dragEnd = index;
            break;
        }
        markNeedsPaint();
      }
    }
  }

  void _onTapRelease(Offset offset) {
    if (_controller.dragStart >= 0) {
      int index = findIndex(offset);
      int start = -1;
      int end = -1;

      switch (editType) {
        case _EditType.start:
          start = index;
          end = _controller.dragEnd;
          break;
        case _EditType.end:
          start = _controller.dragStart;
          end = index;
          break;
        default:
          if (index == _controller.dragStart) {
            ///因为在滑动时，会修改dragEnd，如果选中的时间与开始时间相同，那么就将dragEnd置为-1，为了_onTapStart中区分是否重置。
            _controller.dragEnd = -1;
            editType = null;
            markNeedsPaint();
            return;
          }
          start = _controller.dragStart;
          end = index;
          break;
      }

      bool swap = PeriodTimeUtils.needSwap(start, end);
      if (swap) {
        int t = start;
        start = end;
        end = t;
      }
      if (canTapUpdate(start, end)) {
        switch (editType) {
          case _EditType.start:
            _controller.dragStart = index;
            break;
          case _EditType.end:
            _controller.dragEnd = index;
            break;
          default:
            _controller.dragEnd = index;
            break;
        }
      }

      editType = null;
      markNeedsPaint();
    }
  }

  static const bool kBestMode = false;

  bool hasFreeOnPrevAndNext(int index) {
    var _status = _controller._status;
    if (index == 0) {
      int nextIndex = index + 1;
      return _status[nextIndex] == _SelectStatus.free;
    } else if (index == 24) {
      int prevIndex = index - 1;
      return _status[prevIndex] == _SelectStatus.free;
    }
    return _status[index] == _SelectStatus.free;
  }

  ///是否可选上一个或者下一个时间
  bool hasFreeOnPrevOrNext(int index) {
    var _status = _controller._status;

    if (!kBestMode) {
      if (index == 0) {
        int nextIndex = index + 1;
        return _status[nextIndex] == _SelectStatus.free;
      } else if (index == 24) {
        int prevIndex = index - 1;
        return _status[prevIndex] == _SelectStatus.free;
      } else {
        // return _status[index] == _SelectStatus.free;
        int prevIndex = index - 1;
        int nextIndex = index + 1;
        return (_status[prevIndex] == _SelectStatus.free) ||
            (_status[nextIndex] == _SelectStatus.free);
      }
    }

    const int interval = kBestMode ? 0 : 2;
    int prevIndex = index - interval;
    int nextIndex = index + interval;
    return (prevIndex >= 0 && _status[prevIndex] == _SelectStatus.free) ||
        (nextIndex <= 24 && _status[nextIndex] == _SelectStatus.free);
  }

  bool canTapUpdate(int start, int end) {
    var _status = _controller._status;

    if (!kBestMode) {
      if (hasFreeOnPrevAndNext(start) && hasFreeOnPrevAndNext(end)) {
        for (int i = start; i <= end; ++i) {
          if (_status[i] != _SelectStatus.free) {
            return false;
          }
        }
        return true;
      }
      return false;
    }
    for (int i = start; i <= end; ++i) {
      if (_status[i] != _SelectStatus.free) {
        return false;
      }
    }
    return true;
  }

  @override
  void handleEvent(PointerEvent event, HitTestEntry entry) {
    assert(debugHandleEvent(event, entry));
    if (event is PointerDownEvent) {
      _onTapStart(event.localPosition);
      return;
    }
    if (event is PointerMoveEvent) {
      _onTapUpdate(event.localPosition);
      return;
    }
    if (event is PointerUpEvent) {
      _onTapRelease(event.localPosition);
      return;
    }
    if (event is PointerHoverEvent) {
      return;
    }
    if (event is PointerCancelEvent) {
      _onTapRelease(event.localPosition);
      return;
    }
    if (event is PointerSignalEvent) {
      return;
    }
  }

  String timeTextOf(int index) {
    return "$index:00";
  }
}

enum _EditType { start, end }

class PeriodTimeUtils {
  static bool needSwap(int start, int end) {
    return start > end;
  }
}
