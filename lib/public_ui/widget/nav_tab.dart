/// tab 切换组件
import 'package:flutter/material.dart';
import 'package:flutter_swiper/flutter_swiper.dart';

class NavTab extends StatefulWidget {
  final List<String> tabs;
  final List<Widget> pages;
  final Widget rightBtn;
  final onPressed;

  NavTab({this.tabs, this.pages, this.rightBtn, this.onPressed});

  @override
  _STabState createState() => _STabState();
}

class _STabState extends State<NavTab> {
  int selectedIndex = 0;
  SwiperController swipeControl = new SwiperController();

  // tab 索引变化回调
  void onTabChange(index) {
    setState(() {
      selectedIndex = index;
    });
    swipeControl.move(index);
  }

  void onPageChange(index) {
    setState(() {
      selectedIndex = index;
      widget.onPressed(selectedIndex);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        child: Column(
      children: [
        tabLayout(widget.tabs, selectedIndex, widget.rightBtn, onTabChange),
        contentLayout(widget.pages, swipeControl, onPageChange)
      ],
    ));
  }
}

/// 上面 Tab 的布局
Widget tabLayout(tabs, selectedIndex, rightBtn, onTabChange) {
  List<Widget> tabItems() {
    List<Widget> children = [];
    for (var i = 0; i < tabs.length; i++) {
      children.add(
        GestureDetector(
            onTap: () {
              onTabChange(i);
            },
            child: Container(
                padding: EdgeInsets.only(bottom: 10),
                decoration: BoxDecoration(
                    border: Border(
                        bottom: BorderSide(
                            color: selectedIndex == i
                                ? Color(0xFFE8E8E8)
                                : Colors.transparent,
                            width: 3))),
                child: Text(tabs[i],
                    style: TextStyle(
                        fontSize: 20,
                        color: selectedIndex == i
                            ? Color(0xFFE8E8E8)
                            : Color(0xFF52555C))))),
      );
      if (i < tabs.length - 1) {
        children.add(SizedBox(
          width: 30,
        ));
      }
    }
    return children;
  }

  return Stack(
    children: [
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: tabItems(),
      ),
      Positioned(
        top: 0,
        right: 0,
        child: Container(
            height: 40,
            padding: EdgeInsets.only(left: 10, right: 20, bottom: 10),
            child: rightBtn),
      )
    ],
  );
}

/// 下面页面内容布局
Widget contentLayout(pages, swipeControl, onIndexChanged) {
  return Expanded(
    child: Container(
        alignment: Alignment.bottomCenter,
        padding: EdgeInsets.only(top: 0),
        decoration: BoxDecoration(color: Color(0xff17191B)),
        child: Swiper(
          itemCount: pages.length,
          itemBuilder: (BuildContext context, int index) {
            return pages[index];
          },
          loop: false,
          physics: NeverScrollableScrollPhysics(),
          onIndexChanged: (index) {
            onIndexChanged(index);
          },
          controller: swipeControl,
        )),
  );
}
