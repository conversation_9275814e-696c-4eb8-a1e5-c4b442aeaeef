import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:yvr_assistant/manager/global.dart';
import '../../utils/resource_mananger.dart';

enum UrlType {
  url, //网络地址
  assets, //资源目录
}

// ignore: must_be_immutable
class WrapperImage extends StatelessWidget {
  final String url;
  final double width;
  final double height;
  final BoxFit fit;
  final UrlType urlType;
  String defaultIcon;

  WrapperImage({
    this.url,
    this.width,
    this.height,
    this.urlType = UrlType.url,
    this.fit = BoxFit.cover,
    this.defaultIcon,
  });

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: imageUrl ?? "",
      width: width,
      height: height,
      placeholder: (_, __) => ImageHelper.placeHolderIcon(
          width: width, height: height, defaultIcon: defaultIcon),
      errorWidget: (_, __, ___) => ImageHelper.placeHolderIcon(
          width: width, height: height, defaultIcon: defaultIcon),
      fit: fit,
    );
  }

  String get imageUrl {
    if (urlType == UrlType.assets) {
      return ImageHelper.wrapAssets(url);
    } else {
      return url;
    }
  }
}

class VideoWaitWidget extends StatelessWidget {
  const VideoWaitWidget({Key key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Container(
        color: Colors.black,
        width: Global.screenWidth,
        child: SpinKitCircle(size: 50, color: Colors.white60));
  }
}
