import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';
import 'package:yvr_assistant/utils/textInput_formatter.dart';

import '../../styles/app_color.dart';

///自定义Dialog
class InputDialog extends StatefulWidget {
  //------------------不带图片的dialog------------------------
  final String title; //弹窗标题
  final String content; //弹窗内容
  final String errNote; //错误提示
  final Color contentColor; //确定按钮颜色
  final String confirmText; //按钮文本
  final String cancelText; //按钮文本
  final Color backgroundColor; //弹窗标题
  final Color confirmTextColor; //确定按钮文本颜色
  final bool isCancel; //是否有取消按钮，默认为true true：有 false：没有
  final Color confirmColor; //确定按钮颜色
  final Color cancelColor; //取消按钮颜色
  final bool outsideDismiss; //点击弹窗外部，关闭弹窗，默认为true true：可以关闭 false：不可以关闭
  final Function confirmCallback; //点击确定按钮回调
  final Function cancelCallback; //弹窗关闭回调
  final Function(String) inputCommitCallback; //点击确定按钮回调
  final double height;

  //------------------带输入框的dialog------------------------
  final keyboardType;
  final hintText;
  final maxLength;
  final text;

  const InputDialog(
      {Key key,
      this.title,
      this.content,
      this.errNote,
      this.contentColor,
      this.confirmText,
      this.cancelText,
      this.backgroundColor,
      this.confirmTextColor,
      this.isCancel = true,
      this.confirmColor,
      this.cancelColor,
      this.outsideDismiss = false,
      this.confirmCallback,
      this.cancelCallback,
      this.height,
      this.keyboardType = TextInputType.number,
      this.maxLength,
      @required this.hintText,
      this.text = "",
      this.inputCommitCallback})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _InputDialogState();
  }
}

class _InputDialogState extends State<InputDialog> {
  var _input = new TextEditingController();

  _cancelDialog() {
    _dismissDialog();
    if (widget.cancelCallback != null) {
      widget.cancelCallback();
    }
  }

  _dismissDialog() {
    Navigator.of(context).pop();
  }

  _inputConfirmDialog() {
    if (widget.inputCommitCallback != null) {
      widget.inputCommitCallback(_input.text);
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final width = size.width;
    Color bgColor = widget.backgroundColor ?? Color(0xffFFFFFF);

    Column _dialogForInput = Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Text(
          widget.title == null ? '' : widget.title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 20,
            height: 1,
            color: AppColors.textTitle,
          ),
        ),
        Container(
            height: 50,
            margin: EdgeInsets.only(top: 20),
            child: TextField(
                inputFormatters: [NoSpaceFormatter()],
                keyboardType: widget.keyboardType,
                controller: _input,
                maxLength: widget.maxLength,
                onChanged: (value) {
                  if (_input.value.isComposingRangeValid) {
                    return;
                  }
                  _input.text = value;
                  _input.selection = TextSelection.fromPosition(
                      TextPosition(offset: _input.text.length));
                },
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  height: 1,
                  letterSpacing: 0,
                  color: AppColors.textTitle,
                ),
                decoration: InputDecoration(
                  counterText: "",
                  hintText: widget.hintText,
                  hintStyle: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                    height: 1,
                    letterSpacing: 0,
                    color: AppColors.textWeak,
                  ),
                  fillColor: Color(0xffF0F2F5),
                  filled: true,
                  contentPadding: const EdgeInsets.all(10.0),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: bgColor),
                    borderRadius: BorderRadius.all(
                      Radius.circular(4), //边角为5
                    ),
                  ),
                  enabledBorder: new UnderlineInputBorder(
                    borderSide: new BorderSide(color: bgColor),
                    borderRadius: BorderRadius.all(
                      Radius.circular(4), //边角为5
                    ),
                  ),
                ))),
        Container(
          padding: EdgeInsets.fromLTRB(0, 5, 0, 8),
          alignment: Alignment.centerLeft,
          child: Text(
            widget.errNote ?? "",
            style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 12,
              height: 1,
              color: AppColors.warning,
            ),
          ),
        ),
        Container(
            height: 45,
            child: Row(
              children: <Widget>[
                Expanded(
                    child: widget.isCancel
                        ? YvrTextButton(
                            color: widget.cancelColor ?? Color(0xffF0F2F5),
                            child: Text(
                              widget.cancelText ?? YLocal.of(context).cancel,
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                                letterSpacing: 0,
                                color: widget.confirmTextColor ??
                                    AppColors.textTitle,
                              ),
                            ),
                            onPressed: _cancelDialog,
                          )
                        : SizedBox(),
                    flex: widget.isCancel ? 1 : 0),
                SizedBox(
                  width: widget.isCancel ? 14 : 0,
                ),
                Expanded(
                    child: YvrTextButton(
                      color: widget.confirmColor ?? Color(0xff4F7FFE),
                      onPressed: _inputConfirmDialog,
                      child: Text(
                        widget.confirmText == null
                            ? YLocal.of(context).queding
                            : widget.confirmText,
                        style: TextStyle(
                          fontSize: 16.0,
                          fontWeight: FontWeight.w600,
                          color: widget.confirmTextColor ?? Color(0xffFFFFFF),
                        ),
                      ),
                    ),
                    flex: 1),
              ],
            ))
      ],
    );
    final EdgeInsets effectivePadding = MediaQuery.of(context).viewInsets;

    return AnimatedPadding(
      padding: effectivePadding,
      duration: const Duration(milliseconds: 0),
      child: MediaQuery.removeViewInsets(
        removeLeft: true,
        removeTop: true,
        removeRight: true,
        removeBottom: true,
        context: context,
        child: WillPopScope(
            child: GestureDetector(
              onTap: () => {widget.outsideDismiss ? _dismissDialog() : null},
              child: Material(
                type: MaterialType.transparency,
                child: Center(
                  child: Container(
                    padding: EdgeInsets.all(20),
                    width: width - 68,
                    height: widget.height ?? 218,
                    alignment: Alignment.center,
                    child: _dialogForInput,
                    decoration: BoxDecoration(
                        color: bgColor,
                        borderRadius: BorderRadius.circular(12)),
                  ),
                ),
              ),
            ),
            onWillPop: () async {
              return widget.outsideDismiss;
            }),
      ),
    );
  }
}
