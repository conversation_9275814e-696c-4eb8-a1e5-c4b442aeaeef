import 'package:flutter/material.dart';
import 'package:yvr_assistant/public_ui/widget/image_view.dart';

class ImageGroupWidget extends StatelessWidget {
  final List<String> images;
  final int columnNum;
  final int maxRowNum;
  final double itemWidth;
  final double itemHeight;
  final ValueChanged<int> onPressed;

  const ImageGroupWidget(
      {Key key,
      @required this.images,
      this.columnNum = 3,
      this.maxRowNum = 1,
      this.itemWidth,
      this.itemHeight,
      this.onPressed})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    int rowNum = ((images.length) + (columnNum - 1)) ~/ columnNum;
    int moreIndex = -1;
    int moreNum = 0;
    if (rowNum > maxRowNum) {
      rowNum = maxRowNum;
      moreIndex = columnNum * maxRowNum - 1;
      moreNum = images.length - (columnNum * maxRowNum);
    }
    return Column(
      children: List.generate(
        rowNum,
        (row) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: List.generate(
              columnNum,
              (column) {
                int current = row * columnNum + column;
                Widget child;
                if (current < images.length) {
                  child = NetworkImageWidget(
                    images[current],
                  );
                  if (current == moreIndex) {
                    child = Stack(
                      alignment: AlignmentDirectional.center,
                      children: [
                        Positioned.fill(child: child),
                        Positioned.fill(
                          child: DecoratedBox(
                            decoration: BoxDecoration(
                                color: Color(0x1F2021).withOpacity(0.6)),
                          ),
                        ),
                        Text(
                          '+$moreNum',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                          ),
                        ),
                      ],
                    );
                  }
                }

                child = Container(
                  child: ClipRRect(
                    child: child,
                    borderRadius: BorderRadius.circular(2),
                  ),
                  width: itemWidth,
                  height: itemHeight,
                );
                return GestureDetector(
                  child: child,
                  onTap: () {
                    this.onPressed?.call(current);
                  },
                );
              },
            ),
          );
        },
      ),
    );
  }
}
