import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

import '../../styles/app_color.dart';

class PersonalHomeFlexibleSpaceBar extends StatefulWidget {
  const PersonalHomeFlexibleSpaceBar({
    Key key,
    this.background,
    this.collapseMode = CollapseMode.parallax,
    this.stretchModes = const <StretchMode>[StretchMode.zoomBackground],
    @required this.titleBuilder,
    @required this.avatar,
    @required this.nick,
    @required this.idText,
    @required this.content,
    @required this.contentHeight,
  })  : assert(collapseMode != null),
        super(key: key);

  final Widget background;
  final CollapseMode collapseMode;
  final List<StretchMode> stretchModes;
  final Widget Function(BuildContext context, double) titleBuilder;
  final Widget avatar;
  final String nick;
  final String idText;
  final Widget content;
  final double contentHeight;

  @override
  State<PersonalHomeFlexibleSpaceBar> createState() =>
      _PersonalHomeFlexibleSpaceBarState();
}

class _PersonalHomeFlexibleSpaceBarState
    extends State<PersonalHomeFlexibleSpaceBar> {
  static const double kBottomHeight = 20;

  double getCollapsePadding(double t, FlexibleSpaceBarSettings settings) {
    switch (widget.collapseMode) {
      case CollapseMode.pin:
        return -(settings.maxExtent - settings.currentExtent);
      case CollapseMode.none:
        return 0.0;
      case CollapseMode.parallax:
        final double deltaExtent = settings.maxExtent - settings.minExtent;
        return -Tween<double>(begin: 0.0, end: deltaExtent / 4.0).transform(t);
    }
    return 0.0;
  }

  GlobalKey titleKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        final FlexibleSpaceBarSettings settings = context
            .dependOnInheritedWidgetOfExactType<FlexibleSpaceBarSettings>();
        assert(
          settings != null,
          'A FlexibleSpaceBar must be wrapped in the widget returned by FlexibleSpaceBar.createSettings().',
        );

        final List<Widget> children = <Widget>[];

        final double deltaExtent = settings.maxExtent - settings.minExtent;

        // 0.0 -> Expanded
        // 1.0 -> Collapsed to toolbar
        final double t =
            (1.0 - (settings.currentExtent - settings.minExtent) / deltaExtent)
                .clamp(0.0, 1.0);
        debugPrint(
            "settings:${settings.minExtent},${settings.maxExtent},${settings.currentExtent},t:$t");
        // background

        double contentOpacity = 1.0 - t;

        if (widget.background != null) {
          final double fadeStart =
              math.max(0.0, 1.0 - kToolbarHeight / deltaExtent);
          const double fadeEnd = 1.0;
          assert(fadeStart <= fadeEnd);
          // If the min and max extent are the same, the app bar cannot collapse
          // and the content should be visible, so opacity = 1.
          final double opacity = settings.maxExtent == settings.minExtent
              ? 1.0
              : 1.0 - Interval(fadeStart, fadeEnd).transform(t);
          double height = settings.maxExtent;

          // StretchMode.zoomBackground
          if (widget.stretchModes.contains(StretchMode.zoomBackground) &&
              constraints.maxHeight > height) {
            height = constraints.maxHeight;
          }

          children.add(widget.background);
          // StretchMode.blurBackground
          final double blurAmount =
              (settings.maxExtent - constraints.maxHeight) / 10;

          debugPrint("opacity:$opacity,blurAmount:$blurAmount");

          children.add(Positioned.fill(
            child: BackdropFilter(
              filter: ui.ImageFilter.blur(
                sigmaX: blurAmount,
                sigmaY: blurAmount,
              ),
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ));
        }
        children.add(
          Positioned.fill(
            child: Container(
              height: settings.maxExtent,
              decoration: BoxDecoration(
                  gradient: LinearGradient(
                begin: AlignmentDirectional.topCenter,
                end: AlignmentDirectional.bottomCenter,
                colors: [
                  Color.fromRGBO(0, 0, 0, 0),
                  Color.fromRGBO(0, 0, 0, 0.74 * contentOpacity),
                ],
              )),
            ),
          ),
        );

        children.add(
          Positioned(
            child: Opacity(
              opacity: contentOpacity,
              child: Text(
                widget.idText,
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 13,
                  height: 1.07692,
                  color: Color(0xccffffff),
                ),
              ),
            ),
            left: 123,
            right: 0,
            bottom: widget.contentHeight + 30,
          ),
        );

        children.add(
          Positioned(
            child: Opacity(
              opacity: contentOpacity,
              child: widget.content,
            ),
            left: 0,
            right: 0,
            bottom: 0,
            height: widget.contentHeight,
          ),
        );

        double top = MediaQuery.of(context).padding.top;

        Size titleSize =
            ((titleKey?.currentContext?.findRenderObject()) as RenderBox)?.size;

        Offset avatarEnd;
        {
          const double maxSize = 82;
          const double minSize = 35;

          Offset start = Offset(20, settings.minExtent + 30 - kBottomHeight);
          Offset end;
          if (titleSize == null) {
            end = Offset(103, (top + (kToolbarHeight - minSize) * 0.5));
          } else {
            double titleWidth = minSize + 10 + titleSize.width;
            end = Offset((constraints.maxWidth - titleWidth) * 0.5,
                (top + (kToolbarHeight - minSize) * 0.5));
          }
          avatarEnd = end;

          final Offset avatarTranslateValue =
              Tween<Offset>(begin: start, end: end).transform(t);

          final double avatarScaleValue =
              Tween<double>(begin: 1.0, end: minSize / maxSize).transform(t);

          children.add(
            Transform(
              transform: Matrix4.identity()
                ..translate(avatarTranslateValue.dx, avatarTranslateValue.dy)
                ..scale(avatarScaleValue),
              child: Container(
                width: maxSize,
                height: maxSize,
                // color: Colors.red,
                child: widget.avatar,
              ),
            ),
          );
        }

        {
          const double maxSize = 20;
          const double minSize = 18;

          Offset start = Offset(123, settings.minExtent + 40 - kBottomHeight);

          Offset end =
              Offset(avatarEnd.dx + 43, (top + (kToolbarHeight - 28) * 0.5));

          final Offset avatarTranslateValue =
              Tween<Offset>(begin: start, end: end).transform(t);

          final double avatarScaleValue =
              Tween<double>(begin: 1.0, end: minSize / maxSize).transform(t);

          children.add(
            Transform(
              transform: Matrix4.identity()
                ..translate(avatarTranslateValue.dx, avatarTranslateValue.dy)
                ..scale(avatarScaleValue),
              child: Container(
                child: Row(
                  children: [
                    Container(
                      child: Text(
                        widget.nick,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        key: titleKey,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 20,
                          height: 1,
                          letterSpacing: 1,
                          color: AppColors.background,
                        ),
                      ),
                    ),
                    widget.titleBuilder?.call(context, t),
                  ],
                ),
              ),
            ),
          );
        }

        return ClipRect(child: Stack(children: children));
      },
    );
  }

  double computeTitleWidth(
      double maxWidth, double avatarWidth, double space, double nickWidth) {
    return avatarWidth + space + nickWidth;
  }
}
