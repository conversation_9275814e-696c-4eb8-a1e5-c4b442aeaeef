import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yvr_assistant/utils/number_utils.dart';

import '../../styles/app_color.dart';

class YvrTextButton extends StatelessWidget {
  final VoidCallback onPressed;
  final double width;
  final double height;
  final EdgeInsetsGeometry padding;
  final Widget child;
  final Color color;
  final ButtonStyle style;
  final double radius;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        height: height,
        width: width,
        padding: padding,
        alignment: AlignmentDirectional.center,
        decoration: BoxDecoration(
          gradient: color == null
              ? const LinearGradient(
                  colors: [AppColors.buttonStart, AppColors.buttonEnd])
              : null,
          color: color != null ? color : null,
          borderRadius: BorderRadius.circular(radius),
        ),
        child: DefaultTextStyle(
            style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
                height: Platform.isAndroid ? 1.13 : null),
            child: child),
      ),
    );
  }

  const YvrTextButton({
    Key key,
    @required this.onPressed,
    this.width,
    this.height,
    this.padding = _defaultPadding,
    this.child,
    this.color,
    this.style,
    this.radius = _kRadius,
  }) : super(key: key);
}

class YvrElevatedButton extends StatelessWidget {
  final VoidCallback onPressed;
  final double width;
  final double height;
  final EdgeInsetsGeometry padding;
  final Widget child;
  final Color color;
  final ButtonStyle style;
  final double radius;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        height: height,
        width: width,
        padding: padding,
        alignment: AlignmentDirectional.center,
        decoration: BoxDecoration(
          gradient: color == null
              ? const LinearGradient(
                  colors: [AppColors.buttonStart, AppColors.buttonEnd])
              : null,
          color: color != null ? color : null,
          borderRadius: BorderRadius.circular(radius),
        ),
        child: DefaultTextStyle(
            style: TextStyle(
                color: Colors.white, height: Platform.isAndroid ? 1.13 : null),
            child: child),
      ),
    );
  }

  const YvrElevatedButton({
    Key key,
    @required this.onPressed,
    this.width,
    this.height,
    this.padding = _defaultPadding,
    this.child,
    this.color,
    this.style,
    this.radius = _kRadius,
  }) : super(key: key);
}

class FollowNumberButton extends StatelessWidget {
  final bool followed;
  final int num;
  final VoidCallback onPressed;

  const FollowNumberButton(
      {this.followed = false, this.num = 0, this.onPressed});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: this.onPressed,
      child: Container(
        width: 45,
        height: 38,
        child: Stack(
          alignment: AlignmentDirectional.center,
          children: [
            SvgPicture.asset(
              followed
                  ? 'assets/svg/follow_star_true.svg'
                  : 'assets/svg/follow_star_false.svg',
              color: followed ? Color(0xff4F7FFE) : Color(0xff6E7380),
              width: 24,
              height: 24,
            ),
            Positioned.fill(
              child: Align(
                alignment: Alignment(1, -1),
                child: Container(
                  child: Text(
                    NumberUtils.formatNumberOfFollowers(num),
                    style: TextStyle(
                      color: followed ? Color(0xff4F7FFE) : Color(0xff6E7380),
                      fontSize: 12,
                    ),
                  ),
                  padding: EdgeInsets.all(2),
                  decoration: BoxDecoration(
                      color: followed ? Color(0xffffffff) : Color(0xffF0F2F5),
                      borderRadius: BorderRadius.circular(8)),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}

enum ProgressState { idle, loading, success, failed }

class ElevatedProgressButton extends StatelessWidget {
  final ProgressState state;
  final VoidCallback onPressed;
  final Widget idleChild;
  final Widget successChild;
  final Widget failedChild;

  final double width;
  final double height;
  final EdgeInsetsGeometry padding;
  final Color color;
  final ButtonStyle buttonStyle;
  final double radius;

  const ElevatedProgressButton({
    Key key,
    @required this.state,
    @required this.onPressed,
    @required this.idleChild,
    this.successChild,
    this.failedChild,
    this.width,
    this.height,
    this.padding,
    this.color,
    this.buttonStyle,
    this.radius = _kRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget child;
    switch (state) {
      case ProgressState.idle:
        child = idleChild;
        break;
      case ProgressState.loading:
        child = SizedBox(
          child: CircularProgressIndicator(
            strokeWidth: 2,
            color: Colors.white,
          ),
          width: 14,
          height: 14,
        );
        break;
      case ProgressState.success:
        child = successChild ?? idleChild;
        break;
      case ProgressState.failed:
        child = failedChild ?? idleChild;
        break;
    }

    return YvrElevatedButton(
      onPressed: onPressed == null
          ? null
          : () {
              if (state != ProgressState.loading) {
                this.onPressed();
              }
            },
      child: child,
      width: width,
      height: height,
      padding: padding,
      color: color,
      radius: radius,
    );
  }
}

const EdgeInsets _defaultPadding =
    const EdgeInsets.symmetric(vertical: 8, horizontal: 19);

const double _kRadius = 46;
