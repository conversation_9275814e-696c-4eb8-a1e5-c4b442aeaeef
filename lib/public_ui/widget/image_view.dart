import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/svg.dart';

class NetworkImageWidget extends StatelessWidget {
  final String image;
  final double width;
  final double height;
  final BoxFit fit;

  const NetworkImageWidget(this.image,
      {this.width, this.height, this.fit = BoxFit.cover});

  @override
  Widget build(BuildContext context) {
    return FadeInImage.assetNetwork(
      placeholder: 'assets/images/r_plhd.png',
      image: image ?? "",
      fit: fit,
      width: width,
      height: height,
      imageErrorBuilder: (context, error, stackTrace) {
        return ErrorImage();
      },
    );
  }
}

class ErrorImage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Image.asset(
      'assets/images/r_plhd.png',
      fit: BoxFit.cover,
    );
  }
}

class AvatarImageWidget extends StatelessWidget {
  final String image;
  final double width;
  final double height;
  final BoxFit fit;
  final String sex;
  final int actId;

  const AvatarImageWidget(this.image,
      {this.width, this.height, this.fit = BoxFit.cover, int sex, this.actId})
      : this.sex = sex == null || sex == 1 || sex == 3
            ? 'assets/images/avatar_boy.jpg'
            : "assets/images/avatar_girl.jpg";

  @override
  Widget build(BuildContext context) {
    return AbsorbPointer(
      absorbing: actId == null,
      child: GestureDetector(
        onTap: () {
          onAvatarTap(context, actId);
        },
        child: ClipOval(
          child: CachedNetworkImage(
            width: width,
            height: height,
            imageUrl: image ?? "",
            fit: fit,
            placeholder: (context, url) => Image.asset(
              'assets/images/s_plhd.png',
              fit: fit,
              width: width,
              height: height,
            ),
            errorWidget: (context, url, error) => Image.asset(
              sex,
              fit: fit,
              width: width,
              height: height,
            ),
          ),
        ),
      ),
    );
  }
}

void onAvatarTap(BuildContext context, int actId) {
  Navigator.pushNamed(context, "/person_home",
      arguments: {"relation": 1, "actId": actId});
}

class OnlineAvatarImageWidget extends StatelessWidget {
  final String image;
  final double width;
  final double height;
  final int sex;
  final int online;
  final int actId;

  OnlineAvatarImageWidget(this.image,
      {this.width, this.height, this.sex, this.online, this.actId});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(4),
      decoration: online == 1
          ? BoxDecoration(
              image: DecorationImage(
                image: AssetImage("assets/images/online_border.png"),
              ),
            )
          : null,
      child: AvatarImageWidget(
        image,
        width: width,
        height: height,
        sex: sex,
        actId: actId,
      ),
    );
  }
}

class AppImageWidget extends StatelessWidget {
  final String image;
  final double width;
  final double height;
  final BoxFit fit;

  const AppImageWidget(this.image,
      {this.width, this.height, this.fit = BoxFit.cover});

  @override
  Widget build(BuildContext context) {
    return FadeInImage.assetNetwork(
      placeholder: 'assets/images/s_plhd.png',
      image: image ?? "",
      fit: BoxFit.cover,
      width: width,
      height: height,
      imageErrorBuilder: (context, error, stackTrace) {
        return SvgPicture.asset(
          "assets/images/unknown_app.svg",
          width: width,
          height: height,
        );
      },
    );
  }
}
