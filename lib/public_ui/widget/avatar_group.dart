import 'dart:math';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class AvatarGroupWidget extends StatefulWidget {
  final List<String> avatars;
  final int falseNum;
  final double width;
  final double offset;
  final bool placeholder;

  const AvatarGroupWidget(
      {Key key,
      this.avatars,
      this.falseNum = 0,
      this.width = 16,
      this.offset = 4,
      this.placeholder = false})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _AvatarGroupWidgetState();
}

class _AvatarGroupWidgetState extends State<AvatarGroupWidget> {
  int moreIndex = -1;
  int realN = 0;
  int falseN = 0;
  int fixedN = 0;
  double w;
  static int kMaxAvatarCount = 3;

  void _compute() {
    if (widget.avatars.length + widget.falseNum > kMaxAvatarCount) {
      moreIndex = 0;
    } else {
      moreIndex = -1;
    }
    realN = min<int>(widget.avatars.length, kMaxAvatarCount);
    falseN = min<int>(kMaxAvatarCount - realN, widget.falseNum);

    fixedN = realN + falseN;
    if (fixedN > 0) {
      w = (fixedN - 1) * (widget.width - widget.offset) + widget.width;
    } else {
      w = 0;
    }
  }

  @override
  void initState() {
    super.initState();
    _compute();
  }

  @override
  Widget build(BuildContext context) {
    Widget child = SizedBox(
      width: w,
      height: widget.width,
      child: Stack(
        children: List<Widget>.generate(fixedN, (index) {
          int i = index;

          ///因为从右往左排，所以需要先将虚拟人排前面
          if (i < falseN) {
            return _buildPositioned(
              child: _AvatarImageWidget(null,
                  width: widget.width,
                  height: widget.width,
                  more: index == moreIndex),
              index: index,
            );
          }
          i -= falseN;
          if (i < realN) {
            ///因为是从右往左排，所以需要反转一下数据
            int reversedIndex = realN - i - 1;
            return _buildPositioned(
              child: _AvatarImageWidget(widget.avatars[reversedIndex],
                  width: widget.width,
                  height: widget.width,
                  more: index == moreIndex),
              index: index,
            );
          }
          return SizedBox();
        }),
      ),
    );

    if (falseN < kMaxAvatarCount && widget.placeholder) {
      double w =
          (kMaxAvatarCount - 1) * (widget.width - widget.offset) + widget.width;
      child = Container(
        width: w,
        child: child,
        alignment: AlignmentDirectional.centerStart,
      );
    }
    return child;
  }

  Widget _buildPositioned({@required Widget child, @required index}) {
    return Positioned(
      right: index * (widget.width - widget.offset),
      child: child,
    );
  }

  @override
  void didUpdateWidget(covariant AvatarGroupWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.falseNum != oldWidget.falseNum ||
        widget.avatars != oldWidget.avatars ||
        widget.width != oldWidget.width ||
        widget.offset != oldWidget.offset) {
      _compute();
    }
  }
}

class _AvatarImageWidget extends StatelessWidget {
  final String image;
  final double width;
  final double height;
  final bool more;

  const _AvatarImageWidget(this.image,
      {this.width, this.height, this.more = false});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      child: ClipOval(
        child: Stack(alignment: AlignmentDirectional.center, children: [
          CachedNetworkImage(
            imageUrl: image ?? "",
            fit: BoxFit.cover,
            placeholder: (context, url) => Image.asset(
              'assets/images/avatar_boy.jpg',
              fit: BoxFit.cover,
            ),
            errorWidget: (context, url, error) => Image.asset(
              'assets/images/avatar_boy.jpg',
              fit: BoxFit.cover,
            ),
          ),
          if (more)
            Container(
              decoration:
                  BoxDecoration(color: Color(0xff37373A).withOpacity(0.8)),
            ),
          if (more)
            Positioned(
              right: 0,
              child: Row(
                children: List.generate(
                  3,
                  (index) => Container(
                    width: 1,
                    height: 1,
                    margin: EdgeInsets.only(right: 2),
                    padding: EdgeInsets.only(right: 0.5),
                    decoration: BoxDecoration(
                        color: Color(0xffD8D8D8), shape: BoxShape.circle),
                  ),
                ),
              ),
            ),
        ]),
      ),
    );
  }
}
