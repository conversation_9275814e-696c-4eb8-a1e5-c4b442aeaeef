import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';
import 'package:yvr_assistant/public_ui/widget/chinese_input.dart';
import 'package:yvr_assistant/utils/textInput_formatter.dart';

import '../../styles/app_color.dart';

enum DialogType {
  DialogText,
  DialogInput,
  DialogDIY,
}

///自定义Dialog
// ignore: must_be_immutable
class CustomDialog extends StatefulWidget {
  //------------------不带图片的dialog------------------------
  final String title; //弹窗标题
  final String content; //弹窗内容
  final String confirmText; //按钮文本
  final Color confirmTextColor; //确定按钮文本颜色
  final String cancelText; //按钮文本
  final bool isCancel; //是否有取消按钮，默认为true true：有 false：没有
  final Color confirmColor; //确定按钮颜色
  final Color cancelColor; //取消按钮颜色
  final Color cancelTextColor; //取消文本颜色
  final bool outsideDismiss; //点击弹窗外部，关闭弹窗，默认为true true：可以关闭 false：不可以关闭
  final Function confirmCallback; //点击确定按钮回调
  final Function cancelCallback; //弹窗关闭回调
  final Function(String) inputCommitCallback; //点击确定按钮回调
  final double height;
  final DialogType dialogType;

  //------------------带图片的dialog------------------------
  final String image; //dialog添加图片
  final String imageHintText; //图片下方的文本提示

  //------------------带输入框的dialog------------------------
  final keyboardType;
  String hintText;
  final maxLength;
  final text;

  //------------------带输入框的dialog------------------------
  final Widget contentWidget; // 自定义内容视图
  final Widget contentWidgetAfter; // 自定义内容更新后视图 防止使用的控制器文件无法刷新
  final Widget confirmWidget; // 自定义确认按钮
  final Alignment contentTextAlignment;
  final bool popAutomatically;

  CustomDialog({
    Key key,
    this.dialogType,
    this.title,
    this.content,
    this.confirmText,
    this.confirmTextColor,
    this.cancelText,
    this.isCancel = true,
    this.confirmColor,
    this.cancelColor,
    this.cancelTextColor,
    this.outsideDismiss = false,
    this.confirmCallback,
    this.cancelCallback,
    this.image,
    this.imageHintText,
    this.height = 210,
    this.keyboardType = TextInputType.number,
    this.maxLength,
    this.hintText,
    this.text = "",
    this.inputCommitCallback,
    this.contentWidget,
    this.contentWidgetAfter,
    this.confirmWidget,
    this.contentTextAlignment,
    this.popAutomatically = true,
  }) : super(key: key) {
    hintText ??= YLocal.current.qingshurushoujihao;
  }

  @override
  State<StatefulWidget> createState() {
    return _CustomDialogState();
  }
}

class _CustomDialogState extends State<CustomDialog> {
  ValueNotifier<bool> _isShowDiyConfirmBtn = ValueNotifier<bool>(false);
  var _input = new ChinaTextEditController();

  _confirmDialog() {
    if (widget.popAutomatically) {
      _dismissDialog();
    }
    if (widget.confirmCallback != null) {
      widget.confirmCallback();
    }
  }

  _cancelDialog() {
    if (widget.popAutomatically) {
      _dismissDialog();
    }
    if (widget.cancelCallback != null) {
      widget.cancelCallback();
    }
  }

  _dismissDialog() {
    // if (widget.dismissCallback != null) {
    //   widget.dismissCallback();
    // }
    Navigator.of(context).pop();
  }

  _inputConfirmDialog() {
    if (widget.popAutomatically) {
      _dismissDialog();
    }
    if (widget.inputCommitCallback != null) {
      widget.inputCommitCallback(_input.text);
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final width = size.width;
    Color bgColor = Colors.white;

    Column _dialogForText = Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Text(
          widget.title == null ? '' : widget.title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 20,
            height: 1,
            color: AppColors.textTitle,
          ),
        ),
        if (widget.content != null && widget.content.isNotEmpty)
          Container(
            margin: EdgeInsets.symmetric(vertical: 15),
            alignment: widget.contentTextAlignment,
            child: Text(
              widget.content == null ? '' : widget.content,
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 16,
                height: 1.5,
                color: AppColors.textSub,
              ),
            ),
          ),
        Container(
            height: 45,
            margin: EdgeInsets.only(top: 24),
            child: Row(
              children: <Widget>[
                Expanded(
                    child: widget.isCancel
                        ? YvrElevatedButton(
                            onPressed: _cancelDialog,
                            color:
                                widget.cancelColor ?? AppColors.backgroundItem,
                            child: Text(
                              widget.cancelText ?? YLocal.of(context).cancel,
                              style: TextStyle(
                                  fontSize: 16.0,
                                  color: widget.cancelTextColor ??
                                      AppColors.textSub,
                                  fontWeight: FontWeight.w600),
                            ),
                          )
                        : SizedBox(),
                    flex: widget.isCancel ? 1 : 0),
                SizedBox(
                  width: widget.isCancel ? 14 : 0,
                ),
                Expanded(
                    child: YvrElevatedButton(
                      color: widget.confirmColor ?? AppColors.standard,
                      onPressed: _confirmDialog,
                      child: Text(
                        widget.confirmText == null
                            ? YLocal.of(context).queding
                            : widget.confirmText,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          height: 1,
                          color: widget.confirmTextColor ?? Colors.white,
                        ),
                      ),
                    ),
                    flex: 1),
              ],
            ))
      ],
    );

    Column _dialogForInput = Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Text(
          widget.title == null ? '' : widget.title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 20,
            height: 1,
            color: AppColors.textTitle,
          ),
        ),
        Container(
            height: 50,
            margin: EdgeInsets.only(top: 24),
            child: TextField(
                inputFormatters: [NoSpaceFormatter()],
                keyboardType: widget.keyboardType,
                controller: _input,
                maxLength: widget.maxLength,
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  height: 1,
                  letterSpacing: 0,
                  color: AppColors.textTitle,
                ),
                onChanged: (value) {
                  if (_input.value.isComposingRangeValid) {
                    return;
                  }
                  _input.text = value;
                  _input.selection = TextSelection.fromPosition(
                      TextPosition(offset: _input.text.length));
                },
                decoration: InputDecoration(
                  counterText: "",
                  hintText: widget.hintText,
                  hintStyle: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                    height: 1,
                    letterSpacing: 0,
                    color: AppColors.textWeak,
                  ),
                  fillColor: Color(0xffF0F2F5),
                  filled: true,
                  contentPadding: const EdgeInsets.all(10.0),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: bgColor),
                    borderRadius: BorderRadius.all(
                      Radius.circular(4), //边角为5
                    ),
                  ),
                  enabledBorder: new UnderlineInputBorder(
                    borderSide: new BorderSide(color: bgColor),
                    borderRadius: BorderRadius.all(
                      Radius.circular(4), //边角为5
                    ),
                  ),
                ))),
        Container(
          alignment: Alignment.topLeft,
          margin: EdgeInsets.symmetric(vertical: 15),
          child: Text(
            widget.content == null ? '' : widget.content,
            style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 16,
              height: 1,
              letterSpacing: 0,
              color: AppColors.textSub,
            ),
          ),
        ),
        Container(
            height: 45,
            child: Row(
              children: <Widget>[
                Expanded(
                    child: widget.isCancel
                        ? YvrElevatedButton(
                            color:
                                widget.cancelColor ?? AppColors.backgroundItem,
                            child: Text(
                              widget.cancelText ?? YLocal.of(context).cancel,
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                fontSize: 16,
                                height: 1,
                                letterSpacing: 0,
                                color: AppColors.textSub,
                              ),
                            ),
                            onPressed: _cancelDialog,
                          )
                        : SizedBox(),
                    flex: widget.isCancel ? 1 : 0),
                SizedBox(
                  width: widget.isCancel ? 14 : 0,
                ),
                Expanded(
                    child: YvrElevatedButton(
                      onPressed: _inputConfirmDialog,
                      color: widget.confirmColor ?? AppColors.standard,
                      child: Text(
                        widget.confirmText == null
                            ? YLocal.of(context).queding
                            : widget.confirmText,
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                          height: 1,
                          color: widget.confirmTextColor ?? Colors.white,
                        ),
                      ),
                    ),
                    flex: 1),
              ],
            ))
      ],
    );

    Column _dialogForDIY = Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Text(widget.title == null ? '' : widget.title,
            style: TextStyle(
              fontSize: 20.0,
              fontWeight: FontWeight.w500,
            )),
        Container(
          margin: EdgeInsets.only(top: 24),
          alignment: Alignment.topLeft,
          child: ValueListenableBuilder<bool>(
              valueListenable: _isShowDiyConfirmBtn,
              builder: (context, value, child) {
                return _isShowDiyConfirmBtn.value
                    ? widget.contentWidgetAfter
                    : widget.contentWidget;
              }),
        ),
        Container(
            height: 45,
            child: Row(
              children: <Widget>[
                Expanded(
                    child: widget.isCancel
                        ? YvrElevatedButton(
                            color:
                                widget.cancelColor ?? AppColors.backgroundItem,
                            child: Text(
                              widget.cancelText ?? YLocal.of(context).cancel,
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                fontSize: 16,
                                height: 1,
                                letterSpacing: 0,
                                color: AppColors.textSub,
                              ),
                            ),
                            onPressed: _cancelDialog,
                          )
                        : SizedBox(),
                    flex: widget.isCancel ? 1 : 0),
                SizedBox(
                  width: widget.isCancel ? 14 : 0,
                ),
                Expanded(
                    child: ValueListenableBuilder<bool>(
                        valueListenable: _isShowDiyConfirmBtn,
                        builder: (context, value, child) {
                          return YvrElevatedButton(
                            color: _isShowDiyConfirmBtn.value
                                ? Color(0xff6883CC)
                                : Color(0xff4F7FFE),
                            onPressed: () {
                              // 自定义按钮无反馈事件
                              if (widget.confirmWidget != null) {
                                _isShowDiyConfirmBtn.value = true;
                                widget.confirmCallback();
                              }

                              if (widget.contentWidget != null) {
                                widget.confirmCallback();
                              }
                            },
                            child: _isShowDiyConfirmBtn.value
                                ? widget.confirmWidget
                                : Text(
                                    widget.confirmText == null
                                        ? YLocal.of(context).queding
                                        : widget.confirmText,
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      fontSize: 16,
                                      height: 1,
                                      color: AppColors.background,
                                    ),
                                  ),
                          );
                        }),
                    flex: 1),
              ],
            ))
      ],
    );

    Widget dialogContent(DialogType dialogType) {
      Widget contentWidget;
      switch (dialogType) {
        case DialogType.DialogText:
          contentWidget = _dialogForText;
          break;
        case DialogType.DialogInput:
          contentWidget = _dialogForInput;
          break;
        case DialogType.DialogDIY:
          contentWidget = _dialogForDIY;
          break;
        default:
          contentWidget = _dialogForText;
      }

      return contentWidget;
    }

    return WillPopScope(
        child: GestureDetector(
          onTap: () => {widget.outsideDismiss ? _dismissDialog() : null},
          child: Material(
            type: MaterialType.transparency,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Center(
                  child: Container(
                    padding: EdgeInsets.all(20),
                    width: width - 68,
                    // height: widget.height,
                    alignment: Alignment.center,
                    child: dialogContent(widget.dialogType),
                    decoration: BoxDecoration(
                        color: bgColor,
                        borderRadius: BorderRadius.circular(8.0)),
                  ),
                ),
              ],
            ),
          ),
        ),
        onWillPop: () async {
          return widget.outsideDismiss;
        });
  }
}
