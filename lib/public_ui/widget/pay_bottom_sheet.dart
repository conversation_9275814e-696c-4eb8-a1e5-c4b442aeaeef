import 'package:cached_network_image/cached_network_image.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/public_ui/widget/common_button.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/utils/log.dart';
import '../../generated/l10n.dart';
import '../../manager/event_bus.dart';
import '../../manager/global.dart';
import '../../model/pay_model.dart';
import '../../pages/home/<USER>/views/pay_cell.dart';
import '../../pages/profile/pay/tool/wallet_tool.dart';
import '../../provider/provider_widget.dart';
import '../../utils/data_record.dart';
import '../../view_model/pay_vmodel.dart';
import '../../view_model/user_vmodel.dart';
import 'countdown_widget.dart';
import 'dialog_util.dart';

typedef PayCallback = Function(bool isSuccess);
typedef AddListenerCallback = Function();

///公共支付底部弹窗
class PayBottomSheet {
  static Future showPay(BuildContext context, PayModel payModel,
      {PayCallback payCallback,
      AddListenerCallback addListenerCallback}) async {
    await showModalBottomSheet(
        context: context,
        enableDrag: false,
        isScrollControlled: true,
        backgroundColor: AppColors.transparent,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        )),
        builder: (BuildContext context) {
          return ProviderWidget<PayVModel>(
              model: PayVModel(payModel, payCallback, addListenerCallback),
              onModelReady: (model) {
                model.init();
              },
              builder: (context, model, _widget) => model.idle
                  ? contentWidget(context, model)
                  : const SizedBox());
        });
  }

  //内容体
  static Widget contentWidget(BuildContext context, PayVModel model) {
    /// 11-直接微信支付，12-支付宝支付，13-Y币充足直接支付，14-Y币不足跳转充值，15-卡券支付
    String symbol = (model.payModel.currency == 1) ? "\$" : "￥";
    return Container(
      key: model.payWayWidgetKey,
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      color: AppColors.transparent,
      child: Column(children: [
        Expanded(child: const SizedBox()),
        Container(
          width: double.infinity,
          decoration: const BoxDecoration(
            color: AppColors.colorFFFFFF,
            borderRadius: BorderRadius.all(Radius.circular(16)),
          ),
          child: Stack(children: [
            Positioned(
                top: 14,
                right: 16,
                child: GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    width: 32,
                    height: 32,
                    margin: EdgeInsets.only(left: 8),
                    child: SvgPicture.asset("assets/svg/ic_sheet_close.svg"),
                  ),
                )),
            Column(
              children: [
                const SizedBox(height: 21),
                Text(
                  YLocal.of(context).xuanzezhifufangshixu,
                  style: AppStyle.style_textTitle_w600_18pt(),
                ),
                const SizedBox(height: 48),
                model.payModel.isBundle
                    ? bundledHeadWidget(context, model, symbol)
                    : headWidget(context, model, symbol),
                couponWidget(context, model),
                priceWidget(context, model, symbol),
                Container(
                  width: double.infinity,
                  height: 1,
                  color: Color(0x33393A3C),
                  margin: const EdgeInsets.only(top: 36),
                ),
                Visibility(
                  visible: model.payWayType != PayWayType.payForCoupon,
                  child: payWayWidget(context, model),
                ),
                SizedBox(
                    height:
                        model.payWayType != PayWayType.payForCoupon ? 0 : 32),
                bottomBtnWidget(context, model, symbol),
              ],
            )
          ]),
        )
      ]),
    );
  }

  //单应用头部
  static Widget headWidget(
      BuildContext context, PayVModel model, String symbol) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20),
      child: Row(children: [
        Container(
          width: Global.screenWidth / 3,
          height: 60,
          clipBehavior: Clip.hardEdge,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
          ),
          child: CachedNetworkImage(
              imageUrl: model.payModel.rcover + "?scale=tiny" ?? "",
              fit: BoxFit.cover),
        ),
        const SizedBox(width: 10),
        Expanded(
            child: Text('${model.payModel.name}',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: AppStyle.style_textTitle_w400_14pt())),
        Visibility(
          visible: (model.payModel.discount != null &&
                  model.payModel.discount != '1') ||
              model.payModel.bprice > model.payModel.sprice,
          child: Text(
            "$symbol${MoneyUtil.changeF2Y(model.payModel.bprice)} ",
            style: AppStyle.style_767880_12pt_line(),
          ),
        ),
        Text('$symbol${MoneyUtil.changeF2Y(model.payModel.sprice)}',
            style: AppStyle.style_textTitle_w400_14pt())
      ]),
    );
  }

  //捆绑包应用头部
  static Widget bundledHeadWidget(
      BuildContext context, PayVModel model, String symbol) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20),
      child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Container(
          width: Global.screenWidth / 3,
          height: 60,
          clipBehavior: Clip.hardEdge,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
          ),
          child: CachedNetworkImage(
              imageUrl: model.payModel.rcover ?? "", fit: BoxFit.cover),
        ),
        const SizedBox(width: 10),
        Expanded(
            child: SizedBox(
          width: double.infinity,
          child: Column(children: [
            Row(children: [
              Expanded(
                  child: Text('${model.payModel.name}',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: AppStyle.style_textTitle_w400_14pt())),
              Visibility(
                visible: (model.payModel.discount != null &&
                        model.payModel.discount != '1') ||
                    model.payModel.bprice > model.payModel.sprice,
                child: Text(
                  "$symbol${MoneyUtil.changeF2Y(model.payModel.bprice)} ",
                  style: AppStyle.style_767880_12pt_line(),
                ),
              ),
              const SizedBox(width: 4),
              Text('$symbol${MoneyUtil.changeF2Y(model.payModel.sprice)}',
                  style: AppStyle.style_textTitle_w400_14pt()),
            ]),
            const SizedBox(height: 4),
            Column(children: appWidgets(context, model, symbol))
          ]),
        ))
      ]),
    );
  }

  //捆绑包里面的应用列表
  static List<Widget> appWidgets(
      BuildContext context, PayVModel model, String symbol) {
    List<Apps> apps = model.appList;
    List<Widget> widgets = [];
    for (int index = 0;
        index < (apps.length > 10 ? 10 : apps.length);
        index++) {
      widgets.add(Container(
        margin: EdgeInsets.only(top: 8),
        width: double.infinity,
        child: apps.length >= 10 && index == 9
            ? Row(children: [
                Opacity(
                    opacity: 0,
                    child: Text(YLocal.of(context).baohan,
                        style: AppStyle.style_textSub_w400_10pt())),
                Text('.......................',
                    style: AppStyle.style_textSub_w400_10pt())
              ])
            : Row(children: [
                Opacity(
                    opacity: index == 0 ? 1 : 0,
                    child: Text(YLocal.of(context).baohan,
                        style: AppStyle.style_textSub_w400_10pt())),
                Expanded(
                    child: Text(
                  '${index + 1}.${apps[index].name}',
                  style: AppStyle.style_textSub_w400_10pt(),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                )),
                Text('$symbol${MoneyUtil.changeF2Y(apps[index].bprice)}',
                    style: AppStyle.style_textSub_w400_10pt())
              ]),
      ));
    } //
    return widgets;
  }

  //游戏券栏
  static Widget couponWidget(BuildContext context, PayVModel model) {
    return Visibility(
        visible: !model.payModel.isBundle,
        child: Padding(
            padding: EdgeInsets.only(top: 24, left: 20, right: 20),
            child: GestureDetector(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    YLocal.of(context).youhuquanyouhuiquany,
                    style: AppStyle.style_textWeak_w500_14pt(),
                  ),
                  Row(
                    children: [
                      Text(
                        model.couponText ?? '',
                        style: model.canUseCounts != 0 ||
                                model.canNotUseCounts != 0
                            ? AppStyle.style_standard_w400_14pt()
                            : AppStyle.style_767880_14pt(),
                      ),
                      Container(
                        width: 16,
                        height: 16,
                        margin: EdgeInsets.only(left: 8),
                        child:
                            SvgPicture.asset("assets/svg/ic_arrow_right.svg"),
                      ),
                    ],
                  )
                ],
              ),
              onTap: () async {
                if (model.canUseCounts != 0 || model.canNotUseCounts != 0) {
                  var arguments = {
                    "selCouponId": model.selCouponId,
                    "canUseList": model.canUseList,
                    "canUseCounts": model.canUseCounts,
                    "canNotUseList": model.canNotUseList,
                    "canNotUseCounts": model.canNotUseCounts,
                  };
                  var result = await Navigator.of(context)
                      .pushNamed('/option_coupon', arguments: arguments);
                  Log.d('游戏券页返回 ==================>$result');
                  model.couponResult(result);
                }
              },
            )));
  }

  //金额栏
  static Widget priceWidget(
      BuildContext context, PayVModel model, String symbol) {
    return Padding(
      padding: EdgeInsets.only(top: 40, left: 20, right: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(YLocal.of(context).shijifukuan,
              style: AppStyle.style_textSub_w500_14pt()),
          Text(
            " $symbol${model.prodPrice}",
            style: AppStyle.style_standard_bold_18pt(),
          )
        ],
      ),
    );
  }

  //支付方式栏
  static Widget payWayWidget(BuildContext context, PayVModel model) {
    return Container(
        width: double.infinity,
        margin: EdgeInsets.only(top: 15),
        padding: EdgeInsets.symmetric(horizontal: 20),
        child: Column(children: [
          // PayCellWidget(
          //   type: PayWayType.payForYCoin,
          //   selectedType: model.payWayType,
          //   sprice: model.payModel.sprice,
          //   payCallBack: () {
          //     model.setPayWayType(PayWayType.payForYCoin);
          //   },
          // ),
          PayCellWidget(
            type: PayWayType.payForPayPal,
            selectedType: model.payWayType,
            sprice: model.payModel.sprice,
            payCallBack: () {
              model.setPayWayType(PayWayType.payForPayPal);
            },
          ),
          const SizedBox(height: 20)
          // Visibility(
          //   visible: model.isWeChatInstalled,
          //   child: PayCellWidget(
          //     type: PayWayType.payForWechat,
          //     selectedType: model.payWayType,
          //     payCallBack: () {
          //       model.setPayWayType(PayWayType.payForWechat);
          //     },
          //   ),
          // )
        ]));
  }

  //底部按钮栏
  static Widget bottomBtnWidget(
      BuildContext context,
      PayVModel model,
      // ignore: invalid_required_positional_param
      @required String symbol) {
    if (model.payInfoMap?.text == null || model.payInfoMap.text.isEmpty) {
      model.payInfoMap?.text = (model.payModel.sprice == 0)
          ? YLocal.of(context).home_free
          : "$symbol${MoneyUtil.changeF2Y(model.payModel.sprice)}";
    }
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(bottom: Global.paddingBottom + 12),
      padding: EdgeInsets.symmetric(horizontal: 20),
      child: CommonButton(
        text: model.payInfoMap?.text ?? '',
        textWidget: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              model.payInfoMap?.text ?? '',
              style: AppStyle.style_ffffff_bold_18pt(),
            ),
            CountdownWidget(
                countdownText:
                    Provider.of<PayVModel>(context).countdownTimeText)
          ],
        ),
        onTap: () {
          _pay(context, model);
        },
      ),
    );
  }

  ///eventType: 10弹框选择支付方式，11直接微信支付，12支付宝支付，13Y币充足直接支付，14Y币不足跳转充值，15 PayPal支付
  static void _pay(BuildContext context, PayVModel model) async {
    DataRecord().saveData(
        eventId: "assistant_appstore_appDetail_bottom_buyButton_pit_click",
        extraData: {
          "appId": model.payModel.id,
        });
    if (DBUtil.instance.userBox.get(kToken) == null) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (_) {
            return CustomDialog(
              title: YLocal.of(context).wenxindishiwenxintis,
              content: YLocal.of(context).xuyaodenglucainenggo,
              confirmText: YLocal.of(context).qudenglu,
              height: 180,
              confirmCallback: () {
                Navigator.pushNamed(context, '/login');
              },
            );
          });
    } else {
      if (model.payModel.sprice == 0) {
        if (await model.isBindingDevice()) {
          model.reqPayParams(0);
        } else {
          showNoBindingDevice(context);
        }
      } else {
        switch (model.payInfoMap.eventType) {
          case 10:
            YvrToast.showLoading(message: "");
            if (await model.isBindingDevice()) {
              showPay(context, model.payModel);
            } else {
              showNoBindingDevice(context);
            }
            YvrToast.dismiss();
            break;
          case 11:
          case 12:
          case 13:
            model.reqPayParams(model.payInfoMap.eventType - 10);
            break;
          case 14:
            if (model.payModel.isBundle) {
              Navigator.pop(context);
              model.addListenerCallback();
            } else {
              WalletTool().jumpToWalletPage(context);
            }
            break;
          case 15:
            model.reqPayParams(4);
            break;
          default:
        }
      }
    }
  }

  static void showNoBindingDevice(BuildContext context) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) {
          return CustomDialog(
            title: YLocal.of(context).mofagoumaigaiyingyon,
            content: YLocal.of(context).goumaiyingyongqianqi,
            confirmText: YLocal.of(context).tianjiashebei,
            isCancel: true,
            confirmCallback: () {
              eventBus.fire(EventFn({'selectedTabbarIndex': 2}));
            },
          );
        });
  }
}
