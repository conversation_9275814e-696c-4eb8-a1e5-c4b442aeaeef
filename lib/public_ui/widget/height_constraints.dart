import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

import '../../generated/l10n.dart';

class HeightConstraintWidget extends SingleChildRenderObjectWidget {
  final double maxHeight;
  final bool expanded;
  final VoidCallback onTap;

  const HeightConstraintWidget({
    Key key,
    Widget child,
    @required this.maxHeight,
    @required this.expanded,
    this.onTap,
  })  : assert(maxHeight >= 30),
        super(key: key, child: child);

  @override
  HeightConstraintBoxRenderObject createRenderObject(BuildContext context) {
    return HeightConstraintBoxRenderObject(
        maxHeight: maxHeight, expanded: expanded, onTap: onTap);
  }

  @override
  void updateRenderObject(
      BuildContext context, HeightConstraintBoxRenderObject renderObject) {
    renderObject.maxHeight = maxHeight;
    renderObject.expanded = expanded;
    renderObject.onTap = onTap;
  }
}

class ExpandParentData extends ParentData {
  bool expandable = false;
}

class HeightConstraintBoxRenderObject
    extends RenderProxyBoxWithHitTestBehavior {
  TextPainter _textPainter;
  TextPainter _iconPainter;
  VoidCallback onTap;

  HeightConstraintBoxRenderObject({
    RenderBox child,
    @required double maxHeight,
    @required bool expanded,
    this.onTap,
  })  : _maxHeight = maxHeight,
        _expanded = expanded,
        super(child: child);

  double get maxHeight => _maxHeight;
  double _maxHeight;

  set maxHeight(double value) {
    if (_maxHeight == value) return;
    _maxHeight = value;
    markNeedsLayout();
  }

  bool get expanded => _expanded;
  bool _expanded;

  set expanded(bool value) {
    if (_expanded == value) return;
    _expanded = value;
    markNeedsLayout();
  }

  @override
  void setupParentData(covariant RenderObject child) {
    if (child.parentData is! ExpandParentData) {
      child.parentData = ExpandParentData();
    }
  }

  static const double kTextMarginTop = 14;

  double get textHeight =>
      (_textPainter == null ? 0 : _textPainter.height) + kTextMarginTop;

  @override
  double computeMinIntrinsicHeight(double width) {
    if (child != null) {
      bool expandable = (child.parentData as ExpandParentData).expandable;
      if (expandable) {
        if (expanded) {
          return child.getMinIntrinsicHeight(width) + textHeight;
        } else {
          return maxHeight;
        }
      } else {
        return child.getMinIntrinsicHeight(width);
      }
    }
    return 0.0;
  }

  @override
  double computeMaxIntrinsicHeight(double width) {
    if (child != null) {
      bool expandable = (child.parentData as ExpandParentData).expandable;
      if (expandable) {
        if (expanded) {
          return child.getMinIntrinsicHeight(width) + textHeight;
        } else {
          return maxHeight;
        }
      } else {
        return child.getMinIntrinsicHeight(width);
      }
    }
    return 0.0;
  }

  void ensurePainter(bool expanded, BoxConstraints constraints) {
    String text;
    IconData icon;
    if (expanded) {
      text = YLocal.current.shouqi;
      icon = Icons.keyboard_arrow_up;
    } else {
      text = YLocal.current.zhankai;
      icon = Icons.keyboard_arrow_down;
    }
    _textPainter = TextPainter(
        text: TextSpan(
          text: text,
          style: const TextStyle(
              fontSize: 16, color: Color(0xff4F7FFE), height: 1.0),
        ),
        textAlign: TextAlign.start,
        textDirection: TextDirection.ltr);
    _textPainter.layout(
        minWidth: constraints.minWidth,
        maxWidth: constraints
            .maxWidth); //进行布局 textPainter.paint(canvas,Offset.zero);//进行绘制
    _iconPainter = TextPainter(
        text: TextSpan(
          text: String.fromCharCode(icon.codePoint),
          style: TextStyle(
            color: Color(0xff4F7FFE),
            height: 1.0,
            fontSize: 20,
            fontFamily: icon.fontFamily,
            package: icon
                .fontPackage, // This line is mandatory for external icon packs
          ),
        ),
        textAlign: TextAlign.start,
        textDirection: TextDirection.ltr);
    _iconPainter.layout(
        minWidth: constraints.minWidth, maxWidth: constraints.maxWidth);
  }

  @override
  void performLayout() {
    final BoxConstraints constraints = this.constraints;
    if (child != null) {
      child.layout(constraints, parentUsesSize: true);
      if (child.size.height > maxHeight) {
        double height;
        (child.parentData as ExpandParentData).expandable = true;
        if (expanded) {
          ensurePainter(true, constraints);
          child.layout(constraints, parentUsesSize: true);
          height = child.size.height + textHeight;
        } else {
          ensurePainter(false, constraints);
          child.layout(constraints.tighten(height: maxHeight - textHeight),
              parentUsesSize: true);
          height = maxHeight;
        }
        size = constraints.constrain(Size(child.size.width, height));
      } else {
        _textPainter = null;
        (child.parentData as ExpandParentData).expandable = false;
        size = child.size;
      }
    } else {
      _textPainter = null;
      size = constraints.constrain(Size.zero);
    }
  }

  @override
  void paint(PaintingContext context, Offset offset) {
    super.paint(context, offset);
    if (child != null) {
      if ((child.parentData as ExpandParentData).expandable) {
        double textY = offset.dy + size.height - textHeight + kTextMarginTop;
        _textPainter.paint(
          context.canvas,
          Offset(offset.dx, textY),
        );
        _iconPainter.paint(
            context.canvas,
            Offset(offset.dx + _textPainter.width,
                textY + (_textPainter.height - _iconPainter.height) * 0.5));
      }
    }
  }

  @override
  Size computeDryLayout(BoxConstraints constraints) {
    if (child != null) {
      return child.getDryLayout(constraints.tighten(height: maxHeight));
    } else {
      return constraints.tighten(height: maxHeight).constrain(Size.zero);
    }
  }

  @override
  Size computeSizeForNoChild(BoxConstraints constraints) {
    return constraints.biggest;
  }

  @override
  void handleEvent(PointerEvent event, HitTestEntry entry) {
    assert(debugHandleEvent(event, entry));
    if (event is PointerDownEvent) {
      return;
    }
    if (event is PointerMoveEvent) {
      return;
    }
    if (event is PointerUpEvent) {
      if (onTap != null &&
          child != null &&
          (child.parentData as ExpandParentData).expandable) {
        Rect textRect = Rect.fromLTRB(
            paintBounds.left,
            paintBounds.height - textHeight,
            paintBounds.right,
            paintBounds.bottom);

        if (textRect.contains(event.localPosition)) {
          onTap?.call();
        }
      }
      return;
    }
    if (event is PointerHoverEvent) {
      return;
    }
    if (event is PointerCancelEvent) {
      return;
    }
    if (event is PointerSignalEvent) {
      return;
    }
  }

  @override
  bool hitTestSelf(Offset position) {
    if (child != null && (child.parentData as ExpandParentData).expandable) {
      Rect textRect = Rect.fromLTRB(
          paintBounds.left,
          paintBounds.height - textHeight,
          paintBounds.right,
          paintBounds.bottom);
      return textRect.contains(position);
    }
    return false;
  }
}
