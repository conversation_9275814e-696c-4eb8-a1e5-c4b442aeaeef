import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/rating_star.dart';

import '../../generated/l10n.dart';

class StarJudge extends StatefulWidget {
  final double aver;
  final List starList;
  final int peoples;

  StarJudge({Key key, this.aver = 0, this.starList, this.peoples = 0})
      : super(key: key);

  @override
  _StarJudgeState createState() => _StarJudgeState();
}

class _StarJudgeState extends State<StarJudge> {
  @override
  Widget build(BuildContext context) {
    List<Widget> views = [];
    views.add(starTile());
    // 取出数组
    List tempList = widget.starList ?? [0, 0, 0, 0, 0];
    for (var i = 0; i < tempList.length; i++) {
      int scale = 0;

      if (widget.peoples != 0 && tempList[i] != 0) {
        scale = (tempList[i] * 100 / widget.peoples).round();
      }

      if (i == 0) {
        views.add(SizedBox(height: 20));
      }
      views.add(starProgress(number: 5 - i, scale: scale));
      if (i < 4) {
        views.add(SizedBox(height: 12));
      }
    }
    return Container(child: Column(children: views.toList()));
  }

  Widget starTile() {
    // 3.0 - 3.4 3    3.5-3.9 3.5
    double _star = ((widget.aver.floor() == widget.aver.round())
        ? widget.aver
        : (widget.aver.floor() + 0.5));
    return Container(
      width: double.infinity,
      child: Row(
        children: [
          Text(widget.aver.toString(),
              style: AppStyle.style_textTitle_w600_40pt()),
          SizedBox(
            width: 16,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              RatingBar(
                initialRating: _star,
                allowHalfRating: true,
                itemCount: 5,
                itemSize: 12,
                ignoreGestures: true,
                ratingWidget: ratingWidget,
                itemPadding: EdgeInsets.symmetric(horizontal: 2.0),
                onRatingUpdate: (double value) {},
              ),
              SizedBox(
                height: 8,
              ),
              Text(
                YLocal.of(context).Nrenpingfen(widget.peoples.toString()),
                style: AppStyle.style_textWeak_w400_12pt(),
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget starProgress({@required int number, @required int scale}) {
    return Row(
      children: [
        Text(
          number <= 1
              ? YLocal.of(context).Nxing(number)
              : YLocal.of(context).Nxing_2(number),
          style: AppStyle.style_textSub_w400_12pt(),
        ),
        Expanded(
          child: LinearPercentIndicator(
            lineHeight: 4.0,
            percent: scale / 100,
            backgroundColor: AppColors.divider,
            progressColor: AppColors.standard,
          ),
        ),
        SizedBox(width: 8),
        Container(
          width: 40,
          alignment: Alignment.centerRight,
          child: Text(
            "$scale%",
            style: AppStyle.style_textSub_w400_14pt(),
          ),
        )
      ],
    );
  }
}
