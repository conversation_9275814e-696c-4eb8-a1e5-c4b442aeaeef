import "package:flutter/cupertino.dart";
import 'package:flutter/services.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/styles/app_color.dart';

class FeedbackInput extends StatefulWidget {
  final String hint;
  final double height;
  final int maxLength;
  final Color fillColor;
  final Function(String) onChanged;
  final FocusNode focusNode;
  final TextEditingController controller;

  const FeedbackInput({
    Key key,
    this.hint,
    this.height,
    this.maxLength,
    this.fillColor,
    this.onChanged,
    this.controller,
    this.focusNode,
  }) : super(key: key);

  @override
  State createState() {
    return _FeedbackInputState();
  }
}

class _FeedbackInputState extends State<FeedbackInput>
    with SingleTickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: _textInput(widget.hint),
    );
  }

  String result = "";
  _textInput(String hint) {
    // 防止获取已有文字时 字数为0
    if (widget.controller != null) {
      result = widget.controller.text;
    }

    return Stack(
      children: [
        Container(
          width: double.infinity,
          height: 238,
          margin: EdgeInsets.symmetric(horizontal: 16),
          alignment: AlignmentDirectional.topStart,
          padding: EdgeInsets.only(left: 20, right: 20, top: 20, bottom: 35),
          decoration: BoxDecoration(
            color: AppColors.backgroundItem,
            borderRadius: BorderRadius.all(Radius.circular(16)),
          ),
          child: CupertinoTextField(
            maxLengthEnforcement: MaxLengthEnforcement.enforced,
            controller: widget.controller,
            focusNode: widget.focusNode,
            placeholder: hint,
            maxLength: 200,
            maxLines: null,
            minLines: 1,
            style: TextStyle(
                color: AppColors.textTitle,
                fontSize: 14,
                fontWeight: FontWeight.w400,
                height: 1.3),
            placeholderStyle: AppStyle.style_textWeak_w400_14pt(),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(2)),
            ),
            textInputAction: TextInputAction.unspecified,
            onChanged: (str) {
              result = str;
              widget.onChanged(result);
              setState(() {});
            },
          ),
        ),
        Positioned(
          bottom: 20,
          right: 36,
          child: Text(
            '${result.length}/200',
            style: AppStyle.style_textWeak_w400_14pt(),
          ),
        )
      ],
    );
  }
}
