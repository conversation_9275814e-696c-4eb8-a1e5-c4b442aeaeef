/// @Title:
/// @Package
/// @Description: 单列 多列 日期选择器
/// <AUTHOR> A18ccms_gmail_com
/// @date
/// @version V1.0

// ignore_for_file: unused_element
import 'package:flutter/material.dart';
import 'package:flutter_picker/flutter_picker.dart';
import 'package:yvr_assistant/generated/l10n.dart';

const double _kPickerHeight = 240.0;
const double _kItemHeigt = 40.0;
const double _kTextFontSize = 16.0;
const Color _kTitleColor = Color(0xff4F7FFE);
const Color _kHeaderColor = Colors.white;
const Color _ksecondaryBg = Colors.white;
const Color _kCancelBtnColor = Color(0xffE8E8E8);
const Color _kConfirmBtnColor = Color(0xff4F7FFE);
const Color _kSelectedTextColor = Colors.black;

typedef _StringClickCallBack = void Function(int selectIndex, Object selectStr);
typedef _ArrayClickCallBack = void Function(
    List<int> selecteds, List<dynamic> strData);
typedef _DateClickCallBack = void Function(
    DateTime selectDateStr, dynamic selectData);

enum DateType {
  HM, // y,m
  YMD, // y,m,d
  YM,
  YMD_HM, //y,m,d,hh,mm
  YMD_AP_HM, //y,m,d,ap,hh,mm
}

class JhPickerTool {
  //单列
  static void showStringPicker<T>(
    BuildContext context, {
    @required List<T> data,
    String title,
    int normalIndex,
    PickerDataAdapter adapter,
    @required _StringClickCallBack clickCallBack,
  }) {
    openModalPicker(context,
        adapter: adapter ?? PickerDataAdapter(pickerData: data, isArray: false),
        clickCallBack: (Picker picker, List<int> selecteds) {
      clickCallBack(selecteds[0], data[selecteds[0]]);
    }, selecteds: [normalIndex ?? 0], title: title);
  }

  //多列
  static void showArrayPicker<T>(
    BuildContext context, {
    @required List<T> data,
    String title,
    List<int> normalIndex,
    PickerDataAdapter adapter,
    @required _ArrayClickCallBack clickCallBack,
  }) {
    openModalPicker(context,
        adapter: adapter ?? PickerDataAdapter(pickerData: data, isArray: true),
        clickCallBack: (Picker picker, List<int> selecteds) {
      clickCallBack(selecteds, picker.getSelectedValues());
    }, selecteds: normalIndex, title: title);
  }

  static void openModalPicker(
    BuildContext context, {
    @required PickerAdapter adapter,
    String title,
    List<int> selecteds,
    @required PickerConfirmCallback clickCallBack,
  }) {
    new Picker(
        headerColor: _kHeaderColor,
        backgroundColor: _ksecondaryBg,
        adapter: adapter,
        title: new Text(
          title ?? "",
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18,
            height: 1,
            color: Color(0XFF2C2E33),
          ),
        ),
        selecteds: selecteds,
        cancelText: YLocal.current.quxiao,
        confirmText: YLocal.current.queding,
        cancelTextStyle: TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 16,
          height: 1.40625,
          color: Color(0XFFAFB6CC),
        ),
        confirmTextStyle: TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 16,
          height: 1,
          letterSpacing: 0,
          color: _kTitleColor,
        ),
        textAlign: TextAlign.right,
        itemExtent: _kItemHeigt,
        height: _kPickerHeight,
        textStyle: TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 16,
          height: 1.40625,
          color: Color(0XFF2C2E33),
        ),
        selectedTextStyle: TextStyle(color: _kSelectedTextColor),
        onConfirm: clickCallBack,
        footer: SizedBox(
          height: 30,
          child: Container(
            color: _ksecondaryBg,
          ),
        )).showModal(context);
  }

  //日期选择器
  static void showDatePicker(
    BuildContext context, {
    DateType dateType,
    String title,
    DateTime maxValue,
    DateTime minValue,
    DateTime value,
    DateTimePickerAdapter adapter,
    @required _DateClickCallBack clickCallBack,
  }) {
    int timeType;
    if (dateType == DateType.HM) {
      timeType = PickerDateTimeType.kHM;
    } else if (dateType == DateType.YM) {
      timeType = PickerDateTimeType.kYM;
    } else if (dateType == DateType.YMD_HM) {
      timeType = PickerDateTimeType.kYMDHM;
    } else if (dateType == DateType.YMD_AP_HM) {
      timeType = PickerDateTimeType.kYMD_AP_HM;
    } else {
      timeType = PickerDateTimeType.kYMD;
    }
    openModalPicker(context,
        adapter: adapter ??
            DateTimePickerAdapter(
              type: timeType,
              isNumberMonth: true,
              yearSuffix: "年",
              monthSuffix: "月",
              daySuffix: "日",
              minuteInterval: 30,
              strAMPM: [YLocal.current.shangwu, YLocal.current.xiawu],
              maxValue: maxValue,
              minValue: minValue,
              value: value ?? DateTime.now(),
            ),
        title: title, clickCallBack: (Picker picker, List<int> selecteds) {
      DateTime time = (picker.adapter as DateTimePickerAdapter).value;
      clickCallBack(time, picker.adapter.text);
    });
  }
}
