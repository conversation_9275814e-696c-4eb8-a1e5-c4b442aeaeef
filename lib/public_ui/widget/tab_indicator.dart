import 'package:flutter/material.dart';

const double _kWidth = 3;

class YvrUnderlineTabIndicator extends Decoration {
  const YvrUnderlineTabIndicator({
    this.gradient,
    this.insets = EdgeInsets.zero,
  }) : assert(insets != null);

  final Gradient gradient;

  final EdgeInsetsGeometry insets;

  @override
  Decoration lerpFrom(Decoration a, double t) {
    if (a is YvrUnderlineTabIndicator) {
      return YvrUnderlineTabIndicator(
        gradient: Gradient.lerp(a.gradient, gradient, t),
        insets: EdgeInsetsGeometry.lerp(a.insets, insets, t),
      );
    }
    return super.lerpFrom(a, t);
  }

  @override
  Decoration lerpTo(Decoration b, double t) {
    if (b is YvrUnderlineTabIndicator) {
      return YvrUnderlineTabIndicator(
        gradient: Gradient.lerp(gradient, b.gradient, t),
        insets: EdgeInsetsGeometry.lerp(insets, b.insets, t),
      );
    }
    return super.lerpTo(b, t);
  }

  @override
  BoxPainter createBoxPainter([VoidCallback onChanged]) {
    return _YvrUnderlinePainter(this, onChanged);
  }

  Rect _indicatorRectFor(Rect rect, TextDirection textDirection) {
    assert(rect != null);
    assert(textDirection != null);
    final Rect indicator = insets.resolve(textDirection).deflateRect(rect);
    return Rect.fromLTWH(
      indicator.left,
      indicator.bottom - _kWidth,
      indicator.width,
      _kWidth,
    );
  }

  @override
  Path getClipPath(Rect rect, TextDirection textDirection) {
    return Path()..addRect(_indicatorRectFor(rect, textDirection));
  }
}

class _YvrUnderlinePainter extends BoxPainter {
  _YvrUnderlinePainter(this.decoration, VoidCallback onChanged)
      : assert(decoration != null),
        super(onChanged);

  final YvrUnderlineTabIndicator decoration;

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    assert(configuration != null);
    assert(configuration.size != null);
    final Rect rect = offset & configuration.size;
    final Rect indicator = Rect.fromLTRB(
        rect.left, rect.bottom - _kWidth, rect.right, rect.bottom);
    if (decoration.gradient != null) {
      final Paint paint = Paint();
      paint.shader = decoration.gradient.createShader(indicator);
      canvas.drawRect(indicator, paint);
    }
  }
}
