import 'dart:io';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:image_crop/image_crop.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/download_utils.dart';
import '../../generated/l10n.dart';
import 'buttons.dart';
import 'dialogs.dart';
import 'image_view.dart';
import '../../view_model/vmodel_mixin.dart';
import '../../pages/tabbar/top_navbar.dart';

class LocalImageBrowserPage extends StatefulWidget {
  final List<File> images;
  final int index;

  const LocalImageBrowserPage({Key key, @required this.images, this.index})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _LocalImageBrowsePageState();
  }
}

class _LocalImageBrowsePageState extends State<LocalImageBrowserPage> {
  PageController _controller;
  String title;

  @override
  void initState() {
    super.initState();
    int index;
    if (widget.images.length > 0 && widget.index != null) {
      index = widget.index;
    } else {
      index = 0;
    }
    title = _generateTitle(index, widget.images.length);
    _controller = PageController(initialPage: index);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: TopNavbar(title: title),
        body: PhotoViewGallery.builder(
          scrollPhysics: const BouncingScrollPhysics(),
          builder: (BuildContext context, int index) {
            return PhotoViewGalleryPageOptions(
                imageProvider: FileImage(widget.images[index]),
                errorBuilder: (context, e, s) {
                  return ErrorImage();
                },
                onTapUp: (context, detail, value) {
                  Navigator.of(context).pop();
                }
                // initialScale: PhotoViewComputedScale.contained * 0.8,
                // heroAttributes: PhotoViewHeroAttributes(tag: galleryItems[index].id),
                );
          },
          itemCount: widget.images.length,
          loadingBuilder: (context, event) => Center(
            child: Container(
              width: 20.0,
              height: 20.0,
              child: CircularProgressIndicator(
                value: event == null
                    ? 0
                    : event.cumulativeBytesLoaded / event.expectedTotalBytes,
              ),
            ),
          ),
          // backgroundDecoration: widget.backgroundDecoration,
          pageController: _controller,
          onPageChanged: (index) {
            setState(() {
              title = _generateTitle(index, widget.images.length);
            });
          },
        ));
  }
}

class LocalImageBrowserPage2 extends StatefulWidget {
  final List<AssetEntity> images;
  final int index;

  const LocalImageBrowserPage2({Key key, @required this.images, this.index})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _LocalImageBrowsePageState2();
  }
}

class _LocalImageBrowsePageState2 extends State<LocalImageBrowserPage2> {
  PageController _controller;
  String title;

  @override
  void initState() {
    super.initState();
    int index;
    if (widget.images.length > 0 && widget.index != null) {
      index = widget.index;
    } else {
      index = 0;
    }
    title = _generateTitle(index, widget.images.length);
    _controller = PageController(initialPage: index);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: TopNavbar(title: title),
        body: PhotoViewGallery.builder(
          scrollPhysics: const BouncingScrollPhysics(),
          builder: (BuildContext context, int index) {
            return PhotoViewGalleryPageOptions(
                imageProvider: AssetEntityImageProvider(widget.images[index],
                    isOriginal: true),
                errorBuilder: (context, e, s) {
                  return ErrorImage();
                },
                onTapUp: (context, detail, value) {
                  Navigator.of(context).pop();
                }
                // initialScale: PhotoViewComputedScale.contained * 0.8,
                // heroAttributes: PhotoViewHeroAttributes(tag: galleryItems[index].id),
                );
          },
          itemCount: widget.images.length,
          loadingBuilder: (context, event) => Center(
            child: Container(
              width: 20.0,
              height: 20.0,
              child: CircularProgressIndicator(
                value: event == null
                    ? 0
                    : event.cumulativeBytesLoaded / event.expectedTotalBytes,
              ),
            ),
          ),
          // backgroundDecoration: widget.backgroundDecoration,
          pageController: _controller,
          onPageChanged: (index) {
            setState(() {
              title = _generateTitle(index, widget.images.length);
            });
          },
        ));
  }
}

String _generateTitle(int index, int size) {
  return "${index + 1}/$size";
}

class NetworkImageBrowserPage extends StatefulWidget {
  final List<String> images;
  final int index;

  const NetworkImageBrowserPage({Key key, @required this.images, this.index})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _NetworkImageBrowserPage();
  }
}

class _NetworkImageBrowserPage extends State<NetworkImageBrowserPage> {
  PageController _controller;
  String title;

  @override
  void initState() {
    super.initState();
    int index;
    if (widget.images.length > 0 && widget.index != null) {
      index = widget.index;
    } else {
      index = 0;
    }
    title = _generateTitle(index, widget.images.length);
    _controller = PageController(initialPage: index);
  }

  Future<bool> onDownloadTap(BuildContext context, String url) async {
    return DownloadUtils.downloadImage(context, url);
  }

  @override
  Widget build(BuildContext context) {
    double top = MediaQuery.of(context).padding.top;
    return Scaffold(
        backgroundColor: Colors.black,
        floatingActionButton: Container(
          width: 44,
          height: 44,
          child: FloatingActionButton(
            onPressed: () {
              onDownloadTap(context, widget.images[_controller.page.round()]);
            },
            mini: true,
            child: SvgPicture.asset(
              "assets/svg/download.svg",
              width: 27,
              height: 27,
              color: Color(0xffD9D9D9),
            ),
            backgroundColor: Color.fromARGB(255, 18, 18, 18),
          ),
        ),
        body: Stack(
          alignment: AlignmentDirectional.topCenter,
          children: [
            PhotoViewGallery.builder(
              scrollPhysics: const BouncingScrollPhysics(),
              builder: (BuildContext context, int index) {
                return PhotoViewGalleryPageOptions(
                    imageProvider: NetworkImage(widget.images[index]),
                    errorBuilder: (context, e, s) {
                      return ErrorImage();
                    },
                    onTapUp: (context, detail, value) {
                      Navigator.of(context).pop();
                    }
                    // initialScale: PhotoViewComputedScale.contained * 0.8,
                    // heroAttributes: PhotoViewHeroAttributes(tag: galleryItems[index].id),
                    );
              },
              itemCount: widget.images.length,
              loadingBuilder: (context, event) => Center(
                child: Container(
                  width: 20.0,
                  height: 20.0,
                  child: CircularProgressIndicator(
                    value: event == null
                        ? 0
                        : event.cumulativeBytesLoaded /
                            event.expectedTotalBytes,
                  ),
                ),
              ),
              // backgroundDecoration: widget.backgroundDecoration,
              pageController: _controller,
              onPageChanged: (index) {
                setState(() {
                  title = _generateTitle(index, widget.images.length);
                });
              },
            ),
            Positioned(
              child: Text(
                title,
                style: TextStyle(color: Color(0xffE8E8E8), fontSize: 20),
              ),
              top: top + 10,
            ),
          ],
        ));
  }
}

class BackgroundImageBrowserPage extends StatefulWidget {
  final String image;
  final Future<String> Function(Uint8List data, String fileName) upload;

  const BackgroundImageBrowserPage(
      {Key key, @required this.image, @required this.upload})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _BackgroundImageBrowserPage();
  }
}

class _BackgroundImageBrowserPage extends State<BackgroundImageBrowserPage> {
  PageController _controller;
  String title;

  @override
  void initState() {
    super.initState();
    _controller = PageController();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TopNavbar(),
      body: Column(
        children: [
          Expanded(
            child: PhotoViewGallery.builder(
              backgroundDecoration: BoxDecoration(color: Colors.white),
              scrollPhysics: const BouncingScrollPhysics(),
              builder: (BuildContext context, int index) {
                return PhotoViewGalleryPageOptions(
                    imageProvider: NetworkImage(widget.image),
                    errorBuilder: (context, e, s) {
                      return ErrorImage();
                    },
                    onTapUp: (context, detail, value) {});
              },
              itemCount: 1,
              loadingBuilder: (context, event) => Center(
                child: Container(
                  width: 20.0,
                  height: 20.0,
                  child: CircularProgressIndicator(
                    value: event == null
                        ? 0
                        : event.cumulativeBytesLoaded /
                            event.expectedTotalBytes,
                  ),
                ),
              ),
              // backgroundDecoration: widget.backgroundDecoration,
              pageController: _controller,
              onPageChanged: (index) {},
            ),
          ),
          Container(
            width: double.infinity,
            margin: EdgeInsets.only(
              left: 20,
              top: 12,
              right: 20,
              bottom: 12 + MediaQuery.of(context).padding.bottom,
            ),
            child: YvrTextButton(
              onPressed: () {
                onUpdate(context);
              },
              padding: EdgeInsets.symmetric(vertical: 12),
              child: Text(
                YLocal.of(context).genghuanbeijing,
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  void onUpdate(BuildContext context) {
    doSelectAndCropImage(context, widget.upload).then((value) {
      if (value == 1) {
        Navigator.of(context).pop(1);
      }
    });
  }
}

Future<int> doSelectAndCropImage(BuildContext context,
    Future<String> Function(Uint8List data, String fileName) upload,
    {double aspectRatio = 748 / 1076}) async {
  var value = await Dialogs.showImageSelectorDialog2(context, maxAssets: 1);
  if (value == null || value.isEmpty) {
    return null;
  }
  var file = await value.first.originFile;
  int result = await Navigator.of(context).push(
    ImageBrowserPageRoute(
      builder: (context) {
        return CropBackgroundRoute(
          image: file,
          upload: upload,
          aspectRatio: aspectRatio,
        );
      },
    ),
  );
  if (result == null) {
    return null;
  }

  return 1;
}

class CropBackgroundRoute extends StatefulWidget {
  final File image; //原始图片路径
  final Future<String> Function(Uint8List data, String fileName) upload;
  final double aspectRatio;

  CropBackgroundRoute({
    Key key,
    @required this.image,
    @required this.upload,
    @required this.aspectRatio,
  }) : super(key: key);

  @override
  _CropBackgroundRouteState createState() => new _CropBackgroundRouteState();
}

class _CropBackgroundRouteState extends State<CropBackgroundRoute> {
  double baseLeft; //图片左上角的x坐标
  double baseTop; //图片左上角的y坐标
  double imageWidth; //图片宽度，缩放后会变化
  double imageScale = 1; //图片缩放比例
  Image imageView;
  final cropKey = GlobalKey<CropState>();

  ImageProvider image;

  UploadImageFileViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    image = FileImage(widget.image);
    _viewModel = UploadImageFileViewModel(widget.upload);
  }

  @override
  Widget build(BuildContext context) {
    EdgeInsets padding = MediaQuery.of(context).padding;
    return Scaffold(
      backgroundColor: Color(0xff17191B),
      body: Stack(
        key: ValueKey(image),
        alignment: AlignmentDirectional.topCenter,
        children: [
          Positioned.fill(
            child: Crop(
              image: image,
              key: cropKey,
              aspectRatio: widget.aspectRatio,
              alwaysShowGrid: true,
            ),
          ),
          Positioned(
            child: TopNavbar(
              title: YLocal.of(context).caijian,
              titleColor: Color(0xffFFFFFF),
              backgroundColor: Colors.transparent,
              backColor: Color(0xffFFFFFF),
            ),
            left: 0,
            top: 0,
            right: 0,
            height: kToolbarHeight + padding.top,
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 40,
                vertical: max(31, padding.bottom),
              ),
              color: Color(0xff242527),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  YvrTextButton(
                    padding: EdgeInsets.symmetric(
                      horizontal: 28,
                      vertical: 12,
                    ),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    color: Colors.transparent,
                    child: Text(
                      YLocal.of(context).quxiao,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 18,
                        letterSpacing: 1,
                        color: Color(0xffe8e8e8),
                      ),
                    ),
                  ),
                  YvrTextButton(
                    padding: EdgeInsets.symmetric(
                      horizontal: 28,
                      vertical: 12,
                    ),
                    onPressed: () {
                      _crop(widget.image);
                    },
                    child: Text(
                      YLocal.of(context).queding,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 18,
                        letterSpacing: 1,
                        color: Color(0xffe8e8e8),
                      ),
                    ),
                  ),
                  // InkWell(
                  //   onTap: () {},
                  // ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    image.evict();
  }

  @override
  void didUpdateWidget(covariant CropBackgroundRoute oldWidget) {
    super.didUpdateWidget(oldWidget);
    image.evict();
  }

  void _crop(File originalFile) {
    final crop = cropKey.currentState;
    final area = crop.area;
    if (area == null) {
      return;
    }
    ImageCrop.requestPermissions().then((isAllow) async {
      File f;
      if (isAllow) {
        f = await ImageCrop.cropImage(
          file: originalFile,
          area: crop.area,
        );
      } else {
        f = originalFile;
      }
      if (f != null) {
        fetchUploadUserImage(f);
      }
    });
  }

  void fetchUploadUserImage(File file) {
    YvrToast.showLoading(message: YLocal.of(context).shangchuanzhongshang);
    _viewModel.startUploadImageFile(file, onSuccess: () {
      Navigator.of(context).pop(1);
    }, onComplete: () {
      YvrToast.dismiss();
    });
  }
}

class ImageBrowserPageRoute<T> extends PageRouteBuilder<T> {
  ImageBrowserPageRoute({@required WidgetBuilder builder})
      : super(
          pageBuilder: (BuildContext context, Animation<double> animation,
              Animation<double> secondaryAnimation) {
            return builder.call(context);
          },
          transitionsBuilder: (BuildContext context,
              Animation<double> animation,
              Animation<double> secondaryAnimation,
              Widget child) {
            return ScaleTransition(
              scale: animation,
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          },
          transitionDuration: const Duration(milliseconds: 150),
          reverseTransitionDuration: const Duration(milliseconds: 150),
        );
}
