import 'package:flutter/cupertino.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../generated/l10n.dart';
import '../../provider/view_state.dart';
import '../../provider/view_state_model.dart';
import '../../provider/view_state_widget.dart';

typedef RefreshListBuilder<T extends ViewStateModel2> = Widget Function(
    BuildContext context, T);

class SimpleRefreshList<T extends ViewStateModel2> extends StatefulWidget {
  final T model;
  final RefreshListBuilder<T> builder;
  final RefreshListBuilder<T> emptyBuilder;
  final RefreshListBuilder<T> loadingBuilder;

  const SimpleRefreshList({
    @required this.model,
    @required this.builder,
    this.emptyBuilder,
    this.loadingBuilder,
  });

  @override
  State<StatefulWidget> createState() => SimpleRefreshListState<T>();
}

class SimpleRefreshListState<T extends ViewStateModel2>
    extends State<SimpleRefreshList<T>> {
  @override
  void initState() {
    super.initState();
    widget.model.addListener(notify);
    if (widget.model.autoInitialize) {
      widget.model.viewState = ViewState.busy;
      widget.model.resetInternal();
    }
  }

  void notify() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    var model = widget.model;
    return RefreshList(
      controller: model.refreshController,
      refreshEnabled: model.refreshEnabled &&
          model.refreshState != ListRefreshState.loadMore,
      loadEnabled: model.loadEnabled &&
          model.refreshState != ListRefreshState.refreshing,
      onReset: model.reset,
      onRefresh: model.refreshPage,
      onLoading: model.loadMorePage,
      state: model.viewState,
      builder: (context) {
        return widget.builder(context, model);
      },
      viewStateError: model.viewStateError,
      emptyBuilder: widget.emptyBuilder == null
          ? null
          : (context) {
              return widget.emptyBuilder.call(context, model);
            },
      loadingBuilder: widget.loadingBuilder == null
          ? null
          : (context) {
              return widget.loadingBuilder.call(context, model);
            },
    );
  }

  @override
  void didUpdateWidget(covariant SimpleRefreshList<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.model != oldWidget.model) {
      oldWidget.model.removeListener(notify);
      widget.model.addListener(notify);
    }
  }

  @override
  void dispose() {
    widget.model.removeListener(notify);
    super.dispose();
  }
}

class RefreshList extends StatelessWidget {
  final RefreshController controller;
  final VoidCallback onRefresh;
  final VoidCallback onLoading;
  final VoidCallback onReset;
  final ViewState state;
  final WidgetBuilder builder;
  final ViewStateError viewStateError;
  final bool refreshEnabled;
  final bool loadEnabled;
  final WidgetBuilder emptyBuilder;
  final WidgetBuilder loadingBuilder;

  RefreshList({
    @required this.controller,
    @required this.onReset,
    @required this.onRefresh,
    @required this.onLoading,
    @required this.state,
    @required this.builder,
    @required this.viewStateError,
    this.refreshEnabled = true,
    this.loadEnabled = true,
    this.emptyBuilder,
    this.loadingBuilder,
  });

  @override
  Widget build(BuildContext context) {
    return SmartRefresher(
      controller: controller,
      header: WaterDropHeader(),
      enablePullDown: refreshEnabled && !busy && !error && !empty,
      footer: CustomFooter(
        builder: (BuildContext context, LoadStatus mode) {
          Widget body;
          if (mode == LoadStatus.idle) {
            body = Text("");
          } else if (mode == LoadStatus.loading) {
            body = CupertinoActivityIndicator();
          } else if (mode == LoadStatus.failed) {
            body = Text(YLocal.of(context).state_load_fail);
          } else if (mode == LoadStatus.canLoading) {
            body = Text(YLocal.of(context).state_load_more);
          } else {
            body = Text("");
          }
          return Center(child: body);
        },
      ),
      onRefresh: onRefresh,
      onLoading: onLoading,
      enablePullUp: loadEnabled,
      child: state == null
          ? SizedBox()
          : busy
              ? loadingBuilder == null
                  ? Center(
                      child: ViewStateBusyWidget(),
                    )
                  : loadingBuilder(context)
              : error
                  ? ViewStateErrorWidget(
                      error: viewStateError, onPressed: onReset)
                  : empty
                      ? (emptyBuilder == null
                          ? ViewStateEmptyWidget(onPressed: onReset)
                          : emptyBuilder.call(context))
                      : builder(context),
    );
  }

  bool get busy => state == ViewState.busy;

  bool get idle => state == ViewState.idle;

  bool get empty => state == ViewState.empty;

  bool get error => state == ViewState.error;
}

class StateLayout extends StatelessWidget {
  final VoidCallback onReset;
  final ViewState state;
  final WidgetBuilder builder;
  final ViewStateError viewStateError;
  final WidgetBuilder emptyBuilder;

  StateLayout({
    @required this.onReset,
    @required this.state,
    @required this.builder,
    @required this.viewStateError,
    this.emptyBuilder,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      child: state == null
          ? SizedBox()
          : busy
              ? Center(
                  child: ViewStateBusyWidget(),
                )
              : error
                  ? ViewStateErrorWidget(
                      error: viewStateError, onPressed: onReset)
                  : empty
                      ? (emptyBuilder == null
                          ? ViewStateEmptyWidget(onPressed: onReset)
                          : emptyBuilder.call(context))
                      : builder(context),
    );
  }

  bool get busy => state == ViewState.busy;

  bool get idle => state == ViewState.idle;

  bool get empty => state == ViewState.empty;

  bool get error => state == ViewState.error;
}
