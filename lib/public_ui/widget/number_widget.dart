import 'package:flutter/material.dart';
import 'package:yvr_assistant/public_ui/widget/rich_text.dart';

import '../../utils/number_utils.dart';

class NumberWidget extends StatelessWidget {
  final int num;
  final IntlTextBuilder unit;
  final String defaultText;
  final TextStyle defaultStyle;
  final TextStyle numStyle;

  const NumberWidget({
    @required this.num,
    @required this.unit,
    this.defaultText,
    this.defaultStyle = const TextStyle(
        color: const Color(0xff767880),
        fontSize: 14,
        height: 1,
        fontWeight: FontWeight.w400),
    this.numStyle = const TextStyle(
        color: const Color(0xff767880),
        fontSize: 14,
        height: 1,
        fontWeight: FontWeight.w500),
  });

  @override
  Widget build(BuildContext context) {
    if (num == 0 && defaultText != null) {
      return Text(
        defaultText,
        style: defaultStyle,
      );
    } else {
      return IntlRichText1(
        intlTextBuilder: unit,
        param: '${NumberUtils.formatNumberOfFollowers(num)} ',
        paramStyle: numStyle,
        defaultStyle: defaultStyle,
      );
    }
  }
}
