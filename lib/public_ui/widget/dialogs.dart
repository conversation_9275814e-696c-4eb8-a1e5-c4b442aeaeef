import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:adaptive_dialog/adaptive_dialog.dart';
import 'package:yvr_assistant/utils/permission_util.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:yvr_assistant/provider/view_state_widget.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/pages/device/tool/file_handle.dart';
import 'package:yvr_assistant/public_ui/widget/period_time_selector.dart';

class Dialogs {
  static const String kCamera = 'camera';
  static const String kPhoto = 'photo';
  static const String kCancel = 'cancel';
  static const String kDelete = 'delete';

  static final List<SheetAction<String>> devActions = [
    SheetAction(
      key: kDelete,
      label: YLocal.current.yichu,
      isDefaultAction: true,
    )
  ];

  static final List<SheetAction<String>> photoActions = [
    SheetAction(
      key: kCamera,
      icon: Icons.camera,
      label: YLocal.current.paizhao,
    ),
    SheetAction(
      key: kPhoto,
      icon: Icons.photo,
      label: YLocal.current.congxiangcezhongxuan,
      isDefaultAction: true,
    )
  ];

  static Future<List<AssetEntity>> showImageSelectorDialog2(
    BuildContext context, {
    List<AssetEntity> selectedAssets,
    int maxAssets = 9,
  }) async {
    ThemeData theme = AssetPicker.themeData(Color(0xff4F7FFE));
    final result = await showModalActionSheet<String>(
      context: context,
      actions: photoActions,
      style: AdaptiveStyle.iOS,
    );
    if (result != kCamera && result != kPhoto) {
      return null;
    }

    if (result == kCamera) {
      if (!await PermissionUtil.checkPhotoPermission(context)) {
        return null;
      }
      if (!await PermissionUtil.checkCameraPermission(context)) {
        return null;
      }

      if (selectedAssets != null && selectedAssets.length >= maxAssets) {
        YvrToast.showToast(YLocal.current.zuiduoyunxushangchua(maxAssets));
        return null;
      }
      var f = await ImagePicker().pickImage(source: ImageSource.camera);
      if (f == null) {
        return null;
      }
      List<AssetEntity> result = List.of(selectedAssets ?? []);
      final AssetEntity imageEntity =
          await PhotoManager.editor.saveImageWithPath(f.path);
      result.add(imageEntity);
      return result;
    } else {
      if (Platform.isAndroid) {
        try {
          final List<AssetEntity> assets = await AssetPicker.pickAssets(context,
              selectedAssets: selectedAssets,
              maxAssets: maxAssets,
              pickerTheme: theme);
          if (assets != null) {
            return assets;
          }
        } catch (e, s) {
          print(s);
          if (!await PermissionUtil.checkAndroidShowRequestRationale(
              context, Permission.storage)) {
            PermissionUtil.showNoticeDialog(context, Permission.storage);
          }
          return null;
        }
        return null;
      } else {
        if (!await PermissionUtil.checkPhotoPermission(context)) {
          return null;
        }
        final List<AssetEntity> assets = await AssetPicker.pickAssets(context,
            selectedAssets: selectedAssets,
            maxAssets: maxAssets,
            pickerTheme: theme);
        return assets;
      }
    }
  }

  static Future<List<XFile>> showImageSelectorDialog(
      BuildContext context) async {
    final result = await showModalActionSheet<String>(
      context: context,
      actions: photoActions,
      style: AdaptiveStyle.iOS,
    );
    if (result != kCamera && result != kPhoto) {
      return null;
    }

    bool isAllow = await requestPhotoPermission(result == kPhoto);
    if (!isAllow) {
      return null;
    }
    if (result == kCamera) {
      var f = await ImagePicker().pickImage(source: ImageSource.camera);
      return f == null ? null : [f];
    } else {
      List<XFile> f = await ImagePicker().pickMultiImage();
      return f;
    }
  }

  static Future<List<PeriodTime>> showPeriodTimeSelectorDialog(
      BuildContext context, List<PeriodTime> selected,
      {int editIndex}) {
    return showModalBottomSheet<List<PeriodTime>>(
        context: context,
        enableDrag: false,
        isScrollControlled: true,
        barrierColor: Color.fromRGBO(0, 0, 0, 0.6),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
          ),
        ),
        builder: (context) {
          return SizedBox(
            height: MediaQuery.of(context).padding.bottom + 414,
            child: PeriodTimeSelector(
              selected: selected,
              onConfirm: (value) {
                Navigator.of(context).pop(value);
              },
              onCancel: () {
                Navigator.of(context).pop();
              },
              editIndex: editIndex,
            ),
          );
        });
  }

  static Future<T> showProgressFutureDialog<T>(
      BuildContext context, Future<T> future) async {
    bool finished = false;
    T result = await showDialog<T>(
        context: context,
        builder: (context) {
          return Center(
            child: FutureBuilder<T>(
              future: Future<T>.sync(() async {
                T result = await future;
                if (!finished) {
                  finished = true;
                  Navigator.of(context).pop(result);
                }
                return result;
              }),
              builder: (BuildContext context, AsyncSnapshot<T> snapshot) {
                return Center(
                  child: ViewStateBusyWidget(),
                );
              },
            ),
          );
        });
    finished = true;
    return result;
  }
}
