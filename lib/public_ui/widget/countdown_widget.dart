import 'dart:async';
import 'package:flutter/material.dart';

import '../../utils/date_time_utils.dart';

mixin CountdownController on ChangeNotifier {
  DateTime countdownEndTime;
  Timer _timer;
  String countdownTimeText = "";

  void resetCountdown(int value) {
    DateTime now = DateTime.now();
    DateTime endTime = value == null ? null : now.add(Duration(seconds: value));
    if (endTime != null && endTime.isAfter(now)) {
      if (updateTime(now, endTime)) {
        this.countdownEndTime = endTime;
        if (_timer == null) {
          _makeTimer();
        }
      } else {
        this.countdownEndTime = null;
        if (_timer != null) {
          _timer.cancel();
          _timer = null;
        }
      }
    } else {
      if (_timer != null) {
        _timer.cancel();
        _timer = null;
      }
      this.countdownEndTime = null;
      this.countdownTimeText = "";
    }

    notifyListeners();
  }

  bool isFinished() {
    return countdownEndTime == null;
  }

  void _makeTimer() {
    _timer = Timer.periodic(Duration(milliseconds: 1000), (timer) {
      DateTime now = DateTime.now();
      if (!updateTime(now, countdownEndTime)) {
        this.countdownEndTime = null;
        if (_timer != null) {
          _timer.cancel();
          _timer = null;
        }
        onCountdownFinished();
      }
      notifyListeners();
    });
  }

  void onCountdownFinished() {}

  bool updateTime(DateTime now, DateTime end) {
    final String text = DateTimeUtils.parseCountdown(end, now);
    if (text == null) {
      this.countdownTimeText = "";
      return false;
    } else {
      this.countdownTimeText = text;
      return true;
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    _timer = null;
    super.dispose();
  }
}

class CountdownWidget extends StatelessWidget {
  final String countdownText;

  const CountdownWidget({
    Key key,
    @required this.countdownText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (countdownText.isEmpty) {
      return SizedBox();
    } else {
      return Container(
        padding: EdgeInsets.only(left: 4),
        child: Text(
          countdownText,
          style: TextStyle(
            fontWeight: FontWeight.w400,
            fontSize: 12,
            height: 1,
            color: Color(0xffCCCCCD),
          ),
        ),
      );
    }
  }
}
