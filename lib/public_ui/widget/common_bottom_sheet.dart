import 'package:flutter/cupertino.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';

import '../../generated/l10n.dart';

typedef ActionCallback = Function(int actionIndex);

///公共底部弹窗
class CommonBottomSheet {
  static show(BuildContext context, List<String> actionNames,
      ActionCallback actionCallback) {
    showCupertinoModalPopup(
      context: context,
      builder: (context) {
        List<Widget> widgets = [];
        for (int i = 0; i < actionNames.length; i++) {
          widgets.add(CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
              if (actionCallback != null) actionCallback(i);
            },
            child: Text('${actionNames[i] ?? ''}',
                style: TextStyle(
                    color: Color(0xFFF02020),
                    fontSize: 16,
                    fontWeight: FontWeight.w400)),
          ));
        }
        return CupertinoTheme(
            data: CupertinoThemeData(
              primaryColor: AppColors.colorFFFFFF,
            ),
            child: CupertinoActionSheet(
              // title: Text(''),
              // message: Text(''),
              actions: widgets,
              cancelButton: CupertinoActionSheetAction(
                //取消按钮
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(YLocal.of(context).quxiao,
                    style: AppStyle.style_standard_bold_16pt()),
              ),
            ));
      },
    );
  }
}
