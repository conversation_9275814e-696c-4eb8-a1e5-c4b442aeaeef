import "package:flutter/cupertino.dart";
import 'package:flutter/services.dart';

import '../../styles/app_color.dart';

class FeedbackInput2 extends StatefulWidget {
  final String hint;
  final double height;
  final int maxLength;
  final Color fillColor;
  final Function(String) onChanged;
  final FocusNode focusNode;
  final TextEditingController controller;
  final EdgeInsetsGeometry containerPadding;
  final EdgeInsetsGeometry textFieldPadding;

  const FeedbackInput2({
    Key key,
    this.hint,
    this.height,
    this.maxLength,
    this.fillColor,
    this.onChanged,
    this.controller,
    this.focusNode,
    this.containerPadding = const EdgeInsets.fromLTRB(10, 10, 10, 30),
    this.textFieldPadding = const EdgeInsets.all(6.0),
  }) : super(key: key);

  @override
  State createState() {
    return _FeedbackInputState();
  }
}

class _FeedbackInputState extends State<FeedbackInput2>
    with SingleTickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: _textInput(widget.hint),
    );
  }

  String result = "";

  _textInput(String hint) {
    // 防止获取已有文字时 字数为0
    if (widget.controller != null) {
      result = widget.controller.text;
    }

    return Stack(
      alignment: AlignmentDirectional.bottomEnd,
      children: [
        Container(
          padding: widget.containerPadding,
          height: widget.height,
          decoration: widget.fillColor == null
              ? BoxDecoration()
              : BoxDecoration(
                  color: widget.fillColor ?? Color(0xFFFFFFFF),
                  borderRadius: BorderRadius.all(
                    Radius.circular(4.0),
                  ),
                  // border: Border.all(color: Color(0xFF393A3C)),
                ),
          alignment: AlignmentDirectional.topStart,
          child: CupertinoTextField(
            maxLengthEnforcement: MaxLengthEnforcement.enforced,
            controller: widget.controller,
            focusNode: widget.focusNode,
            padding: widget.textFieldPadding,
            placeholder: hint,
            placeholderStyle: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 14,
              letterSpacing: 1,
              color: AppColors.textWeak,
            ),
            style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 14,
              letterSpacing: 1,
              color: AppColors.textTitle,
            ),
            strutStyle:
                StrutStyle(forceStrutHeight: true, height: 0.8, leading: 0.8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(2)),
            ),
            maxLength: widget.maxLength,
            maxLines: 20,
            textInputAction: TextInputAction.unspecified,
            onChanged: (str) {
              result = str;
              widget.onChanged(result);
              setState(() {});
            },
          ),
        ),
        Container(
          margin: EdgeInsets.only(bottom: 10, right: 10),
          child: Text(
            result.length.toString() + " / " + widget.maxLength.toString(),
            style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 14,
              height: 1,
              letterSpacing: 1,
              color: AppColors.textWeak,
            ),
          ),
        )
      ],
    );
  }
}
