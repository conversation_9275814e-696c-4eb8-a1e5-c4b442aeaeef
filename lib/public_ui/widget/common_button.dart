// ignore_for_file: must_be_immutable

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/public_ui/widget/button_progress_indicator.dart';

///公共按钮
class CommonButton extends StatelessWidget {
  bool isGradient; //是否显示渐变
  bool isShowBorder; //是否显示边框
  String text; //按钮文本
  Function onTap; //按钮事件
  List<Color> colors; //渐变颜色集合
  Color textColor; //按钮文字颜色
  Color secondaryBg; //按钮背景颜色
  double borderWidth; //边框宽度
  Color borderColor; //边框颜色
  double width; //按钮宽度
  double hPadding; //水平方向内边距
  double height; //按钮高度
  double radius; //按钮圆角
  double fontSize; //按钮文字大小
  bool isShowLoadding; //是否显示加载框
  FontWeight fontWeight; //按钮字体是否加粗，如果是传textWidget则无需管它
  Widget textWidget; //按钮内容widget

  CommonButton(
      {this.text = '',
      this.onTap,
      this.colors,
      this.isGradient = true,
      this.textColor = AppColors.colorFFFFFF,
      this.borderWidth = 0.5,
      this.isShowBorder = false,
      this.borderColor = const Color(0xFFD9D9D9),
      this.width = double.infinity,
      this.secondaryBg = const Color(0xFF4F7FFE),
      this.height = 48,
      this.radius = 48,
      this.fontSize = 18,
      this.fontWeight = FontWeight.w600,
      this.textWidget,
      this.hPadding = 0,
      this.isShowLoadding = false}) {
    colors ??= <Color>[Color(0xFF66A6FF), Color(0xFF6D78F2)];
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          if (onTap != null) {
            onTap();
          }
        },
        child: Container(
            width: hPadding > 0 ? null : width,
            height: height,
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(horizontal: hPadding),
            decoration: BoxDecoration(
              color: secondaryBg,
              borderRadius: BorderRadius.all(Radius.circular(radius)),
              border: isShowBorder
                  ? Border.all(width: borderWidth, color: borderColor)
                  : null,
              gradient: isGradient
                  ? LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.topRight,
                      colors: colors)
                  : null,
            ),
            child: isShowLoadding
                ? ButtonProgressIndicator()
                : (textWidget ??
                    Text("$text",
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize: fontSize,
                            color: textColor,
                            fontWeight: fontWeight)))));
  }
}
