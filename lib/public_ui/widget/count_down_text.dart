import 'package:flutter/cupertino.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import '../../provider/provider_widget.dart';
import '../../view_model/count_down_vmodel.dart';

///活动倒计时文本，含天/时/分/秒倒计时
// ignore: must_be_immutable
class CountDownText extends StatefulWidget {
  int countSecond; //总秒数或者总毫秒数
  TextStyle textStyle;
  CountDownText({Key key, this.countSecond, this.textStyle}) : super(key: key) {
    if (textStyle == null) {
      textStyle = TextStyle(
        fontSize: 12,
        color: AppColors.colorCCCCCD,
      );
    }
  }

  @override
  State<StatefulWidget> createState() {
    return _CountDownState();
  }
}

class _CountDownState extends State<CountDownText> {
  @override
  Widget build(BuildContext context) {
    return ProviderWidget<CountDownVModel>(
        model: CountDownVModel(widget.countSecond),
        onModelReady: (model) {
          model.start();
        },
        builder: (context, model, child) {
          return Text(
            '${model.countDownEntity.text}',
            style: widget.textStyle,
          );
        });
  }
}
