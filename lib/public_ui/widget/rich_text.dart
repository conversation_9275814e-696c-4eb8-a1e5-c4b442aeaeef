import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:easy_rich_text/easy_rich_text.dart';

typedef IntlTextBuilder = String Function(Object);

class IntlRichText1 extends StatelessWidget {
  final IntlTextBuilder intlTextBuilder;
  final Object param;
  final TextStyle defaultStyle;
  final TextStyle paramStyle;
  final GestureRecognizer paramRecognizer;

  const IntlRichText1({
    Key key,
    @required this.intlTextBuilder,
    @required this.param,
    this.defaultStyle,
    this.paramStyle,
    this.paramRecognizer,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String arg = param.toString();
    String text = intlTextBuilder != null ? intlTextBuilder(arg) : arg;

    return EasyRichText(
      text,
      defaultStyle: this.defaultStyle,
      patternList: [
        EasyRichTextPattern(
          targetString: arg,
          style: paramStyle,
          matchWordBoundaries: false,
          recognizer: paramRecognizer,
        ),
      ],
    );
  }
}

typedef IntlTextBuilder2 = String Function(Object, Object);

class IntlRichText2 extends StatelessWidget {
  final IntlTextBuilder2 intlTextBuilder;
  final Object param0;
  final Object param1;
  final TextStyle defaultStyle;
  final TextStyle paramStyle0;
  final TextStyle paramStyle1;
  final GestureRecognizer paramRecognizer0;
  final GestureRecognizer paramRecognizer1;

  const IntlRichText2({
    Key key,
    @required this.intlTextBuilder,
    @required this.param0,
    @required this.param1,
    this.defaultStyle,
    this.paramStyle0,
    this.paramStyle1,
    this.paramRecognizer0,
    this.paramRecognizer1,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String arg0 = param0.toString();
    String arg1 = param1.toString();
    String text =
        intlTextBuilder != null ? intlTextBuilder(arg0, arg1) : arg0 + arg1;
    return EasyRichText(
      text,
      defaultStyle: this.defaultStyle,
      patternList: [
        EasyRichTextPattern(
            targetString: arg0,
            style: paramStyle0,
            matchWordBoundaries: false,
            recognizer: paramRecognizer0),
        EasyRichTextPattern(
            targetString: arg1,
            style: paramStyle1,
            matchWordBoundaries: false,
            recognizer: paramRecognizer1),
      ],
    );
  }
}
