import 'package:flutter/material.dart';

import 'skeleton.dart';

class SubjSkeletonItem extends StatelessWidget {
  final int index;

  SubjSkeletonItem({this.index: 0});

  @override
  Widget build(BuildContext context) {
    var width = MediaQuery.of(context).size.width;
    bool isDark = Theme.of(context).brightness == Brightness.dark;

    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Container(
            padding: EdgeInsets.all(5),
            margin: EdgeInsets.only(bottom: 10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: Container(
                    height: width * 0.5 * 9 / 16,
                    width: width * 0.5,
                    decoration: SkeletonDecoration(isDark: isDark),
                  ),
                ),
                Si<PERSON><PERSON>ox(
                  height: 10,
                ),
                Container(
                  height: 12,
                  width: width * 0.3,
                  decoration: SkeletonDecoration(isDark: isDark),
                ),
              ],
            ),
          ),
        ),
        Expanded(
          flex: 1,
          child: Container(
            padding: EdgeInsets.all(5),
            margin: EdgeInsets.only(bottom: 10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: Container(
                    height: width * 0.5 * 9 / 16,
                    width: width * 0.5,
                    decoration: SkeletonDecoration(isDark: isDark),
                  ),
                ),
                SizedBox(
                  height: 10,
                ),
                Container(
                  height: 12,
                  width: width * 0.3,
                  decoration: SkeletonDecoration(isDark: isDark),
                ),
              ],
            ),
          ),
        )
      ],
    );
  }
}
