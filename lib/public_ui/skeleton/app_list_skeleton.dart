import 'package:flutter/material.dart';

import 'skeleton.dart';

class AppSkeletonItem extends StatelessWidget {
  final int index;

  AppSkeletonItem({this.index: 0});

  @override
  Widget build(BuildContext context) {
    var width = MediaQuery.of(context).size.width;
    bool isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
        padding: EdgeInsets.all(5),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    flex: 1,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(4),
                      child: Container(
                        height: width * 0.5 * 9 / 16,
                        width: width * 0.5,
                        decoration: SkeletonDecoration(isDark: isDark),
                      ),
                    )),
                SizedBox(width: 12),
                Expanded(
                    flex: 1,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: 15,
                          width: width * 0.4,
                          decoration: SkeletonDecoration(isDark: isDark),
                        ),
                        SizedBox(
                          height: 12,
                        ),
                        Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(5),
                              margin: EdgeInsets.only(right: 6),
                              height: 15,
                              width: width * 0.15,
                              decoration: SkeletonDecoration(isDark: isDark),
                            ),
                            Container(
                              padding: EdgeInsets.all(5),
                              height: 15,
                              width: width * 0.15,
                              decoration: SkeletonDecoration(isDark: isDark),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 12,
                        ),
                        Row(
                          children: [
                            Container(
                              height: 12,
                              width: width * 0.12,
                              decoration: SkeletonDecoration(isDark: isDark),
                            ),
                            SizedBox(
                              width: 6,
                            ),
                            Container(
                              height: 12,
                              width: width * 0.20,
                              decoration: SkeletonDecoration(isDark: isDark),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 12,
                        ),
                        Container(
                          height: 12,
                          width: width * 0.2,
                          decoration: SkeletonDecoration(isDark: isDark),
                        ),
                      ],
                    ))
              ],
            ),
          ],
        ));
  }
}
