// Router消除报错  hide Router;
import 'dart:async';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'generated/l10n.dart';
import 'manager/router_manger.dart';
import 'view_model/locale_model.dart';
import 'view_model/theme_model.dart';
import 'manager/provider_manager.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/utils/agreement.dart';
import 'package:yvr_assistant/utils/crashlytics.dart';
import 'package:yvr_assistant/utils/init_config.dart';
import 'package:yvr_assistant/manager/environment.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';

_initEnvironment() {
  /// TODO:  1/2 配置接口环境上线前 - PROD 🏳️‍🌈
  String environment = String.fromEnvironment(
    'ENVIRONMENT',
    // defaultValue: Environment.PROD,
    // defaultValue: Environment.UAT,
    // defaultValue: Environment.STAGING,
    // defaultValue: Environment.DEV,
    defaultValue:
        DBUtil.instance.envBox.get(Global.kEnvMode) ?? Environment.PROD,
  );
  Environment().initConfig(environment);
}

main() {
  runZonedGuarded<Future<void>>(() async {
    CustomWidgetsBinding();
    WidgetsFlutterBinding.ensureInitialized();

    FlutterError.onError = Crashlytics.instance.recordFlutterError;

    Provider.debugCheckInvalidValueType = null;
    await StorageManager.init();
    await DBUtil.install();

    YvrToast.setToastStyle();
    _initEnvironment();
    prepareHttp();

    if (AgreementUtils.hasAgreed()) {
      InitConfig().projectInit();
    }
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]).then((_) => runApp(App()));
    // Android状态栏透明 splash为白色,所以调整状态栏文字为黑色
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.light));
  }, (error, stack) => Crashlytics.instance.recordError(error, stack));
}

// 监听导航进入、返回 会引起某页面使用后 其他导航顶部按钮事件无响应
final RouteObserver<PageRoute> routeObserver = RouteObserver<PageRoute>();

class App extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
        providers: providers,
        child: Consumer2<ThemeModel, LocaleModel>(
            builder: (context, themeModel, localeModel, child) {
          return RefreshConfiguration(
            // 刷新加载配置
            hideFooterWhenNotFull: false,
            child: MaterialApp(
              title: 'Play For Dream',
              navigatorKey: Global.navigatorKey,
              navigatorObservers: [routeObserver],
              debugShowCheckedModeBanner: false,
              theme: themeModel.themeData(platformDarkMode: false),
              darkTheme: themeModel.themeData(platformDarkMode: false),
              locale: localeModel.locale,
              localizationsDelegates: const [
                YLocal.delegate,
                RefreshLocalizations.delegate, //下拉刷新
                GlobalCupertinoLocalizations.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate
              ],
              supportedLocales: YLocal.delegate.supportedLocales,
              localeListResolutionCallback: (locales, supportedLocales) {
                return const Locale('en', 'US');
              },
              onGenerateRoute: onGenerateRoute,
              initialRoute: '/',
              builder: (context, child) {
                return MediaQuery(
                  //设置文字大小不随系统设置改变
                  data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
                  child: GestureDetector(
                    onTap: () {
                      hideKeyboard(context);
                    },
                    child: FlutterEasyLoading(child: child),
                  ),
                );
              },
            ),
          );
        }));
  }

  // 点击空白处隐藏键盘
  void hideKeyboard(BuildContext context) {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus.unfocus();
    }
  }
}

class CustomWidgetsBinding extends WidgetsFlutterBinding {
  @override
  ImageCache createImageCache() {
    ImageCache imageCache = super.createImageCache();
    imageCache.maximumSizeBytes = 300 << 20;
    return imageCache;
  }
}
