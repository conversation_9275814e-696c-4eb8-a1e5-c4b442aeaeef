import 'dart:ui';

///颜色相关工具类
class AppColors {
  /*
  具体颜色值变量，color+色值大写（6位或者8位）
  */
  static const colorFF0000 = Color(0xFFFF0000);
  static const colorE5E5E5 = Color(0xFFE5E5E5);
  static const colorF5F5F5 = Color(0xFFF5F5F5);
  static const colorF9F9F9 = Color(0xFFF9F9F9);
  static const colorC9C9C9 = Color(0xFFC9C9C9);
  static const colorCCCCCC = Color(0xFFCCCCCC);
  static const color17191B = Color(0xFF17191B);
  static const colorE8E8E8 = Color(0xFFE8E8E8);
  static const color5EA4E6 = Color(0xFF5EA4E6);
  static const colorCCCCCD = Color(0xFFCCCCCD);
  static const color767880 = Color(0xFF767880);
  static const colorB0B2B8 = Color(0xFFB0B2B8);
  static const color8E94A6 = Color(0xFF8E94A6);
  static const color354E8E = Color(0xFF354E8E);
  static const color7F879E = Color(0xFF7F879E);
  static const colorF5982B = Color(0xFFF5982B);
  static const color545761 = Color(0xFF545761);
  static const color808080 = Color(0xFF808080);
  static const colorAB6BF1 = Color(0xFFAB6BF1);
  static const colorFBBB1C = Color(0xFFFBBB1C);
  static const color6C80F4 = Color(0xFF6C80F4);
  static const color52555C = Color(0xFF52555C);

  static const color000000 = Color(0xFF000000);
  static const colorFFFFFF = Color(0xFFFFFFFF);
  static const colorE9610F = Color(0xFFE9610F);
  static const color2C9A0E = Color(0xFF2C9A0E);
  static const colorF1BC00 = Color(0xFFF1BC00);
  static const colorF6F6F6 = Color(0xFFF6F6F6);
  static const colorCDE1F7 = Color(0xFFCDE1F7);

  static const color000000_5 = Color(0x0D000000);
  static const color5EA4E6_80 = Color(0xCC5EA4E6);
  static const color5EA4E6_10 = Color(0x1A5EA4E6);

  //-----------------------------以下为新版助手主调色-----------------------------------

  //业务型颜色值，用于主题色更换
  static const standard = Color(0xFF4F7FFE); //标准色
  static const standard_10 = Color(0x1A4F7FFE); //标准色，10%的不透明度
  static const buttonStart = Color(0xFF66A6FF); //按钮底色:#66A6FF～#6D78F2
  static const buttonEnd = Color(0xFF6D78F2); //按钮底色:#66A6FF～#6D78F2
  static const buttonDisable = Color(0XFFEEEEEE); //按钮不可用
  static const error = Color(0xFFFA5051); //错误色
  static const warning = Color(0xFFF14E4E); //警示色
  static const success = Color(0xFF55CE13); //成功色
  static const moderate = Color(0xFFF8AE1F); // 中性色
  static const divider = Color(0xFFEEEEEE); //基于有背景颜色的分割线，边框颜色

  //背景色
  static const background = Color(0xFFFFFFFF); //页面背景:一级页面
  static const secondaryBg = Color(0xFFF5F7FA); //页面背景:二级页面
  static const backgroundItem = Color(0xFFF0F2F5); //控件背景:用于底部为灰色控件部分

  //文字色
  static const textTitle = Color(0xFF2C2E33); //主标题
  static const textSub = Color(0xFF6E7380); //副标题、正文
  static const textWeak = Color(0xFFAFB6CC); //弱文字

  //蒙版
  static const transparent = Color(0x00000000);
  static const color000000_40 = Color(0x66000000);
  static const color000000_60 = Color(0x99000000); //页面蒙版:同样适用于浮窗、蒙层,60%的不透明度
}

extension HexColor on Color {
  /// String is in the format "aabbcc" or "ffaabbcc" with an optional leading "#".
  static Color fromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString == null ||
        hexString == 'null' ||
        hexString.contains('linear')) {
      return AppColors.colorFFFFFF;
    }
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  /// Prefixes a hash sign if [leadingHashSign] is set to `true` (default is `true`).
  String toHex({bool leadingHashSign = true}) => '${leadingHashSign ? '#' : ''}'
      '${alpha.toRadixString(16).padLeft(2, '0')}'
      '${red.toRadixString(16).padLeft(2, '0')}'
      '${green.toRadixString(16).padLeft(2, '0')}'
      '${blue.toRadixString(16).padLeft(2, '0')}';
}

  /* 
  不透明度16进制值
    100%   FF
    95%	   F2
    90%	   E6
    85%	   D9
    80%	   CC
    75%	   BF
    70%	   B3
    65%	   A6
    60%	   99
    55%	   8C
    50%	   80
    45%	   73
    40%	   66
    35%	   59
    30%	   4D
    25%	   40
    20%	   33
    15%	   26
    10%	   1A
    5%	   0D
    0%	   00
  */
