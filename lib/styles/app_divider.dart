import 'package:flutter/material.dart';

class AppDivider {
  ///分割线常规样式
  static const Divider commonDivider = Divider(
    height: 0.5,
    thickness: 0.5,
    color: Color(0xfff6f6f6),
  );

  static Divider commonDividerWithIndent(double indent) {
    return Divider(
      height: 0.5,
      thickness: 0.5,
      color: Color(0xfff6f6f6),
      indent: indent,
      endIndent: indent,
    );
  }

  ///基于有背景颜色的分割
  static const Divider backgroundDivider = Divider(
    height: 0.5,
    thickness: 0.5,
    color: Color(0xffeeeeee),
  );

  static Divider backgroundDividerWithIndent(double indent) {
    return Divider(
      height: 0.5,
      thickness: 0.5,
      color: Color(0xffeeeeee),
      indent: indent,
      endIndent: indent,
    );
  }
}
