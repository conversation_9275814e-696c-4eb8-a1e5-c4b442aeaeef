// ignore_for_file: non_constant_identifier_names

import 'app_color.dart';
import 'package:flutter/cupertino.dart';

/// 公共字体样式
class AppStyle {
  static TextStyle style_ffffff_12pt() {
    return const TextStyle(color: AppColors.colorFFFFFF, fontSize: 12);
  }

  static TextStyle style_ffffff_13pt() {
    return const TextStyle(color: AppColors.colorFFFFFF, fontSize: 13);
  }

  static TextStyle style_ffffff_w400_18pt() {
    return const TextStyle(
        color: AppColors.colorFFFFFF,
        fontSize: 18,
        fontWeight: FontWeight.w400);
  }

  static TextStyle style_ffffff_w700_18pt() {
    return const TextStyle(
        color: AppColors.colorFFFFFF,
        fontSize: 18,
        fontWeight: FontWeight.w700);
  }

  static TextStyle style_ffffff_w600_20pt() {
    return const TextStyle(
        color: AppColors.colorFFFFFF,
        fontSize: 20,
        fontWeight: FontWeight.w600);
  }

  static TextStyle style_ffffff_w700_20pt() {
    return const TextStyle(
        color: AppColors.colorFFFFFF,
        fontSize: 20,
        fontWeight: FontWeight.w700);
  }

  static TextStyle style_ffffff_12pt_line() {
    return const TextStyle(
        decoration: TextDecoration.lineThrough,
        color: AppColors.colorFFFFFF,
        fontSize: 12);
  }

  static TextStyle style_ffffff_8pt() {
    return const TextStyle(color: AppColors.colorFFFFFF, fontSize: 8);
  }

  static TextStyle style_ffffff_10pt() {
    return const TextStyle(color: AppColors.colorFFFFFF, fontSize: 10);
  }

  static TextStyle style_ffffff_9pt() {
    return const TextStyle(color: AppColors.colorFFFFFF, fontSize: 9);
  }

  static TextStyle style_ffffff_bold_9pt() {
    return const TextStyle(
        color: AppColors.colorFFFFFF, fontSize: 9, fontWeight: FontWeight.bold);
  }

  static TextStyle style_ffffff_bold_10pt() {
    return const TextStyle(
        color: AppColors.colorFFFFFF,
        fontSize: 10,
        fontWeight: FontWeight.bold);
  }

  static TextStyle style_ffffff_bold_12pt() {
    return const TextStyle(
        color: AppColors.colorFFFFFF,
        fontSize: 12,
        fontWeight: FontWeight.bold);
  }

  static TextStyle style_ffffff_w500_14pt() {
    return const TextStyle(
        color: AppColors.colorFFFFFF,
        fontSize: 14,
        fontWeight: FontWeight.w500);
  }

  static TextStyle style_ffffff_bold_14pt() {
    return const TextStyle(
        color: AppColors.colorFFFFFF,
        fontSize: 14,
        fontWeight: FontWeight.bold);
  }

  static TextStyle style_ffffff_bold_28pt() {
    return const TextStyle(
        color: AppColors.colorFFFFFF,
        fontSize: 28,
        fontWeight: FontWeight.bold);
  }

  static TextStyle style_ffffff_w600_32pt() {
    return const TextStyle(
        color: AppColors.colorFFFFFF,
        fontSize: 32,
        fontWeight: FontWeight.w600);
  }

  static TextStyle style_ffffff_bold_22pt() {
    return const TextStyle(
        color: AppColors.colorFFFFFF,
        fontSize: 22,
        fontWeight: FontWeight.bold);
  }

  static TextStyle style_ffffff_28pt() {
    return const TextStyle(color: AppColors.colorFFFFFF, fontSize: 28);
  }

  static TextStyle style_ffffff_14pt() {
    return const TextStyle(color: AppColors.colorFFFFFF, fontSize: 14);
  }

  static TextStyle style_ffffff_w600_10pt() {
    return const TextStyle(
        color: AppColors.colorFFFFFF,
        fontSize: 10,
        fontWeight: FontWeight.w600);
  }

  static TextStyle style_ffffff_w600_15pt() {
    return const TextStyle(
        color: AppColors.colorFFFFFF,
        fontSize: 15,
        fontWeight: FontWeight.w600);
  }

  static TextStyle style_ffffff_16pt() {
    return const TextStyle(color: AppColors.colorFFFFFF, fontSize: 16);
  }

  static TextStyle style_ffffff_bold_16pt() {
    return const TextStyle(
        color: AppColors.colorFFFFFF,
        fontSize: 16,
        fontWeight: FontWeight.bold);
  }

  static TextStyle style_ffffff_bold_18pt() {
    return const TextStyle(
        color: AppColors.colorFFFFFF,
        fontSize: 18,
        fontWeight: FontWeight.bold);
  }

  static TextStyle style_ffffff_bold_17pt() {
    return const TextStyle(
        color: AppColors.colorFFFFFF,
        fontSize: 17,
        fontWeight: FontWeight.bold);
  }

  static TextStyle style_52555C_w400_14pt() {
    return const TextStyle(
        color: AppColors.color52555C,
        fontSize: 14,
        fontWeight: FontWeight.w400);
  }

  static TextStyle style_e8e8e8_12pt() {
    return const TextStyle(color: AppColors.colorE8E8E8, fontSize: 12);
  }

  static TextStyle style_standard_bold_14pt() {
    return const TextStyle(
        color: AppColors.standard, fontSize: 14, fontWeight: FontWeight.bold);
  }

  static TextStyle style_standard_w600_14pt() {
    return const TextStyle(
        color: AppColors.standard, fontSize: 14, fontWeight: FontWeight.w600);
  }

  static TextStyle style_standard_w400_12pt() {
    return const TextStyle(
      color: AppColors.standard,
      fontSize: 12,
    );
  }

  static TextStyle style_standard_w700_12pt() {
    return const TextStyle(
        color: AppColors.standard,
        fontSize: 12,
        fontFamily: 'DIN Alternate',
        fontWeight: FontWeight.w700);
  }

  static TextStyle style_standard_w400_14pt() {
    return const TextStyle(
      color: AppColors.standard,
      fontSize: 14,
    );
  }

  static TextStyle style_standard_w700_40pt() {
    return const TextStyle(
        color: AppColors.standard, fontSize: 40, fontWeight: FontWeight.w700);
  }

  static TextStyle style_standard_bold_16pt() {
    return const TextStyle(
        color: AppColors.standard, fontSize: 16, fontWeight: FontWeight.bold);
  }

  static TextStyle style_standard_bold_18pt() {
    return const TextStyle(
        color: AppColors.standard, fontSize: 18, fontWeight: FontWeight.bold);
  }

  static TextStyle style_standard_bold_32pt() {
    return const TextStyle(
        color: AppColors.standard, fontSize: 32, fontWeight: FontWeight.bold);
  }

  static TextStyle style_standard_w600_10pt() {
    return const TextStyle(
        color: AppColors.standard, fontSize: 10, fontWeight: FontWeight.w600);
  }

  static TextStyle style_standard_w600_19pt() {
    return const TextStyle(
        color: AppColors.standard, fontSize: 19, fontWeight: FontWeight.w600);
  }

  static TextStyle style_standard_14pt() {
    return const TextStyle(color: AppColors.standard, fontSize: 14);
  }

  static TextStyle style_standard_10pt() {
    return const TextStyle(color: AppColors.standard, fontSize: 10);
  }

  static TextStyle style_standard_13pt() {
    return const TextStyle(color: AppColors.standard, fontSize: 13);
  }

  static TextStyle style_standard_16pt() {
    return const TextStyle(color: AppColors.standard, fontSize: 16);
  }

  static TextStyle style_standard_7pt() {
    return const TextStyle(color: AppColors.standard, fontSize: 7);
  }

  static TextStyle style_7F879E_w700_14pt() {
    return const TextStyle(
        color: AppColors.color7F879E,
        fontWeight: FontWeight.w700,
        fontFamily: 'DIN Alternate',
        fontSize: 14);
  }

  static TextStyle style_7F879E_14pt() {
    return const TextStyle(
      color: AppColors.color7F879E,
      fontSize: 14,
      fontWeight: FontWeight.w400,
    );
  }

  static TextStyle style_7F879E_12pt() {
    return const TextStyle(
      color: AppColors.color7F879E,
      fontSize: 12,
    );
  }

  static TextStyle style_767880_10pt_line() {
    return const TextStyle(
        decoration: TextDecoration.lineThrough,
        color: AppColors.color767880,
        fontSize: 10);
  }

  static TextStyle style_767880_10pt() {
    return const TextStyle(color: AppColors.color767880, fontSize: 10);
  }

  static TextStyle style_767880_12pt() {
    return const TextStyle(color: AppColors.color767880, fontSize: 12);
  }

  static TextStyle style_767880_14pt() {
    return const TextStyle(color: AppColors.color767880, fontSize: 14);
  }

  static TextStyle style_767880_12pt_line() {
    return const TextStyle(
        decoration: TextDecoration.lineThrough,
        color: AppColors.color767880,
        fontSize: 12);
  }

  static TextStyle style_B0B2B8_10pt() {
    return const TextStyle(color: AppColors.colorB0B2B8, fontSize: 10);
  }

  static TextStyle style_B0B2B8_12pt() {
    return const TextStyle(color: AppColors.colorB0B2B8, fontSize: 12);
  }

  static TextStyle style_B0B2B8_14pt() {
    return const TextStyle(color: AppColors.colorB0B2B8, fontSize: 14);
  }

  static TextStyle style_B0B2B8_16pt() {
    return const TextStyle(color: AppColors.colorB0B2B8, fontSize: 16);
  }

  //----------------------------以下为新版助手样式----------------------------

  static TextStyle style_textTitle_w600_12pt() {
    return const TextStyle(
        color: AppColors.textTitle, fontSize: 12, fontWeight: FontWeight.bold);
  }

  static TextStyle style_textTitle_w400_15pt() {
    return const TextStyle(
      color: AppColors.textTitle,
      fontSize: 15,
    );
  }

  static TextStyle style_textTitle_w600_15pt() {
    return const TextStyle(
        color: AppColors.textTitle, fontSize: 15, fontWeight: FontWeight.bold);
  }

  static TextStyle style_textTitle_w700_15pt() {
    return const TextStyle(
        color: AppColors.textTitle,
        fontSize: 15,
        fontFamily: 'DIN Alternate',
        fontWeight: FontWeight.w700);
  }

  static TextStyle style_textTitle_w600_18pt() {
    return const TextStyle(
        color: AppColors.textTitle, fontSize: 18, fontWeight: FontWeight.bold);
  }

  static TextStyle style_textTitle_w700_18pt() {
    return const TextStyle(
        color: AppColors.textTitle, fontSize: 18, fontWeight: FontWeight.w700);
  }

  static TextStyle style_textTitle_w500_18pt() {
    return const TextStyle(
        color: AppColors.textTitle, fontSize: 18, fontWeight: FontWeight.bold);
  }

  static TextStyle style_textTitle_w500_27pt() {
    return const TextStyle(
        color: AppColors.textTitle, fontSize: 27, fontWeight: FontWeight.bold);
  }

  static TextStyle style_textSub_w400_16pt() {
    return const TextStyle(
        color: AppColors.textSub, fontSize: 16, fontWeight: FontWeight.w400);
  }

  static TextStyle style_textSub_w500_16pt() {
    return const TextStyle(
        color: AppColors.textSub, fontSize: 16, fontWeight: FontWeight.w500);
  }

  static TextStyle style_textSub_w400_7pt() {
    return const TextStyle(
      color: AppColors.textSub,
      fontSize: 7,
    );
  }

  static TextStyle style_textSub_w400_16pt_line() {
    return const TextStyle(
        color: AppColors.textSub,
        fontSize: 16,
        decoration: TextDecoration.lineThrough);
  }

  static TextStyle style_textSub_w600_16pt() {
    return const TextStyle(
        color: AppColors.textSub, fontSize: 16, fontWeight: FontWeight.bold);
  }

  static TextStyle style_textSub_w600_15pt() {
    return const TextStyle(
        color: AppColors.textSub, fontSize: 15, fontWeight: FontWeight.bold);
  }

  static TextStyle style_textSub_w400_16pt_15height() {
    return const TextStyle(color: AppColors.textSub, fontSize: 16, height: 1.5);
  }

  static TextStyle style_E9610F_w500_16pt() {
    return const TextStyle(
      color: AppColors.colorE9610F,
      fontSize: 16,
      fontWeight: FontWeight.w500,
    );
  }

  static TextStyle style_2C9A0E_w500_16pt() {
    return const TextStyle(
      color: AppColors.color2C9A0E,
      fontSize: 16,
      fontWeight: FontWeight.w500,
    );
  }

  static TextStyle style_textTitle_w500_22pt() {
    return const TextStyle(
        color: AppColors.textTitle, fontSize: 22, fontWeight: FontWeight.bold);
  }

  static TextStyle style_textTitle_w600_20pt() {
    return const TextStyle(
        color: AppColors.textTitle, fontSize: 20, fontWeight: FontWeight.bold);
  }

  static TextStyle style_textTitle_w500_20pt() {
    return const TextStyle(
        color: AppColors.textTitle, fontSize: 20, fontWeight: FontWeight.bold);
  }

  static TextStyle style_textTitle_w700_20pt() {
    return const TextStyle(
        color: AppColors.textTitle,
        fontSize: 20,
        fontFamily: 'DIN Alternate',
        fontWeight: FontWeight.w700);
  }

  static TextStyle style_textTitle_w700_30pt() {
    return const TextStyle(
        color: AppColors.textTitle,
        fontSize: 30,
        fontFamily: 'DIN Alternate',
        fontWeight: FontWeight.w700);
  }

  static TextStyle style_textTitle_w700_32pt() {
    return const TextStyle(
        color: AppColors.textTitle,
        fontSize: 32,
        fontFamily: 'DIN Alternate',
        fontWeight: FontWeight.w700);
  }

  static TextStyle style_textTitle_w600_40pt() {
    return const TextStyle(
        color: AppColors.textTitle, fontSize: 40, fontWeight: FontWeight.w600);
  }

  static TextStyle style_textTitle_w600_50pt() {
    return const TextStyle(
        color: AppColors.textTitle, fontSize: 50, fontWeight: FontWeight.w600);
  }

  static TextStyle style_textTitle_w500_14pt() {
    return const TextStyle(
        color: AppColors.textTitle, fontSize: 14, fontWeight: FontWeight.w500);
  }

  static TextStyle style_textTitle_w600_14pt() {
    return const TextStyle(
        color: AppColors.textTitle, fontSize: 14, fontWeight: FontWeight.w600);
  }

  static TextStyle style_textTitle_w500_16pt() {
    return const TextStyle(
        color: AppColors.textTitle, fontSize: 16, fontWeight: FontWeight.bold);
  }

  static TextStyle style_textTitle_w600_16pt() {
    return const TextStyle(
        color: AppColors.textTitle,
        fontSize: 16,
        height: 1,
        fontWeight: FontWeight.w600);
  }

  static TextStyle style_textTitle_w700_16pt() {
    return const TextStyle(
        color: AppColors.textTitle,
        fontSize: 16,
        height: 1,
        fontWeight: FontWeight.w700);
  }

  static TextStyle style_textTitle_w700_17pt() {
    return const TextStyle(
      fontWeight: FontWeight.w700,
      fontSize: 17,
      height: 1,
      color: AppColors.textTitle,
    );
  }

  static TextStyle style_textTitle_w400_20pt() {
    return const TextStyle(
      color: AppColors.textTitle,
      height: 1,
      fontSize: 20,
    );
  }

  static TextStyle style_textTitle_w400_16pt() {
    return const TextStyle(
      color: AppColors.textTitle,
      height: 1,
      fontSize: 16,
    );
  }

  static TextStyle style_textTitle_w400_14pt() {
    return const TextStyle(
      color: AppColors.textTitle,
      fontSize: 14,
    );
  }

  static TextStyle style_textTitle_w400_12pt() {
    return const TextStyle(
      color: AppColors.textTitle,
      fontSize: 12,
    );
  }

  static TextStyle style_textTitle_w700_12pt() {
    return const TextStyle(
        color: AppColors.textTitle, fontSize: 12, fontWeight: FontWeight.w700);
  }

  static TextStyle style_FFFFFF_w700_12pt() {
    return const TextStyle(
        color: AppColors.colorFFFFFF,
        fontSize: 12,
        fontWeight: FontWeight.w700);
  }

  static TextStyle style_FFFFFF_w800_12pt() {
    return const TextStyle(
        color: AppColors.colorFFFFFF,
        fontSize: 12,
        fontWeight: FontWeight.w800);
  }

  static TextStyle style_textTitle_w400_13pt() {
    return const TextStyle(color: AppColors.textTitle, fontSize: 13);
  }

  static TextStyle style_textSub_w400_8pt() {
    return const TextStyle(
      color: AppColors.textSub,
      fontSize: 8,
    );
  }

  static TextStyle style_textSub_w400_10pt() {
    return const TextStyle(
      color: AppColors.textSub,
      fontSize: 10,
    );
  }

  static TextStyle style_textSub_w400_12pt() {
    return const TextStyle(
      color: AppColors.textSub,
      fontSize: 12,
    );
  }

  static TextStyle style_textSub_w400_11pt() {
    return const TextStyle(
      color: AppColors.textSub,
      fontSize: 11,
    );
  }

  static TextStyle style_textSub_w500_12pt() {
    return const TextStyle(
        color: AppColors.textSub, fontSize: 12, fontWeight: FontWeight.w500);
  }

  static TextStyle style_textSub_w600_12pt() {
    return const TextStyle(
        color: AppColors.textSub, fontSize: 12, fontWeight: FontWeight.w600);
  }

  static TextStyle style_6C80F4_w600_8pt() {
    return const TextStyle(
        color: AppColors.color6C80F4, fontSize: 8, fontWeight: FontWeight.w600);
  }

  static TextStyle style_6C80F4_w600_12pt() {
    return const TextStyle(
        color: AppColors.color6C80F4,
        fontSize: 12,
        fontWeight: FontWeight.w600);
  }

  static TextStyle style_textWeak_w400_9pt() {
    return const TextStyle(
      color: AppColors.textWeak,
      fontSize: 9,
    );
  }

  static TextStyle style_textWeak_w400_8pt_line() {
    return const TextStyle(
      color: AppColors.textWeak,
      fontSize: 8,
      decoration: TextDecoration.lineThrough,
    );
  }

  static TextStyle style_textWeak_w400_10pt() {
    return const TextStyle(
      color: AppColors.textWeak,
      fontSize: 10,
    );
  }

  static TextStyle style_textWeak_w400_14pt() {
    return const TextStyle(
      color: AppColors.textWeak,
      fontSize: 14,
    );
  }

  static TextStyle style_textWeak_w400_16pt() {
    return const TextStyle(
      color: AppColors.textWeak,
      fontSize: 16,
    );
  }

  static TextStyle style_textWeak_w400_10pt_line() {
    return const TextStyle(
        color: AppColors.textWeak,
        fontSize: 10,
        decoration: TextDecoration.lineThrough);
  }

  static TextStyle style_textSub_w400_14pt() {
    return const TextStyle(
      color: AppColors.textSub,
      fontSize: 14,
    );
  }

  static TextStyle style_textSub_w400_13pt() {
    return const TextStyle(
      color: AppColors.textSub,
      fontSize: 13,
    );
  }

  static TextStyle style_textSub_w500_14pt() {
    return const TextStyle(
        color: AppColors.textSub, fontSize: 14, fontWeight: FontWeight.bold);
  }

  static TextStyle style_textSub_w700_14pt() {
    return const TextStyle(
        color: AppColors.textSub, fontSize: 14, fontWeight: FontWeight.w700);
  }

  static TextStyle style_textSub_w700_16pt() {
    return const TextStyle(
        color: AppColors.textSub,
        fontSize: 16,
        fontFamily: 'DIN Alternate',
        fontWeight: FontWeight.w700);
  }

  static TextStyle style_textSub_w700_20pt() {
    return const TextStyle(
        color: AppColors.textSub,
        fontSize: 20,
        fontFamily: 'DIN Alternate',
        fontWeight: FontWeight.w700);
  }

  static TextStyle style_textSub_w500_18pt() {
    return const TextStyle(
        color: AppColors.textSub, fontSize: 18, fontWeight: FontWeight.bold);
  }

  static TextStyle style_textSub_w400_18pt() {
    return const TextStyle(
      color: AppColors.textSub,
      fontSize: 18,
    );
  }

  static TextStyle style_textSub_w400_20pt() {
    return const TextStyle(
      color: AppColors.textSub,
      fontSize: 20,
    );
  }

  static TextStyle style_5EA4E6_w800_14pt() {
    return const TextStyle(
        color: AppColors.color5EA4E6,
        fontSize: 14,
        fontWeight: FontWeight.w800);
  }

  static TextStyle style_5EA4E6_w600_14pt() {
    return const TextStyle(
        color: AppColors.color5EA4E6,
        fontSize: 14,
        fontWeight: FontWeight.bold);
  }

  static TextStyle style_black_w600_16pt() {
    return const TextStyle(
      color: AppColors.color000000,
      fontSize: 16,
      fontWeight: FontWeight.w600,
    );
  }

  static TextStyle style_textWeak_w400_12pt() {
    return const TextStyle(
      color: AppColors.textWeak,
      fontSize: 12,
    );
  }

  static TextStyle style_textWeak_w500_14pt() {
    return const TextStyle(
        color: AppColors.textWeak, fontSize: 14, fontWeight: FontWeight.bold);
  }

  static TextStyle style_textWeak_w600_12pt() {
    return const TextStyle(
        color: AppColors.textWeak, fontSize: 12, fontWeight: FontWeight.w600);
  }

  static TextStyle style_textWeak_w400_18pt() {
    return const TextStyle(
      color: AppColors.textWeak,
      fontSize: 18,
    );
  }

  static TextStyle style_textWeak_w600_20pt() {
    return const TextStyle(
        color: AppColors.textWeak, fontSize: 20, fontWeight: FontWeight.bold);
  }

  static TextStyle style_textWeak_w400_14pt_line() {
    return const TextStyle(
        color: AppColors.textWeak,
        fontSize: 14,
        decoration: TextDecoration.lineThrough);
  }

  static TextStyle style_FBBB1C_w400_10pt() {
    return const TextStyle(
        fontSize: 10,
        color: AppColors.colorFBBB1C,
        fontFamily: 'DIN Alternate');
  }

  static TextStyle style_success_w400_12pt() {
    return const TextStyle(
      color: AppColors.success,
      fontSize: 12,
    );
  }

  static TextStyle style_success_w600_20pt() {
    return const TextStyle(
        color: AppColors.success, fontSize: 20, fontWeight: FontWeight.bold);
  }

  static TextStyle style_warning_w500_14pt() {
    return const TextStyle(
        color: AppColors.warning, fontSize: 14, fontWeight: FontWeight.w500);
  }

  static TextStyle style_warning_w700_16pt() {
    return const TextStyle(
        color: AppColors.warning,
        fontSize: 16,
        fontFamily: 'DIN Alternate',
        fontWeight: FontWeight.w700);
  }

  static TextStyle style_warning_w400_8pt() {
    return const TextStyle(
        color: AppColors.warning, fontSize: 8, fontFamily: 'DIN Alternate');
  }

  static TextStyle style_warning_w400_10pt() {
    return const TextStyle(
        color: AppColors.warning, fontSize: 10, fontFamily: 'DIN Alternate');
  }

  static TextStyle style_warning_w400_12pt() {
    return const TextStyle(
      color: AppColors.warning,
      fontSize: 12,
    );
  }

  static TextStyle style_warning_w600_20pt() {
    return const TextStyle(
        color: AppColors.warning, fontSize: 20, fontWeight: FontWeight.bold);
  }

  static TextStyle style_warning_w700_20pt() {
    return const TextStyle(
        color: AppColors.warning,
        fontSize: 20,
        fontFamily: 'DIN Alternate',
        fontWeight: FontWeight.w700);
  }

  static TextStyle style_F5982B_w700_20pt() {
    return const TextStyle(
        color: AppColors.colorF5982B,
        fontSize: 20,
        fontFamily: 'DIN Alternate',
        fontWeight: FontWeight.w700);
  }

  static TextStyle style_545761_12pt() {
    return const TextStyle(
      color: AppColors.color545761,
      fontSize: 12,
    );
  }

  static TextStyle style_545761_14pt() {
    return const TextStyle(
        color: AppColors.color545761,
        fontSize: 14,
        fontWeight: FontWeight.w500);
  }

  static TextStyle style_AB6BF1_w700_12pt() {
    return const TextStyle(
        color: AppColors.colorAB6BF1,
        fontSize: 20,
        fontFamily: 'DIN Alternate',
        fontWeight: FontWeight.w700);
  }

  static TextStyle style_warning_w600_24pt() {
    return const TextStyle(
      color: AppColors.warning,
      fontSize: 24,
      fontWeight: FontWeight.bold,
    );
  }
}
