import 'package:fijkplayer/fijkplayer.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:yvr_assistant/model/prod_model.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/provider/view_state_model.dart';
import 'package:yvr_assistant/public_ui/widget/countdown_widget.dart';

class ProdVModel extends ViewStateModel with CountdownController {
  double headHeight = 211;
  ProdModel prodModel;
  ValueNotifier<bool> isShowVideo = ValueNotifier(false);

  final RxBool hideStuff = true.obs;
  final RxBool isShowTopNav = false.obs;
  // ValueNotifier<bool> isShowVideo = ValueNotifier(false);

  Rx<FijkState> fijkState = FijkState.idle.obs;

  Future reqProdData(appId) async {
    setBusy();
    try {
      prodModel = await _reqProd(appId);
      resetCountdown(prodModel.promRemainTime);
      if (prodModel.state != 1) {
        // 延迟播放视频
        Future.delayed(Duration(seconds: 1), () {
          isShowVideo.value = true;
        });
      }
      setIdle();
      return true;
    } catch (e, s) {
      setError(e, s);
      return false;
    }
  }

  static Future<ProdModel> _reqProd(appId) async {
    var prodModel = await YvrRequests.getAppDetail(appId);
    for (int i = 0; i < prodModel.vedios.length; i++) {
      prodModel.pics.insert(i, prodModel.vedios[i].pic);
    }
    return prodModel;
  }

  @override
  void onCountdownFinished() {
    ///限时折扣倒计时结束,刷新一下数据
    final ProdModel model = this.prodModel;
    if (model != null) {
      refresh(model);
    }
  }

  void refresh(ProdModel model) {
    Future.delayed(const Duration(milliseconds: 500), () {
      _reqProd(model.id).then((value) {
        if (this.prodModel == null) {
          ///页面已经关闭
          return;
        }
        this.prodModel = value;
        resetCountdown(value.promRemainTime);
        notifyListeners();

        ///如果倒计时结束后，还是没有清空限时折扣信息，那么再次刷新时间，直到限时折扣结束为止
        if (this.prodModel != null && !_isPromFinished(this.prodModel)) {
          debugPrint("prodModel startRefreshAgain");
          refresh(this.prodModel);
        }
      });
    });
  }

  bool _isPromFinished(ProdModel prod) {
    return prod.promRemainTime == null || prod.promEndTimeStamp == null;
  }

  @override
  void dispose() {
    this.prodModel = null;
    super.dispose();
  }
}
