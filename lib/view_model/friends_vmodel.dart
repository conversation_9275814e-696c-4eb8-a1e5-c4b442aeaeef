import 'package:flutter/foundation.dart';
import 'package:yvr_assistant/network/socket.dart';
import 'package:yvr_assistant/network/socket_connect.dart';
import 'package:yvr_assistant/network/socket_notify.dart';
import 'package:yvr_assistant/network/ws_request.dart';

import '../generated/l10n.dart';
import '../model/my_friends_model.dart';
import '../provider/view_state_model.dart';
import '../public_ui/widget/toast_show.dart';

class FriendVModel extends ViewStateModel2<MyFriendsDataModel>
    implements OnFriendNotifyListener, ConnectCallback {
  static final Set<int> _requestFriendIDs = Set();

  static final FriendVModel _instance = FriendVModel();

  static FriendVModel getInstance() => _instance;

  int onlineNum = 0;

  FriendVModel() : super(autoInitialize: false) {
    SocketManager.getInstance().addConnectCallback(this);
    SocketManager.getInstance().addFriendNotifyCallback(this);
  }

  @override
  Future<MyFriendsDataModel> loadData(int pageSize, int pageNum) async {
    var result = await YvrWSRequests.getMyFriends();
    checkAndThrowWSException(result.errCode);
    return result.data;
  }

  @override
  void onRefreshComplete(MyFriendsDataModel data) {
    super.onRefreshComplete(data);
    // 在线好友置顶
    data.users?.sort((a, b) => a.online.compareTo(b.online));
    _computeOnlineNum();
  }

  void _computeOnlineNum() {
    int onlineN = 0;
    model?.users?.forEach((element) {
      if (element.online == 1) {
        ++onlineN;
      }
    });
    this.onlineNum = onlineN;
  }

  @override
  void release() {
    super.release();
    onlineNum = 0;
  }

  void logout() {
    release();
    _requestFriendIDs.clear();
  }

  void requestFriend(int actId) {
    YvrWSRequests.inviteFriend(actId).then((value) {
      if (checkAndShowWSToast(value.errCode,
          errorMatcher: {10740: YLocal.current.haoyouyijingtianjia})) {
        _requestFriendIDs.add(actId);
        notifyListeners();
      } else {
        switch (value.errCode) {
          case 10740:
            _requestFriendIDs.remove(actId);
            notifyListeners();
            refreshQuietly();
            break;
        }
      }
    }).catchError((e, s) {
      YvrToast.showExceptionMessage(e);
    });
  }

  void blackFriend(int actId, {VoidCallback onSuccess}) {
    YvrWSRequests.disposeFdRelate(actId, 4).then((value) {
      if (checkAndShowWSToast(value.errCode,
          errorMatcher: {10741: YLocal.current.weichengweihaoyou})) {
        _requestFriendIDs.remove(actId);
        _removeDataUser(actId);
        notifyListeners();
        onSuccess?.call();
      } else {
        switch (value.errCode) {
          case 10741:
            _requestFriendIDs.remove(actId);
            _removeDataUser(actId);
            notifyListeners();
            refreshQuietly();
            break;
        }
      }
    }).catchError((e, s) {
      YvrToast.showExceptionMessage(e);
    });
  }

  void _removeDataUser(int actId) {
    model.users.removeWhere((element) => actId == element.actId);
    _computeOnlineNum();
  }

  void removeFriend(int actId, {VoidCallback onSuccess}) {
    YvrWSRequests.disposeFdRelate(actId, 5).then((value) {
      if (checkAndShowWSToast(value.errCode,
          errorMatcher: {10741: YLocal.current.weichengweihaoyou})) {
        _requestFriendIDs.remove(actId);
        _removeDataUser(actId);
        notifyListeners();
        onSuccess?.call();
      } else {
        switch (value.errCode) {
          case 10741:
            _requestFriendIDs.remove(actId);
            _removeDataUser(actId);
            notifyListeners();
            refreshQuietly();
            break;
        }
      }
    }).catchError((e, s) {
      YvrToast.showExceptionMessage(e);
    });
  }

  void restoreFriend(int actId, {VoidCallback onSuccess}) {
    YvrWSRequests.disposeFdRelate(actId, 6).then((value) {
      if (checkAndShowWSToast(value.errCode,
          errorMatcher: {10741: YLocal.current.weichengweihaoyou})) {
        _requestFriendIDs.remove(actId);
        _removeDataUser(actId);
        notifyListeners();
        refreshQuietly();
        onSuccess?.call();
      } else {
        switch (value.errCode) {
          case 10741:
            _requestFriendIDs.remove(actId);
            _removeDataUser(actId);
            notifyListeners();
            refreshQuietly();
            break;
        }
      }
    }).catchError((e, s) {
      YvrToast.showExceptionMessage(e);
    });
  }

  bool isRequest(int actId) {
    return _requestFriendIDs.contains(actId);
  }

  @override
  void onReceiveFriendFeedback(int status) {
    refreshQuietly();
  }

  @override
  void onReceiveFriendInvited() {}

  @override
  void onReceiveFriendOnOffline(bool online) {
    refreshQuietly();
  }

  @override
  void onReceiveFriendProcess(int status) {
    refreshQuietly();
  }

  @override
  void onConnectFailed(Exception e) {}

  @override
  void onConnectSuccess() {
    ///socket首次连接成功后可以重置列表
    reset();
  }

  @override
  void onConnectionClosed(int closeCode) {}

  @override
  void onReconnectFailed(int time, Exception e) {}

  @override
  void onReconnectSuccess(int time) {
    ///socket首次可能连接失败，不会reset vm，如果force为false，导致vm的model为null，从而refresh失败
    ///设置为true，不校验model是否为null，强制refresh
    refreshQuietly(force: true);
  }

  @override
  void onStartReconnect(int time) {}
}
