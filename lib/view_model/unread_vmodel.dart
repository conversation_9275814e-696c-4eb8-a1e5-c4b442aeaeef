import 'package:flutter/cupertino.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';

class UnreadVModel extends ChangeNotifier {
  int _unreadNum = 0;

  getUnreadNtyNum({String fromTime}) async {
    try {
      if (DBUtil.instance.userBox.get(kToken) != null) {
        _unreadNum = await YvrRequests.getUnreadNtyNum(fromTime) ?? 0;
      } else {
        _unreadNum = 0;
      }
      notifyListeners();
    } catch (e, s) {
      print(s);
    }
  }

  resetUnreadNum() {
    _unreadNum = 0;
    notifyListeners();
  }

  /*获取_theNum*/
  int get unreadNum => _unreadNum;
}
