import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/model/coupon_model.dart';
import 'package:yvr_assistant/provider/view_state_model.dart';

class CouponVModel extends ViewStateModel {
  List<CouponModel> models;

  initData() async {
    setBusy();
    try {
      models = await YvrRequests.getCouponListFromMobile() ?? [];
      setIdle();
    } catch (e, s) {
      setError(e, s);
    }
  }
}
