import 'package:yvr_assistant/model/qa_model.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/provider/view_state_model.dart';

class FeedbackVModel extends ViewStateModel {
  List<QaModel> qaList;

  initData() async {
    setBusy();
    try {
      qaList = await YvrRequests.getAllFeedbackDesc();
      setIdle();
    } catch (e, s) {
      setError(e, s);
    }
  }
}
