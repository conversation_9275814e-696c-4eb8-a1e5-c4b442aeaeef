// import 'package:yvr_assistant/manager/storage_manager.dart';

import 'package:yvr_assistant/model/consume_model.dart';
import 'package:yvr_assistant/model/recharge.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/provider/view_state_model.dart';

class ConsumeVModel extends ViewStateModel {
  List<ConsumeModel> consumeList = [];
  List<RechargeModel> rechargeList = [];

  initData() async {
    setBusy();
    try {
      consumeList = await YvrRequests.queryYconsumeList();
      rechargeList = await YvrRequests.queryRechargeList();
      setIdle();
    } catch (e, s) {
      setError(e, s);
    }
  }
}
