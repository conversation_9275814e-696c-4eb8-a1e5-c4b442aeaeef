import 'package:yvr_assistant/model/mine_app_model.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/provider/view_state_model.dart';

class MineAppVModel extends ViewStateModel {
  MineAppModel mineAppModel;

  getAppsPurchased(subjectId) async {
    setBusy();
    try {
      mineAppModel = await YvrRequests.getAppsPurchased();
      setIdle();
    } catch (e, s) {
      setError(e, s);
    }
  }
}
