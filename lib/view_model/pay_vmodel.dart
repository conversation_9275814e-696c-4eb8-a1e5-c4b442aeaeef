import 'package:fluwx/fluwx.dart' as fluwx;
import 'package:common_utils/common_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:yvr_assistant/utils/log.dart';
import '../generated/l10n.dart';
import '../manager/event_bus.dart';
import '../manager/global.dart';
import '../model/pay_model.dart';
import '../model/user_model.dart';
import '../network/http.dart';
import '../network/request.dart';
import '../pages/home/<USER>/prod.dart';
import '../pages/home/<USER>/views/pay_cell.dart';
import '../provider/view_state_model.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import '../public_ui/widget/countdown_widget.dart';
import '../public_ui/widget/pay_bottom_sheet.dart';
import '../public_ui/widget/toast_show.dart';

import 'package:yvr_assistant/manager/navigation_serive.dart';

///支付逻辑处理
class PayVModel extends ViewStateModel with CountdownController {
  int _yCoin;
  int get yCoin => _yCoin;
  bool _isWeChatInstalled = false;
  bool get isWeChatInstalled => _isWeChatInstalled;
  PayWayType _payWayType = PayWayType.payForPayPal;
  PayWayType get payWayType => _payWayType;
  bool _isPaySuccess = false;
  bool get isPaySuccess => _isPaySuccess;
  GlobalKey _payWayWidgetKey = GlobalKey();
  GlobalKey get payWayWidgetKey => _payWayWidgetKey;
  List<AccountCouponDetailList> _canUseList = [];
  List<AccountCouponDetailList> get canUseList => _canUseList;
  List<AccountCouponDetailList> _canNotUseList = [];
  List<AccountCouponDetailList> get canNotUseList => _canNotUseList;
  int _canUseCounts = 0;
  int get canUseCounts => _canUseCounts;
  int _canNotUseCounts = 0;
  int get canNotUseCounts => _canNotUseCounts;
  String _couponText;
  String get couponText => _couponText;
  Color _couponTextColor;
  Color get couponTextColor => _couponTextColor;
  String _prodPrice;
  String get prodPrice => _prodPrice;
  num _prodYPrice;
  num get prodYPrice => _prodYPrice;
  PayInfoMap _payInfoMap;
  PayInfoMap get payInfoMap => _payInfoMap;
  int _selCouponId = 0;
  int get selCouponId => _selCouponId;
  String _selectedCouponName;
  String get selectedCouponName => _selectedCouponName;
  List<AccountCouponDetailList> _coupons;
  List<AccountCouponDetailList> get coupons => _coupons;
  List<Apps> _appList = [];
  List<Apps> get appList => _appList;
  PayModel payModel;
  PayCallback payCallback;
  AddListenerCallback addListenerCallback;
  PayVModel(this.payModel, this.payCallback, this.addListenerCallback);

  void init() async {
    initApps();
    // payListener();
    _isWeChatInstalled = await fluwx.isWeChatInstalled;
    getUserCoupon();
  }

  void initApps() {
    if (payModel.apps != null && payModel.apps.isNotEmpty) {
      List<Apps> apps = payModel.apps;
      //purchaseStatus 0 未购买，1：已购买 2：退款中
      // _appList = apps.where((element) => element.purchaseStatus != 1).toList();
      _appList = apps.where((element) => element.purchaseStatus == 0).toList();
    }
  }

  //优惠券选择后返回数据
  couponResult(dynamic result) {
    if (result != null) {
      bool kChangeCouponInfo = result["kChangeCouponInfo"] ?? false;
      if (kChangeCouponInfo) {
        initCoupon();
        _selCouponId = result["kSelectedCouponId"] ?? 0;
        _selectedCouponName = result["kSelectedCouponName"] ?? "";
        if (_selCouponId == 0) {
          setPayWayType(PayWayType.payForPayPal);
        } else {
          setPayWayType(PayWayType.payForCoupon);
        }
      }
    }
  }

  //初始化优惠券
  void initCoupon() {
    if (_coupons.length > 0) {
      // 可用卡券
      _canUseList = _coupons.where((element) => element.canUse).toList();
      if (_coupons.length != _canUseList.length) {
        // 不可用卡券
        _canNotUseList = _coupons.where((element) => !element.canUse).toList();
      }
    }
    if (_canUseList.length > 0) {
      _canUseCounts = 0;
      for (var item in _canUseList) {
        _canUseCounts += item.surplus;
      }
    }
    if (_canUseList.length > 0) {
      for (var item in _canNotUseList) {
        _canNotUseCounts += item.surplus;
      }
    }
    _prodPrice = MoneyUtil.changeF2Y(payModel.sprice);
    _prodYPrice = NumUtil.getNumByValueDouble(payModel.sprice / 10, 0);
    _couponText = _canUseCounts > 0
        ? YLocal.current.Nzhangkeyong(_canUseCounts)
        : YLocal.current.zanmokeyongzanwukeyo;
    _couponTextColor = Color(_canUseCounts > 0 ? 0xFFE8E8E8 : 0xFF767880);
    // if (_yCoin >= payModel.sprice / 10) {
    //   _payInfoMap = PayInfoMap(
    //       eventType: 13,
    //       reqType: 3,
    //       text: "${YLocal.current.Ybizhifu} $_prodYPrice");
    // } else {
    //   _payInfoMap = PayInfoMap(
    //       eventType: 14, reqType: 3, text: YLocal.current.Ybiyuebuzuqingchongz);
    // }
    String symbol = (payModel.currency == 1) ? "\$" : "￥";
    _payInfoMap = PayInfoMap(
        eventType: 15, reqType: 4, text: "PayPal $symbol$_prodPrice");
  }

  //更新支付方式类型
  void setPayWayType(PayWayType type) {
    String symbol = (payModel.currency == 1) ? "\$" : "￥";
    switch (type) {
      case PayWayType.payForYCoin:
        _payWayType = PayWayType.payForYCoin;
        if (_yCoin >= payModel.sprice / 10) {
          _payInfoMap = PayInfoMap(
              eventType: 13,
              reqType: 3,
              text: "${YLocal.current.Ybizhifu} $_prodYPrice");
        } else {
          _payInfoMap = PayInfoMap(
              eventType: 14,
              reqType: 3,
              text: YLocal.current.Ybiyuebuzuqingchongz);
        }
        break;

      case PayWayType.payForAlipay:
        _payWayType = PayWayType.payForAlipay;
        _payInfoMap = PayInfoMap(
            eventType: 12,
            reqType: 2,
            text: "${YLocal.current.zhifubaozhifu} $symbol$_prodPrice");
        break;

      case PayWayType.payForWechat:
        _payWayType = PayWayType.payForWechat;
        _payInfoMap = PayInfoMap(
            eventType: 11,
            reqType: 1,
            text: "${YLocal.current.weixinzhifu} $symbol$_prodPrice");
        break;

      case PayWayType.payForCoupon:
        // 后端目前均为全额扣减卡券，需求更改时需要计算扣减金额
        _couponText = "-$symbol$_prodPrice";
        _prodPrice = "0";
        _prodYPrice = 0;
        _couponTextColor = Color(0xFF4F7FFE);
        type = PayWayType.payForCoupon;
        _payWayType = PayWayType.payForCoupon;
        _payInfoMap = PayInfoMap(
            eventType: 12,
            reqType: 3,
            text: YLocal.current.mianfeigoumaiwenfeig);
        break;

      case PayWayType.payForPayPal:
        _payWayType = PayWayType.payForPayPal;
        _payInfoMap = PayInfoMap(
            eventType: 15, reqType: 4, text: "PayPal $symbol$_prodPrice");
        break;

      default:
        _payInfoMap = PayInfoMap();
        break;
    }
    notifyListeners();
  }

  //获取用户优惠券
  getUserCoupon() async {
    setBusy();
    try {
      _coupons =
          await YvrRequests.appCouponDetailListFromMobile(payModel.id) ?? [];
      _yCoin = StorageManager.localStorage.getItem("kUserYcoin") ?? 0;
      initCoupon();
      setIdle();
    } catch (e, s) {
      setIdle();
      setError(e, s);
      YvrToast.showToast(errorMessage);
    }
  }

  reqPayParams(int type,
      {int couponType = 0, int couponId = 0, Function callBack}) {
    String couponName;
    String url;
    Map<String, dynamic> reqData;
    if (_selCouponId != 0) {
      type = 3;
      couponType = 1;
      couponId = _selCouponId;
      couponName = _selectedCouponName;
    }
    if (payModel.isBundle) {
      //捆绑支付
      url = 'vrmcsys/appstore/subjectPay';
      reqData = {
        "subId": payModel.subId,
        "type": type,
        "promId": payModel.promId,
        "source": 0
      };
    } else {
      //常规支付
      url = 'vrmcsys/appstore/wxPay';
      reqData = {
        "appInfos": [
          {
            "appId": payModel.id,
            "promId": payModel.promId,
            "couponType": couponType,
            "couponId": couponId
          }
        ],
        "source": 0,
        "type": type,
        "country": -1,
        "currency": payModel.currency
      };
    }
    YvrToast.showLoading();
    if (type != 4) {
      Future.delayed(Duration(seconds: 3), () {
        YvrToast.dismiss();
      });
    }

    http.post<Map>(url, data: reqData).then((response) {
      Log.d('response ===============>$response');
      if (response.data["errCode"] == 0) {
        switch (type) {
          case 0:
            handlePaySuccess();
            NavigationService.pushNamed('/pay_result', arguments: {
              "appPayType": PayWayType.payForWechat,
              "price": "0",
              "appId": payModel.id,
              'isBundled': payModel.isBundle,
              "currency": payModel.currency,
            });
            break;
          case 1:
            weChatPay(response.data);
            break;
          case 2:
            aliPay();
            break;
          case 3:
            NavigationService.goBack();
            handlePaySuccess();
            String priceText =
                (NumUtil.getNumByValueDouble(payModel.sprice / 10, 0))
                    .toString();
            NavigationService.pushNamed('/pay_result', arguments: {
              "appPayType": (couponId != 0)
                  ? PayWayType.payForCoupon
                  : PayWayType.payForYCoin,
              "appId": payModel.id,
              "price": (couponId != 0) ? couponName : priceText,
              'isBundled': payModel.isBundle,
              "currency": payModel.currency,
            });
            break;
          case 4:
            YvrToast.dismiss();
            NavigationService.pushNamed('/paypal_web', arguments: {
              "url": response.data["approve"],
              "title": "PayPal",
              "type": 3
            }).then((isPay) {
              if (payCallback != null) {
                payCallback(isPay != 1 ? false : true);
              }
              if (isPay != 1) {
                YvrToast.showToast(YLocal.current.zhifushibai);
              } else {
                if (payCallback != null) {
                  payCallback(true);
                }
                handlePaySuccess();
                NavigationService.goBack();
              }
            });
            break;
          default:
        }
      } else {
        YvrToast.showToast(YLocal.current.zhifushibai);
      }
    });
  }

  weChatPay(result) async {
    if (!_isWeChatInstalled) {
      YvrToast.showWarn(YLocal.current.qingxiananzhuangweix);
      return;
    }
    fluwx.payWithWeChat(
      // appId: result['appId'],
      appId: Global.wechatAppid,
      partnerId: result['partnerId'],
      prepayId: result['prepayId'],
      packageValue: result['packageValue'],
      nonceStr: result['nonceStr'],
      timeStamp: result['timeStamp'],
      sign: result['sign'],
    );
    NavigationService.goBack();
  }

  aliPay() {}

  handlePaySuccess() {
    // _isPaySuccess.value = true;
    _isPaySuccess = true;
    int appId = payModel.isBundle ? payModel.subId : payModel.id;
    // Log.d('appId ================>$appId');
    String ntyId =
        StorageManager.localStorage.getItem("NotiIdForApp_$appId") ?? null;
    Log.d('ntyId ================>$ntyId');
    if (ntyId != null) {
      notiPayResultForVR(ntyId: ntyId, isPay: true, appId: payModel.id);
      YvrRequests.disposeNotify(
          ntyId: int.parse(ntyId), tag: "recvAppPayOnMobile", mark: 1);
    }
    YvrToast.dismiss();
    eventBus.fire(EventFn({Global.kRefreshHome: true}));
  }

  notiPayResultForVR({
    @required String ntyId,
    @required int appId,
    @required bool isPay,
  }) {
    if (ntyId.length != 0 && ntyId != "") {
      /// 应用购买成功之后，若为VR通知购买的应用，需要给予回馈
      http.post<Map>('vrmcsys/appstore/handleDevAppPay',
          data: {"ntyId": ntyId, "status": (isPay ? 2 : 3)}).then((response) {
        if (response.data["errCode"] == 0) {
          StorageManager.localStorage.deleteItem("NotiIdForApp_$appId");
        }
      });
    }
  }

  //是否绑定设备
  Future<bool> isBindingDevice() async {
    try {
      List devs = await YvrRequests.getLoginDevIds();
      return devs != null && devs.length > 0;
    } catch (e) {
      YvrToast.showToast(YLocal.current.zanshimofahuoqushebe);
      return false;
    }
  }
}
