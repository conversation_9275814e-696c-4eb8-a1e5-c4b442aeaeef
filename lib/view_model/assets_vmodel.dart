import 'package:yvr_assistant/model/user_assets.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/provider/view_state_model.dart';

class AssetsVModel extends ViewStateModel {
  UserAssetsModel assetsModel;

  initData() async {
    setBusy();
    try {
      assetsModel = await YvrRequests.getUserAssets();
      setIdle();
    } catch (e, s) {
      setError(e, s);
    }
  }
}
