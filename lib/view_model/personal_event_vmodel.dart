import 'package:flutter/widgets.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/network/socket.dart';
import 'package:yvr_assistant/network/ws_request.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';

import '../model/event_detl_model.dart';
import '../provider/view_state_model.dart';
import '../public_ui/widget/toast_show.dart';

class BasePersonalEventDetailVModel extends ViewStateModel2<EventDetlModel> {
  final int editId;

  BasePersonalEventDetailVModel({
    this.editId,
    bool refreshEnabled = true,
  }) : super(refreshEnabled: refreshEnabled);

  @override
  Future<EventDetlModel> loadData(int pageSize, int pageNum) async {
    var result = await YvrWSRequests.getPersonalEventDetails(editId);
    checkAndThrowWSException(
      result.errCode,
      errorMatcher: {
        10760: YLocal.current.state_empty,
      },
    );
    return result.data;
  }
}

class PersonalEventVModel extends BasePersonalEventDetailVModel {
  List<bool> btnStatusNotifier = [false, false, false, false];
  TextEditingController nameController;

  TextEditingController detlController;

  bool dataChanged = false;

  PersonalEventVModel({
    int editId,
    bool refreshEnabled = true,
  }) : super(editId: editId, refreshEnabled: refreshEnabled);

  @override
  bool get isEmpty => editId != null && super.isEmpty;

  @override
  Future<EventDetlModel> loadData(int pageSize, int pageNum) async {
    if (editId == null) {
      return EventDetlModel();
    }

    return super.loadData(pageSize, pageNum);
  }

  @override
  void onRefreshComplete(EventDetlModel data) {
    btnStatusNotifier[0] = data.appId != null;
    btnStatusNotifier[1] = data.name != null && data.name.length > 0;
    btnStatusNotifier[2] = data.startTime != null && data.startTime.length > 0;
    btnStatusNotifier[3] = data.duration != null && data.duration > 0;
    final String name = data.name ?? '';
    nameController = TextEditingController(text: name);
    _updateName(name);

    nameController.addListener(() {
      dataChanged = true;
      data.name = nameController.text;
      _updateName(nameController.text);
      notifyListeners();
    });

    final String detail = data.detail ?? '';
    detlController = TextEditingController(text: detail);
    _updateDetail(detail);

    detlController.addListener(() {
      dataChanged = true;
      data.detail = detlController.text;
      _updateDetail(detlController.text);
      notifyListeners();
    });

    detlController.text = data.detail ?? '';
    dataChanged = false;
  }

  void editOrCreate({ValueChanged<int> onSuccess}) {
    final int appId = model.appId;
    final String rcover = model.rcover;
    final String scover = model.scover;
    final String name = model.name;
    final String startTime = model.startTime;
    final int duration = model.duration;

    if (appId == null ||
        rcover == null ||
        rcover.isEmpty ||
        name == null ||
        name.isEmpty ||
        startTime == null ||
        startTime.isEmpty ||
        duration == null ||
        duration <= 0) {
      YvrToast.showToast(YLocal.current.qingwanchengbitianxi);
      return;
    }
    final String detail = model.detail?.trim() ?? '';
    int auth = model.auth == null ? 1 : model.auth;

    if ((btnStatusNotifier[0] &&
        btnStatusNotifier[1] &&
        btnStatusNotifier[2] &&
        btnStatusNotifier[3])) {
      if (isEdit) {
        YvrWSRequests.editPersonalEvent(
                editId, appId, scover, rcover, name, startTime, duration, auth,
                detail: detail)
            .then((value) {
          if (checkAndShowWSToast(value.errCode, errorMatcher: {
            10762: YLocal.current.huodongxiugaishibai,
            10008: YLocal.current.huodongxiugaishibai,
            12000: YLocal.current.nindijiaodezifugeshi,
          })) {
            YvrToast.showToast(YLocal.current.huodongxiugaichenggo);
            onSuccess.call(editId);
          }
        }).catchError((e, s) {
          YvrToast.showExceptionMessage(e);
        });
      } else {
        YvrWSRequests.createPersonalEvent(
                appId, scover, rcover, name, startTime, duration, auth,
                detail: detail)
            .then((value) {
          if (checkAndShowWSToast(value.errCode, errorMatcher: {
            10762: YLocal.current.huodongchuangjianshi,
            12000: YLocal.current.nindijiaodezifugeshi,
          })) {
            YvrToast.showToast(YLocal.current.huodongchuangjianche);
            onSuccess.call(value.data);
          }
        }).catchError((e, s) {
          YvrToast.showExceptionMessage(e);
        });
      }
    }
  }

  void updateApp({int id, String rcover, String scover}) {
    dataChanged = true;
    model.appId = id;
    model.rcover = rcover;
    model.scover = scover;
    btnStatusNotifier[0] = id != null;
    notifyListeners();
  }

  void _updateName(String name) {
    btnStatusNotifier[1] = name != null && name.isNotEmpty;
  }

  void _updateDetail(String detail) {}

  void updateDateTime(String dateTime) {
    dataChanged = true;
    btnStatusNotifier[2] = dateTime != null && dateTime.isNotEmpty;
    model.startTime = dateTime;
    notifyListeners();
  }

  void updateDuration(int duration) {
    dataChanged = true;

    model.duration = duration;
    btnStatusNotifier[3] = duration > 0;
    notifyListeners();
  }

  void updateAuth(int auth) {
    dataChanged = true;

    model.auth = auth;
    notifyListeners();
  }

  void clearApp() {
    model.id = null;
    model.rcover = null;
    model.scover = null;
    notifyListeners();
  }

  bool get isAllowNotifier => model.auth == null || model.auth == 1;

  bool get isEdit => editId != null;
}

class PersonalEventDetailVModel extends BasePersonalEventDetailVModel {
  PersonalEventDetailVModel({
    int editId,
    bool refreshEnabled = true,
  }) : super(editId: editId, refreshEnabled: refreshEnabled);

  int enentJoinStatus;

  List<int> getUserIdList() {
    return model.users.map<int>((e) {
      return e.actId;
    }).toList();
  }

  @override
  void onRefreshComplete(EventDetlModel data) {
    int myId = DBUtil.instance.userBox.get(kActId);

    if (data.cId == myId) {
      enentJoinStatus = 0;
    } else {
      bool isJoin = data.users.any((element) {
        return element.actId == myId;
      });

      if (data.auth == 1) {
        enentJoinStatus = !isJoin ? 1 : 2;
      } else {
        enentJoinStatus = !isJoin ? 3 : 4;
      }
    }
  }

  void deleteEvent({VoidCallback onSuccess}) {
    YvrWSRequests.deletePersonalEvent(model.id).then((value) {
      if (checkAndShowWSToast(value.errCode, errorMatcher: {
        10760: YLocal.current.huodongyijieshu,
        10762: YLocal.current.huodongyikaishishanc,
      })) {
        onSuccess?.call();
      }
    }).catchError((e, s) {
      YvrToast.showExceptionMessage(e);
    });
  }

  void joinEvent({VoidCallback onSuccess}) {
    YvrWSRequests.joinEvent(model.id).then((value) {
      if (checkAndShowWSToast(
        value.errCode,
        errorMatcher: {
          10760: YLocal.current.huodongyijieshu,
          10761: YLocal.current.huodongyiguoqi,
          10511: YLocal.current.jiarushibaigaiyingyo,
        },
      )) {
        enentJoinStatus = model.auth == 1 ? 2 : 4;
        notifyListeners();
        YvrToast.showToast(YLocal.current.yichenggongjiaruhuod);
        refreshQuietly();
        onSuccess?.call();
      }
    }).catchError((e, s) {
      YvrToast.showExceptionMessage(e);
    });
  }

  void quitEvent({VoidCallback onSuccess}) {
    YvrWSRequests.quitEvent(model.id).then((value) {
      if (checkAndShowWSToast(value.errCode, errorMatcher: {})) {
        enentJoinStatus = model.auth == 1 ? 1 : 3;
        notifyListeners();
        YvrToast.showToast(YLocal.current.yituichugaihuodong);
        refreshQuietly();
        onSuccess?.call();
      }
    }).catchError((e, s) {
      YvrToast.showExceptionMessage(e);
    });
  }
}
