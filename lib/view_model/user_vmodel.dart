import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/model/user_model.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/provider/view_state_model.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';

const String kUser = 'kUser';
const String kMobile = 'kMobile';
const String kActId = 'kActId';
const String kToken = 'kToken';
const String kAuth = 'kAuth'; //0普通用户 1开发者
const String kRefreshToken = 'kRefreshToken';

class UserVModel extends ViewStateModel {
  UserModel _user;

  UserModel get user => _user;

  bool get hasUser => user != null;

  UserVModel() {
    var userMap = StorageManager.localStorage.getItem(kUser);
    _user = userMap != null ? UserModel.fromJson(userMap) : null;
  }

  bool isLogin() {
    return DBUtil.instance.userBox.get(kToken) != null;
  }

  getUserInfoFromMobile() async {
    setBusy();
    try {
      _user = await YvrRequests.getUserInfoFromMobile();
      await StorageManager.localStorage.setItem(kUser, _user.toJson());
      setIdle();
    } catch (e, s) {
      setError(e, s);
    }
  }
}
