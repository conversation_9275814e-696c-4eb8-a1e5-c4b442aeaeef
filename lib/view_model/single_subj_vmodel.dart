import 'package:yvr_assistant/model/subject_model.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/provider/view_state_model.dart';

class SingleSubjVModel extends ViewStateModel {
  SubjectModel subjectModel;

  getAppsOnSubject(subjectId) async {
    setBusy();
    try {
      subjectModel = await YvrRequests.getAppsOnSubject(subjectId);
      setIdle();
    } catch (e, s) {
      setError(e, s);
    }
  }
}
