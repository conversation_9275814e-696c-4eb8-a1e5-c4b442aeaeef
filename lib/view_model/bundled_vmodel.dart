import 'package:flutter/cupertino.dart';
import 'package:yvr_assistant/manager/navigation_serive.dart';
import 'package:yvr_assistant/utils/log.dart';

import '../generated/l10n.dart';
import '../manager/event_bus.dart';
import '../manager/global.dart';
import '../manager/storage_manager.dart';
import '../model/bundled_info_model.dart';
import '../network/http.dart';
import '../network/request.dart';
import '../pages/home/<USER>/views/pay_cell.dart';
import '../provider/view_state_model.dart';
import '../public_ui/widget/toast_show.dart';
import 'appbar_vmodel.dart';

///捆绑应用详情页逻辑处理
class BundledApplicationVModel extends ViewStateModel {
  int mpId;
  double _height = 0;
  double headHeight = 211;
  ScrollController _scrollController;
  ScrollController get scrollController => _scrollController;
  BundledAppbarVModel _appbarVModel = BundledAppbarVModel();
  BundledAppbarVModel get appbarVModel => _appbarVModel;
  BundledInfoModel _bundledInfo;
  BundledInfoModel get bundledInfo => _bundledInfo;
  dynamic _bundledStr;
  dynamic get bundledStr => _bundledStr;
  List<Apps> _appList = [];
  List<Apps> get appList => _appList;
  bool isPaySuccess = false;

  void init(int mpId, ScrollController scroll, double toolbarHeight) {
    this.mpId = mpId;
    _scrollController = scroll;
    _height = headHeight - toolbarHeight;
    addScrollListener();
    loadAllData();
  }

  //加载所有数据
  loadAllData() {
    getAppsOnSubject();
    getBundledIntroduce();
  }

  void addScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.offset > _height && !_appbarVModel.showTabBar) {
        _appbarVModel.setShowTabBar(true);
      } else if (_scrollController.offset < _height &&
          _appbarVModel.showTabBar) {
        _appbarVModel.setShowTabBar(false);
      }
    });
  }

  setPaySuccess(bool b) {
    isPaySuccess = b;
  }

  //查询专题信息
  getAppsOnSubject({Function callback}) async {
    YvrToast.showLoading();
    Map<String, dynamic> parameters = {'subjectId': mpId};
    try {
      ResponseData<BundledInfoModel> object =
          await YvrRequests.getBundledInfo(parameters);
      _bundledInfo = object.data;
      initApps();
      if (error) {
        setIdle();
      } else {
        notifyListeners();
      }
      if (callback != null) {
        Future.delayed(Duration(milliseconds: 300), () {
          callback();
        });
      }
    } catch (e, s) {
      setError(e, s);
    }
    YvrToast.dismiss();
  }

  //初始化app列表数据
  initApps() {
    _appList.clear();
    List<Apps> apps = [];
    for (Apps info in _bundledInfo.apps) {
      ////0 未购买，1：已购买 2：退款中
      if (info.purchaseStatus == 0) {
        _appList.add(info);
      } else {
        apps.add(info);
      }
    }
    if (apps.isNotEmpty) _appList.addAll(apps);
  }

  //获取捆绑介绍
  getBundledIntroduce() async {
    try {
      ResponseData<dynamic> object = await YvrRequests.getBundledIntroduce();
      _bundledStr = object.data;
      Log.d('bundledStr =============>$_bundledStr');
    } catch (e, s) {
      setError(e, s);
    }
  }

  //是否绑定设备
  Future<bool> isBindingDevice() async {
    try {
      List devs = await YvrRequests.getLoginDevIds();
      return devs != null && devs.length > 0;
    } catch (e) {
      // YvrToast.showToast("暂时无法获取设备绑定信息");
      YvrToast.showToast(YLocal.current.zanshimofahuoqushebe);
      return false;
    }
  }

  //免费支付
  freePay(SubSaleSummaryInfo info) {
    var reqData = {
      "subId": info.subId,
      "type": 0,
      "promId": info.promId,
      "source": 0
    };
    YvrToast.showLoading();
    http
        .post<Map>('vrmcsys/appstore/subjectPay', data: reqData)
        .then((response) {
      Log.d('response ======支付结果=========>$response');
      YvrToast.dismiss();
      if (response.data["errCode"] == 0) {
        setPaySuccess(true);
        getAppsOnSubject();
        handlePaySuccess(info);
        NavigationService.pushNamed('/pay_result', arguments: {
          "appPayType": PayWayType.payForWechat,
          "price": "0",
          "appId": info.subId,
          'isBundled': true
        });
      } else {
        // YvrToast.showToast("支付失败");
        YvrToast.showToast(YLocal.current.zhifushibai);
      }
    });
  }

  handlePaySuccess(SubSaleSummaryInfo info) {
    String ntyId =
        StorageManager.localStorage.getItem("NotiIdForApp_${info.subId}") ??
            null;
    if (ntyId != null) {
      notiPayResultForVR(ntyId: ntyId, isPay: true, appId: info.subId);
      YvrRequests.disposeNotify(
          ntyId: int.parse(ntyId), tag: "recvAppPayOnMobile", mark: 1);
    }
    eventBus.fire(EventFn({Global.kRefreshHome: true}));
  }

  notiPayResultForVR({
    @required String ntyId,
    @required int appId,
    @required bool isPay,
  }) {
    if (ntyId.length != 0 && ntyId != "") {
      /// 应用购买成功之后，若为VR通知购买的应用，需要给予回馈
      http.post<Map>('vrmcsys/appstore/handleDevAppPay',
          data: {"ntyId": ntyId, "status": (isPay ? 2 : 3)}).then((response) {
        if (response.data["errCode"] == 0) {
          StorageManager.localStorage.deleteItem("NotiIdForApp_$appId");
        }
      });
    }
  }
}
