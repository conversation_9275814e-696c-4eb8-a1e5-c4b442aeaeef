import 'package:flutter/material.dart';
import 'package:yvr_assistant/model/hall_friends_model.dart';
import 'package:yvr_assistant/model/page_list_model.dart';
import 'package:yvr_assistant/network/socket.dart';
import 'package:yvr_assistant/provider/view_state_refresh_list_model.dart';
import 'package:yvr_assistant/utils/collection_utils.dart';
import 'package:yvr_assistant/view_model/search_vmodel_mixin.dart';
import '../network/ws_request.dart';
import '../provider/view_state_model.dart';

class HallFriendsVModel extends ViewStateModel2<HallDataModel> {
  @override
  Future<HallDataModel> loadData(int pageSize, int pageNum) async {
    var result = await YvrWSRequests.getPotentialFriends(3);
    checkAndThrowWSException(result.errCode);
    return result.data;
  }
}

class HallSearchFriendsVModel extends SearchViewModel<FriendDataModel> {
  @override
  Future<FriendDataModel> doSearch(String key) async {
    var result = await YvrWSRequests.searchFriend(key);
    checkAndThrowWSException(result.errCode);
    return result.data;
  }

  bool get isEmpty => searchKey == null ? false : (model.users.isEmpty);
}

class FindSameAppsVModel extends ViewStateRefreshListModel2<HallAppModel> {
  FindSameAppsVModel() : super(pageParams: PageParams(5, 1));

  @override
  Future<PageListModel<HallAppModel>> loadData(
      int pageSize, int pageNum) async {
    var result = await YvrWSRequests.getMutualAppInfos(pageSize, pageNum, 2);
    checkAndThrowWSException(result.errCode);
    return PageListModel.create(pageNum, pageSize, result.data.apps);
  }

  /// 如果是分页拿的，按照在线人数排，可能排名有变动，排在前面的应用下次分页拿的时候又出现在下一页，
  /// 如果是一页之内，后端能保证不重复
  @override
  void onLoadComplete(PageListModel<HallAppModel> data) {
    var oldList = list;
    var addList = data.content;

    if (oldList != null && addList != null) {
      CollectionUtils.removeRepeat<HallAppModel, int>(
          addList, oldList, (e) => e.id);
    }
  }
}

class AppFriendsVModel extends ViewStateRefreshListModel2<AppFriendsUser> {
  final int id;
  final String image;
  final String name;

  AppFriendsVModel(
      {@required this.id, @required this.image, @required this.name})
      : super(pageParams: PageParams(5, 1));

  @override
  Future<PageListModel<AppFriendsUser>> loadData(
      int pageSize, int pageNum) async {
    var result = await YvrWSRequests.getMutualAppUsers(id, pageSize, pageNum);
    checkAndThrowWSException(result.errCode);
    return PageListModel.create(pageNum, pageSize, result.data.users,
        totalCount: result.data.userNum);
  }

  @override
  bool get isEmpty => super.isEmpty && (name == null || image == null);
}
