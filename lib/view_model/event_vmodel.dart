import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter/cupertino.dart';
import 'package:yvr_assistant/model/Upload_file_model.dart';
import 'package:yvr_assistant/model/event_comment_model.dart';
import 'package:yvr_assistant/model/official_event_detail_model.dart';
import 'package:yvr_assistant/model/page_list_model.dart';
import 'package:yvr_assistant/network/socket.dart';
import 'package:yvr_assistant/network/ws_request.dart';
import 'package:yvr_assistant/provider/view_state_model.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/date_time_utils.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';
import '../generated/l10n.dart';
import '../model/event_model.dart';
import '../model/official_event_model.dart';
import '../network/http.dart';
import '../network/request.dart';
import '../provider/view_state.dart';
import '../provider/view_state_refresh_list_model.dart';
import '../public_ui/widget/buttons.dart';
import 'vmodel_mixin.dart';

mixin EventSortViewModelMixin<T> on ViewStateRefreshListModel2<T> {
  int _sortIndex = -1;
  List<String> _sortTexts;
  List<DateTimeDiffStatusData> dateTimeTextList;

  List<String> get sortTexts => _sortTexts;

  String get selectText => _sortTexts[_sortIndex];

  int get sortIndex => _sortIndex;

  @protected
  DateTime nowTime;

  String selectTextAt(int index) {
    return _sortTexts[index];
  }

  void resetSortIndex(int index) {
    this._sortIndex = index;
    reset();
  }

  @override
  void reset() {
    dateTimeTextList = null;
    super.reset();
  }

  @override
  void onLoadComplete(PageListModel<T> data) {
    dateTimeTextList.addAll(
      data.content.map(
        (e) => DateTimeUtils.parseDateTimeDiff(
          DateTimeUtils.parseServerTime(getStartTime(e)),
          DateTimeUtils.parseServerTime(getEndTime(e)),
          nowTime,
        ),
      ),
    );
  }

  @override
  void onRefreshComplete(PageListModel<T> data) {
    dateTimeTextList = data.content
        .map(
          (e) => DateTimeUtils.parseDateTimeDiff(
            DateTimeUtils.parseServerTime(getStartTime(e)),
            DateTimeUtils.parseServerTime(getEndTime(e)),
            nowTime,
          ),
        )
        .toList();
  }

  String getStartTime(T data);

  String getEndTime(T data);
}

mixin EventMixinViewModel<T>
    on
        ViewStateRefreshListModel2<OfficialEventModel>,
        EventSortViewModelMixin<OfficialEventModel> {
  @override
  String getEndTime(OfficialEventModel data) {
    return data.endTime;
  }

  @override
  String getStartTime(OfficialEventModel data) {
    return data.startTime;
  }

  bool isFollowed(OfficialEventModel model) {
    return model.focusesStatus != 'no';
  }

  void follow(OfficialEventModel model, VoidCallback successCallback,
      bool refreshSelfAllList) {
    bool f = !isFollowed(model);
    YvrRequests.followOfficialEvent(model.eventId, f).catchError((e, s) {
      YvrToast.showExceptionMessage(e);
    }).then((value) async {
      switch (value.errCode) {
        case 10760:
          // 官方活动不存在
          YvrToast.showToast(YLocal.current.huodongyixiaxian);
          remove(model);
          break;
        case 10763:
          // 已经关注该活动，不可重复关注
          YvrToast.showToast(YLocal.current.yijingguanzhugaihuod);
          var detail = await YvrRequests.getOfficialEventDetail(model.eventId);
          applyItem(model, detail.data, model.commentNum);
          break;
        case 10764:
          // 未关注该活动，不可取消关注
          YvrToast.showToast(YLocal.current.weiguanzhugaihuodong);
          var detail = await YvrRequests.getOfficialEventDetail(model.eventId);
          applyItem(model, detail.data, model.commentNum);
          break;
        case 0:
          if (refreshSelfAllList) {
            refreshQuietly();
          } else {
            var detail =
                await YvrRequests.getOfficialEventDetail(model.eventId);
            applyItem(model, detail.data, model.commentNum);
          }
          successCallback.call();
          break;
      }
    });
  }

  void applyItemById(
      int eventId, OfficialEventDetailModel details, int commentNum) {
    OfficialEventModel model = list?.firstWhere(
        (element) => eventId == element.eventId,
        orElse: () => null);
    if (model != null) {
      applyItem(model, details, commentNum);
    }
  }

  void removeById(int eventId) {
    if (list != null) {
      removeWhere((element) => eventId == element.eventId);
    }
  }

  void applyItem(OfficialEventModel model, OfficialEventDetailModel details,
      int commentNum) {
    model.commentNum = commentNum;
    var detail = details.eventOfficialInfo;
    model.falseNum = detail.falseNum;
    model.realNum = detail.realNum;
    model.focusesPortraits = detail.focusesPortraits;
    model.focusesStatus = detail.focusesStatus;
    model.focusesPortraits = detail.focusesPortraits;
    notifyListeners();
  }
}

class OfficialEventVModel extends ViewStateRefreshListModel2<OfficialEventModel>
    with EventSortViewModelMixin<OfficialEventModel>, EventMixinViewModel {
  static const List<String> kSortTypes = ["default", "newest", "most"];
  static List<String> kSortTypeText = [
    YLocal.current.tuijian,
    YLocal.current.zuixinhuodong,
    YLocal.current.zuirehuodong
  ];

  OfficialEventVModel({int defaultSortIndex = 0}) : super() {
    _sortTexts = kSortTypeText;
    this._sortIndex = defaultSortIndex;
  }

  @override
  Future<PageListModel<OfficialEventModel>> loadData(
      int pageSize, int pageNum) async {
    var result = await YvrRequests.getOfficialEvent(
        pageSize, pageNum, kSortTypes[sortIndex]);
    checkAndThrowException(result.errCode);
    nowTime = DateTimeUtils.parseHttpTime(result.timestamp);
    return result.data;
  }
}

class MyEventVModel extends ViewStateRefreshListModel2<OfficialEventModel>
    with EventSortViewModelMixin<OfficialEventModel>, EventMixinViewModel {
  static const List<String> kSortTypes = ["newest", "most"];
  static List<String> kSortTypeText = [
    YLocal.current.zuixinhuodong,
    YLocal.current.zuirehuodong
  ];

  MyEventVModel({int defaultSortIndex = 0}) {
    _sortTexts = kSortTypeText;
    this._sortIndex = defaultSortIndex;
  }

  @override
  Future<PageListModel<OfficialEventModel>> loadData(
      int pageSize, int pageNum) async {
    var result =
        await YvrRequests.getMyEvent(pageSize, pageNum, kSortTypes[sortIndex]);
    checkAndThrowException(result.errCode);
    nowTime = DateTimeUtils.parseHttpTime(result.timestamp);
    return result.data;
  }
}

class EventSquareVModel extends ViewStateRefreshListModel2<EventModel>
    with EventSortViewModelMixin<EventModel> {
  static const List<int> kSortTypes = [1, 2];
  static List<String> kSortTypeText = [
    YLocal.current.zuixinhuodong,
    YLocal.current.zuirehuodong
  ];

  EventSquareVModel({int defaultSortIndex = 0})
      : super(pageParams: PageParams(30, 1)) {
    _sortTexts = kSortTypeText;
    this._sortIndex = defaultSortIndex;
  }

  @override
  Future<PageListModel<EventModel>> loadData(int pageSize, int pageNum) async {
    //服务端默认30条
    var result =
        await YvrWSRequests.getSquareEvents(pageNum, kSortTypes[sortIndex]);
    checkAndThrowWSException(result.errCode);
    String currentTime = result.data.currentTime;
    this.nowTime = (currentTime == null
            ? null
            : DateTimeUtils.parseServerTime(currentTime)) ??
        DateTime.now();
    return PageListModel.create(pageNum, pageSize, result.data.events);
  }

  @override
  String getEndTime(EventModel data) {
    return data.endTime;
  }

  @override
  String getStartTime(EventModel data) {
    return data.startTime;
  }
}

class EventCommentListData {
  String commentTime;
  bool isSelf;
  String commentText;
  List<String> commentImages;

  EventCommentListData(
      this.commentTime, this.isSelf, this.commentText, this.commentImages);
}

class OfficialEventDetailsVModel
    extends ViewStateRefreshListModel2<EventCommentModel>
    with UploadImageViewModel {
  static const int _kOfficialEventDirtyCode = 10760;
  bool ruleExpanded = false;
  final int eventId;
  TextEditingController commentTextEditingController = TextEditingController();
  OfficialEventDetailModel detail;
  List<int> tagList;
  DateTimeDiffStatusData status;
  List<EventCommentListData> eventCommentListData;
  ProgressState sendCommentState = ProgressState.idle;

  bool _following = false;

  int _userId;

  String _lastRequestTime;

  bool Function() onOfficialEventDirty;

  OfficialEventDetailsVModel(this.eventId, this.onOfficialEventDirty)
      : super() {
    commentTextEditingController.addListener(() {
      notifyListeners();
    });
    _userId = DBUtil.instance.userBox.get(kActId);
  }

  @override
  Future<PageListModel<EventCommentModel>> loadData(
      int pageSize, int pageNum) async {
    var result = await YvrRequests.getEventComment(pageSize, pageNum, eventId);
    if (_fireOfficialEventExpired(result, test: false)) {
      YvrToast.showToast(YLocal.current.huodongyixiaxian);
    } else {
      checkAndThrowException(result.errCode);
    }
    _lastRequestTime = result.timestamp;
    return result.data;
  }

  bool _fireOfficialEventExpired<T>(ResponseData<T> data, {bool test = false}) {
    if ((data.errCode == _kOfficialEventDirtyCode || test) &&
        this.onOfficialEventDirty.call()) {
      return true;
    }
    return false;
  }

  @override
  void reset() {
    release();
    super.reset();
  }

  void release() {
    _lastRequestTime = null;
    detail = null;
    tagList = null;
    sendCommentState = ProgressState.idle;
    eventCommentListData = null;
  }

  void toggleRuleExpand() {
    ruleExpanded = !ruleExpanded;
    notifyListeners();
  }

  void _processDetail() {
    if (detail != null) {
      tagList = detail.appInfo?.tag
              ?.split(',')
              ?.map<int>((e) => int.parse(e))
              ?.toList() ??
          [];
      status = DateTimeUtils.parseDateTimeDiff(
        DateTimeUtils.parseServerTime(detail.eventOfficialInfo.startTime),
        DateTimeUtils.parseServerTime(detail.eventOfficialInfo.endTime),
        DateTimeUtils.parseHttpTime(_lastRequestTime),
      );
    }
  }

  bool get isExpired => status.status == DateTimeDiffStatus.expired;

  @override
  void onRefreshComplete(PageListModel<EventCommentModel> data) {
    _processDetail();
    eventCommentListData = data.content.map<EventCommentListData>((e) {
      var j = jsonDecode(e.content);
      String text = j['words'] ?? '';
      List<String> images = (j['images'] as List)?.cast<String>() ?? <String>[];
      return EventCommentListData(
          formatCommentTime(e.time), e.userId == _userId, text, images);
    }).toList();
  }

  @override
  void onLoadComplete(PageListModel<EventCommentModel> data) {
    super.onLoadComplete(data);
    eventCommentListData.addAll(data.content.map<EventCommentListData>((e) {
      var j = jsonDecode(e.content);
      String text = j['words'] ?? '';
      List<String> images = (j['images'] as List)?.cast<String>() ?? <String>[];
      return EventCommentListData(
          formatCommentTime(e.time), e.userId == _userId, text, images);
    }).toList());
  }

  String formatCommentTime(String text) {
    return DateTimeUtils.parseDateTimeElapse(
        DateTimeUtils.parseServerTime(text),
        DateTimeUtils.parseHttpTime(_lastRequestTime));
  }

  bool hasCommentFormContent() {
    return commentTextEditingController.text.isNotEmpty ||
        uploadImageSize() > 0;
  }

  void clearCommentFormContent() {
    commentTextEditingController.text = "";
    clearUploadImages();
  }

  @override
  bool get isEmpty => detail == null && super.isEmpty;

  bool get isFollowed =>
      detail != null && detail.eventOfficialInfo.focusesStatus == 'yes';

  int get followNum =>
      (detail.eventOfficialInfo.realNum ?? 0) +
      (detail.eventOfficialInfo.falseNum ?? 0);

  void follow() {
    if (_following) {
      return;
    }
    _following = true;
    bool f = !isFollowed;
    YvrRequests.followOfficialEvent(eventId, f).then((value) async {
      if (_fireOfficialEventExpired(value)) {
        YvrToast.showToast(YLocal.current.huodongyixiaxian);
        return;
      }

      switch (value.errCode) {
        case _kOfficialEventDirtyCode:
          YvrToast.showToast(YLocal.current.huodongyixiaxian);
          break;
        case 10760:
          // 官方活动不存在
          release();
          viewState = ViewState.empty;
          YvrToast.showWarn(YLocal.current.guanfanghuodongbucun);
          break;
        case 10763:
          // 已经关注该活动，不可重复关注
          YvrToast.showToast(YLocal.current.yijingguanzhugaihuod);
          detail.eventOfficialInfo.focusesStatus = 'yes';
          break;
        case 10764:
          YvrToast.showToast(YLocal.current.weiguanzhugaihuodong);
          // 未关注该活动，不可取消关注
          detail.eventOfficialInfo.focusesStatus = 'no';
          break;
        case 0:
          detail = (await YvrRequests.getOfficialEventDetail(eventId)).data;
          _processDetail();
          break;
      }
    }).whenComplete(() {
      _following = false;
      notifyListeners();
    });
  }

  void deleteCommentImage(int index) {
    startCancelUploadImageAt(index).catchError((e, s) {
      if (e is UploadImageException) {
        YvrToast.showToast(e.message);
      }
      print(s);
    }).whenComplete(() {
      notifyListeners();
    });
  }

  void deleteAllCommentImage() {
    if (sendCommentState != ProgressState.loading) {
      startCancelUploadAllImage();
    }
  }

  void deleteOrReportComment(
      EventCommentModel comment, EventCommentListData commentData) {
    if (commentData.isSelf) {
      YvrRequests.deleteComment(comment.id).then((value) {
        if (_fireOfficialEventExpired(value)) {
          YvrToast.showToast(YLocal.current.huodongyixiaxian);
          return;
        }
        if (checkAndShowToast(value.errCode, errorMatcher: <int, String>{
          _kOfficialEventDirtyCode: YLocal.current.huodongyixiaxian
        })) {
          if (remove(comment)) {
            eventCommentListData.remove(commentData);
          }
        }
      }).catchError((e, s) {
        YvrToast.showExceptionMessage(e);
        print(s);
      }).whenComplete(() {
        notifyListeners();
      });
    } else {
      Future.delayed(
          Duration(
            milliseconds: Random().nextInt(500),
          ), () {
        YvrToast.showToast(YLocal.current.ganxienindezhichinwo);
      });
    }
  }

  void sendComment() {
    if (!hasCommentFormContent()) {
      return;
    }
    sendCommentState = ProgressState.loading;
    notifyListeners();

    Future.sync(() async {
      await startUploadImages();
      var result = await YvrRequests.insertComment(
        detail.eventOfficialInfo.eventOfficialId,
        detail.eventOfficialInfo.appId,
        DBUtil.instance.userBox.get(kActId),
        DBUtil.instance.userBox.get(kMobile),
        words: commentTextEditingController.text,
        images: uploadImageList.map<String>((e) => e.networkUrl).toList(),
      );
      return result;
    }).then((value) {
      if (_fireOfficialEventExpired(value)) {
        YvrToast.showToast(YLocal.current.huodongyixiaxian);
        return;
      }

      bool success = checkAndShowToast(value.errCode,
          errorMatcher: {10540: YLocal.current.pinglunnarongweiguip});
      if (success) {
        clearCommentFormContent();
        refreshQuietly();
      }
    }).catchError((e, s) {
      if (e is UploadImageException) {
        YvrToast.showToast(e.message);
      } else {
        YvrToast.showExceptionMessage(e);
      }
      print(s);
    }).whenComplete(() {
      sendCommentState = ProgressState.idle;
      notifyListeners();
    });
  }

  bool get isSending => sendCommentState == ProgressState.loading;

  @override
  Future<void> beforeRefreshSuccess() async {
    var result = await YvrRequests.getOfficialEventDetail(eventId);
    if (_fireOfficialEventExpired(result, test: false)) {
      YvrToast.showToast(YLocal.current.huodongyixiaxian);
      return;
    }
    checkAndThrowException(result.errCode);
    detail = result.data;
  }

  @override
  Future<String> startUploadImage(Uint8List data, String fileName) async {
    ResponseData<UploadFileModel> result =
        await YvrRequests.uploadOfficialEventImage(data, fileName, eventId);
    if (_fireOfficialEventExpired(result)) {
      sendCommentState = ProgressState.idle;
      throw UploadImageException(YLocal.current.huodongyixiaxian);
    }
    switch (result.errCode) {
      case 11001:
        throw UploadImageException(YLocal.current.canshubuduiwenjianwe);
      case 10540:
        throw UploadImageException(YLocal.current.shangchuanwenjianwei);
      case 10041:
        throw UploadImageException(YLocal.current.jitongcuowuxitongcuo);
      case 0:
        return result.data.fileUrl;
      default:
        throw UploadImageException(YLocal.current.shangchuanshibaishan);
    }
  }

  @override
  Future<void> startCancelUploadImage(UploadImageData data) async {
    ResponseData<void> result =
        await YvrRequests.deleteOfficialEventImage(data.networkUrl);

    ///11001	参数不对/文件为空
    ///10041	系统错误
    switch (result.errCode) {
      case 11001:
        throw UploadImageException(YLocal.current.shanchushibai);
      case 10041:
        throw UploadImageException(YLocal.current.jitongcuowuxitongcuo);
      case 0:
        break;
      default:
        throw UploadImageException(YLocal.current.shanchushibai);
    }
  }
}
