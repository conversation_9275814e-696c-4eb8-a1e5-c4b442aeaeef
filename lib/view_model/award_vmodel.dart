import 'dart:typed_data';
import 'package:flutter/material.dart';

import '../generated/l10n.dart';
import 'vmodel_mixin.dart';
import '../network/http.dart';
import '../network/request.dart';
import 'package:yvr_assistant/model/res_file_model.dart';

class AwardVModel extends ChangeNotifier with UploadImageViewModel {
  String requestType;
  AwardVModel.init(String requestType) {
    this.requestType = requestType;
  }
  AwardVModel.requestType(String requestType) {
    this.requestType = requestType;
  }

  Future<void> uploadImage() async {
    await startUploadImages();
  }

  @override
  Future<String> startUploadImage(Uint8List data, String fileName) async {
    ResponseData<ResUploadFileModel> result = await YvrRequests.uploadAuditFile(
        data: data, fileName: fileName, requestType: requestType);
    switch (result.errCode) {
      case 0:
        return result.data.url;
      case 11001:
        throw UploadImageException(YLocal.current.canshubuduiwenjianwe);
      case 10540:
        throw UploadImageException(YLocal.current.shangchuanwenjianwei);
      case 10041:
        throw UploadImageException(YLocal.current.jitongcuowuxitongcuo);
      default:
        throw UploadImageException(YLocal.current.shangchuanshibaishan);
    }
  }

  @override
  Future<void> startCancelUploadImage(UploadImageData data) {
    throw UnimplementedError();
  }
}
