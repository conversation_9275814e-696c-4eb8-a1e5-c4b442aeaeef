import 'package:yvr_assistant/model/theme_data_model.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/provider/view_state_model.dart';

class SingleThemeVModel extends ViewStateModel {
  ThemeDataModel themeDataModel;

  getThemeInfo(themeId) async {
    setBusy();
    try {
      themeDataModel = await YvrRequests.getThemeInfo(themeId);
      setIdle();
    } catch (e, s) {
      setError(e, s);
    }
  }
}
