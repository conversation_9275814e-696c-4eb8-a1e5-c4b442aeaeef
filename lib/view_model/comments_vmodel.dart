import 'package:flutter/material.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';

class UserIsComment extends ChangeNotifier {
  void changeToUserIsComment(int appId) {
    StorageManager.localStorage.setItem("UserIsComment_$appId", true);
    notifyListeners();
  }

  bool getIsComment(int appId) {
    return StorageManager.localStorage.getItem("UserIsComment_$appId") ?? false;
  }
}
