import 'dart:async';
import '../manager/event_bus.dart';
import '../model/count_down_entity.dart';
import '../provider/view_state_model.dart';

///倒计时处理逻辑
class CountDownVModel extends ViewStateModel {
  int day = 0;
  int hour = 0;
  int minute = 0;
  int second = 0;
  Timer timer;
  final CountDownEntity _countDownEntity = CountDownEntity();
  CountDownEntity get countDownEntity => _countDownEntity;
  int countSecond;

  CountDownVModel(this.countSecond) {
    if (countSecond > 0) {
      //如果是毫秒则换算成秒
      if ('$countSecond'.length == 13) {
        countSecond = countSecond ~/ 1000;
      }
      initTime(countSecond);
    } else {
      //测试数据
      //23小时50分45秒
      // countSecond = 23*60*60+50*60+45;
      // initTime(countSecond);
    }
  }

  void start() {
    int day = 24 * 60 * 60;
    if (countSecond > 0 && countSecond < day) {
      startCountDown();
    }
  }

  @override
  void dispose() {
    timer?.cancel();
    timer = null;
    super.dispose();
  }

  void initTime(int countSecond) {
    int day7 = 7 * 24 * 60 * 60;
    int d = 24 * 60 * 60;
    int h = 60 * 60;
    int m = 60;
    if (countSecond < d) {
      day = 0;
      //计算小时差
      double hh = countSecond / h;
      hour = countSecond ~/ h;
      //计算分钟差
      minute = ((hh - hour) * m).toInt();
      double mm = (hh - hour) * m - minute;
      //计算秒差
      second = (mm * 60).toInt();

      String _hour = hour >= 10 ? '$hour:' : (hour > 0 ? '0$hour:' : '');
      String _minute = minute >= 10
          ? '$minute:'
          : (minute > 0
              ? '0$minute:'
              : hour > 0
                  ? '00:'
                  : '');
      String _second = second > 0
          ? (_hour.isEmpty && _minute.isEmpty
              ? '$second秒后结束'
              : '${second >= 10 ? second : '0$second'} 后结束')
          : _hour.isEmpty && _minute.isEmpty
              ? ''
              : '00 后结束';
      _countDownEntity.text = '$_hour$_minute$_second';
    } else if (countSecond <= day7) {
      day = countSecond ~/ d;
      _countDownEntity.text = '$day天后结束';
    }
  }

  void startCountDown() {
    timer = Timer.periodic(const Duration(milliseconds: 1000), (timer) {
      if (second > 0) {
        second -= 1;
      } else if (minute > 0) {
        minute -= 1;
        second = 59;
      } else if (hour > 0) {
        hour -= 1;
        minute = 59;
        second = 59;
      } else if (day > 0) {
        day -= 1;
        hour = 23;
        minute = 59;
        second = 59;
      } else {
        day = 0;
        hour = 0;
        minute = 0;
        second = 0;
        eventBus.fire(EventFn({'HomeRefresh': true}));
        eventBus.fire(EventFn({'BundledEnd': true}));
        timer.cancel();
      }
      String _hour = hour >= 10 ? '$hour:' : (hour > 0 ? '0$hour:' : '');
      String _minute = minute >= 10
          ? '$minute:'
          : (minute > 0
              ? '0$minute:'
              : hour > 0
                  ? '00:'
                  : '');
      String _second = second > 0
          ? (_hour.isEmpty && _minute.isEmpty
              ? '$second秒后结束'
              : '${second >= 10 ? second : '0$second'} 后结束')
          : _hour.isEmpty && _minute.isEmpty
              ? ''
              : '00 后结束';
      _countDownEntity.text = '$_hour$_minute$_second';
      notifyListeners();
    });
  }
}
