import 'package:flutter/material.dart';
import 'package:yvr_assistant/provider/view_state_model.dart';

abstract class SearchViewModel<T> extends ViewStateModel2<T> {
  static const Duration kSearchMinTime = Duration(milliseconds: 500);

  SearchViewModel()
      : super(autoInitialize: false, refreshEnabled: false, loadEnabled: false);

  String searchKey; //null为空闲状态
  bool _searching = false; //是否正在搜索
  void loadBySearchKey(String key) {
    if (key.isEmpty) {
      clear();
      return;
    }
    if (key == this.searchKey) {
      return;
    }

    if (_searching) {
      searchKey = key;
    } else {
      searchKey = key;
      reset();
    }
  }

  ///如果处于搜索状态时，没有数据，才认为它为empty状态（页面会展示空态图）。否则则认为是正常的状态。
  @override
  bool get isEmpty => searchKey == null ? false : super.isEmpty;

  @override
  void reset() {
    _searching = true;
    super.reset();
  }

  @override
  void onRefreshComplete(data) {
    _searching = false;
  }

  @mustCallSuper
  @override
  Future<T> loadData(int pageSize, int pageNum) async {
    final String lastSearchKey = searchKey;
    DateTime now = DateTime.now();
    T result = await doSearch(lastSearchKey);
    Duration duration = DateTime.now().difference(now);
    var x = kSearchMinTime - duration;
    if (x.inMilliseconds > 0) {
      await Future.delayed(x);
    }
    if (searchKey != lastSearchKey) {
      result = null;
      if (searchKey == null) {
        return null;
      } else {
        return await loadData(pageSize, pageNum);
      }
    } else {
      return result;
    }
  }

  Future<T> doSearch(String key);

  void clear() {
    searchKey = null;
    release();
    setIdle();
  }
}
