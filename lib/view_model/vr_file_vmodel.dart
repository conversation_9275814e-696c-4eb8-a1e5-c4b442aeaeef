import 'package:collection/collection.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:yvr_assistant/network/vr_request.dart';
import 'package:yvr_assistant/provider/view_state_model.dart';
import '../generated/l10n.dart';
import '../model/vr_file_model.dart';
import '../public_ui/widget/toast_show.dart';
import '../utils/log.dart';

class VrFileVModel extends ViewStateModel {
  int page = 1;
  List rangeList = [];
  List originList = [];
  bool isShowNoMoreWidget = false;
  RefreshController controller = RefreshController(initialRefresh: false);
  bool isLoadError = false;

  initData({int pages = 1, @required String snCode}) async {
    setBusy();
    bool isLoadError = false;
    dynamic fileModels;
    try {
      fileModels = await VrHttp.doQueryFiles(pages: pages, snCode: snCode);
      setIdle();
    } catch (e, s) {
      if (rangeList.length > 0) {
        Log.d("获取数据失败：$page");
        page--;
        isLoadError = true;
        controller.loadComplete();
      }
      setError(e, s);
    }

    List<VrFileModel> models = fileModels ?? [];
    List tempList = [];
    if (isLoadError) {
      models = [];
    }
    if (models.length != 0 && !originList.contains(models.first)) {
      String today = DateUtil.formatDate(DateTime.now(), format: "yyyy-MM-dd");
      int day = DateUtil.getDateMsByTimeStr(today);
      double beforeWeekDay = (day - 7 * 24 * 60 * 60 * 1000) / 1000;
      models.forEach((element) {
        if (int.parse(element.date) > beforeWeekDay) {
          element.title = YLocal.current.zuijinyizhou;
        } else {
          element.title = DateUtil.formatDateMs(int.parse(element.date) * 1000,
              format: "yyyy-MM-dd");
        }
      });

      Map groupData = groupBy(models, (VrFileModel file) => file.title);
      groupData.forEach((key, value) {
        tempList.add({"title": key, "files": value});
      });
    } else {
      models = [];
    }
    if (page == 1) {
      isShowNoMoreWidget = false;
      rangeList.clear();
      originList.clear();
      if (tempList.length != 0) {
        rangeList.addAll(tempList);
        originList.addAll(models);
        Log.d("新加载: ${models.length}条数据");
      }
      controller.refreshCompleted();
    } else if (models.length > 0) {
      if (isLoadError) {
        YvrToast.showToast(YLocal.current.shujuqingqiuchaoshij);
      } else if (tempList.length > 0) {
        Log.d("加载更多: ${models.length}条数据");
        String lastTitle = rangeList.last["title"];
        String firstTitle = tempList.first["title"];
        if (lastTitle == firstTitle) {
          rangeList.last["files"].addAll(tempList.first["files"]);
          if (tempList.length > 0) {
            tempList.removeAt(0);
          }
        }
        rangeList.addAll(tempList);
        originList.addAll(models);
        controller.loadComplete();
      }
    }
    if (rangeList.length == 0) {
      isShowNoMoreWidget = false;
    } else if (models.length < 10 && !isLoadError) {
      isShowNoMoreWidget = true;
      controller.loadNoData();
    } else {
      isShowNoMoreWidget = false;
    }
    notifyListeners();
  }

  bool remove(int sectionIdx, int itemIdx) {
    List selFileModels = rangeList[sectionIdx]["files"];
    selFileModels.removeAt(itemIdx);
    if (selFileModels.isEmpty) {
      rangeList.removeAt(sectionIdx);
      if (rangeList.isEmpty) {
        isShowNoMoreWidget = false;
      }
      notifyListeners();
      return true;
    } else {
      notifyListeners();
      return false;
    }
  }
}
