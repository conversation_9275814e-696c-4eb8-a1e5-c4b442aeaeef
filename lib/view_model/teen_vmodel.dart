import 'package:flutter/foundation.dart';
import 'package:yvr_assistant/public_ui/widget/period_time_selector.dart';

import '../generated/l10n.dart';
import '../model/teen_model.dart';
import '../network/http.dart';
import '../network/request.dart';
import '../provider/view_state_model.dart';
import '../public_ui/widget/toast_show.dart';
import '../utils/date_time_utils.dart';

class TeenVModel extends ViewStateModel2<TeenModeModel> {
  String devId;
  String passwd;
  bool isRequesting = false;
  DateTime _lastDateTime;
  List<PeriodTime> weekdayTimes;
  List<PeriodTime> weekendTimes;
  List<MapEntry<PeriodTime, bool>> times;
  VoidCallback onDevDirty;
  VoidCallback onModeDirty;
  VoidCallback onPasswdDirty;
  VoidCallback onTeenDisabled;
  bool hasFreeWeekdayTime = false;
  bool hasFreeWeekendTime = false;

  TeenVModel(
    this.devId,
    this.passwd, {
    bool refreshEnabled = false,
    bool loadEnabled = false,
    @required this.onDevDirty,
    @required this.onModeDirty,
    @required this.onPasswdDirty,
    @required this.onTeenDisabled,
  }) : super(refreshEnabled: refreshEnabled, loadEnabled: loadEnabled);

  bool get isOpen => model.mode == 1;

  void applyModel(TeenModeModel data) {
    this.model = data;
    _processTimes(_lastDateTime, data);
    notifyListeners();
  }

  void setOpen(bool open) {
    isOpen = open;
    notifyListeners();
  }

  set isOpen(bool open) {
    model.mode = open ? 1 : 0;
  }

  @override
  void release() {
    super.release();
    _lastDateTime = null;
    times = null;
    weekdayTimes = null;
    weekendTimes = null;
  }

  void toggle() {
    if (isRequesting) {
      return;
    }
    final open = !isOpen;
    isRequesting = true;
    if (open) {
      YvrRequests.openTeenagerMode(passwd, devId).then((value) {
        if (checkAndShowToast(value.errCode)) {
          isOpen = open;
          notifyListeners();
        } else {
          switch (value.errCode) {
            case 10035:
              YvrToast.showToast(YLocal.current.gaishebeibucunzai);
              onDevDirty?.call();
              break;
          }
        }
      }).catchError((e, s) {
        YvrToast.showExceptionMessage(e);
      }).whenComplete(() {
        isRequesting = false;
      });
    } else {
      YvrRequests.closeTeenagerMode(passwd, devId).then((value) {
        if (checkAndShowToast(value.errCode)) {
          this.isOpen = open;
          notifyListeners();
        } else {
          switch (value.errCode) {
            case 10035:
              YvrToast.showToast(YLocal.current.gaishebeibucunzai);
              release();
              onDevDirty?.call();
              break;
            case 10830:
              YvrToast.showToast(YLocal.current.qingxiandakaiqingsha);
              release();
              onModeDirty?.call();
              break;
            case 10831:
              YvrToast.showToast(YLocal.current.mimacuowu);
              release();
              onPasswdDirty?.call();
              break;
          }
        }
      }).catchError((e, s) {
        YvrToast.showExceptionMessage(e);
      }).whenComplete(() {
        isRequesting = false;
      });
    }
  }

  @override
  void onRefreshComplete(TeenModeModel data) {
    super.onRefreshComplete(data);
    _processTimes(_lastDateTime, data);
  }

  void _processTimes(DateTime lastDateTime, TeenModeModel data) {
    String dayTime = data.daytime;
    String weekday = data.weekday;
    String weekend = data.weekend;
    String t;
    int weekDay = lastDateTime.weekday;
    if (dayTime != null) {
      t = dayTime;
    } else if (weekDay != DateTime.sunday && weekDay != DateTime.saturday) {
      t = weekday;
    } else {
      t = weekend;
    }
    List<PeriodTime> today;
    if (t == null) {
      today = [PeriodTime(0, 24)];
      times = [MapEntry<PeriodTime, bool>(PeriodTime(0, 24), true)];
    } else if (t.isEmpty) {
      today = List.empty(growable: true);
      times = List.empty(growable: true);
    } else {
      today = parseTimeDuringList(t);
    }
    times = today.map<MapEntry<PeriodTime, bool>>((e) {
      bool during = false;
      DateTime now = _lastDateTime;
      DateTime start = toDateTime(e.start);
      DateTime end = toDateTime(e.end);
      if (!now.isBefore(start) && now.isBefore(end)) {
        during = true;
      }
      return MapEntry<PeriodTime, bool>(e, during);
    }).toList();
    weekdayTimes = parseTimeDuringList(weekday);
    weekendTimes = parseTimeDuringList(weekend);
    hasFreeWeekdayTime = hasMoreFreeTime(weekdayTimes);
    hasFreeWeekendTime = hasMoreFreeTime(weekendTimes);
  }

  void removeWeekday(PeriodTime value) {
    var wdTimes = weekdayTimes.toList()..remove(value);
    var weTimes = weekendTimes.toList();
    _updateTime(wdTimes, weTimes);
  }

  void removeWeekend(PeriodTime value) {
    var wdTimes = weekdayTimes.toList();
    var weTimes = weekendTimes.toList()..remove(value);
    _updateTime(wdTimes, weTimes);
  }

  void updateWeekdayTime(List<PeriodTime> values) {
    var wdTimes = values;
    var weTimes = weekendTimes.toList();
    _updateTime(wdTimes, weTimes);
  }

  void updateWeekendTime(List<PeriodTime> values) {
    var wdTimes = weekdayTimes.toList();
    var weTimes = values;
    _updateTime(wdTimes, weTimes);
  }

  void updateCurrentTime(List<PeriodTime> values) {
    String daytime = values == null ? null : toMultipleTimeText(values);
    YvrRequests.setTeenagerDayTime(devId, daytime).then((value) {
      if (checkAndShowToast(value.errCode)) {
        _lastDateTime = DateTimeUtils.parseHttpTime(value.timestamp);
        model.daytime = daytime;
        _processTimes(_lastDateTime, model);
        notifyListeners();
        // refreshQuietly();
      } else {
        switch (value.errCode) {
          case 10035:
            YvrToast.showToast(YLocal.current.gaishebeibucunzai);
            release();
            onDevDirty?.call();
            break;
          case 10830:
            YvrToast.showToast(YLocal.current.qingxiandakaiqingsha);
            release();
            onModeDirty?.call();
            break;
          case 10832:
            YvrToast.showToast(YLocal.current.qingshaonianmoshiyig_1);
            model.mode = 0;
            onTeenDisabled?.call();
            notifyListeners();
            break;
        }
      }
    }).catchError((e, s) {
      YvrToast.showExceptionMessage(e);
    });
  }

  void _updateTime(List<PeriodTime> wdTimes, List<PeriodTime> weTimes) {
    String weekday = wdTimes == null ? null : toMultipleTimeText(wdTimes);
    String weekend = weTimes == null ? null : toMultipleTimeText(weTimes);
    YvrRequests.changeTeenagerTime(devId, weekday, weekend).then((value) {
      if (checkAndShowToast(value.errCode)) {
        _lastDateTime = DateTimeUtils.parseHttpTime(value.timestamp);
        model.weekday = weekday;
        model.weekend = weekend;
        _processTimes(_lastDateTime, model);
        notifyListeners();
      } else {
        switch (value.errCode) {
          case 10035:
            YvrToast.showToast(YLocal.current.gaishebeibucunzai);
            release();
            onDevDirty?.call();
            break;
          case 10830:
            YvrToast.showToast(YLocal.current.qingxiandakaiqingsha);
            release();
            onModeDirty?.call();
            break;
          case 10832:
            YvrToast.showToast(YLocal.current.qingshaonianmoshiyig_1);
            model.mode = 0;
            onTeenDisabled?.call();
            notifyListeners();
            break;
        }
      }
    }).catchError((e, s) {
      YvrToast.showExceptionMessage(e);
    });
  }

  List<PeriodTime> times2ParsedTime() {
    return times.map<PeriodTime>((e) => e.key).toList();
  }

  String toMultipleTimeText(List<PeriodTime> time) {
    return time.map<String>((e) => toTimeText(e)).join(",");
  }

  String toTimeText(PeriodTime time) {
    return time.toTimeText();
  }

  static List<PeriodTime> parseTimeDuringList(String ts) {
    if (ts.isEmpty) {
      return List.empty(growable: true);
    }
    return ts.split(',').map<PeriodTime>((e) => parseTimeDuring(e)).toList()
      ..sort(periodTimeSortFun);
  }

  static PeriodTime parseTimeDuring(String t) {
    return PeriodTime.parseTimeDuring(t);
  }

  static bool hasMoreFreeTime(List<PeriodTime> sortedTime) {
    if (sortedTime.isEmpty) {
      return true;
    }
    if (sortedTime.length == 1) {
      return !sortedTime[0].isAllDay();
    }
    if (sortedTime[0].start > 0 || sortedTime.last.end < 24) {
      ///0点可选或者24点可选
      return true;
    }

    int i = 1;
    do {
      int prevEnd = sortedTime[i - 1].end;
      int currentStart = sortedTime[i].start;
      if (prevEnd != currentStart) {
        //中间有空闲时间
        return true;
      }
      ++i;
    } while (i < sortedTime.length);
    return false;
  }

  DateTime toDateTime(int hour) {
    DateTime result;
    if (_lastDateTime.isUtc) {
      result = DateTime.utc(
          _lastDateTime.year, _lastDateTime.month, _lastDateTime.day, hour);
    } else {
      result = DateTime(
          _lastDateTime.year, _lastDateTime.month, _lastDateTime.day, hour);
    }
    return result;
  }

  @override
  Future<TeenModeModel> loadData(int pageSize, int pageNum) async {
    var result = await YvrRequests.getTeenagerInfo(devId);
    checkAndThrowException(result.errCode);
    _lastDateTime = DateTimeUtils.parseHttpTime(result.timestamp);
    return result.data;
  }
}
