
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';

import '../network/request.dart';
import '../provider/view_state_model.dart';

class EmailValidationVModel extends ViewStateModel{

  String _email;
  String get email => _email;
  dynamic arguments;
  EmailValidationVModel(dynamic arguments){
    this.arguments = arguments;
    _email = arguments['email'];
  }

  bool isResetSend = true;
  //邮箱注册
  Future<void> registerEmail() async {
    if(isResetSend){
      isResetSend = false;
      Future.delayed(Duration(seconds: 60), () {
        isResetSend = true;
      });
      if(arguments != null && arguments['email'] != null){
        YvrToast.showLoading();
        try{
          await YvrRequests.registerEmail(arguments);
          // YvrToast.showToast('sending succeeded');
          YvrToast.showToast('Email has been sent again');
        } catch (e, s) {
          setError(e, s);
        }
        YvrToast.dismiss();
      }
    }else{
      YvrToast.showToast('Please do not resend frequently');
    }



  }
}