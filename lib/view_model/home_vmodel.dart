import 'package:flutter/cupertino.dart';
import 'package:yvr_assistant/model/home_model.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/provider/view_state_model.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';

import '../model/subject_model.dart';
import '../model/summary_info_model.dart';

class HomeVModel extends ViewStateModel {
  HomeModel homeList;

  initData() async {
    setBusy();
    try {
      homeList = await YvrRequests.getAppsRecmd();
      setIdle();
    } catch (e, s) {
      setError(e, s);
    }
  }

  Future<void> refreshQuietly() async {
    try {
      homeList = await YvrRequests.getAppsRecmd();
      setIdle();
    } catch (e, s) {
      print(s);
    }
  }

  //区分是跳转专题应用还是捆绑应用
  subjectJump(BuildContext context, int id) async {
    YvrToast.showLoading();
    try {
      HomeModel homeModel = await YvrRequests.getAppsRecmd();
      if (homeModel.themes != null && homeModel.themes.isNotEmpty) {
        for (int i = 0; i < homeModel.themes.length; i++) {
          List<SubjectModel> subjects = homeModel.themes[i].subjects;
          if (subjects != null && subjects.isNotEmpty) {
            for (int j = 0; j < subjects.length; j++) {
              if (id == subjects[j].id) {
                SummaryInfoModel summaryInfo = subjects[j].summaryInfo;
                //捆绑包专题
                if (summaryInfo != null &&
                    summaryInfo.bundle != null &&
                    summaryInfo.bundle) {
                  Navigator.pushNamed(context, '/bundledApplication',
                      arguments: {'mpId': summaryInfo.subId});
                } else {
                  //非捆绑包专题
                  Navigator.pushNamed(context, '/apps', arguments: {
                    "isSingleSubject": true,
                    "title": subjects[j].name,
                    "id": subjects[j].id,
                    "type": subjects[j].type,
                  });
                }
                YvrToast.dismiss();
                Future.delayed(Duration(seconds: 2), () {
                  refreshQuietly();
                });
                return;
              }
            }
          }
        }
      }
    } catch (e, s) {
      setError(e, s);
    }
    YvrToast.dismiss();
  }
}
