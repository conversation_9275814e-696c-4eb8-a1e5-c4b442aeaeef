import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/view_model/device_list_vmodel.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import '../manager/global.dart';
import '../pages/device/tool/file_handle.dart';
import '../provider/view_state_model.dart';

class AddDeviceVModel extends ViewStateModel {
  BuildContext _context;
  double marginW = 49;
  double rippleH = Global.screenWidth - 98;
  // ignore: unused_field
  List<String> _deviceIds = []; //已添加的设备id列表
  // 搜索状态: 0-引导页  1-搜索中  2-未搜索到  3-搜索到设备
  ValueNotifier<int> searchState = ValueNotifier<int>(0);
  ValueNotifier<bool> showStaticPic = ValueNotifier<bool>(false);
  ValueNotifier<bool> showAddDeviceView = ValueNotifier<bool>(true);
  bool get scanDevicesNoempty => scanDevices != null && scanDevices.isNotEmpty;
  int get scanDevicesLength => scanDevicesNoempty ? scanDevices.length : 0;
  List<Map<String, dynamic>> scanDevices;
  //波纹动画控制器
  AnimationController _rippleController;
  //雷达扫描动画控制器
  AnimationController _radarController;
  Timer _mTimer;

  DeviceListVModel _deviceListVModel = DeviceListVModel.instance;

  init(BuildContext context, List<String> deviceIds) async {
    Log.d('deviceIds =====添加设备页======>${deviceIds.toString()}');
    _context = context;
    _deviceIds = deviceIds;
  }

  setController(AnimationController rippleController,
      AnimationController radarController) async {
    this._rippleController = rippleController;
    this._radarController = radarController;
  }

  void startAmin() {
    if (_rippleController != null && _radarController != null) {
      _rippleController.repeat();
      _radarController.repeat();
    }
  }

  void stopAmin() {
    if (_rippleController != null && _radarController != null) {
      _rippleController.stop();
      _radarController.stop();
    }
  }

  bool isScan = true;
  //扫描权限检查
  scanPermissionCheck({bool b = true}) {
    if (isScan) {
      isScan = false;
      Future.delayed(const Duration(seconds: 3), () => isScan = true);
      requestLocationPermission2(_context, callBack: () {
        FlutterBluePlus.adapterState.listen((state) async {
          if (state == BluetoothAdapterState.on && b) {
            b = false;
            startScan();
          } else {
            YvrToast.showToast(YLocal.current.lanheweikaiqixianggu);
          }
        });
      });
    }
  }

  // 搜索状态: 0-引导页  1-搜索中  2-未搜索到  3-搜索到设备
  void setSearchState(int state) {
    searchState.value = state;
    if (state != 0 && showAddDeviceView.value) {
      showAddDeviceView.value = false;
    } else if (state == 0 && !showAddDeviceView.value) {
      showAddDeviceView.value = true;
    }
    Future.delayed(const Duration(milliseconds: 100), () {
      if (state == 1) {
        startAmin();
      } else {
        stopAmin();
      }
    });
  }

  startTimer(int time, Function callBack) {
    _mTimer?.cancel();
    _mTimer = Timer(Duration(milliseconds: time), callBack);
  }

  //开启蓝牙扫描
  startScan() {
    _deviceListVModel.startScan(isAddDevice: true);
    setSearchState(1);
    startTimer(3000, () {
      stopAmin();
    });
  }

  //更新设备列表
  void setScanDevices(List<Map<String, dynamic>> list) {
    scanDevices = list;
    setSearchState(scanDevicesNoempty ? 3 : 2);
  }

  @override
  void dispose() {
    super.dispose();
    stopAmin();
    scanDevices?.clear();
    _mTimer?.cancel();
    _deviceListVModel.removeListener(() {});
  }
}
