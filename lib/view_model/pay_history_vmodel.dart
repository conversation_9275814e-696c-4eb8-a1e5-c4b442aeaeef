import 'package:yvr_assistant/model/pay_history_model.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/provider/view_state_model.dart';

class PayHistoryVModel extends ViewStateModel {
  List<PayHistoryModel> historyModels;
  initData() async {
    setBusy();
    try {
      historyModels = await YvrRequests.purchaseAppList();
      setIdle();
    } catch (e, s) {
      setError(e, s);
    }
  }
}
