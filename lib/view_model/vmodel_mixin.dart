import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import '../generated/l10n.dart';
import '../public_ui/widget/toast_show.dart';

class UploadImageData {
  String networkUrl;
  AssetEntity localFile;

  UploadImageData({this.localFile, this.networkUrl});

  bool get hasLocal => localFile != null;

  bool get hasNetwork => networkUrl != null;
}

class UploadImageException implements Exception {
  String message;

  UploadImageException([this.message = ""]);
}

mixin UploadImageViewModel on ChangeNotifier {
  List<UploadImageData> uploadImageList = List.empty(growable: true);

  void addLocalImages(Iterable<AssetEntity> images) {
    uploadImageList.addAll(
      images.map<UploadImageData>(
        (e) => UploadImageData(
          localFile: e,
        ),
      ),
    );
    notifyListeners();
  }

  void replaceLocalImages(Iterable<AssetEntity> images) {
    uploadImageList.clear();
    uploadImageList.addAll(
      images.map<UploadImageData>(
        (e) => UploadImageData(
          localFile: e,
        ),
      ),
    );
    notifyListeners();
  }

  void removeUploadImageAt(int index) {
    uploadImageList.removeAt(index);
    notifyListeners();
  }

  int uploadImageSize() {
    return uploadImageList.length;
  }

  void clearUploadImages() {
    uploadImageList.clear();
    notifyListeners();
  }

  static String getFileSize(File file) {
    final data = file.readAsBytesSync();
    return getBytesSize(data);
  }

  static String getBytesSize(Uint8List data) {
    int bytes = data.lengthInBytes;
    final kb = bytes / 1024;
    final mb = kb / 1024;
    return mb.toStringAsFixed(2) + "MB";
  }

  Future<Uint8List> compressFile(File file) async {
    var result = await FlutterImageCompress.compressWithFile(
      file.absolute.path,
    );
    // debugPrint("compressFile:$file:${getFileSize(file)}=>${getBytesSize(result)}");
    return result;
  }

  void setUploadImageNetworkImage(int index, String url) {
    uploadImageList[index].networkUrl = url;
    notifyListeners();
  }

  @protected
  Future<void> startUploadImages() async {
    for (int i = 0; i < uploadImageList.length; ++i) {
      var d = uploadImageList[i];
      if (!d.hasNetwork) {
        File f = await d.localFile.originFile;
        Uint8List data;
        try {
          data = await compressFile(f);
        } catch (e) {
          throw UploadImageException(YLocal.current.tupianyasushibaitupi);
        }
        String fileName = f.path.split(Platform.pathSeparator).last;
        String url = await startUploadImage(data, fileName);
        d.networkUrl = url;
      }
    }
  }

  @protected
  Future<void> startCancelUploadAllImage() async {
    uploadImageList.removeWhere((element) {
      if (element.hasNetwork) {
        Future.sync(() async {
          await startCancelUploadImage(element);
          element.networkUrl = null;
        });
      }
      return true;
    });
  }

  @protected
  Future<void> startCancelUploadImageAt(int index) async {
    var d = uploadImageList[index];
    if (d.hasNetwork) {
      await startCancelUploadImage(d);
      d.networkUrl = null;
    }
    uploadImageList.remove(d);
  }

  @protected
  Future<String> startUploadImage(Uint8List data, String fileName);

  @protected
  Future<void> startCancelUploadImage(UploadImageData data);
}

class UploadImageFileData {
  String networkUrl;
  File localFile;

  UploadImageFileData({this.localFile, this.networkUrl});

  bool get hasLocal => localFile != null;

  bool get hasNetwork => networkUrl != null;
}

class UploadImageFileViewModel extends ChangeNotifier {
  List<UploadImageFileData> uploadImageList = List.empty(growable: true);

  Future<String> Function(Uint8List data, String fileName) startUploadImage;

  UploadImageFileViewModel(this.startUploadImage);

  void startUploadImageFile(File file,
      {VoidCallback onSuccess, VoidCallback onComplete}) {
    replaceLocalImages([file]);
    notifyListeners();
    startUploadImages().then((value) {
      onSuccess?.call();
    }).catchError((e, s) {
      if (e is UploadImageException) {
        YvrToast.showToast(e.message);
      } else {
        YvrToast.showExceptionMessage(e);
      }
    }).whenComplete(() {
      onComplete?.call();
      notifyListeners();
    });
  }

  void replaceLocalImages(Iterable<File> images) {
    uploadImageList.clear();
    uploadImageList.addAll(
      images.map<UploadImageFileData>(
        (e) => UploadImageFileData(
          localFile: e,
        ),
      ),
    );
    notifyListeners();
  }

  int uploadImageSize() {
    return uploadImageList.length;
  }

  Future<Uint8List> compressFile(File file) async {
    var result = await FlutterImageCompress.compressWithFile(
      file.absolute.path,
    );
    return result;
  }

  void setUploadImageNetworkImage(int index, String url) {
    uploadImageList[index].networkUrl = url;
    notifyListeners();
  }

  @protected
  Future<void> startUploadImages() async {
    for (int i = 0; i < uploadImageList.length; ++i) {
      var d = uploadImageList[i];
      if (!d.hasNetwork) {
        File f = d.localFile;
        Uint8List data;
        try {
          data = await compressFile(f);
        } catch (e) {
          throw UploadImageException(YLocal.current.tupianyasushibaitupi);
        }
        String fileName = f.path.split(Platform.pathSeparator).last;
        String url = await startUploadImage(data, fileName);
        d.networkUrl = url;
      }
    }
  }
}
