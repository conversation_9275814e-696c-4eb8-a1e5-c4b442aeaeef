import 'package:flutter/cupertino.dart';
import 'package:yvr_assistant/base/multiple_progress_button_state_mixin.dart';
import 'package:yvr_assistant/model/noti_model.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/network/socket.dart';
import 'package:yvr_assistant/network/ws_request.dart';
import 'package:yvr_assistant/provider/view_state_model.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';

import '../model/bundled_info_model.dart';
import '../network/http.dart';

class NotiVModel extends ViewStateModel2<List<NotiModel>>
    with MultipleProgressButtonProviderMixin<NotiModel> {
  @override
  Future<List<NotiModel>> loadData(int pageSize, int pageNum) async {
    List<NotiModel> notiModels = await YvrRequests.getAllNtyList(null);
    notiModels = notiModels
        .where((element) => element.tag != "recvAppPayDevHandler")
        .toList();
    notiModels.sort((a, b) => b.time.compareTo((a.time)));
    return notiModels;
  }

  @override
  bool get isEmpty => model == null || model.isEmpty;

  void remove(NotiModel model) {
    this.model?.remove(model);
    notifyListeners();
  }

  void agreeFriendInvitation(NotiModel model) {
    progressExecute<WSResponseData<void>>(model, computation: () async {
      var result = await YvrWSRequests.disposeFdInvite(model.id, 1);
      checkAndThrowWSException(result.errCode);
      return result;
    }, onSuccess: (result) {
      progressComplete(model);
      if (checkAndShowWSToast(
        result.errCode,
      )) {
        model.status = 1;
        notifyListeners();
        refreshQuietly();
      } else {
        // 10730	非法的通知id
        // 10731	该通知id已处理过，不能再次处理
        refreshQuietly();
      }
    }, onError: (e, s) {
      YvrToast.showExceptionMessage(e);
    });
  }

  //查询专题信息
  getAppsOnSubject(BuildContext context,dynamic appId) async {
    YvrToast.showLoading();
    Map<String, dynamic> parameters = {'subjectId': appId};
    try{
      ResponseData<BundledInfoModel> object = await YvrRequests.getBundledInfo(parameters);
      BundledInfoModel bundledInfo = object.data;
      if(bundledInfo.subSaleSummaryInfo != null && bundledInfo.subSaleSummaryInfo.bundle){
        Navigator.pushNamed(context, '/bundledApplication',arguments: {'mpId': appId});
      }else{
        Navigator.pushNamed(context, '/apps',arguments: {"isSingleSubject": true, "title": "", "id": appId});
      }
    } catch (e, s) {
      setError(e, s);
    }
    YvrToast.dismiss();
  }
}
