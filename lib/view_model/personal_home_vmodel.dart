import 'dart:math';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/base/multiple_progress_button_state_mixin.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/network/socket.dart';
import 'package:yvr_assistant/network/ws_request.dart';
import 'package:yvr_assistant/provider/view_state_model.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';

import '../generated/l10n.dart';
import '../model/personal_home_model.dart';
import '../public_ui/widget/toast_show.dart';
import '../utils/date_time_utils.dart';

class UserInfoVModel extends ViewStateModel2<PersonalHomeModel>
    with MultipleProgressButtonProviderMixin<int> {
  DateTime userBirthday;
  String userBirthdayText;

  static const int kType = 1;

  @override
  Future<PersonalHomeModel> loadData(int pageSize, int pageNum) async {
    var result = await YvrRequests.getPersonalHome();
    checkAndThrowException(result.errCode);
    return result.data;
  }

  ProgressState getProgressState() {
    return progressState(kType);
  }

  void updateUserInfo(
      {String nick,
      String birth,
      int sex,
      String motto,
      VoidCallback onSuccess}) {
    progressExecute<ResponseData<void>>(kType, computation: () async {
      var result = await YvrRequests.updateUserInfo(
          nick: nick, birth: birth, sex: sex, motto: motto);
      return result;
    }, onSuccess: (result) {
      String invalidMessage;
      if (result.errCode == 10540) {
        if (nick != null) {
          invalidMessage = YLocal.current.nindeyonghunichengsh;
        } else if (motto != null) {
          invalidMessage = YLocal.current.nindegexingqianmings;
        }
      }

      if (checkAndShowWSToast(result.errCode,
          errorMatcher: {10540: invalidMessage})) {
        if (nick != null) {
          model.nick = nick;
        }
        if (birth != null) {
          model.birth = birth;
        }
        if (sex != null) {
          model.sex = sex;
        }
        if (motto != null) {
          model.motto = motto;
        }
        onRefreshComplete(model);
        onSuccess?.call();
      }
      progressComplete(kType);
    }, onError: (e, s) {
      YvrToast.showExceptionMessage(e);
    });
  }

  @override
  void onRefreshComplete(PersonalHomeModel data) {
    List<int> date =
        data.birth?.split('-')?.map<int>((e) => int.parse(e))?.toList();
    if (date == null) {
      userBirthday = null;
      userBirthdayText = '';
    } else {
      userBirthday = DateTime(date[0], date[1], date[2]);
      userBirthdayText =
          DateTimeUtils.formatTime(userBirthday, pattern: 'yyyy-MM-dd');
    }
  }
}

class PersonalHomeVModel extends ViewStateModel2<PersonalHomeModel> {
  double mottoHeight = 0;
  final double mottoWidth;
  final TextStyle mottoTextStyle;
  List<String> appDurations;
  final int actId;

  PersonalHomeVModel(this.mottoWidth, this.mottoTextStyle, {this.actId});

  @override
  void onRefreshComplete(PersonalHomeModel data) {
    super.onRefreshComplete(data);
    TextPainter textPainter = TextPainter(
        textDirection: TextDirection.ltr,
        text: TextSpan(
          text: data.motto,
          style: mottoTextStyle,
        ));
    textPainter.layout(maxWidth: mottoWidth);
    mottoHeight = textPainter.height;

    appDurations = data.apps?.map<String>((e) {
          Duration time = Duration(minutes: e.time ?? 0);
          var num = time.inMinutes;
          if (num < 30) {
            return YLocal.current.Nfenzhong(30);
          } else {
            num = max<int>(time.inHours, 1);
            return YLocal.current.Nxiaoshi(num);
          }
        })?.toList() ??
        List.empty();
  }

  // 用户关系 -1自己 0陌生人 1好友 2黑名单 3被黑名单
  bool isSelf() => actId == null || model.relate == -1;

  bool editVisible() => isSelf();

  bool friendOptionVisible() => actId != null && model.relate == 1;

  bool blackListOptionVisible() => isSelf();

  bool removeBlackListVisible() => actId != null && model.relate == 2;

  bool addFriendVisible() => actId != null && model.relate == 0;

  int isOnline() => isSelf() ? 1 : model.online;

  @override
  Future<PersonalHomeModel> loadData(int pageSize, int pageNum) async {
    if (actId != null) {
      var result = await YvrWSRequests.getPersonalHome(actId);
      checkAndThrowWSException(result.errCode);
      return result.data;
    } else {
      var result = await YvrRequests.getPersonalHome();
      checkAndThrowException(result.errCode);
      return result.data;
    }
  }
}
