import 'dart:async';
import 'dart:convert';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:wifi_iot/wifi_iot.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:notification_listener_service/notification_event.dart';
import 'package:notification_listener_service/notification_listener_service.dart';

import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/utils/channel.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/model/dev_model.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/pages/home/<USER>';
import 'package:yvr_assistant/model/teen_model.dart';
import 'package:yvr_assistant/model/user_model.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/utils/native_utils.dart';
import 'package:yvr_assistant/provider/view_state.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';
import 'package:yvr_assistant/model/dev_info_model.dart';
import 'package:yvr_assistant/utils/ble_data_buffer.dart';
import 'package:yvr_assistant/utils/permission_util.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/pages/home/<USER>/home_tool.dart';
import 'package:yvr_assistant/provider/view_state_model.dart';
import 'package:yvr_assistant/pages/device/tool/dev_tool.dart';
import 'package:yvr_assistant/pages/device/tool/ble_func.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/pages/device/tool/ble_state.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/pages/device/views/ble_dialog.dart';
import 'package:yvr_assistant/pages/device/tool/file_handle.dart';

class DeviceListVModel extends ViewStateModel {
  // 构造函数私有化，防止外部创建实例
  DeviceListVModel._();

  // 创建单例实例
  static final DeviceListVModel _instance = DeviceListVModel._();

  // 提供公共访问点
  static DeviceListVModel get instance => _instance;

  BuildContext _context;
  final String kUser = 'kUser';
  UserModel _user;
  UserModel get user => _user;
  List<DevInfoModel> _deviceResult;
  List<DevInfoModel> get deviceResult => _deviceResult;
  ScrollController childScrollView = ScrollController();
  List<DevModel> _deviceModels = []; //设备模型列表
  List<DevModel> get deviceModels => _deviceModels;
  List<String> _deviceIds = []; //设备id列表
  List<String> get deviceIds => _deviceIds;
  String _connectedDevId; //蓝牙连接成功后的设备id
  String get connectedDevId => _connectedDevId;
  BluetoothDevice _connectedDevice; //蓝牙连接成功后的设备信息
  BluetoothDevice get connectedDevice => _connectedDevice;
  String _phoneModel = StorageManager.deviceModel +
      "::${Global.screenWidth.toInt()}X${Global.screenHeight.toInt()}";
  bool _isRemindVRNoWiFi = false;
  String _connectedDevName = "";
  String _password;
  int _battery; //设备电量
  List _vrSsidWraps = [];
  List _vr3dPhotoThum = [];
  List _vr3dVideoThum = [];
  BluetoothCharacteristic _baseChar;
  final BleDataBuffer bleBuffer = BleDataBuffer();
  StreamSubscription _stateSubscription;
  StreamSubscription _baseStream;
  bool isFirstLoad = true;
  var subscription;
  bool isOpenedBlue = false;
  Map<String, dynamic> scanCacheData = {};
  StreamSubscription _blueStateStream;
  String phoneInfo;
  bool isFromAdd = false;

  StreamSubscription<ServiceNotificationEvent> _subscription;
  List<ServiceNotificationEvent> events = [];
  bool isOpenNotificationDialog = false;

  init(BuildContext context) async {
    isFirstLoad = true;
    _context = context;
    netWorkListener();
    blueStateListener();
    DBUtil.instance.userBox.put(Global.kInProjection, 0);
    if (UserVModel().isLogin()) {
      loadAllData();
      phoneInfo = await PlatformUtils.getPhoneModel();
      Log.d('$phoneInfo');
    }
    FlutterBluePlus.setLogLevel(LogLevel.error, color: true);
  }

  @override
  void dispose() {
    super.dispose();
    resetData();
    _blueStateStream?.cancel();
    DBUtil.instance.userBox.put(Global.kInProjection, 0);
    blueDisconnect();
  }

  //检查权限
  Future<bool> checkPermission() async {
    bool isOpen = false;
    final status = await Permission.location.request();
    if (status == PermissionStatus.granted) {
      if (await NativeUtils.isOpenLocation()) {
        final bool isOpenBleRelationPermissions =
            await PermissionUtil().requestBlePermissions();
        if (isOpenBleRelationPermissions) {
          isOpen = true;
        } else {
          showOpenPhotoDialog(remind: YLocal.current.saomiaolanhexuyaonin);
        }
      } else {
        showOpenPhotoDialog(remind: YLocal.current.saomiaolanhexuyaonin);
      }
    } else {
      loganInfo('device_list_page', '$phoneInfo,定位权限关闭');
      showOpenPhotoDialog(remind: YLocal.current.saomiaolanhexuyaonin);
    }
    return isOpen;
  }

  //蓝牙状态监听
  blueStateListener() async {
    _blueStateStream = FlutterBluePlus.adapterState.listen((state) async {
      if (state == BluetoothAdapterState.on) {
        if (!isFirstLoad && !isOpenedBlue) {
          Log.d('isOpenedBlue ==============>$isOpenedBlue');
          loganInfo('device_list_page', '$phoneInfo,蓝牙开启');
          isOpenedBlue = true;
          Future.delayed(
              const Duration(seconds: 2), () => isOpenedBlue = false);
          resetConnect();
        }
      } else if (state == BluetoothAdapterState.off) {
        // Log.d('----------------------------蓝牙关闭-----------------------');
        loganInfo('device_list_page', '$phoneInfo,蓝牙关闭');
        showBlueDialog();
        await blueDisconnect();
        resetData();
        initData();
        notifyListeners();
      }
    });
  }

  //蓝牙未开启弹窗
  showBlueDialog() {
    showDialog(
        context: _context,
        barrierDismissible: false,
        builder: (_) {
          return CustomDialog(
            title: YLocal.current.dishitishi,
            content: YLocal.current.lanheweikaiqixianggu,
            isCancel: false,
          );
        });
  }

  int networkTag = 0;
  //网络监听
  netWorkListener() {
    if (subscription == null) subscription = Connectivity();
    subscription.onConnectivityChanged
        .listen((ConnectivityResult result) async {
      Log.e("---------------监听到网络变化---------------");
      loganInfo('device_list_page', '$phoneInfo,网络状态变化');

      switch (result) {
        case ConnectivityResult.wifi:
        case ConnectivityResult.mobile:
          if (await checkWifiIsChange()) {
            DBUtil.instance.userBox.put(Global.kInProjection, 0);

            networkTag = 1;
            if (_connectedDevId != null &&
                ObjectUtil.isNotEmpty(_connectedDevId)) {
              Future.delayed(const Duration(seconds: 3), () async {
                if (viewState == ViewState.error) setIdle();
                String ssid = await WiFiForIoTPlugin.getSSID();
                reqNetworkForVR(ssid: ssid, funType: 2);
              });
            } else {
              resetConnect();
            }
          }
          break;
        default:
          networkTag = 0;
          Future.delayed(const Duration(seconds: 3), () {
            if (networkTag == 0) {
              setError2(YLocal.current.wanglaolianjieyiduan);
            }
          });
          break;
      }
    });
  }

  //设置青少年关闭状态
  void setTeenagersCloseState(String id) {
    if (id != null) {
      _deviceModels?.firstWhere((element) => element.devId == id)?.teen = 0;
      notifyListeners();
    }
  }

  //青少年返回刷新
  void teenagersResultRefresh(TeenModeModel teen) {
    if (teen != null) {
      _deviceModels
          ?.firstWhere((element) => element.devId == teen.devId)
          ?.teen = teen.mode;
      notifyListeners();
    }
  }

  //重置数据
  void resetData() {
    _scanResultsSubscription?.cancel();
    _isScanningSubscription?.cancel();
    _stateSubscription?.cancel();
    _baseStream?.cancel();
    _vrSsidWraps?.clear();
    _scanTimer?.cancel();
    _connectTimer?.cancel();
    _connectedDevId = "";
    _connectedDevName = "";
    _connectedDevice = null;
    _baseChar = null;
    _password = '';
    _battery = 0;
    _isRemindVRNoWiFi = false;
    cleanConnectTimer();
  }

  //加载所有数据
  loadAllData({String devId, bool isFromEventBus = false}) {
    getUserInfo();
    getLoginDevice(devId: devId, isFromEventBus: isFromEventBus);
  }

  //获取用户数据
  getUserInfo() async {
    var userMap = StorageManager.localStorage.getItem(kUser);
    if (userMap != null) {
      _user = userMap != null ? UserModel.fromJson(userMap) : null;
      notifyListeners();
    } else {
      try {
        _user = await YvrRequests.getUserInfoFromMobile();
        StorageManager.localStorage.setItem(kUser, _user.toJson());
        notifyListeners();
      } catch (e, s) {
        setError(e, s);
      }
    }
  }

  bool isGotoAddDevice = false;
  //获取登录设备信息
  getLoginDevice({String devId, bool isFromEventBus = false}) async {
    try {
      setBusy();
      _deviceResult = await YvrRequests.getLoginDevInfos();
      isFirstLoad = false;
      if (_deviceResult != null && _deviceResult.isNotEmpty) {
        initData();
        if (await checkPermission()) {
          startScan(devId: devId);
        }
      } else {
        _deviceModels?.clear();
        _deviceIds?.clear();
        blueDisconnectAndReset();
        if (isFromEventBus != null && !isFromEventBus && !isGotoAddDevice) {
          isGotoAddDevice = true;
          Navigator.pushNamed(_context, '/add_search',
              arguments: {"devIds": null});
          eventBus.fire(EventFn({Global.switchTabEventKey: 0}));
          Future.delayed(const Duration(milliseconds: 800), () {
            isGotoAddDevice = false;
          });
        }
      }
      setIdle();
    } catch (e, s) {
      setError(e, s);
    }
  }

  //解绑设备
  unBindDevice(String deviceId) async {
    for (DevModel devModel in _deviceModels) {
      if (devModel.devId == deviceId) {
        if (devModel.teen == 0) {
          YvrToast.showLoading(message: YLocal.current.jiebangzhongxiebangz);
          try {
            Log.d('_deviceIds ===================>$_deviceIds');
            await YvrRequests.unBindDevice({'devId': deviceId});
            _deviceModels = _deviceModels
                .where((element) => element.devId != deviceId)
                .toList();
            _deviceIds = _deviceIds.where((id) => id != deviceId).toList();
            if (_connectedDevId == deviceId) blueDisconnectAndReset();
            Log.d('_deviceIds =======2============>$_deviceIds');
            notifyListeners();
          } catch (e, s) {
            setError(e, s);
          }
          YvrToast.dismiss();
        } else {
          YvrToast.showToast(YLocal.current.qingxianguanbiqingsh);
        }
      }
    }
  }

  //切换设备
  switchDevice(int linkState, String devId) async {
    Log.d('当前已连接设备数：${FlutterBluePlus.connectedDevices.length}');

    if (_deviceModels.length > 1 ||
        FlutterBluePlus.connectedDevices.length == 0) {
      if (_connectedDevId != devId) {
        YvrToast.showLoading(message: YLocal.current.zhengzaiqiehuanshebe);
        startScan(devId: devId);
        Future.delayed(const Duration(seconds: 2), () {
          YvrToast.dismiss();
        });
      } else {
        YvrToast.showToast(YLocal.current.dangqianshebeiyilian);
      }
    }
  }

  //初始化设备信息数据
  initData() {
    if (_deviceResult != null && _deviceResult.isNotEmpty) {
      _deviceModels.clear();
      _deviceIds.clear();
      _deviceResult.forEach((element) {
        DevModel devModel = DevModel();
        devModel.btName = "";
        devModel.devId = element.devId;
        devModel.linkState = 2;
        devModel.sameWiFi = true;
        devModel.getWifiIp = false;
        devModel.auth = element.auth;
        devModel.teen = element.teen;
        _deviceModels.add(devModel);
        _deviceIds.add(element.devId);
      });
    }
  }

  /// 点击重新连接
  bool isResetConnect = true;
  void clickResetConnect() async {
    if (await checkPermission()) {
      if (isResetConnect) {
        isResetConnect = false;
        resetConnect();
        Future.delayed(const Duration(seconds: 8), () {
          isResetConnect = true;
        });
      } else {
        YvrToast.showToast(YLocal.current.shuaxinguoyupinfanqi);
        // ignore: deprecated_member_use
        await FlutterBluePlus.turnOff();
        await Future.delayed(Duration(seconds: 1));
        await FlutterBluePlus.turnOn();
      }
    }
  }

  //重新连接
  resetConnect({String devId, bool isFromEventBus}) {
    resetData();
    loadAllData(devId: devId, isFromEventBus: isFromEventBus);
  }

  Timer _scanTimer;
  Timer _connectTimer;
  int connectCount = 0;
  //啄木鸟计时日志，1扫描，2连接,3获取设备IP
  void zmnConnectLog(int type,
      {bool isAddDevice = false,
      BluetoothDevice device,
      String devId,
      String devName}) {
    if (type == 1) {
      if (_scanTimer != null) _scanTimer?.cancel();
      _scanTimer = Timer(Duration(seconds: 4), () {
        if (_scanDeviceIdList == null || _scanDeviceIdList.isEmpty) {
          // YvrToast.showToast('没有搜索到设备');
          loganInfo('device_list_page', '$phoneInfo,没有搜索到设备');
        }
      });
    } else if (type == 2) {
      if (_connectTimer != null) _connectTimer?.cancel();
      _connectTimer = Timer(Duration(seconds: 6), () {
        if (!isGetVrReply) {
          loganInfo('device_list_page', '$phoneInfo,连接失败,');
          if (!isAddDevice && devId != null && connectCount < 3) {
            connectCount += 1;
            blueConnect(device: device, devId: devId, devName: devName ?? "");
          }
        }
      });
    }
  }

  //清除连接计时
  void cleanConnectTimer() {
    connectCount = 0;
    isGetVrReply = true;
    _connectTimer?.cancel();
  }

  List<String> _scanDeviceIdList = []; //过滤重复id后的集合
  List<Map<String, dynamic>> _scanDevices = []; //扫描设备列表，添加设备时用到
  //开启蓝牙扫描
  startScan({String devId, bool isAddDevice = false}) async {
    await FlutterBluePlus.stopScan();
    _scanDeviceIdList?.clear();
    isFromAdd = isAddDevice;
    if (isAddDevice) {
      Log.d('扫描附近设备。。。😄');
      _scanDevices?.clear();
      resetData();
      startScanBLE();
    } else {
      if (_deviceModels != null && _deviceModels.isNotEmpty) {
        Log.d('开始扫描。。。😄 目标设备ID: $devId');

        YvrToast.showLoading();
        Future.delayed(Duration(seconds: 3), () {
          YvrToast.dismiss();
        });

        resetData();
        startScanBLE(targetDevId: devId);
        zmnConnectLog(1); //啄木鸟计时日志，1扫描，2连接
      }
    }
  }

  //匹配id并且连接
  Future<void> matchingIdConnect(ScanResult result, String devId) async {
    blueConnect(
        device: result.device,
        devId: devId,
        devName: result.device.advName ?? "");
  }

  bool isGetVrReply = false; //是否得到vr回应
  bool isLoginSucceeded = false; //是否登录成功
  bool isGetDeviceIp = false;

  void blueConnect(
      {bool isAddDevice = false,
      BluetoothDevice device,
      String devId,
      String devName}) async {
    Log.d(
        '蓝牙连接 ================>设备id：$devId，设备名称：${devName ?? '无'}，isAddDevice：$isAddDevice');
    _scanTimer?.cancel();
    isGetVrReply = false;
    isLoginSucceeded = false;
    isGetDeviceIp = false;
    await blueDisconnect();
    // await device.disconnect();
    await Future.delayed(Duration(milliseconds: 500));

    _connectedDevId = "";
    _connectedDevice = null;
    zmnConnectLog(2,
        isAddDevice: isAddDevice,
        device: device,
        devId: devId,
        devName: devName); //啄木鸟计时日志，1扫描，2连接
    await device.connect(autoConnect: false).catchError((error) async {
      Log.d("连接失败 =================>$error");
      loganInfo('device_list_page', '$phoneInfo,连接失败,');
      YvrToast.showToast(YLocal.current.lianjieshibai);
    });

    _stateSubscription = device.connectionState.listen((state) async {
      switch (state) {
        case BluetoothConnectionState.disconnected:
          Log.d('----------------断开连接----------------');
          loganInfo('device_list_page', '$phoneInfo,设备断开连接');
          // await blueDisconnect();
          resetData();
          initData();
          notifyListeners();
          break;
        case BluetoothConnectionState.connected:
          Log.d('----------------连接成功----------------devId: $devId');
          initData();
          _connectedDevId = devId;
          _connectedDevice = device;
          writeStreamAfterConnected(isAddDevice, device, devName, devId);
          break;
        default:
          break;
      }
    });
  }

  //蓝牙断开连接并重置数据
  Future<void> blueDisconnectAndReset() async {
    await blueDisconnect();
    resetData();
  }

  //蓝牙断开连接
  Future<void> blueDisconnect() async {
    if (FlutterBluePlus.connectedDevices.isNotEmpty) {
      for (var dev in FlutterBluePlus.connectedDevices) {
        await dev.disconnect();
        // await dev.connectionState
        //     .where((s) => s == BluetoothConnectionState.disconnected)
        //     .first;
        Log.d("-------------断开与 ${dev.platformName} 蓝牙连接--------------");
      }
    }
  }

  void setRemindVRNoWiFi(bool isRemindVRNoWiFi) {
    _isRemindVRNoWiFi = isRemindVRNoWiFi;
  }

  void setPassword(String password) {
    _password = password;
  }

  void sendStreamValue(BleTransmissionStatus status, {String data = "0"}) {
    writeStreamValue(bleChar: _baseChar, status: status, data: data);
  }

  Future<void> writeStreamAfterConnected(bool isAddDevice,
      BluetoothDevice device, String devName, String devId) async {
    // 等待连接状态
    await device.connectionState
        .where((state) => state == BluetoothConnectionState.connected)
        .first;
    List<BluetoothService> services = await device.discoverServices();
    if (services == null || services.isEmpty) return;
    services.forEach((service) {
      // Log.d('service ============蓝牙服务==========> \n${service.toString()}');

      if (service.serviceUuid.toString() == "1000ffe2" && _baseChar == null) {
        Log.d('service.uuid 信息: ${service.uuid.toString()}');
        List<BluetoothCharacteristic> characteristics = service.characteristics;

        characteristics.forEach((characteristic) async {
          String charUuid = characteristic.characteristicUuid.toString();
          if (charUuid == Global.bleCharUUID && _baseChar == null) {
            Log.d('charUuid 信息: $charUuid');
            _baseChar = characteristic;
            _baseChar.setNotifyValue(true);
            _baseStream = _baseChar.lastValueStream.listen((response) {
              listenBleResponse(
                  response: response,
                  devId: devId,
                  device: device,
                  devName: devName,
                  isAddDevice: isAddDevice);
            });
          }
        });
      }
    });
  }

  //请求获取网络设备状态
  reqDeviceNetworkState() async {
    // 询问VR网络情况
    bool isHaveWiFi = await WiFiForIoTPlugin.isConnected();
    Log.d("isHaveWiFi =====================>$isHaveWiFi");
    if (isHaveWiFi) {
      _isRemindVRNoWiFi = false;
      String ssid = await WiFiForIoTPlugin.getSSID();
      await StorageManager.foreverData.setItem("kWifiSsid", ssid);
      //助手请求获取设备网络状态
      reqNetworkForVR(ssid: ssid, funType: 2);
    } else {
      for (int i = 0; i < _deviceModels.length; i++) {
        if (_deviceModels[i].devId == _connectedDevId) {
          Log.d("------------非相同WiFi-2------------");
          _deviceModels[i].sameWiFi = false;
          _deviceModels[i].getWifiIp = false;
          notifyListeners();
          break;
        }
      }
    }
  }

  //VR是否同意添加设备状态1：同意 0：拒绝
  agreeAddDevice(bool isAddDevice, int dataForByte) {
    Log.d("VR回复是否添加 ================> $dataForByte，isAddDevice：$isAddDevice");
    // 是否同意绑定
    if (dataForByte == 0) {
      Log.d("VR拒绝登录");
      Future.delayed(const Duration(milliseconds: 500), () async {
        // 蓝牙连接结果：0-连接中 1-连接失败 2-VR端拒绝 3-账号绑定成功
        if (isAddDevice) eventBus.fire(EventFn({'kResultTypeForAddVR': 2}));
      });
    } else if (dataForByte == 1) {
      Log.d("VR允许登录");
      Future.delayed(const Duration(milliseconds: 500), () async {
        String ssid = await WiFiForIoTPlugin.getSSID();
        Log.d("获取ssid: $ssid");
        writeStreamValue(
            bleChar: _baseChar,
            status: BleTransmissionStatus.REQ_NETWORK,
            data: ssid ?? "0");
      });
    } else if (dataForByte == 2) {
      Future.delayed(const Duration(milliseconds: 500), () async {
        // 蓝牙连接结果：0-连接中 1-连接失败 2-VR端拒绝 3-账号绑定成功
        if (isAddDevice) eventBus.fire(EventFn({'kResultTypeForAddVR': 4}));
      });
    }
  }

  sendBleStartRecordingOnlyProj() {
    Log.d('---------------------拼接录制通知----------------------');
    // 记录蓝牙发出时间戳
    StorageManager.localStorage.setItem(
        Global.kBleSendTimestamp, "${DateTime.now().millisecondsSinceEpoch}");

    int actId = DBUtil.instance.userBox.get(kActId);
    sendStreamValue(BleTransmissionStatus.REQ_START_RECORDER_AND_SHARE,
        data: "$actId");
  }

  //回复手机助手设备网络状态 0：不连通 1：联通  2：同一个WiFi 3：VR未打开WiFi
  vRNetWOrkState(bool isAddDevice, int dataForByte) async {
    if (isAddDevice) {
      if (dataForByte == 1 || dataForByte == 2) {
        Log.d("----------------相同一WiFi 或WiFi已打开----------------");
        sendAccountToVR();
      } else if (dataForByte == 3) {
        Log.d("----------------未打开WiFi功能----------------");
        YvrToast.showToast(YLocal.current.qingxiandakaiVRshebe);
      } else {
        Log.d("WiFi打开但未连接: $dataForByte");
        BleDialog.showSyncNetworkDialog(
            context: _context,
            showErrNote: false,
            // remidText: "无法添加VR眼镜",
            remidText: YLocal.current.mofatianjiaVRyanjing,
            connectWiFi: (String ssid, String password) {
              ssid = ssid;
              _password = password;
              writeStreamValue(
                  bleChar: _baseChar,
                  status: BleTransmissionStatus.REQ_CONNECT_WIFI_SSID,
                  data: ssid);
            });
      }
    } else {
      if (_isRemindVRNoWiFi) {
        if (dataForByte == 3) {
          Log.d("----------------未打开WiFi功能----------------");
          // YvrToast.showToast("请先打开VR眼镜中的WiFi功能");
          YvrToast.showToast(YLocal.current.qingxiandakaiVRyanji);
        } else if (dataForByte != 2) {
          String ssid = await WiFiForIoTPlugin.getSSID();
          //助手发送WiFi ssid
          writeStreamValue(
              bleChar: _baseChar,
              status: BleTransmissionStatus.REQ_CONNECT_WIFI_SSID,
              data: ssid);
        }
      } else {
        if (dataForByte != 2) {
          for (int i = 0; i < _deviceModels.length; i++) {
            if (_deviceModels[i].devId == _connectedDevId) {
              _deviceModels[i].linkState = 0;
              _deviceModels[i].btName = _connectedDevName;
              _deviceModels[i].sameWiFi = false;
              _deviceModels[i].getWifiIp = false;
              Future.delayed(const Duration(milliseconds: 200), () async {
                notifyListeners();
              });
              break;
            }
          }
        } else {
          //助手请求设备ip
          await writeStreamValue(
              bleChar: _baseChar,
              status: BleTransmissionStatus.REQ_VR_IP_STATUS);

          //手机助手主动告知是否在同一wifi网络中 在同网络值为1，否则为0
          await writeStreamValue(
              bleChar: _baseChar,
              data: "1",
              status: BleTransmissionStatus.REQ_WIFI_CHANGED_STATUS);
        }
      }
    }
  }

  sendAccountToVR() {
    // WiFi连接成功后发出登录请求
    Future.delayed(const Duration(milliseconds: 500), () {
      String account = DBUtil.instance.userBox.get(kMobile).toString();
      writeStreamValue(
          bleChar: _baseChar,
          status: BleTransmissionStatus.REQ_ACCOUNT_STATUS,
          data: account);
    });
  }

  // 限制VR回复空值次数，防止连接成功后持续弹框提示
  listenBleResponse({
    @required List response,
    @required String devId,
    @required String devName,
    @required bool isAddDevice,
    @required BluetoothDevice device,
  }) async {
    // 解析数据
    if (response.length == 0 ||
        BleTransmissionStatus.values.contains(response[0])) {
      Log.d("VR回复空值 =================>$response");

      if (isGetVrReply && Platform.isIOS) {
        return;
      }
      cleanConnectTimer();
      //啄木鸟计时日志，1扫描，2连接,3获取设备IP
      zmnConnectLog(3);
      if (!isAddDevice) {
        _connectTimer?.cancel();
        YvrToast.showToast(YLocal.current.VRyanjinglianjiechen);
      }
      if (isAddDevice) {
        Log.d("------------助手请求添加设备------------${StorageManager.deviceModel}");
        Future.delayed(const Duration(milliseconds: 200), () {
          //助手请添加设备
          writeStreamValue(
              bleChar: _baseChar,
              data: StorageManager.deviceModel,
              status: BleTransmissionStatus.REQ_ADD_DEV_STATUS,
              callBack: () {
                // YvrToast.showToast('添加设备请求已发送，请到vr设备端确认');
                Log.d('--------------助手添加设备请求已发送，请到vr设备里确认--------------');
              });
        });
      } else {
        Future.delayed(const Duration(milliseconds: 200), () {
          Log.d("------------助手发送手机型号------------$_phoneModel");
          //助手发送手机型号
          writeStreamValue(
              bleChar: _baseChar,
              status: BleTransmissionStatus.REQ_WRITE_PHONE_TYPE_STATUS,
              data: _phoneModel);
        });
      }
      return;
    }

    BleTransmissionStatus status = BleTransmissionStatus.values[response[0]];
    int dataForByte = -100;
    if (response.length >= 4) {
      dataForByte = response[3];
    }
    List dataForStr = response.getRange(3, response.length).toList() ?? [];
    String retData = "";
    try {
      retData = utf8.decode(dataForStr);
    } catch (e) {
      Log.d("无法解析 ===============>$dataForStr");
    }

    Log.w("""
收到VR信息：$response
status:$status
[3]字节值:${dataForByte == -100 ? '' : dataForByte}
retData:$retData     
kInProjection:${DBUtil.instance.userBox.get(Global.kInProjection) ?? 0}""");

    switch (status) {
      // 2: 回复手机助手添加设备状态 1：同意 0：拒绝
      case BleTransmissionStatus.RET_ADD_DEV_STATUS:
        Log.d('回复手机助手添加设备状态 1：同意 0：拒绝 =================>$dataForByte');
        agreeAddDevice(isAddDevice, dataForByte);
        break;
      //回复助手收到账户 1：成功 0：失败
      case BleTransmissionStatus.RET_ACCOUNT_STATUS:
        Log.d('回复助手收到账户 1：成功 0：失败 =================>$dataForByte');
        if (dataForByte == 0) {
          Log.d("---------VR用户账号接收失败---------");
          // 蓝牙连接结果：0-连接中 1-连接失败 2-VR端拒绝 3-账号绑定成功
          if (isAddDevice) eventBus.fire(EventFn({'kResultTypeForAddVR': 1}));
        } else if (dataForByte == 1) {
          writeStreamValue(
              bleChar: _baseChar,
              status: BleTransmissionStatus.REQ_ACCOUNT_PWD_STATUS,
              data: DBUtil.instance.userBox.get(kToken));
        }
        break;
      //回复助手是否登录成功 1：成功 0：失败
      case BleTransmissionStatus.RET_ACCOUNT_PWD_STATUS:
        Log.d('回复助手是否登录成功 1：成功 0：失败 =================>$dataForByte');
        if (dataForByte == 0) {
          Log.d("VR接收账号密码失败");
          // 蓝牙连接结果：0-连接中 1-连接失败 2-VR端拒绝 3-账号绑定成功
          if (isAddDevice) eventBus.fire(EventFn({'kResultTypeForAddVR': 1}));
        } else if (dataForByte == 1) {
          Log.d("--------------账号登录成功--------------");
          // 蓝牙连接结果：0-连接中 1-连接失败 2-VR端拒绝 3-账号绑定成功
          isLoginSucceeded = true;
          if (isAddDevice) eventBus.fire(EventFn({'kResultTypeForAddVR': 3}));
          Future.delayed(const Duration(milliseconds: 100), () {
            //重新刷新界面
            resetConnect(devId: devId);
          });
        }
        break;
      //回复手机助手设备网络状态 0：不连通 1：联通  2：同一个WiFi 3：VR未打开WiFi
      case BleTransmissionStatus.RET_NETWORK:
        vRNetWOrkState(isAddDevice, dataForByte);
        break;
      //回复助手设备登录状态 actid：用户号 0：没有登录
      case BleTransmissionStatus.RET_LOGIN:
        if (retData.isEmpty) return;
        final loginStatus = int.parse(retData);
        if (loginStatus > 0) {
          String actid = DBUtil.instance.userBox.get(kActId).toString() ?? '';
          if (retData == actid && actid.isNotEmpty) {
            Log.d('-----------DevicePage-$retData 当前设备已登录！-----------');
          } else {
            // 设备未解除绑定 未登录 切换到在附近状态
            for (int i = 0; i < _deviceModels.length; i++) {
              if (_deviceModels[i].devId == _connectedDevId) {
                _deviceModels[i].linkState = 1;
                // YvrToast.showToast("请先在VR眼镜中登录您的账号！");
                YvrToast.showToast(YLocal.current.qingxianzaiVRyanjing);
                notifyListeners();
                break;
              }
            }
          }
        } else if (loginStatus == -2) {
          Log.d("-------------当前设备退出登录！-------------");
          // refreshPage();
        } else {
          Log.d("----------------当前设备未登录！----------------");
        }
        break;

      // 回复助手收到ssid  1：收到ssid
      case BleTransmissionStatus.RET_CONNECT_WIFI_SSID:
        if (dataForByte == 1 && _password.length >= 0) {
          writeStreamValue(
              bleChar: _baseChar,
              status: BleTransmissionStatus.REQ_CONNECT_WIFI_PWD,
              data: _password);
        }
        break;

      // 回复助手收到pwd 0:联wifi失败，1：联wifi成功
      case BleTransmissionStatus.RET_CONNECT_WIFI_PWD:
        Log.d("VR回复WiFi密码消息 ====================>$dataForByte");
        YvrToast.dismiss();
        if (dataForByte == 0) {
          BleDialog.showSyncNetworkDialog(
              context: _context,
              showErrNote: true,
              connectWiFi: (String ssid, String password) {
                Log.i("$password");
                _password = password;
                writeStreamValue(
                    bleChar: _baseChar,
                    status: BleTransmissionStatus.REQ_CONNECT_WIFI_SSID,
                    data: ssid);
              });
        } else if (dataForByte == 1) {
          if (isAddDevice) {
            // WiFi连接成功后发出登录请求
            sendAccountToVR();
          } else {
            for (var model in _deviceModels) {
              if (model.devId == _connectedDevId) {
                model.linkState = 0;
                model.btName = _connectedDevName;
                model.sameWiFi = true;
                Future.delayed(const Duration(milliseconds: 200), () async {
                  writeStreamValue(
                      bleChar: _baseChar,
                      status: BleTransmissionStatus.REQ_VR_IP_STATUS);
                });
                break;
              }
            }
          }
        }
        break;

      // 助手停止投屏 1：成功 0：失败
      case BleTransmissionStatus.RET_STOP_SCREENCAST_STATUS:
        // YvrToast.showToast("VR端已终止投屏");
        // YvrToast.showToast(YLocal.current.VRduanyizhongzhitoub);
        Log.d('----------------VR端已终止投屏---------------');
        break;

      // 回复助手电量百分比值、回复助手设备电量
      case BleTransmissionStatus.RET_READ_BATTERY_LEVEL_STATUS:
      case BleTransmissionStatus.RET_BATTERY_LEVEL_STATUS:
        for (int i = 0; i < _deviceModels.length; i++) {
          Log.d("---------${_deviceModels[i].btName}获取电量：$retData--------");
          if (_deviceModels[i].devId == _connectedDevId) {
            _deviceModels[i].linkState = 0;
            _deviceModels[i].btName = _connectedDevName;
            _deviceModels[i].battery = int.parse(retData);
            _battery = _deviceModels[i].battery;
          }
        }
        reqDeviceNetworkState();
        break;

      // 回复助手设备蓝牙名称
      case BleTransmissionStatus.RET_READ_BT_NAME_STATUS:
        _connectedDevName = retData ?? devName;
        Log.d('回复助手设备蓝牙名称 =============>$_connectedDevName');
        Log.d('----------------助手请求读电量百分比值--------------');
        //助手请求读电量百分比值
        writeStreamValue(
            bleChar: _baseChar,
            status: BleTransmissionStatus.REQ_READ_BATTERY_LEVEL_STATUS);
        break;

      // 回复助手收到手机型号
      case BleTransmissionStatus.RET_WRITE_PHONE_TYPE_STATUS:
        Log.d("手机型号接收成功 \n字节回复：$dataForByte");
        Log.d("------------助手请求读取设备蓝牙名称------------");
        //助手请求读取设备蓝牙名称
        writeStreamValue(
            bleChar: _baseChar,
            status: BleTransmissionStatus.REQ_READ_BT_NAME_STATUS);
        break;

      // 回复助手修改蓝牙名称完成
      case BleTransmissionStatus.RET_FIX_BT_NAME_STATUS:
        Log.d("蓝牙名称修改结果 ===========>：$dataForByte");
        break;
      //回复助手投屏结果
      case BleTransmissionStatus.RET_START_PUBLISH_FROM_MOBILE_STATUS:
        YvrToast.showToast('接口已过期');
        // handleProjectionNoti(retData: retData, projPath: '/'proj_screen);
        break;
      //回复助手退出登录
      case BleTransmissionStatus.RET_LOG_OUT_STATUS:
        // eventBus.fire(EventFn({Global.kAppStopServe: true}));
        break;
      //VR端发起投屏
      case BleTransmissionStatus.RET_VR_START_PUBLISH_STATUS:
        Log.d('-----------------VR端发起投屏----------------$retData');
        if (retData == "0" &&
            ((DBUtil.instance.userBox.get(Global.kInProjection) ?? 0) == 0)) {
          YvrToast.showToast('接口已过期');
          // handleProjectionNoti(retData: "online", projPath: '/'proj_screen);
        }
        break;
      //回复助手设备ip
      case BleTransmissionStatus.RET_VR_IP_STATUS:
        if (dataForByte != -1) {
          Log.e("回复助手设备ip =====================>$retData");
          StorageManager.localStorage.setItem("vr_host", retData);
          for (int i = 0; i < _deviceModels.length; i++) {
            if (_deviceModels[i].devId == _connectedDevId) {
              _deviceModels[i].getWifiIp = true;
              _deviceModels[i].sameWiFi = true;
              notifyListeners();
              break;
            }
          }
          Future.delayed(const Duration(milliseconds: 400), () async {
            await writeStreamValue(
                bleChar: _baseChar, status: BleTransmissionStatus.REQ_GET_INFO);
          });
        }
        break;
      //回复助手局域网投屏结果
      case BleTransmissionStatus.RET_WLAN_CAST_STATUS:
        Log.d('------------------------回复助手局域网投屏结果--------------------------');
        handleProjectionNoti(retData: retData, projPath: '/lan_proj');
        break;
      //回复助手停止局域网投屏结果
      case BleTransmissionStatus.RET_STOP_WLAN_CAST_STATUS:
        DBUtil.instance.userBox.put(Global.kInProjection, 0);
        break;
      //vr发起局域网投屏停止或结束，通知助手值字符串为1开始，否则为结束0
      case BleTransmissionStatus.RET_SEND_VR_WLAN_STATUS:
        Log.d('vr发起局域网投屏停止或结束，通知助手值字符串为1开始，否则为结束0--$retData');
        if (retData ==
            "1" /* && ((DBUtil.instance.userBox.get(Global.kInProjection) ??0) == 0)*/) {
          handleProjectionNoti(retData: "online", projPath: '/lan_proj');
        } else if (retData == "0" &&
            ((DBUtil.instance.userBox.get(Global.kInProjection) ?? 0) == 1)) {
          DBUtil.instance.userBox.put(Global.kInProjection, 0);
          // YvrToast.showToast("VR眼镜已终止投屏");
          YvrToast.showToast(YLocal.current.VRyanjingyizhongzhit);
          loganInfo('(PopUntilFirst) projection stopped',
              StackTrace.current.toString());
          Navigator.of(_context).popUntil((route) => route.isFirst);
        }
        break;
      //回复助手设备网络发生变化 设备连接wifi时发送ssid,分包；断开wifi发送
      case BleTransmissionStatus.RET_WIFI_CHANGED_STATUS:
        if (retData == "-1") {
          // VR网络切换时停止投屏
          eventBus.fire(EventFn({Global.vrNetworkChangeKey: true}));
          for (int i = 0; i < _deviceModels.length; i++) {
            if (_deviceModels[i].devId == _connectedDevId) {
              Log.i("非相同WiFi-2");
              _deviceModels[i].sameWiFi = false;
              _deviceModels[i].getWifiIp = false;
              notifyListeners();
              break;
            }
          }
        } else {
          // 分包接收 SSID
          if ((response[1] == 0)) {
            String vrSsid = retData;
            String phoneSsid = await WiFiForIoTPlugin.getSSID();
            reloadWifiStatus(vrSsid == phoneSsid);
          } else {
            if (!_vrSsidWraps.contains(response)) {
              _vrSsidWraps.add(response);
            }
            if (response[1] == response[2] &&
                _vrSsidWraps.length == response[1]) {
              _vrSsidWraps.sort((a, b) => a[2].compareTo(b[2]));
              List<int> ssidBytes = [];
              _vrSsidWraps.forEach((item) =>
                  ssidBytes.addAll(item.getRange(3, item.length).toList()));
              String vrSsid = utf8.decode(ssidBytes);
              String phoneSsid = await WiFiForIoTPlugin.getSSID();
              Log.e("vrSsid:$vrSsid\nphoneSsid:$phoneSsid");
              reloadWifiStatus(vrSsid == phoneSsid);
            }
          }
        }
        break;
      //回复助手开始同屏录结果
      case BleTransmissionStatus.RET_START_RECORDER_AND_SHARE:
        bool inRecordingPage =
            DBUtil.instance.userBox.get(Global.kOnlyRecordingProjWithoutJump) ??
                false;
        if (retData == "storage") {
          await DBUtil.instance.projectConfigBox
              .put(Global.kVRStorageEnough, false);
        } else if (retData == "online") {
          await DBUtil.instance.projectConfigBox
              .put(Global.kVRStorageEnough, true);
        }
        if (inRecordingPage) {
          if (retData == "offline") {
            // YvrToast.showToast("VR眼镜已终止拼接录制");
            YvrToast.showToast(YLocal.current.qingquebaoVRyanjingl);
            loganInfo('(PopUntilFirst) recording stopped',
                StackTrace.current.toString());
            Navigator.of(_context).popUntil((route) => route.isFirst);
          } else {
            eventBus.fire(EventFn({Global.kRefreshRecordingPage: true}));
          }
        } else {
          int sendTime = int.parse(
              StorageManager.localStorage.getItem(Global.kBleSendTimestamp));
          int receiveTime = DateTime.now().millisecondsSinceEpoch;
          StorageManager.localStorage.setItem(
              Global.kBleTimestampDifference, "${receiveTime - sendTime}");

          if (_battery <= 40) {
            // YvrToast.showToast("建议VR电量40%以上使用");
            YvrToast.showToast(YLocal.current.jianyiVRdianliang40y);
          }
          handleProjectionNoti(retData: retData, projPath: '/recording');
        }
        break;

      // 回复助手停止同屏录
      case BleTransmissionStatus.RET_STOP_RECORDER_AND_SHARE:
        break;

      // VR端端请求停止同屏录
      case BleTransmissionStatus.RET_STOP_RECORDER_AND_SHARE_FROM_DEV:
        if (((DBUtil.instance.userBox.get(Global.kInProjection) ?? 0) == 2)) {
          eventBus.fire(EventFn({Global.kVRStopRecording: true}));
        }
        break;

      // 回复手机助手打开应用结果
      case BleTransmissionStatus.RET_START_APP:
        if (bleBuffer.write(response)) {
          String launchResult = bleBuffer.toStringData(response[0]);
          Map result = jsonDecode(launchResult);
          if (_connectedDevId != null && _connectedDevId.isNotEmpty) {
            result["devId"] = _connectedDevId;
            result["devName"] = _connectedDevName;

            ///flag:结果
            ///pkg:应用包名
            ///devId:设备id
            ///devName:设备名称
            eventBus.fire(EventFn({Global.kLaunchAppResult: result}));
          }
        }
        break;

      case BleTransmissionStatus.RET_START_MRC:
        switch (retData) {
          case "0":
            FlutterChannel().jumpToIOSMethod();
            break;
          case "1":
            YvrToast.showToast(YLocal.current.dangqianVRyanjingtou);
            break;
          default:
            YvrToast.showToast("VR眼镜混合录制服务启动失败");
        }
        break;

      case BleTransmissionStatus.RET_START_RECORD:
        if (retData == "1") {
          eventBus.fire(EventFn({Global.kVRRecordingIs1200x900: false}));
        }
        break;

      case BleTransmissionStatus.RET_GET_INFO:
        StorageManager.localStorage.setItem("vr_http_port", retData);
        Log.d('端口号：$retData');
        break;

      // 请求遥控拍摄 投屏结果
      case BleTransmissionStatus.RET_START_PREVIEW:
        handleProjectionNoti(retData: retData, projPath: '/remote_control');
        break;

      // 拍照结果回复
      case BleTransmissionStatus.RET_3D_TAKE_PHOTH:
        if ((response[1] == 0)) {
          _vr3dPhotoThum = [];
          eventBus.fire(EventFn(
              {Global.kResultTakingPhotos: retData == '1' ? retData : '0'}));
        } else {
          // 分包接收 拍照预览图
          if (!_vr3dPhotoThum.contains(response)) {
            _vr3dPhotoThum.add(response);
          }
          if (response[1] == response[2] &&
              _vr3dPhotoThum.length == response[1]) {
            _vr3dPhotoThum.sort((a, b) => a[2].compareTo(b[2]));
            List<int> imageBytes = [];
            _vr3dPhotoThum.forEach((item) =>
                imageBytes.addAll(item.getRange(3, item.length).toList()));
            String imageName = utf8.decode(imageBytes);
            eventBus.fire(EventFn({Global.kResultTakingPhotos: imageName}));
          }
        }

        break;

      // 开始录像结果回复
      case BleTransmissionStatus.RET_3D_START_RECORD_VIDEO:
        eventBus.fire(EventFn({Global.kResultStartRecording: retData}));
        break;

      // 结束录像结果回复
      case BleTransmissionStatus.RET_3D_STOP_RECORD_VIDEO:
        if ((response[1] == 0)) {
          _vr3dVideoThum = [];
          eventBus.fire(EventFn(
              {Global.kResultStopRecording: retData == '1' ? retData : '0'}));
        } else {
          // 分包接收 录像预览图
          if (!_vr3dVideoThum.contains(response)) {
            _vr3dVideoThum.add(response);
          }
          if (response[1] == response[2] &&
              _vr3dVideoThum.length == response[1]) {
            _vr3dVideoThum.sort((a, b) => a[2].compareTo(b[2]));
            List<int> imageBytes = [];
            _vr3dVideoThum.forEach((item) =>
                imageBytes.addAll(item.getRange(3, item.length).toList()));
            String imageName = utf8.decode(imageBytes);
            eventBus.fire(EventFn({Global.kResultStopRecording: imageName}));
          }
        }
        break;

      default:
        break;
    }
  }

  void reqNetworkForVR({String ssid, int funType}) {
    Log.d('---------------------助手请求获取设备网络状态------------------------');
    //助手请求获取设备网络状态
    writeStreamValue(
        bleChar: _baseChar,
        status: BleTransmissionStatus.REQ_NETWORK,
        data: ssid ?? "0");
  }

  Future<void> handleProjectionNoti({String retData, String projPath}) async {
    YvrToast.dismiss();
    Log.d('retData ==========回复助手局域网投屏结果======>$retData');
    switch (retData) {
      case "online":
      case "storage":
        WidgetsFlutterBinding.ensureInitialized();
        dynamic cameras = "";
        if (projPath == '/lan_proj' || projPath == '/remote_control') {
          bool isAllowJump =
              await DevTool.isHaveLocalNetworkPrivacy(snCode: _connectedDevId);
          Log.d('isAllowJump =========投屏结果===========>$isAllowJump');
          if (!isAllowJump) {
            sendStreamValue(BleTransmissionStatus.REQ_STOP_SCREENCAST_STATUS);
            return;
          }
        }
        if (projPath == '/recording') {
          bool isAllowJump =
              await DevTool.isHaveLocalNetworkPrivacy(snCode: _connectedDevId);
          if (!isAllowJump) {
            Log.e('isAllowJump: 不允许进入所以结束');
            sendStreamValue(
              BleTransmissionStatus.REQ_STOP_RECORDER_AND_SHARE,
              data: "1",
            );
            return;
          }
          if (_battery <= 40) {
            // YvrToast.showToast("建议VR电量40%以上使用");
            YvrToast.showToast(YLocal.current.jianyiVRdianliang40y);
          }
          cameras = await availableCameras();
        }
        await DBUtil.instance.userBox
            .put(Global.kInProjection, (projPath == '/lan_proj') ? 1 : 2);
        Log.e("pushNamed:${DBUtil.instance.userBox.get(Global.kInProjection)}");
        int actId = DBUtil.instance.userBox.get(kActId);
        Navigator.pushNamed(_context, projPath, arguments: {
          "cameras": cameras,
          "actId": actId,
          "deviceName": _connectedDevName,
          "devId": _connectedDevId,
          "projectionChar": _baseChar,
        }).then((value) {
          if (value != null) {
            int popResult = value as int;
            switch (popResult) {
              case Global.phoneWifiChangeValue:
                _showDialog(YLocal.current.shoujiyuVRyanjingwan);
                break;
              case Global.vrWifiChangeValue:
                _showDialog(YLocal.current.VRyanjingyushoujiwan);
                break;
              case Global.vrCloseScreenValue:
                _showDialog(YLocal.current.qingquebaoVRyanjingl);
                break;
            }
          }
        });
        break;
      case "offline":
        YvrToast.showToast(YLocal.current.qingquebaoVRyanjingl);
        break;
      case "NO_USER":
        YvrToast.showToast(YLocal.current.qingquebaoVRzhongyid);
        break;
      case "closePublish":
        eventBus.fire(EventFn({Global.kVRStopProj: true}));
        break;
      case "REC":
        _showDialog(YLocal.current.dangqianVRyanjinglub, callBack: () {
          sendStreamValue(BleTransmissionStatus.REQ_STOP_RECORDER_AND_SHARE);
        });
        break;
      case "TV":
        _showDialog(YLocal.current.dangqianVRyanjingdia);
        break;
      case "PROJECTING":
        _showDialog(YLocal.current.dangqianVRyanjingtou, callBack: () {
          sendStreamValue(BleTransmissionStatus.REQ_STOP_SCREENCAST_STATUS);
        });
        break;
      case "storage":
        _showDialog(YLocal.current.VRyanjingnacunbuzuqi_1);
        break;
      default:
    }
  }

  _showDialog(String text,
      {void callBack(), bool isCancel = false, String confirmText}) {
    showDialog(
        context: _context,
        barrierDismissible: false,
        builder: (_) {
          return CustomDialog(
            title: YLocal.current.dishitishi,
            content: text,
            isCancel: isCancel,
            confirmText: confirmText,
            confirmCallback: callBack,
          );
        });
  }

  reloadWifiStatus(bool isSameWifi) async {
    if (isSameWifi) {
      //手机助手主动告知是否在同一wifi网络中 在同网络值为1，否则为0
      await writeStreamValue(
          bleChar: _baseChar,
          data: "1",
          status: BleTransmissionStatus.REQ_WIFI_CHANGED_STATUS);

      //助手请求设备ip
      await writeStreamValue(
          bleChar: _baseChar, status: BleTransmissionStatus.REQ_VR_IP_STATUS);

      //助手发送手机型号
      await writeStreamValue(
          bleChar: _baseChar,
          status: BleTransmissionStatus.REQ_WRITE_PHONE_TYPE_STATUS,
          data: _phoneModel);
    } else {
      eventBus.fire(EventFn({Global.vrNetworkChangeKey: true}));

      //手机助手主动告知是否在同一wifi网络中 在同网络值为1，否则为0
      await writeStreamValue(
          bleChar: _baseChar,
          data: "0",
          status: BleTransmissionStatus.REQ_WIFI_CHANGED_STATUS);

      for (int i = 0; i < _deviceModels.length; i++) {
        if (_deviceModels[i].devId == _connectedDevId) {
          Log.i("非相同WiFi-1");
          _deviceModels[i].sameWiFi = false;
          _deviceModels[i].getWifiIp = false;
          notifyListeners();
          break;
        }
      }
    }
  }

  bool navigateTeenModePageById(String id) {
    var dev = _deviceModels?.firstWhere((element) => element.devId == id,
        orElse: () => null);
    if (dev == null) return false;
    navigateTeenModePage(dev);
    return true;
  }

  ///防止青少年模式多次点击
  bool checkingTeen = false;
  //青少年模式相关
  void onTeenModeTap(DevModel item) {
    if (checkingTeen) return;
    checkingTeen = true;
    navigateTeenModePage(item).whenComplete(() {
      checkingTeen = false;
    });
  }

  //青少年模式相关
  Future<void> navigateTeenModePage(DevModel item) async {
    bool value = item.teen == 1;
    int index = _deviceModels.indexOf(item);
    Log.d('设置青少模式的item索引 ==============>$index');
    if (value) {
      ///如果青少年模式已经打开，防止VR端关闭青少年模式导致App设备列表没有及时更新isOpen状态，需要再次检测到底有没有打开青少年模式
      var result = await YvrRequests.getTeenagerInfo(item.devId);
      if (!checkAndShowToast(result.errCode)) return;
      value = result.data?.mode == 1;
      if (!value) {
        // YvrToast.showToast('青少年模式已关闭');
        YvrToast.showToast(YLocal.current.qingshaonianmoshiyig);
        item.teen = 0;
        _deviceModels[index] = item;
        notifyListeners();
        return;
      }
    }
    if (!value) {
      int val = await Navigator.pushNamed(_context, '/teen_intro') as int;
      if (val == null) return;
      val = await Navigator.pushNamed(_context, '/teen_passwd',
          arguments: {"devId": item.devId}) as int;
      if (val == null) return;
      if (val == -1) {
        // YvrToast.showToast('该设备不存在');
        YvrToast.showToast(YLocal.current.gaishebeibucunzai);
      } else {
        // YvrToast.showToast('设置成功');
        YvrToast.showToast(YLocal.current.shezhichenggong);
        item.teen = 1;
        _deviceModels[index] = item;
        notifyListeners();
      }
    } else {
      String passwd = await Navigator.pushNamed(_context, '/teen_verify',
          arguments: {"devId": item.devId}) as String;
      if (passwd == null) return;
      int val = await Navigator.pushNamed(_context, '/teen_menu',
          arguments: {"passwd": passwd, "devId": item.devId}) as int;
      Log.d('val =========青少年模式设置返回============>$val');
    }
  }

  addNotificationMessages(ServiceNotificationEvent event) {
    if (event.title != null || event.content != null) {
      events.add(event);
    }
  }

  subscriptionNotification() {
    _subscription?.cancel();
    _subscription =
        NotificationListenerService.notificationsStream.listen((event) {
      // Log.d("通知栏消息: $event");

      if (events.length > 0) {
        bool isHaveId = false;
        events.map((e) {
          if (e.id == event.id) {
            isHaveId = true;
          }
        });
        if (!isHaveId) {
          addNotificationMessages(event);
        }
      } else {
        addNotificationMessages(event);
      }

      // Log.d('通知栏消息条数：${events.length} \n${events.toList().toString()}');
    });
  }

  /// 注册Android获取栏消息通知服务
  registerNotificationListenerService() async {
    if (await NotificationListenerService.isPermissionGranted()) {
      subscriptionNotification();
    } else if (!isOpenNotificationDialog) {
      bool isDenia =
          DBUtil.instance.projectConfigBox.get("kDenialNotice") ?? false;
      if (isDenia) {
        return;
      }
      isOpenNotificationDialog = true;
      showDialog(
          context: Global.context,
          barrierDismissible: false,
          builder: (_) {
            return CustomDialog(
              title: YLocal.of(Global.context).dishitishi,
              content: "开启设备和应用通知权限，便于沉浸VR体验时获取手机重要通知消息",
              cancelText: YLocal.of(Global.context).quxiao,
              confirmText: YLocal.of(Global.context).qukaiqi,
              height: 200,
              cancelCallback: () {
                isOpenNotificationDialog = false;
                DBUtil.instance.projectConfigBox.put("kDenialNotice", true);
              },
              confirmCallback: () async {
                isOpenNotificationDialog = false;
                NotificationListenerService.requestPermission().then((status) {
                  if (status) {
                    subscriptionNotification();
                  }
                });
              },
            );
          });
    }
  }

  bool _isScanning = false;
  List<ScanResult> _scanResults = [];
  StreamSubscription<bool> _isScanningSubscription;
  StreamSubscription<List<ScanResult>> _scanResultsSubscription;

  void startScanBLE({String targetDevId}) {
    // 1. 设置监听器
    _scanResultsSubscription?.cancel(); // 确保旧的监听已移除
    _isScanningSubscription?.cancel();
    _scanResultsSubscription =
        FlutterBluePlus.scanResults.listen((results) async {
      _scanResults = results;
    });

    _isScanningSubscription = FlutterBluePlus.isScanning.listen((state) {
      _isScanning = state;

      // 只要第一次结果
      if (_scanResults.isNotEmpty) {
        bool hasAdded = false;
        Log.d('结束扫描 🌈  ${_scanResults.length}');

        for (final result in _scanResults) {
          List<Guid> serviceUuids = result.advertisementData.serviceUuids;
          if (serviceUuids.isNotEmpty &&
              serviceUuids.first.toString() == "1000ffe2") {
            var mtfData = result.advertisementData.manufacturerData[256];
            String devId = utf8.decode(mtfData); //获取设备ID

            if (isFromAdd) {
              if (!_deviceIds.contains(devId) &&
                  !_scanDeviceIdList.contains(devId)) {
                _scanDeviceIdList.add(devId);
                _scanDevices.add({
                  "rssi": result.rssi,
                  "devId": devId,
                  "device": result.device,
                  "name": result.device.advName ?? "",
                });
                hasAdded = true;
              }
            } else if (targetDevId != null &&
                targetDevId == devId &&
                !_scanDeviceIdList.contains(devId)) {
              Log.i('🌈连接指定设备：$targetDevId - $devId');
              _scanDeviceIdList.add(devId);
              blueConnect(
                  devId: devId,
                  device: result.device,
                  devName: result.device.advName ?? "");
              break;
            } else if (targetDevId == null &&
                _deviceIds != null &&
                _deviceIds.contains(devId) &&
                !_scanDeviceIdList.contains(devId)) {
              Log.d('已连接设备数： ${FlutterBluePlus.connectedDevices.length}');
              // 多设备处理？
              Log.d('等待连接的用户绑定设备ID： $devId');
              _scanDeviceIdList.add(devId);

              blueConnect(
                  devId: devId,
                  device: result.device,
                  devName: result.device.advName ?? "");
              break;
            }
          }
        }

        // 在循环后统一打印数量
        if (isFromAdd && hasAdded) {
          Log.d('最终扫描到设备数： ${_scanDevices.length}');
          eventBus.fire(EventFn({Global.scanResultsKey: _scanDevices}));
        }
      }
    });

    // 2. 启动扫描
    if (_isScanning == false) {
      FlutterBluePlus.startScan(timeout: const Duration(seconds: 3));
    }
  }
}
