import 'dart:async';

import 'package:flutter/foundation.dart'
    show
        DiagnosticsN<PERSON>,
        Flutter<PERSON>rror,
        FlutterErrorDetails,
        // ignore: unused_shown_name
        debugPrint,
        kDebugMode,
        required;
import 'package:yvr_assistant/pages/home/<USER>';
import 'package:yvr_assistant/utils/log.dart';

class Crashlytics {
  Crashlytics._();

  static Crashlytics _instance;

  static Crashlytics get instance {
    _instance ??= Crashlytics._();
    return _instance;
  }

  /// Submits a Crashlytics report of a caught error.
  Future<void> recordError(dynamic exception, StackTrace stack,
      {dynamic reason,
      Iterable<DiagnosticsNode> information = const [],
      bool printDetails,
      bool fatal = false}) async {
    // Use the debug flag if printDetails is not provided
    printDetails ??= kDebugMode;

    final String _information = information.isEmpty
        ? ''
        : (StringBuffer()..writeAll(information, '\n')).toString();

    if (printDetails) {
      // ignore: avoid_print
      print('----------------CRASHLYTICS----------------');

      // If available, give a reason to the exception.
      if (reason != null) {
        // ignore: avoid_print
        print('The following exception was thrown $reason:');
      }

      // Need to print the exception to explain why the exception was thrown.
      // ignore: avoid_print
      print(exception);

      // Print information provided by the Flutter framework about the exception.
      // ignore: avoid_print
      if (_information.isNotEmpty) print('\n$_information');

      // Not using Trace.format here to stick to the default stack trace format
      // that Flutter developers are used to seeing.
      // ignore: avoid_print
      if (stack != null) print('\n$stack');
      // ignore: avoid_print
      print('----------------------------------------------------');
    }

    final StackTrace stackTrace = stack ?? StackTrace.current;

    _recordError(
      exception: exception.toString(),
      reason: reason.toString(),
      information: _information,
      // stackTraceElements: stackTraceElements,
      stackTrace: stackTrace,
      fatal: fatal,
    );
  }

  /// Submits a Crashlytics report of an error caught by the Flutter framework.
  /// Use [fatal] to indicate whether the error is a fatal or not.
  Future<void> recordFlutterError(FlutterErrorDetails flutterErrorDetails,
      {bool fatal = false}) {
    FlutterError.presentError(flutterErrorDetails);

    return recordError(
      flutterErrorDetails.exceptionAsString(),
      flutterErrorDetails.stack,
      reason: flutterErrorDetails.context,
      information: flutterErrorDetails.informationCollector == null
          ? []
          : flutterErrorDetails.informationCollector(),
      printDetails: false,
      fatal: fatal,
    );
  }

  /// Submits a Crashlytics report of a caught error.
  void _recordError({
    @required String exception,
    @required String information,
    @required String reason,
    bool fatal = false,
    StackTrace stackTrace,
  }) {
    StringBuffer sb = StringBuffer();

    sb.write('reason:');
    sb.writeln(reason);

    sb.write('information:');
    sb.writeln(information);

    if (stackTrace != null) {
      sb.write(stackTrace);
    }
    // debugPrint("sb:$sb");
    logan(exception, sb.toString(), 1);
  }

  Timer _timer;
  static const int kUploadLogIntervalMinutes = 15;

  void startReportTimer({int delaySeconds = 10}) {
    if (_timer == null) {
      Log.i("begin upload logs to server,delaySeconds:$delaySeconds");
      _timer = Timer(Duration(seconds: delaySeconds), () {
        uploadToServer();
        _timer = Timer.periodic(
            const Duration(minutes: kUploadLogIntervalMinutes), (timer) {
          Log.i("periodically upload logs to server");
          uploadToServer();
        });
      });
    }
  }

  void cancelReportTimer() {
    if (_timer != null) {
      _timer.cancel();
      _timer = null;
    }
  }
}
