class DataForEventId {
  Map eventId = {
    // "/": "assistant_appstore_store_0_0_page_view",
    // "/home": "assistant_appstore_store_0_0_page_view",
    "/search": "assistant_appstore_search_0_0_page_view",
    "/mine_app": "assistant_appstore_myApps_0_0_page_view",
    "/category": "assistant_appstore_allApps_0_0_page_view",
    "/message": "assistant_appstore_notifications_0_0_page_view",
    "/comment": "assistant_appstore_ratingsNComment_0_0_page_view",
    // "": "assistant_appstore_appDetail_0_0_page_view", //应用id
    // "": "assistant_appstore_appSubject_0_0_page_view", //主题id
    // "/social": "assistant_social_activity_0_0_page_view", // 活动点击
    "/activity_create": "assistant_social_createActivity_0_0_page_view",
    // "/all_activitys": "assistant_social_myActivity_0_0_page_view",
    // "": "assistant_social_friend_0_0_page_view", // 好友点击
    "/search_friends": "assistant_social_addFriend_0_0_page_view",
    // "": "assistant_social_friendDetail_0_0_page_view", // 好友主页
    // "/device": "assistant_device_device_0_0_page_view",
    "/proj_screen": "assistant_device_deviceMirror_0_0_page_view",
    "/lan_proj": "assistant_device_deviceMirror_0_0_page_view",
    "/clock_in": "assistant_device_dailyAttendance_0_0_page_view",
    "/add_search": "assistant_device_addDevice_0_0_page_view",
    // "/match_result": "assistant_device_addDeviceSuccess_0_0_page_view",
    "/vr_file": "assistant_device_screenshotRecordList_0_0_page_view",
    // "": "assistant_device_screenshotRecordDetail_0_0_page_view", //截录屏详情
    "/profile": "assistant_me_me_0_0_page_view",
    // "": "assistant_me_myHomepage_0_0_page_view", //个人主页
    "/user": "assistant_me_profile_0_0_page_view",
    "/blacklist": "assistant_me_blacklist_0_0_page_view",
    "/reset_pwd": "assistant_me_modifyPassword_0_0_page_view",
    "/phone": "assistant_me_modifyMobile_0_0_page_view",
    "/wallet": "assistant_me_myWallet_0_0_page_view",
    // "/bill": "assistant_me_ycoinIncome_0_0_page_view", //Y币获取
    // "": "assistant_me_ycoinExpense_0_0_page_view", //Y币消费
    "/pay_history": "assistant_me_purchaseHistory_0_0_page_view",
    "/consumption": "assistant_me_purchaseHistoryItem_0_0_page_view",
    // "/": "assistant_me_lawInformation_0_0_page_view", 已删除
    "/feedback": "assistant_me_userFeedback_0_0_page_view",
    "/recording": "assistant_device_sameScreenRecordBefore_0_0_page_view",
    // "/merge_video": "assistant_device_sameScreenRecordSuccess_0_0_page_view",// 合成视频大小(VideoSize)
  };
}
