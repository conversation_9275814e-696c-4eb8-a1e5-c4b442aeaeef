import 'package:intl/intl.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'dart:io';

class TimeExpiredException implements Exception {
  String message;

  TimeExpiredException([this.message = ""]);
}

enum DateTimeDiffStatus { expired, running, today, nextDay, later }

class DateTimeDiffStatusData {
  String text;
  DateTimeDiffStatus status;
}

class DateTimeUtils {
  static const String kDateTimePattern = 'yyyy-MM-dd HH:mm:ss';
  static const String kTimePattern = 'HH:mm';
  static const String kMDT_Pattern = 'MM-dd HH:mm';
  static const String kGMTDateTimePattern = "EEE, dd MMM yyyy HH:mm:ss 'GMT'";

  static const int kIn3Days = 3 * 24 * 60 * 60 * 1000;
  static const int kIn2Days = 2 * 24 * 60 * 60 * 1000;
  static const int kIn1Days = 1 * 24 * 60 * 60 * 1000;
  static const int kIn1Hours = 1 * 60 * 60 * 1000;
  static const int kIn1Minute = 1 * 60 * 1000;

  static String parseDateTimeElapse(DateTime time, DateTime now) {
    Duration duration = now.difference(time);
    int milliseconds = duration.inMilliseconds;
    if (milliseconds >= kIn3Days) {
      return formatTime(time, pattern: 'yyyy/MM/dd');
    }
    if (milliseconds >= kIn2Days) {
      return YLocal.current.tianqian_1;
    }
    if (milliseconds >= kIn1Days) {
      return YLocal.current.tianqian;
    }
    if (milliseconds >= kIn1Hours) {
      return YLocal.current.Nxiaoshiqian(duration.inHours);
    }
    if (milliseconds >= kIn1Minute) {
      return YLocal.current.Nfenzhongqian(duration.inMinutes);
    }
    return YLocal.current.ganggang;
  }

  static DateTimeDiffStatusData parseDateTimeDiff(
      DateTime start, DateTime end, DateTime now) {
    DateTimeDiffStatusData data = DateTimeDiffStatusData();

    if (now.isBefore(start)) {
      if (start.isBefore(getTomorrow0AM(now, days: 1))) {
        data.status = DateTimeDiffStatus.today;
        data.text =
            '${YLocal.current.jintian} ${formatTime(start, pattern: kTimePattern)}';
      } else if (start.isBefore(getTomorrow0AM(now, days: 2))) {
        data.status = DateTimeDiffStatus.nextDay;
        data.text =
            '${YLocal.current.mingtian} ${formatTime(start, pattern: kTimePattern)}';
      } else {
        data.status = DateTimeDiffStatus.later;
        data.text = formatTime(start, pattern: kMDT_Pattern);
      }
    } else if (now.isBefore(end)) {
      data.status = DateTimeDiffStatus.running;
      data.text = YLocal.current.jinhangzhongjinhengz;
    } else {
      data.status = DateTimeDiffStatus.expired;
      data.text = YLocal.current.yijieshu;
    }
    return data;
  }

  static DateTime getTomorrow0AM(DateTime time, {int days}) {
    DateTime newDateTime = time.add(Duration(days: days));
    return DateTime(newDateTime.year, newDateTime.month, newDateTime.day);
  }

  // "2022-09-22T02:17:11.000+00:00"
  static DateTime parseUtcTime(String time) {
    DateTime parsedDate = DateTime.parse(time);
    return parsedDate.toLocal();
  }

  // Thu, 22 Sep 2022 02:28:22 GMT+0
  static DateTime parseHttpTime(String time) {
    DateTime parsedDate = HttpDate.parse(time);
    return parsedDate.toLocal();
  }

  //"2022-09-22 10:17:11"  服务器的时间是GMT+8
  static DateTime parseServerTime(String time) {
    DateTime parsedDate = DateTime.parse(time);
    DateTime result = DateTime.utc(
        parsedDate.year,
        parsedDate.month,
        parsedDate.day,
        parsedDate.hour - 8,
        parsedDate.minute,
        parsedDate.second);
    return result.toLocal();
  }

  static String formatTime(DateTime time, {String pattern = kDateTimePattern}) {
    String formattedDate = DateFormat(pattern).format(time);
    return formattedDate;
  }

  static String parseCountdown(DateTime end, DateTime now) {
    Duration duration = end.difference(now);
    if (duration.inSeconds <= 0) {
      return null;
    }
    if (duration.inSeconds < 24 * 60 * 60) {
      ///今天内
      int seconds = duration.inSeconds;
      if (seconds < 60) {
        return YLocal.current.Nmiaohoujieshu(seconds);
      }
      int minutes = duration.inMinutes;
      if (minutes < 60) {
        return YLocal.current.NNhoujieshu(
            _ensureTwoDigit(minutes), _ensureTwoDigit(seconds % 60));
      }
      int hours = duration.inHours;
      return YLocal.current.NNNhoujieshu(_ensureTwoDigit(hours),
          _ensureTwoDigit(minutes % 60), _ensureTwoDigit(seconds % 60));
    } else if (duration.inDays <= 7) {
      int days = duration.inDays;
      return YLocal.current.Ntianhoujieshu(days);
    } else {
      return "";
    }
  }

  static String _ensureTwoDigit(int value) {
    return value < 10 ? "0$value" : value.toString();
  }

  //获取结束时间
  static String getEndTimeStr(int endDate) {
    int currentDate = currentTimeMillis();
    if ('$endDate'.length == 13) {
      endDate = endDate ~/ 1000;
    }
    if ('$currentDate'.length == 13) {
      currentDate = currentDate ~/ 1000;
    }
    if (endDate > currentDate) {
      int endValue = endDate - currentDate;
      return parseEndTimeStr(endValue);
    }
    return '';
  }

  //获取结束时间，endValue单位秒
  static String parseEndTimeStr(int endValue) {
    int week = 7 * 24 * 60 * 60;
    int d = 24 * 60 * 60;
    int h = 60 * 60;
    int m = 60;
    if (endValue <= week) {
      if (endValue > d) {
        int day = endValue ~/ d;
        return YLocal.current.Ntianhoujieshu(day);
      } else if (endValue > h) {
        int hour = endValue ~/ h;
        return YLocal.current.Nxiaoshihoujieshu(hour);
      } else if (endValue > m) {
        int minute = endValue ~/ m;
        return YLocal.current.Nfenzhonghoujieshu(minute);
      } else {
        return YLocal.current.Nmiaohoujieshu(endValue);
      }
    } else {
      return '';
    }
  }

  //获取显示倒计时文本，endValue单位秒
  static bool isShowCountDownText(int endValue) {
    int week = 7 * 24 * 60 * 60;
    return endValue > 0 && endValue <= week;
  }

  static int currentTimeMillis() {
    return new DateTime.now().millisecondsSinceEpoch;
  }

  ///将服务器时间(GMT+8)转换为本地时区时间
  static String formatToLocalFromDateTimeText(
      String serverDateTimeText, String localPattern) {
    DateTime time = parseServerTime(serverDateTimeText);
    return formatTime(time, pattern: localPattern);
  }

  ///将服务器时间(GMT+8)转换为本地时区时间
  static String formatToLocalFromDateTime(
      DateTime serverDateTime, String localPattern) {
    return formatTime(serverDateTime, pattern: localPattern);
  }

  ///将本地时区时间转换为服务器的时间(GMT+8)
  static String formatToServerDateTime(
      DateTime localTime, String serverPattern) {
    Duration offsets = Duration(hours: 8) - localTime.timeZoneOffset;
    DateTime zone = DateTime.utc(
            localTime.year,
            localTime.month,
            localTime.day,
            localTime.hour,
            localTime.minute,
            localTime.second,
            localTime.millisecond,
            localTime.microsecond)
        .add(offsets);
    return formatTime(zone, pattern: serverPattern);
  }
}
