import 'package:flutter/services.dart';

///不允许有连续的换行符
class SignTextInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    TextEditingValue value = newValue;
    if (value.text.startsWith('\n')) {
      int end = 1;
      for (int i = 1; i < value.text.length; ++i) {
        if (value.text[i] == '\n') {
          end = i + 1;
        } else {
          break;
        }
      }

      value = value.replaced(TextRange(start: 0, end: end), '');
    }
    int start = -1;
    int end = -1;
    for (int i = 0; i < value.text.length; ++i) {
      if (value.text[i] == '\n') {
        if (start < 0) {
          start = i;
          end = i;
        } else {
          end = i;

          if (i == value.text.length - 1) {
            int x = end - start;
            if (end - start > 0) {
              value =
                  value.replaced(TextRange(start: start, end: end + 1), '\n');
              i -= x;
            }
          }
        }
      } else {
        int x = end - start;
        if (end - start > 0) {
          value = value.replaced(TextRange(start: start, end: end + 1), '\n');
          i -= x;
        }

        start = -1;
        end = -1;
      }
    }

    return value;
  }
}
