class NumberUtils {
  static String formatNumberOfFollowers(int num) {
    if (num < 10000) {
      return num.toString();
    } else if (num < 100000) {
      double n = (num / 10000.0);
      try {
        if (isInteger(n)) {
          return '${n.toInt()}W';
        } else {
          String s = n.toStringAsFixed(1);
          int fractionIndex = s.lastIndexOf('.');
          if (fractionIndex >= 0 &&
              int.parse(s.substring(fractionIndex + 1)) == 0) {
            s = s.substring(0, fractionIndex);
          }
          return (s + 'W');
        }
      } catch (e, st) {
        print(st);
        String s = n.toStringAsFixed(1);
        return (s + 'W');
      }
    } else {
      return '10W+';
    }
  }

  ///海外用户Y币数量展示
  ///1000=1K，1000K=1M，”K“和”M“都是大写，不用w了。
  static String formatShortNumber(int num) {
    if (num >= 1000000) {
      return numOfForeignYCoins(num / 1000000.0, 'M');
    } else if (num >= 1000) {
      return numOfForeignYCoins(num / 1000.0, 'K');
    } else {
      return num.toString();
    }
  }

  ///取小数点后几位
  static String formatNum(double num, int location) {
    if ((num.toString().length - num.toString().lastIndexOf(".") - 1) <
        location) {
      return num.toStringAsFixed(location)
          .substring(0, num.toString().lastIndexOf(".") + location + 1)
          .toString();
    } else {
      return num.toString()
          .substring(0, num.toString().lastIndexOf(".") + location + 1)
          .toString();
    }
  }

  static numOfForeignYCoins(double n, String unit) {
    try {
      if (isInteger(n)) {
        return '${n.toInt()}$unit';
      } else {
        return formatNum(n, 1) + unit;
      }
    } catch (e, st) {
      print(st);
      String s = n.toStringAsFixed(1);
      return (s + unit);
    }
  }

  static bool isInteger(double value) => value == value.roundToDouble();
}
