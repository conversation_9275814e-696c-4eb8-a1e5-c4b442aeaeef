import 'dart:async';
import 'package:fluwx/fluwx.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:common_utils/common_utils.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/network/socket.dart';
import 'package:yvr_assistant/pages/home/<USER>';
import 'package:yvr_assistant/utils/data_record.dart';

class InitConfig {
  void projectInit() {
    _initFluwx();
    initLogan();
    // ShareSdk().initShareSdk();
    SocketManager().initWebSocket(
        onOpen: () {},
        onMessage: (data) {},
        onError: (e) {
          Log.d("Socket初始化失败：\n" + e);
        });

    DataRecord().initDefaultData();

    String openTime = DateUtil.getNowDateStr();
    Timer.periodic(Duration(seconds: 60), (timer) {
      DataRecord().saveData(
          eventId: "assistant_global_0_0_0_module_pend",
          extraData: {"openTime": openTime, "duration": 60});
    });
  }

  _initFluwx() async {
    // 微信端注册用于跳转至微信 不跟随环境切换
    await registerWxApi(
        appId: Global.wechatAppid, universalLink: Global.kUniversalLink);
  }
}
