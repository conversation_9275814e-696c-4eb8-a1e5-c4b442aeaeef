import 'package:flutter/services.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';

class FlutterChannel {
  static const channelToIOS = const MethodChannel('samples.flutter.jumpto.iOS');

  //跳转到iOS页面
  Future<Null> jumpToIOSMethod() async {
    final String result = await channelToIOS.invokeMethod('jumpToIOSPage', {
      'title': 'Flutter send IP to iOS succes.',
      "vrIP": StorageManager.localStorage.getItem("vr_host")
    });
    print('Success to $result');
  }

  ///iOS横竖屏方法 0:portrait 1: landscapeRight 2: .all 允许各个方向
  Future<Null> iOSChangeOrientation({int orientation = 0}) async {
    await channelToIOS.invokeMethod('iOSChangeOrientation', {
      'orientation': orientation,
    });
  }
}
