import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/app_navigator.dart';
import 'package:yvr_assistant/public_ui/widget/rich_text.dart';
import 'package:yvr_assistant/utils/init_config.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';

import '../styles/app_color.dart';

class AgreementUtils {
  static bool _enabled() {
    return !Platform.isIOS;
  }

  static bool hasAgreed() {
    return !_enabled() ||
        (StorageManager.foreverData.getItem("isAgree") ?? false);
  }

  static void _agree() {
    StorageManager.foreverData.setItem("isAgree", true);
  }

  static Future<bool> showAgreementDialog(BuildContext context) async {
    if (hasAgreed()) {
      return true;
    }
    TextStyle textStyle = TextStyle(
      fontWeight: FontWeight.w400,
      fontSize: 16,
      height: 1.5,
      color: AppColors.textSub,
    );
    TextStyle protocolStyle = TextStyle(
      color: Color(0xFF4F7FFE),
    );
    bool agreed = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (_) {
          return CustomDialog(
            popAutomatically: false,
            height: 240,
            title: YLocal.of(context).fuwuxieyiheyinsizhen,
            dialogType: DialogType.DialogDIY,
            contentWidget: Container(
              margin: EdgeInsets.only(bottom: 20),
              child: IntlRichText2(
                intlTextBuilder: YLocal.of(context).qingniwubishenshenyu,
                defaultStyle: textStyle,
                param0: '《${YLocal.of(context).service_agreement}》',
                paramStyle0: protocolStyle,
                paramRecognizer0: TapGestureRecognizer()
                  ..onTap = () {
                    String title = YLocal.of(context).service_agreement;
                    navigateToAgreementPage(
                        context: context,
                        pageName:
                            YLocal.of(context).service_agreement_html_page_name,
                        title: title);
                  },
                param1: '《${YLocal.of(context).privacy_policy}》',
                paramStyle1: protocolStyle,
                paramRecognizer1: TapGestureRecognizer()
                  ..onTap = () {
                    String title = YLocal.of(context).privacy_policy;
                    navigateToAgreementPage(
                        context: context,
                        pageName:
                            YLocal.of(context).privacy_policy_html_page_name,
                        title: title);
                  },
              ),
            ),
            cancelText: YLocal.of(context).zanbushiyong,
            confirmText: YLocal.of(context).tongyi,
            cancelCallback: () {
              Navigator.of(context).pop();
              exit(0);
            },
            confirmCallback: () async {
              await StorageManager.loadDeviceInfo();
              _agree();
              InitConfig().projectInit();
              Navigator.of(context).pop(true);
            },
          );
        });
    return agreed ?? false;
  }
}
