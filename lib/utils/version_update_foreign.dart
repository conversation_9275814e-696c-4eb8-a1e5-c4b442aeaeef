import 'dart:io';
import 'package:package_info/package_info.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/material.dart';

class AppUpgradeInfo {
  AppUpgradeInfo(
      {@required this.title,
      @required this.contents,
      this.force = false,
      this.apkDownloadUrl});

  ///
  /// title,显示在提示框顶部
  ///
  final String title;

  ///
  /// 升级内容
  ///
  final List<String> contents;

  ///
  /// 是否强制升级
  ///
  final bool force;

  final String apkDownloadUrl;
}

class AppUpgrade {
  static void appUpgrade(
    BuildContext context,
    Future<AppUpgradeInfo> future, {
    TextStyle titleStyle,
    TextStyle contentStyle,
    String cancelText,
    TextStyle cancelTextStyle,
    String okText,
    TextStyle okTextStyle,
    List<Color> okBackgroundColors,
    Color progressBarColor,
    double borderRadius = 20.0,
    @required String iosAppId,
    VoidCallback onCancel,
    VoidCallback onOk,
    Widget Function(VoidCallback onOk) dialogBuilder,
  }) {
    future.then((AppUpgradeInfo appUpgradeInfo) {
      if (appUpgradeInfo != null) {
        _showUpgradeDialog(
            context, appUpgradeInfo.title, appUpgradeInfo.contents,
            force: appUpgradeInfo.force,
            titleStyle: titleStyle,
            contentStyle: contentStyle,
            cancelText: cancelText,
            cancelTextStyle: cancelTextStyle,
            okBackgroundColors: okBackgroundColors,
            okText: okText,
            okTextStyle: okTextStyle,
            borderRadius: borderRadius,
            progressBarColor: progressBarColor,
            iosAppId: iosAppId,
            onCancel: onCancel,
            onOk: onOk,
//自定义升级界面
            dialogBuilder: dialogBuilder);
      }
    }).catchError((onError) {
      print('$onError');
    });
  }
}

void _showUpgradeDialog(
  BuildContext context,
  String title,
  List<String> contents, {
  bool force = false,
  TextStyle titleStyle,
  TextStyle contentStyle,
  String cancelText,
  TextStyle cancelTextStyle,
  String okText,
  TextStyle okTextStyle,
  List<Color> okBackgroundColors,
  Color progressBarColor,
  double borderRadius = 20.0,
  @required String iosAppId,
  VoidCallback onCancel,
  VoidCallback onOk,
  Widget Function(VoidCallback onOk) dialogBuilder,
}) {
  showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return WillPopScope(
          onWillPop: () async {
            return false;
          },
          child: Dialog(
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(borderRadius))),
            child: SimpleAppUpgradeWidget(
              title: title,
              titleStyle: titleStyle,
              contents: contents,
              contentStyle: contentStyle,
              cancelText: cancelText,
              cancelTextStyle: cancelTextStyle,
              okText: okText,
              okTextStyle: okTextStyle,
              okBackgroundColors: okBackgroundColors ??
                  [
                    Theme.of(context).primaryColor,
                    Theme.of(context).primaryColor
                  ],
              progressBarColor: progressBarColor,
              borderRadius: borderRadius,
              force: force,
              iosAppId: iosAppId,
              onCancel: onCancel,
              onOk: onOk,
            ),
          ),
        );
      });
}

class SimpleAppUpgradeWidget extends StatefulWidget {
  const SimpleAppUpgradeWidget({
    @required this.title,
    this.titleStyle,
    @required this.contents,
    this.contentStyle,
    this.cancelText,
    this.cancelTextStyle,
    this.okText,
    this.okTextStyle,
    this.okBackgroundColors,
    this.progressBar,
    this.progressBarColor,
    this.borderRadius = 10,
    this.force = false,
    @required this.iosAppId,
    this.onCancel,
    this.onOk,
  });

  ///
  /// 升级标题
  ///
  final String title;

  ///
  /// 标题样式
  ///
  final TextStyle titleStyle;

  ///
  /// 升级提示内容
  ///
  final List<String> contents;

  ///
  /// 提示内容样式
  ///
  final TextStyle contentStyle;

  ///
  /// 下载进度条
  ///
  final Widget progressBar;

  ///
  /// 进度条颜色
  ///
  final Color progressBarColor;

  ///
  /// 确认控件
  ///
  final String okText;

  ///
  /// 确认控件样式
  ///
  final TextStyle okTextStyle;

  ///
  /// 确认控件背景颜色,2种颜色左到右线性渐变
  ///
  final List<Color> okBackgroundColors;

  ///
  /// 取消控件
  ///
  final String cancelText;

  ///
  /// 取消控件样式
  ///
  final TextStyle cancelTextStyle;

  ///
  /// 圆角半径
  ///
  final double borderRadius;

  ///
  /// 是否强制升级,设置true没有取消按钮
  ///
  final bool force;

  ///
  /// ios app id,用于跳转app store
  ///
  final String iosAppId;

  final VoidCallback onCancel;
  final VoidCallback onOk;

  @override
  State<StatefulWidget> createState() => _SimpleAppUpgradeWidget();
}

class _SimpleAppUpgradeWidget extends State<SimpleAppUpgradeWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Stack(
        children: <Widget>[
          _buildInfoWidget(context),
          Container(
            height: 10,
          )
        ],
      ),
    );
  }

  ///
  /// 信息展示widget
  ///
  Widget _buildInfoWidget(BuildContext context) {
    return Container(
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            //标题
            _buildTitle(),
            //更新信息
            _buildAppInfo(),
            //操作按钮
            _buildAction()
          ],
        ),
      ),
    );
  }

  ///
  /// 构建标题
  ///
  _buildTitle() {
    return Padding(
        padding: EdgeInsets.only(top: 20, bottom: 30),
        child: Text(widget.title,
            style: widget.titleStyle ?? TextStyle(fontSize: 22)));
  }

  ///
  /// 构建版本更新信息
  ///
  _buildAppInfo() {
    return Container(
        padding: EdgeInsets.only(left: 15, right: 15, bottom: 30),
        constraints: BoxConstraints(maxHeight: 150, minHeight: 120),
        child: ListView(
          children: widget.contents.map((f) {
            return Text(
              f,
              style: widget.contentStyle ?? TextStyle(),
            );
          }).toList(),
        ));
  }

  ///
  /// 构建取消或者升级按钮
  ///
  _buildAction() {
    return Column(
      children: <Widget>[
        Divider(
          height: 1,
          color: Colors.grey,
        ),
        Row(
          children: <Widget>[
            widget.force
                ? Container()
                : Expanded(
                    child: _buildCancelActionButton(),
                  ),
            Expanded(
              child: _buildOkActionButton(),
            ),
          ],
        ),
      ],
    );
  }

  ///
  /// 取消按钮
  ///
  _buildCancelActionButton() {
    return Ink(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(widget.borderRadius))),
      child: InkWell(
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(widget.borderRadius)),
          child: Container(
            height: 45,
            alignment: Alignment.center,
            child: Text(widget.cancelText ?? '以后再说',
                style: widget.cancelTextStyle ?? TextStyle()),
          ),
          onTap: () {
            widget.onCancel?.call();
            Navigator.of(context).pop();
          }),
    );
  }

  ///
  /// 确定按钮
  ///
  _buildOkActionButton() {
    var borderRadius =
        BorderRadius.only(bottomRight: Radius.circular(widget.borderRadius));
    if (widget.force) {
      borderRadius = BorderRadius.only(
          bottomRight: Radius.circular(widget.borderRadius),
          bottomLeft: Radius.circular(widget.borderRadius));
    }
    var _okBackgroundColors = widget.okBackgroundColors;
    if (_okBackgroundColors == null || _okBackgroundColors.length != 2) {
      _okBackgroundColors = [
        Theme.of(context).primaryColor,
        Theme.of(context).primaryColor
      ];
    }
    return Ink(
      decoration: BoxDecoration(
          gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [_okBackgroundColors[0], _okBackgroundColors[1]]),
          borderRadius: borderRadius),
      child: InkWell(
        borderRadius: borderRadius,
        child: Container(
          height: 45,
          alignment: Alignment.center,
          child: Text(widget.okText ?? '立即体验',
              style: widget.okTextStyle ?? TextStyle(color: Colors.white)),
        ),
        onTap: () {
          _clickOk();
        },
      ),
    );
  }

  ///
  /// 点击确定按钮
  ///
  _clickOk() async {
    widget.onOk?.call();
    final url = Uri.parse(
      Platform.isAndroid
          ? "https://play.google.com/store/apps/details?id=${(await PackageInfo.fromPlatform()).packageName}"
          : "https://apps.apple.com/app/${widget.iosAppId}",
    );
    launchUrl(
      url,
      mode: LaunchMode.externalApplication,
    );
  }
}
