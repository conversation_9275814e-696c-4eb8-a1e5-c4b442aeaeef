import 'dart:io';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';

/// Hive 数据操作
class DBUtil {
  /// 实例
  static DBUtil instance;

  /// 接口环境
  Box envBox;

  /// 数据埋点
  Box recordBox;

  /// 用户信息
  Box userBox;

  /// 设备名称 key: sn value:btName
  Box devNameBox;

  /// 应用程序设置
  Box projectConfigBox;

  /// 永久存储
  Box foreverBox;

  /// 初始化，需要在 main.dart 调用
  /// <https://docs.hivedb.dev/>
  static Future<void> install() async {
    /// 初始化数据库地址
    Directory document = await getApplicationDocumentsDirectory();
    Hive.init(document.path);

    await DBUtil.getInstance();

    /// 注册自定义对象（实体）
    /// <https://docs.hivedb.dev/#/custom-objects/type_adapters>
    //Hive.registerAdapter(RecordItemAdapter());
  }

  /// 初始化 Box
  static Future<DBUtil> getInstance() async {
    if (instance == null) {
      instance = DBUtil();
      await Hive.initFlutter();

      instance.envBox = await Hive.openBox('envBox');
      instance.userBox = await Hive.openBox('userBox');
      instance.recordBox = await Hive.openBox('recordBox');
      instance.devNameBox = await Hive.openBox('devNameBox');
      instance.foreverBox = await Hive.openBox('foreverBox');
      instance.projectConfigBox = await Hive.openBox('projectConfigBox');
    }

    return instance;
  }

  /// 初始化 Box
  static Future clearAll() async {
    if (instance != null) {
      instance.userBox.clear();
      instance.recordBox.clear();
      instance.devNameBox.clear();
      instance.projectConfigBox.clear();
    }
  }
}
