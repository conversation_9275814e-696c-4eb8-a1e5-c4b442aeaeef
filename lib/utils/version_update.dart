import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:yvr_assistant/model/version_model.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/pages/home/<USER>/message.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/agreement.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';

import '../generated/l10n.dart';
import 'version_update_foreign.dart';

/// 版本升级相关设置
class VersionUpdate {
  static bool _isInAppleReview = false;

  static bool get isInAppleReview => _isInAppleReview;

  static const kDownloadEnabled = false;

  static getAppVerInfo(String version, BuildContext context,
      {isShowToast = false,
      VoidCallback onAgree,
      VoidCallback onVersionCallBack}) {
    Future.sync(() async {
      if (onAgree != null) {
        bool value = await AgreementUtils.showAgreementDialog(context);
        if (!value) {
          return;
        }
        onAgree?.call();
      }
      http.post<Map>('vrmcsys/appstore/getMbVerInfo',
          data: {"source": Platform.isIOS ? "5" : "4"}).then((response) async {
        if (response.data["errCode"] == 0) {
          VerionModel verionModel = VerionModel.fromJson(response.data);
          if (compareVersion(localVer: version, newVer: verionModel.version)) {
            //0不弹窗 1弹窗
            if (verionModel.popup == 1) {
              //当前版本是否低于最低兼容版本
              verionModel.isLessThanMinVer =
                  compareVersion(localVer: version, newVer: verionModel.supVer);
              _checkAppUpgrade(context, verionModel, onVersionCallBack);
            } else if (!isShowToast) {
              HandelPayMessage.handelMsg();
            }
          } else if (isShowToast) {
            YvrToast.showToast(YLocal.of(context).nindangqianyishizuix,
                position: EasyLoadingToastPosition.bottom);
          } else {
            HandelPayMessage.handelMsg();
          }
        }
      }).catchError((e, s) {
        print(s);
      });
    });
  }

  static _checkAppUpgrade(BuildContext context, VerionModel verionModel,
      VoidCallback onVersionCallBack) {
    AppUpgrade.appUpgrade(
      context,
      _checkAppInfo(verionModel),
      cancelText: YLocal.of(context).zanbugengxin,
      okText: YLocal.of(context).lijigengxin,
      iosAppId: 'id1662776611',
      progressBarColor: Colors.white.withOpacity(.8),
      okBackgroundColors: [Color(0xff4F7FFE), Color(0xff4F7FFE)],
      onCancel: () {
        Log.d('onCancel');
      },
      onOk: () {
        Log.d('onOk');
      },
    );
  }

  static Future<AppUpgradeInfo> _checkAppInfo(VerionModel verionModel) async {
    //这里一般访问网络接口，将返回的数据解析成如下格式
    List<String> contents = verionModel.note.split("\n");
    return await Future.delayed(Duration(milliseconds: 0), () {
      return AppUpgradeInfo(
        title: YLocal.current.banbengengxin,
        contents: contents,
        //新版本升级判断字段 0非强制 1强制
        force: verionModel.enforce == 1 || verionModel.isLessThanMinVer,
      );
    });
  }

  //版本号比对
  static bool compareVersion({String localVer, String newVer}) {
    Log.d("本地版本 =========>$localVer，新版本 =========>$newVer");
    if (localVer != null && newVer != null) {
      List<String> currentList = localVer.split(".");
      List<String> newList = newVer.split(".");
      int length = min(newList.length, currentList.length);
      for (int i = 0; i < length; i++) {
        int newVer = int.parse(newList[i]);
        int currentVer = int.parse(currentList[i]);
        if (newVer > currentVer) {
          return true;
        } else if (newVer < currentVer) {
          return false;
        }
      }
      return newList.length > currentList.length;
    }
    return false;
  }
}
/** 版本更新上传说明数据格式
    forced：0-非强制 1-强制 2-不弹升级框(解决App Store更新提醒延时问题) source：2-Android 3-iOS

    {
    "version":"1.5.8",
    "note":"最新版本：1.5.8 \n新版本大小：62.1M \n \n1.新增\n2.优化",
    "forced":0,
    "source":2
    }

 */
