import 'dart:io';

import 'package:flutter/material.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info/device_info.dart';

import 'package:yvr_assistant/generated/l10n.dart';
import '../pages/device/tool/file_handle.dart';

///暂时只处理权限永久拒绝的情况，实际的请求由三方库自己处理
class PermissionUtil {
  static Map<Permission, String> _permissionExplainMap = {
    Permission.camera: YLocal.current.xiangji,
    Permission.storage: YLocal.current.cunchu,
    Permission.manageExternalStorage: YLocal.current.cunchu,
    Permission.photos: YLocal.current.xiangce,
    Permission.photosAddOnly: YLocal.current.bendexiangcebendixia,
  };

  static Future<bool> checkAndroidShowRequestRationale(
      BuildContext context, Permission permission) async {
    if (await permission.shouldShowRequestRationale) {
      return true;
    }
    return false;
  }

  static Future<bool> checkCameraPermission(BuildContext context) {
    return checkPermission(context, Permission.camera);
  }

  static Future<bool> checkPhotoPermission(BuildContext context) {
    if (Platform.isAndroid) {
      return checkPermission(context, Permission.storage);
    } else if (Platform.isIOS) {
      return checkPermission(context, Permission.photos);
    }
    return Future.value(false);
  }

  static Future<bool> checkPhotoSavePermission(BuildContext context) async {
    if (Platform.isAndroid) {
      var androidInfo = await DeviceInfoPlugin().androidInfo;
      if (androidInfo.version.sdkInt >= 33) {
        // Android 13+ 检查照片/视频权限
        if (await Permission.photos.isGranted ||
            await Permission.videos.isGranted) {
          return true;
        }
        var photoStatus = await Permission.photos.request();
        var videoStatus = await Permission.videos.request();
        if (photoStatus.isGranted || videoStatus.isGranted) {
          return true;
        }
        // 兜底再检测 manageExternalStorage/storage
      }
      if (await Permission.manageExternalStorage.isGranted ||
          await Permission.storage.isGranted) {
        return true;
      }
      var status = await Permission.manageExternalStorage.request();
      if (status.isGranted) {
        return true;
      }
      status = await Permission.storage.request();
      if (status.isGranted) {
        return true;
      }
      // 都不行再弹窗
      return checkPermission(context, Permission.manageExternalStorage);
    } else if (Platform.isIOS) {
      return checkPermission(context, Permission.photosAddOnly);
    }
    return Future.value(false);
  }

  /// 封装 Permission 公用方法
  static Future<bool> checkPermission(
      BuildContext context, Permission permission) async {
    PermissionStatus status = await permission.status;
    if (status.isGranted) {
      return true;
    }
    // 先请求一次
    status = await permission.request();
    if (status.isGranted) {
      return true;
    }
    if (status.isPermanentlyDenied) {
      showNoticeDialog(context, permission);
      return false;
    }
    if (status.isDenied) {
      if (Platform.isIOS) {
        showNoticeDialog(context, permission);
      } else if (Platform.isAndroid) {
        if (!await permission.shouldShowRequestRationale) {
          showNoticeDialog(context, permission);
        }
      }
      return false;
    }
    if (status.isRestricted) {
      if (Platform.isIOS) {
        showNoticeDialog(context, permission);
      }
      return false;
    }
    return false;
  }

  static void showNoticeDialog(BuildContext context, Permission permission) {
    String permissionName = _permissionExplainMap[permission];
    if (permissionName == null) {
      throw Exception("please register permission name first");
    }
    showOpenPhotoDialog(
        remind: YLocal.current.qingxiankaiqiSquanxi(permissionName));
  }

  /// 申请蓝牙权限 https://www.jianshu.com/p/1b21895e3674
  Future<bool> requestBlePermissions() async {
    var isBleGranted = await Permission.bluetooth.request();
    Log.d('checkBlePermissions, isBleGranted=$isBleGranted');
    if (Platform.isIOS) {
      return isBleGranted == PermissionStatus.granted;
    }

    var isLocationGranted = await Permission.locationWhenInUse.request();
    var isBleScanGranted = await Permission.bluetoothScan.request();
    var isBleConnectGranted = await Permission.bluetoothConnect.request();
    var isBleAdvertiseGranted = await Permission.bluetoothAdvertise.request();

    Log.d('''
  checkBlePermissions:
      isLocationGranted        $isLocationGranted
      isBleScanGranted         $isBleScanGranted
      isBleConnectGranted      $isBleConnectGranted 
      isBleAdvertiseGranted    $isBleAdvertiseGranted
''');

    return isLocationGranted == PermissionStatus.granted &&
        isBleScanGranted == PermissionStatus.granted &&
        isBleConnectGranted == PermissionStatus.granted &&
        isBleAdvertiseGranted == PermissionStatus.granted;
  }
}
