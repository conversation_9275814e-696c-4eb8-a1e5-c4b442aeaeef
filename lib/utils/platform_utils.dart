import 'dart:io';
export 'dart:io';
import 'dart:async';
import 'package:device_info/device_info.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:ios_utsname_ext/extension.dart';
import 'package:package_info/package_info.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';

/// 是否是生产环境
const bool inProduction = const bool.fromEnvironment("dart.vm.product");

class PlatformUtils {
  static Future<PackageInfo> getAppPackageInfo() {
    return PackageInfo.fromPlatform();
  }

  static Future<String> getAppVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    return packageInfo.version;
  }

  static Future<String> getBuildNum() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    return packageInfo.buildNumber;
  }

  static Future<String> getDeviceName() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return "${androidInfo?.brand} ${androidInfo?.model}";
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      return iosInfo.name;
    } else {
      return "No name";
    }
  }

  static Future<String> getDeviceModel() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return "${androidInfo?.brand} ${androidInfo?.model}";
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      return iosInfo.utsname.machine.iOSProductName;
    } else {
      return "Unkonwn Model";
    }
  }

  static Future<Map<String, dynamic>> _readDeviceInfo() async {
    IosDeviceInfo iosInfo;
    AndroidDeviceInfo androidInfo;
    DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    if (Platform.isIOS) {
      iosInfo = await deviceInfoPlugin.iosInfo;
    } else if (Platform.isAndroid) {
      androidInfo = await deviceInfoPlugin.androidInfo;
    }
    Map<String, dynamic> data = <String, dynamic>{
      //手机品牌加型号
      "brand": Platform.isIOS
          ? iosInfo?.name
          : "${androidInfo?.brand} ${androidInfo?.model}",
      //当前系统版本
      "systemVersion": Platform.isIOS
          ? iosInfo?.systemVersion
          : androidInfo?.version?.release,
      //系统名称
      "Platform": Platform.isIOS ? iosInfo?.systemName : "Android",
      //是不是物理设备
      "isPhysicalDevice": Platform.isIOS
          ? iosInfo?.isPhysicalDevice
          : androidInfo?.isPhysicalDevice,
      //用户唯一识别码
      "uuid": Platform.isIOS
          ? iosInfo?.identifierForVendor
          : androidInfo?.androidId,
      //手机具体的固件型号/Ui版本
      "incremental": Platform.isIOS
          ? iosInfo?.systemVersion
          : androidInfo?.version?.incremental,
    };
    return data;
  }

  static Map<String, dynamic> deviceData;
  //手机品牌、型号、系统版本号，用户手机号，失败原因
  static Future<String> getPhoneModel() async {
    String account = DBUtil.instance.userBox.get(kMobile).toString();
    if (deviceData == null) {
      deviceData = await _readDeviceInfo();
    }
    return '手机号：$account，品牌型号：${deviceData['brand']}，系统版本：${deviceData['systemVersion']}';
  }

  /*
    对照表： https://blog.csdn.net/u014600626/article/details/*********
    最低支持 A12芯片 & iOS13 & iPhoneXS
  */
  static Future<bool> getIPhoneIsSupportMRC() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (!Platform.isIOS) {
      return false;
    }

    IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
    String machine = iosInfo.utsname.machine;
    if (!machine.contains("iPhone")) {
      return false;
    }

    int model =
        int.parse(machine.split(",")[0].replaceFirst(RegExp(r'iPhone'), ''));
    if (model < 11) {
      return false;
    }

    String version = Platform.operatingSystemVersion.split(" ")[1];
    int iOSBigNo = int.parse(version.split(".")[0]);
    if (iOSBigNo < 13) {
      return false;
    }

    Log.d(
        'version: $version machine: $machine iOSBigNo: $iOSBigNo model: $model');
    return true;
  }

  static Future<String> getDeviceId() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isIOS) {
      IosDeviceInfo iosDeviceInfo = await deviceInfo.iosInfo;
      return iosDeviceInfo.identifierForVendor; // unique ID on iOS
    } else if (Platform.isAndroid) {
      AndroidDeviceInfo androidDeviceInfo = await deviceInfo.androidInfo;
      return androidDeviceInfo.androidId; // unique ID on Android
    } else {
      return "";
    }
  }

  /* 获取iOS系统版本
  String version = Platform.operatingSystemVersion
      .substring(8, Platform.operatingSystemVersion.indexOf(' ('));
  if (double.parse(version) >= 15.0) {
  } */
}
