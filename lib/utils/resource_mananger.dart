import 'dart:math';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class ImageHelper {
  static String wrapAssets(String url) {
    return "assets/images/" + url;
  }

  static String wrap2xAssets(String url) {
    return "assets/images/2.0x/" + url;
  }

  static Widget placeHolderIcon(
      {double width, double height, String defaultIcon}) {
    if (defaultIcon != null && defaultIcon.isNotEmpty) {
      return Image.asset(wrapAssets(defaultIcon), width: width, height: height);
    }
    return const SizedBox();
  }

  static Widget placeHolder({double width, double height}) {
    return SizedBox(
        width: width,
        height: height,
        child: CupertinoActivityIndicator(radius: min(10.0, width / 3)));
  }

  static Widget error({double width, double height, double size}) {
    return SizedBox(
        width: width,
        height: height,
        child: Icon(
          Icons.error_outline,
          size: size,
        ));
  }
}

class IconFonts {
  IconFonts._();

  /// iconfont:flutter base
  static const String fontFamily = 'iconfont';
  static const IconData pageEmpty = IconData(0xe63c, fontFamily: fontFamily);
  static const IconData pageError = IconData(0xe600, fontFamily: fontFamily);
  static const IconData pageNetworkError =
      IconData(0xe678, fontFamily: fontFamily);
  static const IconData pageUnAuth = IconData(0xe65f, fontFamily: fontFamily);
}
