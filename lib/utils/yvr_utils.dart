import 'dart:math';
import 'package:device_info/device_info.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';

import '../generated/l10n.dart';
import 'log.dart';

class YvrUtils {
  // 当前语言
  // static Locale curLocale;

  // 格式化手机号, 添加空格
  static String formatPhone(account) {
    String phoneStr = account.toString().replaceAll(' ', '');
    if (phoneStr.length > 7 && phoneStr.length <= 11) {
      phoneStr = phoneStr.substring(0, 7) + ' ' + phoneStr.substring(7);
      phoneStr = phoneStr.substring(0, 3) + ' ' + phoneStr.substring(3);
    }
    if (phoneStr.length > 3 && phoneStr.length < 8) {
      phoneStr = phoneStr.substring(0, 3) + ' ' + phoneStr.substring(3);
    }
    return phoneStr;
  }

  // 手机号去除空格
  static String getPhone(account) {
    return account.toString().replaceAll(' ', '');
  }

  // 判断手机号是否符合规则
  static bool isPhoneNum(String account) {
    return account.length == 11;
  }

  // 验证是否为数字
  static bool isNumber(String str) {
    // const _numRegExp = r"^[0-9A-Za-z\@\.]+$";
    final reg = RegExp(r"^[0-9]+$");
    print('$str 是否全是数字 =========>${reg.hasMatch(str)}');
    return reg.hasMatch(str);
  }

  // 验证是否为字母
  static bool isLetter(String str) {
    final reg = RegExp(r"^[A-Za-z]+$");
    print('$str 是否全是字母 =========>${reg.hasMatch(str)}');
    return reg.hasMatch(str);
  }

  // 是否是基本密码
  static bool isPassword(String pwd) {
    /*final _pwdRegExp = r"^[A-Za-z0-9_\$!~&=%#@\[\]\.\_\-\+\`\|\{\}\?\^\*\/]+$";
    // final reg = RegExp(r"^[0-9A-Za-z]+$");
    final reg = RegExp(_pwdRegExp);
    print('$pwd 是否包含密码相关字符 =========>${reg.hasMatch(pwd)}');
    return reg.hasMatch(pwd);*/
    bool reg = pwd.contains(RegExp(r'[a-z]'));
    bool reg2 = pwd.contains(RegExp(r'[A-Z]'));
    bool reg3 = pwd.contains(RegExp(r'[0-9]'));
    print('$pwd 是否包含字母 =========>$reg');
    print('$pwd 是否包含大写字母 =========>$reg2');
    print('$pwd 是否包含数字 =========>$reg3');
    return reg3 && (reg || reg2);
  }

  // 判断手机号和邮箱是否符合规则
  static bool isPhoneAndEmain(String account) {
    Log.d('account ===============>$account');
    if (account.length == 0) {
      return false;
    } else {
      if (isNumber(account) &&
          account.length == 11 &&
          account.substring(0, 1) == '1') {
        return true;
      }
      if (isEmail(account)) {
        return true;
      } else {
        return false;
      }
    }
  }

  //检查密码是否符合规则
  static bool checkPwd(String pwd) {
    if (pwd.length < 6) {
      return false;
    }
    if (isNumber(pwd) || isLetter(pwd)) {
      return false;
    }
    return YvrUtils.isPassword(pwd);
  }

  // 判断邮箱是否符合规则
  static bool isEmail(String email) {
    RegExp regex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    return regex.hasMatch(email);
  }

  // 字符串补0
  static String fillInt(int number) {
    if (number < 10) {
      return '0$number';
    }
    return '$number';
  }

  static Future<String> getPlatformUid() async {
    String identifier;
    final DeviceInfoPlugin deviceInfoPlugin = new DeviceInfoPlugin();
    try {
      if (Platform.isAndroid) {
        var build = await deviceInfoPlugin.androidInfo;
        identifier = build.androidId; //UUID for Android
      } else if (Platform.isIOS) {
        var data = await deviceInfoPlugin.iosInfo;
        identifier = data.identifierForVendor; //UUID for iOS
      }
    } on PlatformException {
      print('Failed to get platform version');
    }
    return identifier;
  }

  Color getRandomColor({int r = 255, int g = 255, int b = 255, a = 255}) {
    if (r == 0 || g == 0 || b == 0) return Colors.black;
    if (a == 0) return Colors.white;
    return Color.fromARGB(
      a,
      r != 255 ? r : Random.secure().nextInt(r),
      g != 255 ? g : Random.secure().nextInt(g),
      b != 255 ? b : Random.secure().nextInt(b),
    );
  }

  static resetUserInfo({String environment}) async {
    await StorageManager.localStorage.clear();
    await StorageManager.localStorage
        .setItem("Environment", environment ?? "PROD");
  }

  static String getSexText(int sex) {
    switch (sex) {
      case 1:
        return YLocal.current.nan;
      case 2:
        return YLocal.current.nv;
      case 3:
        return YLocal.current.baomi;
      default:
        return YLocal.current.weizhi;
    }
  }
}

/**
 * flutter build apk --debug   flutter build ios --debug<TestFlight>
 * 切换flutter版本：https://www.jianshu.com/p/ada4c6684934
 * 终端查看本地证书: security find-identity -v -p codesigning
 * 运行iOS14手机上
 * 1. 拷贝新项目后
 * 2. FLUTTER_BUILD_MODE release（Edit scheme->Build Configuration->Release）
 * 3. cd ios/
    flutter clean;
    rm ios/Podfile ios/Podfile.lock pubspec.lock;
    rm -rf ios/Pods ios/Runner.xcworkspace;
    flutter run
 */

/**
 * 账号
 * 18217604531   q123123
 * 13818711276  Hejiani123
 *
 *  TAG格式说明
    用于说明 commit的类别，类别只允许使用下面的标识。
    feat：新功能（feature）。
    fix：修复bug，可以是QA发现的BUG，也可以是研发自己发现的BUG。
    docs：文档（documentation）。
    refactor：重构（即不是新增功能，也不是修改bug的代码变动）。
    perf：优化相关，比如提升性能、体验。
    test：增加测试。
    revert：回滚到上一个版本。
    merge：代码合并。
    sync：同步主线或分支的Bug。
 */

/*
  flutter_blue: ^0.8.0  #蓝牙 BLE
  flutter_bluetooth_serial: ^0.3.2 #暂时只支持Android
  flutter_scan_bluetooth: ^2.0.2   #Android 全设备 iOS BLE
  flutter_beacon #BLE
  flutter_bt_bluetooth #BLE
  flutter-ble #BLE
  https://github.com/flutter/flutter/issues/23562
*/

// 打开Android Studio- Terminal排查Android问题：./gradlew build --stacktrace
