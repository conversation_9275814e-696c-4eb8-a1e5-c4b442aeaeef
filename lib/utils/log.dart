import 'package:logger/logger.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';

class Log {
  static Logger _logger = Logger(
    printer: PrefixPrinter(PrettyPrinter(colors: Platform.isAndroid)),
  );

  static void v(dynamic message) {
    _logger.v(message);
  }

  static void d(dynamic message) {
    _logger.d(message);
  }

  static void i(dynamic message) {
    _logger.i(message);
  }

  static void w(dynamic message) {
    _logger.w(message);
  }

  static void e(dynamic message) {
    _logger.e(message);
  }

  static void wtf(dynamic message) {
    _logger.wtf(message);
  }
}
