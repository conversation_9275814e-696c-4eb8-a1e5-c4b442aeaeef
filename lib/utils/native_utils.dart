import 'package:flutter/services.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';

class NativeUtils {
  static var yvrIos = MethodChannel('yvr_ios');
  static var yvrAndroid = MethodChannel('yvr_android');

  static Future<bool> isOpenLocation() async {
    if (Platform.isIOS) {
      return true;
    } else {
      bool result = await yvrAndroid.invokeMethod('isOpenLocation');
      Log.d('是否开启定位服务 ====flutter=====>$result');
      return result;
    }
  }

  static void openLocationSettings() {
    if (Platform.isAndroid) {
      yvrAndroid.invokeMethod('openLocationSettings');
    }
  }
}
