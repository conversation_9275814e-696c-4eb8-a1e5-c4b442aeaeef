import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:image_downloader/image_downloader.dart';

import '../generated/l10n.dart';
import '../public_ui/widget/toast_show.dart';
import 'permission_util.dart';

class DownloadUtils {
  static Future<bool> downloadImage(BuildContext context, String url) async {
    if (!await PermissionUtil.checkPhotoPermission(context)) {
      return false;
    }

    var imageId = await ImageDownloader.downloadImage(
      url,
      destination: AndroidDestinationType.directoryPictures
        ..subDirectory("YVR/${url.split('/').last}"),
    );
    if (imageId == null) {
      return false;
    }
    var fileName = await ImageDownloader.findName(imageId);
    var path = await ImageDownloader.findPath(imageId);
    var size = await ImageDownloader.findByteSize(imageId);
    var mimeType = await ImageDownloader.findMimeType(imageId);
    debugPrint(
        "imageId:$imageId,fileName:$fileName,path:$path,size:$size,mimeType:$mimeType");
    if (Platform.isAndroid) {
      YvrToast.showToast("${YLocal.of(context).yibaocunzhi} $path");
    } else {
      YvrToast.showToast(YLocal.current.baocunchenggong);
    }
    return true;
  }
}
