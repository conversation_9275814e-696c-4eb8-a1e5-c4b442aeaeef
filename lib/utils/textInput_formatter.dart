// 字母数字特殊字符
import 'package:flutter/services.dart';

const _pwdRegExp = r"^[A-Za-z0-9_\$!~&=%#@\[\]\.\_\-\+\`\|\{\}\?\^\*\/]+$";
const _numRegExp = r"^[0-9A-Za-z\@\.\_]+$";
// const _accountRegExp = "[A-Z,a-z,0-9]";
const _noSpaceRegExp = r"^\S+$";

//[FilteringTextInputFormatter.allow(RegExp("[A-Z,a-z,0-9]"))]

List<TextInputFormatter> inputNumberFormatters = [
  FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
  FilteringTextInputFormatter.digitsOnly
];

final TextInputFormatter kNoSpaceFormatter =
    FilteringTextInputFormatter.deny(RegExp(r'\s'));

class PwdFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    if (newValue.text.length > 0) {
      if (RegExp(_pwdRegExp).hasMatch(newValue.text)) {
        return newValue;
      }
      return oldValue;
    }
    return newValue;
  }
}

class NumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    if (newValue.text.length > 0) {
      if (RegExp(_numRegExp).hasMatch(newValue.text)) {
        return newValue;
      }
      return oldValue;
    }
    return newValue;
  }
}

class NoSpaceFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    if (newValue.text.length > 0) {
      if (RegExp(_noSpaceRegExp).hasMatch(newValue.text)) {
        return newValue;
      }
      return oldValue;
    }
    return newValue;
  }
}
