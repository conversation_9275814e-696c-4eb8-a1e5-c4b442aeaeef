class CollectionUtils {
  ///去除重复
  ///unique:获取唯一标识
  static void retainUnique<E, U>(List<E> list, U unique(E e)) {
    Set<U> u = list.map<U>((e) => unique(e)).toSet();
    list.retainWhere((element) => u.remove(unique(element)));
  }

  ///从list中去除relative已经存在的element
  ///unique:获取唯一标识
  static void removeRepeat<E, U>(List<E> list, List<E> relative, U unique(E e)) {
    list.removeWhere((e) {
      U current = unique(e);
      E relate = relative.firstWhere((e2) => current == unique(e2),
          orElse: () => null);
      return relate != null;
    });
  }
}
