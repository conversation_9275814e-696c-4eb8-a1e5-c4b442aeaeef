//dart实例 深拷贝字符串
class DeepCopy {
  String strValue;
  int intValue;

  DeepCopy({this.strValue, this.intValue});

  DeepCopy.fromJson(Map<String, dynamic> json) {
    strValue = json['string'];
    intValue = json['int'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['string'] = this.strValue;
    data['int'] = this.intValue;
    return data;
  }
}
