import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as request;
import 'package:common_utils/common_utils.dart';
import 'package:yvr_assistant/model/user_model.dart';
import 'package:yvr_assistant/manager/environment.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';

/// 最少上传数据条数；
final int _minCount = 10;
final String kDataRecord = "kDataRecord";
String _phoneModel;
String _appVersion;
String _systemVersion;
String _snCode;

class DataRecord {
  initDefaultData() async {
    _snCode = await PlatformUtils.getDeviceId();
    _phoneModel = await PlatformUtils.getDeviceModel();
    _appVersion = await PlatformUtils.getAppVersion();
    _systemVersion = Platform.operatingSystemVersion;
  }

  saveData({
    @required String eventId,
    Map extraData,
  }) async {
    final date = DateUtil.getNowDateStr();
    // Log.e("此时用户ID为：${yvrUserId()}");

    Map defaultData = {
      "phoneModel": _phoneModel,
      "appVersion": _appVersion,
      "userId": yvrUserId(),
      "snCode": _snCode,
      "systemVersion": _systemVersion,
    };
    final Map<dynamic, dynamic> item = {
      "eventId": eventId,
      "timeData": date,
      "defaultData": defaultData,
      "extraData": extraData ?? {}
    };
    if (await StorageManager.foreverData.ready) {
      List dataList = StorageManager.foreverData.getItem(kDataRecord) ?? [];
      dataList.insert(0, item);

      if (dataList.length >= _minCount) {
        await StorageManager.foreverData.deleteItem(kDataRecord);
        uploadData(dataList);
      } else {
        await StorageManager.foreverData.setItem(kDataRecord, dataList);
        // Log.e("埋点记录:\n$dataList");
      }
    }
  }

  int yvrUserId() {
    int userId = -1;
    if (StorageManager.localStorage.getItem(kUser) != null) {
      UserModel userModel =
          UserModel.fromJson(StorageManager.localStorage.getItem(kUser));
      userId = userModel.actId;
    }
    return userId;
  }

  uploadData(dynamic dataList) {
    // Log.e("埋点数据：\n${jsonEncode(dataList)}");
    var jsonParams = utf8.encode(json.encode(dataList));
    var reqUri = Uri.parse(
        "${Environment().config.apiHost}vrmcsys/datacenter/mobile_data_upload");
    request.Client().post(reqUri,
        body: jsonParams,
        headers: {"Content-Type": "application/json"}).then((response) async {
      if (response.statusCode == HttpStatus.ok) {
        //Log.e("埋点成功上报:\n$dataList");
      }
    }).catchError((error) async {
      //Log.e("埋点接口访问失败:\n$error");
      if (await StorageManager.foreverData.ready) {
        await StorageManager.foreverData.setItem(kDataRecord, dataList);
      }
    });
  }
}
