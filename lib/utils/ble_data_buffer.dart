class BleDataBuffer {
  Map<int, List<List<int>>> buffers = Map<int, List<List<int>>>();

  bool write(List<int> data, {int key}) {
    int k = key == null ? data[0] : key;
    int len = data[1];
    int index = data[2];

    if (len == 0) {
      ///不需要分包
      buffers[k] = List<List<int>>.generate(1, (index) => data, growable: true);
      return true;
    }
    assert(len >= 2);
    assert(index >= 1);
    if (index == 1) {
      ///第一个包
      buffers[k] = List<List<int>>.generate(1, (index) => data, growable: true);
      return false;
    }

    List<List<int>> values = buffers[k];
    assert(values != null && values.isNotEmpty);
    values.add(data);
    return index == len;
  }

  String toStringData(int key) {
    List<List<int>> values = buffers.remove(key);
    if (values == null) {
      return null;
    }
    StringBuffer stringBuffer = StringBuffer();
    for (var e in values) {
      if (e.length > 3) {
        stringBuffer.write(String.fromCharCodes(e, 3, e.length));
      }
    }
    return stringBuffer.toString();
  }

  bool has(int key) {
    return buffers.containsKey(key);
  }

  void clear() {
    buffers.clear();
  }
}
