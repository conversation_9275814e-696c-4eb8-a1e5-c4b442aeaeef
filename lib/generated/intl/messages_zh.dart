// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a zh locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'zh';

  static m0(date_0) => "${date_0}购买";

  static m1(str_0) => "你如何评价${str_0}？";

  static m2(str_0) => "我知悉本次参与的VR产品打卡试用活动将会获得不同激励金额，我已阅读并同意${str_0}，根据国家法律规定，玩出梦想将为我申报个人所得税。";

  static m3(num_0, num_1, num_2) => "${num_0}:${num_1}:${num_2} 后结束";

  static m4(num_0, num_1) => "${num_0}:${num_1}后结束";

  static m5(num_0) => "${num_0}Y币";

  static m6(num_0) => "${num_0}Y币已发放，可在商店中进行消费，感谢您对玩出梦想的喜爱和支持！";

  static m7(num_0) => "${num_0}分钟";

  static m8(num_0) => "${num_0}分钟后结束";

  static m9(num_0) => "${num_0}分钟前";

  static m10(num_0) => "${num_0}秒后结束";

  static m11(num_0) => "${num_0}人参加";

  static m12(num_0) => "${num_0}人评分";

  static m13(num_0) => "${num_0}人评价";

  static m14(num_0) => "${num_0}人已报名";

  static m15(num_0) => "${num_0}人已参加";

  static m16(num_0) => "${num_0}折";

  static m17(num_0) => "${num_0}天后结束";

  static m18(num_0) => "${num_0}小时";

  static m19(num_0) => "${num_0}小时后结束";

  static m20(num_0) => "${num_0}小时前";

  static m21(num_0) => "${num_0}星";

  static m22(num_0) => "${num_0}星";

  static m23(num_0) => "${num_0}则评论";

  static m24(num_0) => "${num_0}张";

  static m25(num_0, str_0) => "${num_0}张\"${str_0}\"游戏券已发放，可在商店中进行消费，感谢您对玩出梦想的喜爱和支持！";

  static m26(num_0) => "${num_0}张可用";

  static m27(num_0) => "密码错误，您还剩余${num_0}次机会";

  static m28(str_0) => "《${str_0}》购买成功，快去您的VR设备上体验吧！";

  static m29(str_0) => "${str_0}  活动不存在";

  static m30(str_0) => "${str_0}  今日未打卡";

  static m31(str_0) => "${str_0}  已领奖";

  static m32(str_0) => "${str_0}  已完成打卡";

  static m33(str_0) => "与${str_0}是共同好友";

  static m34(str_0, str_1) => "“${str_0}”在“${str_1}”中未安装，请前往设备安装并体验。";

  static m35(str_0) => "${str_0}中的回复因涉嫌违规，系统已进行屏蔽处理。共建良好社区环境，请您文明发言。";

  static m36(str_0, str_1) => "${str_0}中的回复因涉嫌${str_1}，系统已进行屏蔽处理。共建良好社区环境，请您文明发言。";

  static m37(str_0) => "VR眼镜SN号${str_0}";

  static m38(str_0) => "1.VR应用/游戏：${str_0}";

  static m39(str_0) => "玩出梦想在此善意提醒您，注销账号为不可恢复的操作。为防止误操作，请仔细阅读以下提醒，再次确认是否注销玩出梦想账号并确认注销后的影响。建议您在注销前自行备份注销账号的相关信息，并请确认与该账号相关的所有服务均已进行妥善处理。注销成功后，我们将按照${str_0}相关条款处理您的个人信息，或对其进行匿名化处理。";

  static m40(num_0) => "2.Y币：${num_0}个";

  static m41(num_0) => "不可用游戏券${num_0}";

  static m42(str_0) => "打开${str_0}超时，请重试";

  static m43(num_0) => "当前在线人数${num_0}人";

  static m44(num_0) => "第 ${num_0} 行";

  static m45(num_0) => "第${num_0}行";

  static m46(str_0) => "该账号已申请注销，将于${str_0}删除。继续登录，将放弃账号注销。";

  static m47(str_0) => "购买《${str_0}》";

  static m48(num_0) => "${num_0}人评论";

  static m49(str_0) => "后端接口需兼容${str_0}字段";

  static m50(num_0) => "可用游戏券${num_0}";

  static m51(str_0) => "快去VR里体验“${str_0}”吧！";

  static m52(str_0) => "快去VR里体验 \"${str_0}\" 吧！";

  static m53(str_0) => "拉黑${str_0}";

  static m54(num_0) => "累计打卡${num_0}天";

  static m55(str_0) => "*流程码：${str_0}，请用原购买账号，前往对应电商平台咨询客服，进行最终核对。奖励将在审核通过后尽快发放，请留意短信通知";

  static m56(num_0) => "${num_0}接受了你的好友请求。";

  static m57(num_0) => "${num_0}邀请你参加活动。";

  static m58(str_0) => "你们都在玩${str_0}";

  static m59(str_0) => "您的VR设备向您请求购买《${str_0}》，是否前往购买？";

  static m60(num_0, str_0) => "您在应用商店中为《${str_0}》评价了${num_0}分。";

  static m61(num_0, str_0) => "您在应用商店中为《${str_0}》评价了${num_0}分，审核已通过。";

  static m62(num_0, str_0) => "您在应用商店中为《${str_0}》评价了${num_0}分，审核不通过，原因为：存在违规内容。";

  static m63(num_0, str_0, str_1) => "您在应用商店中为《${str_0}》评价了${num_0}分，审核不通过，原因为：存在${str_1}内容。";

  static m64(str_0) => "预计${str_0}上线";

  static m65(str_0) => "请对应用${str_0}打分";

  static m66(str_0, str_1) => "       欢迎使用玩出梦想！\n       为了更好地保证您的合法权益，请务必仔细阅读、充分理解我们的${str_0}和${str_1}各项内容，以帮助您了解我们对您个人信息的收集、保存、使用等情况及您所享有的相关权利。\n       我们需要收集您的手机号码、姓名、电子邮箱等信息，来帮助您进行账户的注册和登录，以及验证您的身份是否有效。\n       我们需要收集您所使用的设备的一些基本信息，包括硬件型号、操作系统版本号、设备标识符、网络设备硬件地址、服务日志、设备传感器数据等，以保障软件与服务的安全运行、运营的质量及效率。\n       我们可能会收集您的位置信息（您当时所处的地理位置），当您使用的浏览、搜索、匹配、推荐服务时为您推荐适合您区域位置的服务。\n       您可以透过开启装置设定，允许我们接受并存储例如您的GPS位置、相机功能或相片，蓝牙讯号，和附近Wi-Fi存取点、信标和基地台相关资讯。\n       当您使用提供的服务时，为保障服务安全稳定运行、防控风险、改进服务，我们的服务器会自动收集您使用的服务类别、方式及设备型号、IP地址、设备软件版本信息、设备识别码、设备标识、日志信息、上网记录。\n       我们仅会根据您使用我们产品与服务的具体功能需要，在您授权的范围内收集必要的用户信息。我们未获得您明确授权的情况下，不会共享、转让、公开披露您的个人信息。所有信息均存储在中华人民共和国境内。\n       您可以在“我的”中查看、变更、删除个人信息并管理您的授权。\n       若您对以上内容有任何疑问、意见或建议，欢迎致电客服400-618-1160与我们联系。\n       点击下方“同意并继续”按钮，即代表您同意以上协议内容，祝您使用愉快！\n";

  static m67(num_0) => "请输入${num_0}位数字";

  static m68(str_0) => "请输入${str_0}";

  static m69(str_0) => "请先开启${str_0}权限";

  static m70(str_0) => "删除${str_0}";

  static m71(num_0) => "(剩余${num_0})";

  static m72(num_0, num_1) => "(剩余${num_0}，需支付${num_1})";

  static m73(num_0) => "${num_0}人参加";

  static m74(num_0) => "送${num_0}Y币";

  static m75(str_0) => "我已阅读并同意${str_0}";

  static m76(str_0, str_1) => "我已阅读并同意玩出梦想${str_0}和${str_1}";

  static m77(str_0) => "要参加活动，你需要下载应用${str_0}";

  static m78(num_0) => "已玩${num_0}分钟";

  static m79(num_0) => "已玩${num_0}小时";

  static m80(num_0) => "3.游戏券：${num_0}张";

  static m81(num_0) => "预计还需${num_0}s";

  static m82(str_0, str_1) => "使用邮箱注册代表你已经同意玩出梦想${str_0}和${str_1}";

  static m83(str_0) => "正在使用${str_0}";

  static m84(str_0) => "正在使用应用${str_0}";

  static m85(num_0) => "最多允许上传${num_0}张图片";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static _notInlinedMessages(_) => <String, Function> {
    "AddNewAddress" : MessageLookupByLibrary.simpleMessage("新建收货地址"),
    "AdditionalContent" : MessageLookupByLibrary.simpleMessage("附加内容"),
    "AgeAdvice" : MessageLookupByLibrary.simpleMessage("年龄建议："),
    "AllApps" : MessageLookupByLibrary.simpleMessage("全部应用"),
    "AllGames" : MessageLookupByLibrary.simpleMessage("全部游戏"),
    "Application" : MessageLookupByLibrary.simpleMessage("应用"),
    "Appointment" : MessageLookupByLibrary.simpleMessage("预约"),
    "Attention" : MessageLookupByLibrary.simpleMessage("注意"),
    "BackToCheck" : MessageLookupByLibrary.simpleMessage("返回检查"),
    "CheckInActivity" : MessageLookupByLibrary.simpleMessage("打卡签到活动"),
    "Comfort" : MessageLookupByLibrary.simpleMessage("舒适度："),
    "ComfortRating" : MessageLookupByLibrary.simpleMessage("舒适度评级"),
    "Comfortable" : MessageLookupByLibrary.simpleMessage("舒适"),
    "CommonQuestion" : MessageLookupByLibrary.simpleMessage("常见问题"),
    "CompatibilityRating" : MessageLookupByLibrary.simpleMessage("兼容性评级"),
    "ConfirmOrder" : MessageLookupByLibrary.simpleMessage("确认订单"),
    "Confrontation" : MessageLookupByLibrary.simpleMessage("对抗"),
    "ConnectNearbyDevicesPermission" : MessageLookupByLibrary.simpleMessage("扫描蓝牙需要您在玩出梦想应用设置中先开启连接附近的设备权限"),
    "ControlMode" : MessageLookupByLibrary.simpleMessage("控制方式："),
    "Cooperate" : MessageLookupByLibrary.simpleMessage("合作"),
    "CurrentlyAvailablePoints" : MessageLookupByLibrary.simpleMessage("当前可用积分"),
    "DataAccumulation" : MessageLookupByLibrary.simpleMessage("暂无评分，数据累积中"),
    "Dgoumai" : m0,
    "EditAddress" : MessageLookupByLibrary.simpleMessage("编辑收货地址"),
    "EmailAddressHasBeenRegistered" : MessageLookupByLibrary.simpleMessage("此邮箱已注册"),
    "ExchangeRecord" : MessageLookupByLibrary.simpleMessage("兑换记录"),
    "ExchangeResult" : MessageLookupByLibrary.simpleMessage("兑换结果"),
    "Explore" : MessageLookupByLibrary.simpleMessage("探索"),
    "ExploreArea" : MessageLookupByLibrary.simpleMessage("探索专区的应用可能在开发或测试中，可参考兼容性评级和游玩建议进行体验"),
    "FailedToVerifyTheDigits" : MessageLookupByLibrary.simpleMessage("身份证号码位数验证失败"),
    "FitsMostPlayers" : MessageLookupByLibrary.simpleMessage("不适合少部分玩家，但适应后可变为舒适。"),
    "GameMode" : MessageLookupByLibrary.simpleMessage("游戏模式："),
    "Gamepad" : MessageLookupByLibrary.simpleMessage("游戏手柄"),
    "Games" : MessageLookupByLibrary.simpleMessage("游戏"),
    "GiftCard" : MessageLookupByLibrary.simpleMessage("礼品卡"),
    "HandTracking" : MessageLookupByLibrary.simpleMessage("手势追踪"),
    "HowDoYouEvaluate" : m1,
    "IAgreeThatYVRWill" : MessageLookupByLibrary.simpleMessage("我同意玩出梦想使用第三方服务进行发放奖励、退税等操作，需要向该等第三方提供我的个人信息。玩出梦想承诺玩出梦想和该第三方均有数据安全保障措施和管理水平，并对全部信息严格保密。"),
    "IConfirmThatTheAbove" : MessageLookupByLibrary.simpleMessage("我确认以上信息无误。信息一旦提交将不能修改，如信息错误导致未能取得奖励由本人承担责任。"),
    "IDCardError" : MessageLookupByLibrary.simpleMessage("身份证号码校验未通过"),
    "IKnownThisVRAcitivity" : m2,
    "IUnderstandAndAgree" : MessageLookupByLibrary.simpleMessage("我知悉并同意为打卡活动提供的信息，且知悉以上信息包含个人信息。"),
    "IncorrectEmailAddress" : MessageLookupByLibrary.simpleMessage("邮箱地址不正确"),
    "Individual" : MessageLookupByLibrary.simpleMessage("单人"),
    "KeepTimeSynchronized" : MessageLookupByLibrary.simpleMessage("请保持手机和设备时间同步"),
    "Keyboard" : MessageLookupByLibrary.simpleMessage("键盘"),
    "Latest" : MessageLookupByLibrary.simpleMessage("最新"),
    "LoginFailure" : MessageLookupByLibrary.simpleMessage("登录失败"),
    "LoginPasswordRules" : MessageLookupByLibrary.simpleMessage("密码要求：8-12位，数字、大小写字母、符号需至少出现3种"),
    "MajorUpdate" : MessageLookupByLibrary.simpleMessage("主要更新"),
    "ManyPeople" : MessageLookupByLibrary.simpleMessage("多人"),
    "Moderate" : MessageLookupByLibrary.simpleMessage("适中"),
    "MyPoints" : MessageLookupByLibrary.simpleMessage("我的积分"),
    "NNNhoujieshu" : m3,
    "NNhoujieshu" : m4,
    "NYbi" : m5,
    "NYbiyifafangkezaisha" : m6,
    "Nervous" : MessageLookupByLibrary.simpleMessage("紧张"),
    "Nfenzhong" : m7,
    "Nfenzhonghoujieshu" : m8,
    "Nfenzhongqian" : m9,
    "Nmiaohoujieshu" : m10,
    "NoPointDetailsYet" : MessageLookupByLibrary.simpleMessage("暂无积分明细哦"),
    "NoRatingYet" : MessageLookupByLibrary.simpleMessage("暂无评分"),
    "NotJoinPunch" : MessageLookupByLibrary.simpleMessage("暂未参加任何打卡活动"),
    "NotPossess" : MessageLookupByLibrary.simpleMessage("未拥有"),
    "NrencanjiaNrencenjia" : m11,
    "Nrenpingfen" : m12,
    "Nrenpinglun" : m13,
    "Nrenyibaoming" : m14,
    "NrenyicanjiaNrenyice" : m15,
    "NsheNzhe" : m16,
    "Ntianhoujieshu" : m17,
    "Nxiaoshi" : m18,
    "Nxiaoshihoujieshu" : m19,
    "Nxiaoshiqian" : m20,
    "Nxing" : m21,
    "Nxing_2" : m22,
    "Nzepinglun" : m23,
    "Nzhang" : m24,
    "NzhangSyouhuquanyifa" : m25,
    "Nzhangkeyong" : m26,
    "OrderDetails" : MessageLookupByLibrary.simpleMessage("订单详情页"),
    "PageNotFound" : MessageLookupByLibrary.simpleMessage("请求页面不存在"),
    "PasswordsError" : m27,
    "PayPal" : MessageLookupByLibrary.simpleMessage("PayPal"),
    "PlatformServiceAgreement" : MessageLookupByLibrary.simpleMessage("《平台服务协议》"),
    "PlayForDream" : MessageLookupByLibrary.simpleMessage("玩出梦想"),
    "PleaseEnterAtLeastFive" : MessageLookupByLibrary.simpleMessage("应用评价至少为5个字"),
    "PleaseFillInCorrectly" : MessageLookupByLibrary.simpleMessage("请正确填写，信息缺漏、错误，则视为申领无效"),
    "PleaseFillInTheIDNumber" : MessageLookupByLibrary.simpleMessage("请填写银行卡号实名认证人的身份证号码（根据相关财税规定，请务必正确填写身份证号码，且年满18周岁，信息仅用于校验，我们将对您的信息保密）"),
    "PleaseFillInYourPurchase" : MessageLookupByLibrary.simpleMessage("请填写您的购买渠道"),
    "PleaseInstallAppAndExperience" : MessageLookupByLibrary.simpleMessage("请先安装应用并体验5分钟再来评价吧！"),
    "PleaseTickTheAboveAgreement" : MessageLookupByLibrary.simpleMessage("请先勾选以上协议"),
    "PleaseUploadAPaymentScreenshot" : MessageLookupByLibrary.simpleMessage("请上传付款截图（支付宝/微信截图）"),
    "PointsMall" : MessageLookupByLibrary.simpleMessage("积分商城"),
    "PointsMallIsUnderConstruction" : MessageLookupByLibrary.simpleMessage("积分商城预计7月上线，敬请期待"),
    "PointsRules" : MessageLookupByLibrary.simpleMessage("积分规则"),
    "Popular" : MessageLookupByLibrary.simpleMessage("热门"),
    "PrivacySetting" : MessageLookupByLibrary.simpleMessage("隐私设置"),
    "ProductDetails" : MessageLookupByLibrary.simpleMessage("商品详情"),
    "PublicScope" : MessageLookupByLibrary.simpleMessage("公开范围"),
    "Recommend" : MessageLookupByLibrary.simpleMessage("推荐"),
    "RelatedActivity" : MessageLookupByLibrary.simpleMessage("相关活动"),
    "RelatedContentHasBeenRemoved" : MessageLookupByLibrary.simpleMessage("相关内容已下架"),
    "RemoteShooting" : MessageLookupByLibrary.simpleMessage("遥控拍摄"),
    "SafePay" : MessageLookupByLibrary.simpleMessage("支付安全"),
    "SelectGames" : MessageLookupByLibrary.simpleMessage("选择游戏"),
    "Sgoumaichenggongkuai" : m28,
    "Share" : MessageLookupByLibrary.simpleMessage("分享"),
    "ShippingAddress" : MessageLookupByLibrary.simpleMessage("收货地址"),
    "Shuodongbucunzai" : m29,
    "SjinriweidakaSjinriw" : m30,
    "SomePlayersAreNotSuitable" : MessageLookupByLibrary.simpleMessage("部分玩家可能产生眩晕等不适，刚接触VR的玩家请谨慎体验。"),
    "SportsPoster" : MessageLookupByLibrary.simpleMessage("运动海报"),
    "StarAll" : MessageLookupByLibrary.simpleMessage("全部评分"),
    "Star_1" : MessageLookupByLibrary.simpleMessage("1星评分"),
    "Star_2" : MessageLookupByLibrary.simpleMessage("2星评分"),
    "Star_3" : MessageLookupByLibrary.simpleMessage("3星评分"),
    "Star_4" : MessageLookupByLibrary.simpleMessage("4星评分"),
    "Star_5" : MessageLookupByLibrary.simpleMessage("5星评分"),
    "Stores" : MessageLookupByLibrary.simpleMessage("线下门店"),
    "SuitableForAllPlayers" : MessageLookupByLibrary.simpleMessage("适合绝大多数玩家体验。"),
    "SupportedPlatforms" : MessageLookupByLibrary.simpleMessage("支持平台："),
    "Syilingjiang" : m31,
    "SyiwanchengdakaSyiwa" : m32,
    "SyuSshigongtonghaoyo" : m33,
    "SzaiSzhongweian" : m34,
    "Szhongdehuifuyinshex" : m35,
    "Szhongdehuifuyinshex_1" : m36,
    "TheAccountHasBeenLocked" : MessageLookupByLibrary.simpleMessage("账号已锁定，请五分钟后尝试。"),
    "TheComfortOfVRPlay" : MessageLookupByLibrary.simpleMessage("VR的游玩舒适度取决于游戏优化、玩法模式和玩家适应性等因素，可参考舒适度评级选择适合的游戏："),
    "TheEvaluationIsUnderReview" : MessageLookupByLibrary.simpleMessage("评价将在审核通过后展示"),
    "TheEvaluationYouSubmit" : MessageLookupByLibrary.simpleMessage("您提交的评价会经过平台审核后展示"),
    "TipszaiVRyanjingzhon" : MessageLookupByLibrary.simpleMessage("Tips：\n\n1.在VR眼镜中登录相同账号，即完成添加。可返回列表，无需重复操作\n\n2.设备号位于VR眼镜的【设置中心】-【通用】-【关于本机】中\n\n3.如无法添加VR眼镜，请检查玩出梦想相关权限是否打开后重试"),
    "Today" : MessageLookupByLibrary.simpleMessage("今天"),
    "Tomorrow" : MessageLookupByLibrary.simpleMessage("明天"),
    "VRController" : MessageLookupByLibrary.simpleMessage("VR手柄"),
    "VRduanyizhongzhitoub" : MessageLookupByLibrary.simpleMessage("VR端已终止投屏！"),
    "VRnarongzhiyushangfa" : MessageLookupByLibrary.simpleMessage("VR内容置于上方\n手机录制内容置于下方"),
    "VRnarongzhiyuzuocens" : MessageLookupByLibrary.simpleMessage("VR内容置于左侧\n手机录制内容置于右侧"),
    "VRshebeigoumaiqingqi" : MessageLookupByLibrary.simpleMessage("VR设备购买请求"),
    "VRshebeijiangzailian" : MessageLookupByLibrary.simpleMessage("VR设备将在连网时自动下载该应用"),
    "VRshipinchuanshuzhon" : MessageLookupByLibrary.simpleMessage("VR视频传输中..."),
    "VRweizhu" : MessageLookupByLibrary.simpleMessage("VR为主"),
    "VRyanjingSNhaoS" : m37,
    "VRyanjingchuyuqingsh" : MessageLookupByLibrary.simpleMessage("VR眼镜处于青少年模式，无法再次添加。"),
    "VRyanjingduankailian" : MessageLookupByLibrary.simpleMessage("VR眼镜断开连接，请重新配对蓝牙"),
    "VRyanjingjiangtongbu" : MessageLookupByLibrary.simpleMessage("VR眼镜将同步删除此文件，删除后无法还原，是否删除？"),
    "VRyanjinglianjiechen" : MessageLookupByLibrary.simpleMessage("VR眼镜连接成功"),
    "VRyanjingmofalianjie" : MessageLookupByLibrary.simpleMessage("VR眼镜无法连接此Wi-Fi，请重试"),
    "VRyanjingnacunbuzuqi" : MessageLookupByLibrary.simpleMessage("VR眼镜内存不足，请清理内存后再开始录制"),
    "VRyanjingnacunbuzuqi_1" : MessageLookupByLibrary.simpleMessage("VR眼镜内存不足，请清理内存后再使用拼接录制功能"),
    "VRyanjingtianjiachen" : MessageLookupByLibrary.simpleMessage("VR眼镜添加成功！"),
    "VRyanjingtoubingshib" : MessageLookupByLibrary.simpleMessage("VR眼镜投屏失败"),
    "VRyanjingyizhongzhip" : MessageLookupByLibrary.simpleMessage("VR眼镜已终止拼接录制"),
    "VRyanjingyizhongzhit" : MessageLookupByLibrary.simpleMessage("VR眼镜已终止投屏"),
    "VRyanjingyushoujiwan" : MessageLookupByLibrary.simpleMessage("VR眼镜与手机网络不一致，请连接至相同网络后再重试"),
    "VRyingyongyouhuS1VRy" : m38,
    "WiFibuyizhibufengong" : MessageLookupByLibrary.simpleMessage("Wi-Fi不一致，部分功能受限"),
    "WriteReviewAndMoreFeedback" : MessageLookupByLibrary.simpleMessage("写评价留下更多反馈吧！"),
    "YVRGoSport" : MessageLookupByLibrary.simpleMessage("YVR GO运动"),
    "YVRRefundPolicy" : MessageLookupByLibrary.simpleMessage("玩出梦想退款政策"),
    "YVRshebeihaoSNhao" : MessageLookupByLibrary.simpleMessage("YVR设备号（SN号）"),
    "YVRzaicishanyidixing" : MessageLookupByLibrary.simpleMessage("玩出梦想在此善意提醒您，注销账号为不可恢复的操作，账号注销后您将无法再使用该账号或找回您购买、浏览、收藏的任何内容或信息（即使您使用相同的手机号码再次注册并使用玩出梦想平台）。"),
    "YVRzaicishanyidixing_1" : m39,
    "YVRzhanghao" : MessageLookupByLibrary.simpleMessage("玩出梦想账号"),
    "Ybi" : MessageLookupByLibrary.simpleMessage("Y币"),
    "YbiNge" : m40,
    "Ybichongzhi" : MessageLookupByLibrary.simpleMessage("Y币充值"),
    "Ybichongzhixieyi" : MessageLookupByLibrary.simpleMessage("《Y币充值协议》"),
    "YbifafangtongzhiYbif" : MessageLookupByLibrary.simpleMessage("Y币发放通知"),
    "Ybihuoqu" : MessageLookupByLibrary.simpleMessage("Y币获取"),
    "Ybishouzhimingxi" : MessageLookupByLibrary.simpleMessage("Y币收支明细"),
    "Ybixiaofei" : MessageLookupByLibrary.simpleMessage("Y币消费"),
    "Ybiyifafangkezaishan" : MessageLookupByLibrary.simpleMessage("Y币已发放，可在商店中进行消费，感谢您对玩出梦想的喜爱和支持！"),
    "Ybiyuebuzu" : MessageLookupByLibrary.simpleMessage("Y币余额不足"),
    "Ybiyuebuzuqingchongz" : MessageLookupByLibrary.simpleMessage("Y币余额不足，请充值"),
    "Ybizhifu" : MessageLookupByLibrary.simpleMessage("Y币支付"),
    "account_cancellation_agreement_url_dev" : MessageLookupByLibrary.simpleMessage("https://apitest.yvrdream.com/yvrdvcenter/#/cancellationagreement"),
    "account_cancellation_agreement_url_release" : MessageLookupByLibrary.simpleMessage("https://developer.yvrdream.com/#/cancellationagreement"),
    "account_yet" : MessageLookupByLibrary.simpleMessage("已有账号？"),
    "alipayPwdFree" : MessageLookupByLibrary.simpleMessage("支付宝免密支付"),
    "anzhuangyingyong" : MessageLookupByLibrary.simpleMessage("安装应用"),
    "banben" : MessageLookupByLibrary.simpleMessage("版本"),
    "banbengengxin" : MessageLookupByLibrary.simpleMessage("版本更新"),
    "baocun" : MessageLookupByLibrary.simpleMessage("保存"),
    "baocunchenggong" : MessageLookupByLibrary.simpleMessage("保存成功"),
    "baocunzhong" : MessageLookupByLibrary.simpleMessage("保存中"),
    "baocunzhong_1" : MessageLookupByLibrary.simpleMessage("保存中..."),
    "baohan" : MessageLookupByLibrary.simpleMessage("包含："),
    "baohanyingyong" : MessageLookupByLibrary.simpleMessage("包含应用"),
    "baomi" : MessageLookupByLibrary.simpleMessage("保密"),
    "baoqianmeiyouzhaodao" : MessageLookupByLibrary.simpleMessage("抱歉，没有找到附近的VR眼镜，请检查VR眼镜的蓝牙，确认开启再重新搜索。"),
    "bendexiangcebendixia" : MessageLookupByLibrary.simpleMessage("本地相册"),
    "bianjihuodong" : MessageLookupByLibrary.simpleMessage("编辑活动"),
    "bianjisirenhuodong" : MessageLookupByLibrary.simpleMessage("编辑私人活动"),
    "bianjiziliao" : MessageLookupByLibrary.simpleMessage("编辑资料"),
    "bindAlipay" : MessageLookupByLibrary.simpleMessage("确定要开通支付宝免密支付吗？"),
    "bindFail" : MessageLookupByLibrary.simpleMessage("绑定失败"),
    "bindSuccess" : MessageLookupByLibrary.simpleMessage("绑定成功"),
    "birthday" : MessageLookupByLibrary.simpleMessage("生日"),
    "bukeyongyouhuquanNzh" : m41,
    "bushidangnarongzaoch" : MessageLookupByLibrary.simpleMessage("不适当内容造成骚扰"),
    "buzhichiduobushoujit" : MessageLookupByLibrary.simpleMessage("不支持多部手机同时投屏！"),
    "buzhichigaiiPhonexin" : MessageLookupByLibrary.simpleMessage("不支持该iPhone型号"),
    "caijian" : MessageLookupByLibrary.simpleMessage("裁剪"),
    "cancel" : MessageLookupByLibrary.simpleMessage("取消"),
    "canjiabingwanchengda" : MessageLookupByLibrary.simpleMessage("参加并完成打卡任务后可获取50％购机款现金福利，具体信息参照活动详情。"),
    "canshubuduiwenjianwe" : MessageLookupByLibrary.simpleMessage("参数不对/文件为空"),
    "canshucuowucenshucuo" : MessageLookupByLibrary.simpleMessage("参数错误"),
    "canyushijiancenyushi" : MessageLookupByLibrary.simpleMessage("参与时间："),
    "cardsForCN" : MessageLookupByLibrary.simpleMessage("张"),
    "chakanquanbuzhakanqu" : MessageLookupByLibrary.simpleMessage("查看全部"),
    "chakanxiangqingzhaka" : MessageLookupByLibrary.simpleMessage("查看详情"),
    "chongxinluzhizhongxi" : MessageLookupByLibrary.simpleMessage("重新录制"),
    "chongyaozhongyao" : MessageLookupByLibrary.simpleMessage("重要"),
    "chongzhimimazhongzhi" : MessageLookupByLibrary.simpleMessage("重置密码"),
    "chongzhishibai" : MessageLookupByLibrary.simpleMessage("充值失败"),
    "chuangjian" : MessageLookupByLibrary.simpleMessage("创建"),
    "chuangjianhuodong" : MessageLookupByLibrary.simpleMessage("创建活动"),
    "chuangjiansirenhuodo" : MessageLookupByLibrary.simpleMessage("创建私人活动"),
    "chuliyuanyuzhouzhong" : MessageLookupByLibrary.simpleMessage("助力元宇宙中的健康成长"),
    "cimimaweiguanbiqings" : MessageLookupByLibrary.simpleMessage("此密码为关闭青少年模式及时间锁常用密码"),
    "cizhanghaokenengbeid" : MessageLookupByLibrary.simpleMessage("此账号可能被盗用"),
    "confirm" : MessageLookupByLibrary.simpleMessage("确定"),
    "confirmTheContract" : MessageLookupByLibrary.simpleMessage("确认签约"),
    "confirm_leave" : MessageLookupByLibrary.simpleMessage("确认退出吗?"),
    "congxiangcezhongxuan" : MessageLookupByLibrary.simpleMessage("从相册中选择"),
    "consume_time" : MessageLookupByLibrary.simpleMessage("消费时间"),
    "coupon" : MessageLookupByLibrary.simpleMessage("优惠券"),
    "cunchu" : MessageLookupByLibrary.simpleMessage("存储"),
    "cundaoxiangce" : MessageLookupByLibrary.simpleMessage("存到相册"),
    "cunzaijitaqinquanhan" : MessageLookupByLibrary.simpleMessage("存在其他侵权行为"),
    "cuxiaohuodongfasheng" : MessageLookupByLibrary.simpleMessage("促销活动发生变更，请刷新后重试"),
    "daifabudaifeibu" : MessageLookupByLibrary.simpleMessage("待发布"),
    "dakafanxiandaqiafanx" : MessageLookupByLibrary.simpleMessage("打卡活动"),
    "dakahuo50goujikuanxi" : MessageLookupByLibrary.simpleMessage("打卡获50%购机款现金"),
    "dakai" : MessageLookupByLibrary.simpleMessage("打开"),
    "dakaiSchaoshiqingcho" : m42,
    "dakailianjieshibai" : MessageLookupByLibrary.simpleMessage("打开链接失败"),
    "dakaiyingyong" : MessageLookupByLibrary.simpleMessage("打开应用"),
    "dakashujuyouyidingya" : MessageLookupByLibrary.simpleMessage("打卡数据有一定延迟，属于正常现象，请注意保持VR眼镜连网"),
    "dakatianshumeiyoudad" : MessageLookupByLibrary.simpleMessage("打卡天数没有达到活动打卡要求"),
    "dangVRyanjingzaifuji" : MessageLookupByLibrary.simpleMessage("当VR眼镜在附近时，请开启手机与VR眼镜的蓝牙，并确认玩出梦想的蓝牙和位置权限已开启。无法连接时，可点击屏幕上方的刷新按钮重新连接。"),
    "dangqianVRyanjingdia" : MessageLookupByLibrary.simpleMessage("当前VR眼镜电视投屏中，请停止投屏后重试"),
    "dangqianVRyanjinglub" : MessageLookupByLibrary.simpleMessage("当前VR眼镜录屏中，请停止录屏后重试"),
    "dangqianVRyanjingtou" : MessageLookupByLibrary.simpleMessage("当前VR眼镜投屏中，请停止投屏后重试"),
    "dangqianVRyanjingyid" : MessageLookupByLibrary.simpleMessage("当前VR眼镜已登录相同账号，快去体验手机与VR互动的魅力吧。"),
    "dangqianquanbuzhichi" : MessageLookupByLibrary.simpleMessage("当前券不支持"),
    "dangqianshebeichuyuq" : MessageLookupByLibrary.simpleMessage("当前设备处于“青少年模式”，请前往设备列表设置，再前往设备体验。"),
    "dangqianshebeiyilian" : MessageLookupByLibrary.simpleMessage("当前设备已连接"),
    "dangqianwanglaobukey" : MessageLookupByLibrary.simpleMessage("当前网络不可用，请检查你的网络设置"),
    "dangqianweibangdingV" : MessageLookupByLibrary.simpleMessage("当前未绑定VR眼镜"),
    "dangqianyingyongyixi" : MessageLookupByLibrary.simpleMessage("当前应用已下架"),
    "dangqianzaixianrensh" : m43,
    "dangqianzhanghaoyong" : MessageLookupByLibrary.simpleMessage("当前账号拥有财产包括但不限于："),
    "dangqianzhengzaibian" : MessageLookupByLibrary.simpleMessage("当前正在编辑活动，如退出将丢失已编辑内容"),
    "dangqianzhengzaichua" : MessageLookupByLibrary.simpleMessage("当前正在创建活动，如退出将丢失所有内容"),
    "datingdaiting" : MessageLookupByLibrary.simpleMessage("大厅"),
    "denglu" : MessageLookupByLibrary.simpleMessage("登录"),
    "denglumimaxiugaichen" : MessageLookupByLibrary.simpleMessage("登录密码修改成功！"),
    "denglumimaxiugaishib" : MessageLookupByLibrary.simpleMessage("登录密码修改失败！"),
    "denglushibai" : MessageLookupByLibrary.simpleMessage("登录失败"),
    "dev_add_dev" : MessageLookupByLibrary.simpleMessage("添加设备"),
    "dev_add_remind" : MessageLookupByLibrary.simpleMessage("1.点击下方【搜索附近VR眼镜】按钮\n2.点击需要添加的VR眼镜\n3.添加成功后，VR眼镜将自动登录当前账号，即可使用投屏、获取VR眼镜截录屏文件等功能"),
    "dev_ble_lead" : MessageLookupByLibrary.simpleMessage("添加指引"),
    "dev_can_connect" : MessageLookupByLibrary.simpleMessage("可配对设备"),
    "dev_connected" : MessageLookupByLibrary.simpleMessage("已连接"),
    "dev_connecting" : MessageLookupByLibrary.simpleMessage("连接中..."),
    "dev_modify" : MessageLookupByLibrary.simpleMessage("修改设备名称"),
    "dev_my_vr" : MessageLookupByLibrary.simpleMessage("我的VR设备"),
    "dev_nearby" : MessageLookupByLibrary.simpleMessage("在附近"),
    "dev_need_vr" : MessageLookupByLibrary.simpleMessage("该功能无法使用，需要连接VR眼镜"),
    "dev_not_connected" : MessageLookupByLibrary.simpleMessage("未连接"),
    "dev_open_bluetooth" : MessageLookupByLibrary.simpleMessage("未打开蓝牙，无法搜索到VR眼镜。"),
    "dev_proj" : MessageLookupByLibrary.simpleMessage("投屏"),
    "dev_proj_desc" : MessageLookupByLibrary.simpleMessage("如若长时间未投屏成功，请检查VR眼镜是否连接了Wi-Fi并重试。"),
    "dev_proj_from" : MessageLookupByLibrary.simpleMessage("投屏来源"),
    "dev_proj_to" : MessageLookupByLibrary.simpleMessage("投屏到"),
    "dev_research" : MessageLookupByLibrary.simpleMessage("重新搜索"),
    "dev_search_ble" : MessageLookupByLibrary.simpleMessage("搜索附近VR眼镜"),
    "dev_searching" : MessageLookupByLibrary.simpleMessage("正在搜索附近设备..."),
    "dev_sel_dev" : MessageLookupByLibrary.simpleMessage("选择设备"),
    "dev_setting" : MessageLookupByLibrary.simpleMessage("去设置"),
    "dev_unavailable_func" : MessageLookupByLibrary.simpleMessage("无法使用功能"),
    "diNhangdiNhengdiNxin" : m44,
    "diNhangdiNhengdiNxin_1" : m45,
    "dianjichakanhuodongx" : MessageLookupByLibrary.simpleMessage("点击查看活动详情"),
    "dianjidengluzhuce" : MessageLookupByLibrary.simpleMessage("点击登录/注册"),
    "dijiaotijiao" : MessageLookupByLibrary.simpleMessage("同意并提交"),
    "dingchangoumaishijia" : MessageLookupByLibrary.simpleMessage("订单购买时间"),
    "dingchanhaodingdanha" : MessageLookupByLibrary.simpleMessage("订单号："),
    "dingchanhaodingdanha_1" : MessageLookupByLibrary.simpleMessage("订单号"),
    "direct_login" : MessageLookupByLibrary.simpleMessage("直接登录"),
    "dishitishi" : MessageLookupByLibrary.simpleMessage("提示"),
    "disuruma" : MessageLookupByLibrary.simpleMessage("低俗辱骂"),
    "do_it_later" : MessageLookupByLibrary.simpleMessage("以后再说"),
    "douyin" : MessageLookupByLibrary.simpleMessage("抖音"),
    "douyinhao" : MessageLookupByLibrary.simpleMessage("抖音号"),
    "downloadNow" : MessageLookupByLibrary.simpleMessage("是否立即下载？"),
    "downloadText" : MessageLookupByLibrary.simpleMessage("点击立即下载，VR设备将在连网时自动下载该应用。"),
    "duihuan" : MessageLookupByLibrary.simpleMessage("兑换"),
    "duihuanchenggong" : MessageLookupByLibrary.simpleMessage("兑换成功"),
    "email_address" : MessageLookupByLibrary.simpleMessage("电子邮箱"),
    "erweiliaobangchuninw" : MessageLookupByLibrary.simpleMessage("二、为了帮助您完成账号注销，您进一步向玩出梦想声明与保证："),
    "fabiaofeibiao" : MessageLookupByLibrary.simpleMessage("发表"),
    "fabiaoyixianidekanfa" : MessageLookupByLibrary.simpleMessage("发表一下你的看法吧"),
    "fangchenmikongzhishi" : MessageLookupByLibrary.simpleMessage("防沉迷 控制时段"),
    "fangqibaocundangqian" : MessageLookupByLibrary.simpleMessage("放弃保存当前所拼接的视频内容"),
    "fangqidangqianpinglu" : MessageLookupByLibrary.simpleMessage("放弃当前评价"),
    "fanhuishangyiye" : MessageLookupByLibrary.simpleMessage("返回上一页"),
    "faqituikuanshibaifei" : MessageLookupByLibrary.simpleMessage("发起退款失败"),
    "faxiancuowuqingzaish" : MessageLookupByLibrary.simpleMessage("接口请求错误码"),
    "faxiantongxingcuhaoy" : MessageLookupByLibrary.simpleMessage("发现同兴趣好友"),
    "feishiyongshiduanVRy" : MessageLookupByLibrary.simpleMessage("非使用时段，VR眼镜将锁定，可在青少年守护工具页为孩子修改当日可使用时段"),
    "female" : MessageLookupByLibrary.simpleMessage("女"),
    "fenzhong" : MessageLookupByLibrary.simpleMessage("30分钟"),
    "fenzhong_1" : MessageLookupByLibrary.simpleMessage("90分钟"),
    "forget_pwd" : MessageLookupByLibrary.simpleMessage("忘记密码？"),
    "fuwuqiqingqiushibai" : MessageLookupByLibrary.simpleMessage("服务器请求失败"),
    "fuwuxieyiheyinsizhen" : MessageLookupByLibrary.simpleMessage("服务协议和隐私政策"),
    "gaishebeibucunzai" : MessageLookupByLibrary.simpleMessage("该设备不存在"),
    "gaishebeiyijingshenq" : MessageLookupByLibrary.simpleMessage("该设备已经申请打卡活动，不能再次申请"),
    "gaixuanzejinyingxian" : MessageLookupByLibrary.simpleMessage("该选择仅影响最终拼接视频的展示形式"),
    "gaiyingyongmeiyougou" : MessageLookupByLibrary.simpleMessage("该应用没有购买"),
    "gaiyingyongtuikuanzh" : MessageLookupByLibrary.simpleMessage("该应用退款中，无法打开"),
    "gaizhanghaodehaoyouj" : MessageLookupByLibrary.simpleMessage("4．该账号的好友将无法通过账号找到您；"),
    "gaizhanghaodequanbug" : MessageLookupByLibrary.simpleMessage("3．该账号的全部个人资料和历史信息（包括但不限于头像、用户名、发布内容、浏览记录、关注、收藏等）将无法找回；"),
    "gaizhanghaojitongguo" : MessageLookupByLibrary.simpleMessage("1．该账号系通过玩出梦想官方渠道注册且为您本人的用户账号；"),
    "gaizhanghaojitongton" : MessageLookupByLibrary.simpleMessage("1.该账号系统通过玩出梦想官方渠道注册且为您本人的用户账号"),
    "gaizhanghaoleijidede" : MessageLookupByLibrary.simpleMessage("5．该账号累计的等级、积分、权益等将全部清空；"),
    "gaizhanghaonamofachu" : MessageLookupByLibrary.simpleMessage("2.该账号内无未处理完毕的交易"),
    "gaizhanghaonamoweich" : MessageLookupByLibrary.simpleMessage("2．该账号内无未处理完毕的交易；"),
    "gaizhanghaoyishenqin" : m46,
    "ganggang" : MessageLookupByLibrary.simpleMessage("刚刚"),
    "ganxienindezhichinwo" : MessageLookupByLibrary.simpleMessage("感谢您的支持，\n我们会尽快处理您的举报！"),
    "gechengshibaiqingcho" : MessageLookupByLibrary.simpleMessage("合成失败，请重新录制"),
    "gender" : MessageLookupByLibrary.simpleMessage("性别"),
    "gengduo" : MessageLookupByLibrary.simpleMessage("更多"),
    "genghuanbeijing" : MessageLookupByLibrary.simpleMessage("更换背景"),
    "genghuanshoujihao" : MessageLookupByLibrary.simpleMessage("更换手机号"),
    "get_verify_code" : MessageLookupByLibrary.simpleMessage("获取验证码"),
    "get_vfcode_failed" : MessageLookupByLibrary.simpleMessage("验证码获取失败！"),
    "gexinghuanarongtuiji" : MessageLookupByLibrary.simpleMessage("个性化内容推荐"),
    "gexingqianming" : MessageLookupByLibrary.simpleMessage("个性签名"),
    "gongtonghaoyou" : MessageLookupByLibrary.simpleMessage("共同好友"),
    "gongxininjinridakach" : MessageLookupByLibrary.simpleMessage("恭喜您今日打卡成功"),
    "gongxininnindedakahu" : MessageLookupByLibrary.simpleMessage("恭喜您，您的打卡活动奖励已根据您在玩出梦想中填写的领奖信息进行发放。\n如有疑问，请及时联系购买店铺的客服或者拨打400-618-1160"),
    "goumai" : MessageLookupByLibrary.simpleMessage("购买"),
    "goumaiS" : m47,
    "goumaiyingyong" : MessageLookupByLibrary.simpleMessage("购买应用"),
    "goumaiyingyongqianqi" : MessageLookupByLibrary.simpleMessage("购买应用前，请先前往【设备】中添加设备。"),
    "guanbi" : MessageLookupByLibrary.simpleMessage("关闭"),
    "guanbihoujiangmofash" : MessageLookupByLibrary.simpleMessage("关闭后将无法收到个性化推荐，建议开启以看到更多感兴趣内容。"),
    "guanfang" : MessageLookupByLibrary.simpleMessage("官方"),
    "guanfanghuodong" : MessageLookupByLibrary.simpleMessage("官方活动"),
    "guanfanghuodongbucun" : MessageLookupByLibrary.simpleMessage("官方活动不存在"),
    "guanlishiyongshijian" : MessageLookupByLibrary.simpleMessage("管理使用时间、有效预防沉迷"),
    "guizexiangqing" : MessageLookupByLibrary.simpleMessage("规则详情"),
    "haimeiyouhaoyoukuaiq" : MessageLookupByLibrary.simpleMessage("还没有好友，快去添加吧"),
    "haimeiyourenhedongta" : MessageLookupByLibrary.simpleMessage("还没有任何动态"),
    "haoyou" : MessageLookupByLibrary.simpleMessage("好友"),
    "haoyouyijingtianjia" : MessageLookupByLibrary.simpleMessage("好友已经添加"),
    "hechenghengpingshipi" : MessageLookupByLibrary.simpleMessage("合成横屏视频"),
    "hechengshibai" : MessageLookupByLibrary.simpleMessage("合成失败，请重新录制"),
    "hechengshupingshipin" : MessageLookupByLibrary.simpleMessage("合成竖屏视频"),
    "hehuhuo" : MessageLookupByLibrary.simpleMessage("和"),
    "heimingchanheimingda" : MessageLookupByLibrary.simpleMessage("黑名单"),
    "henbaoqianmeiyouzhao" : MessageLookupByLibrary.simpleMessage("很抱歉，没有找到你需要的应用"),
    "hengbinghengping" : MessageLookupByLibrary.simpleMessage("横屏"),
    "hengbingpinjiehengpi" : MessageLookupByLibrary.simpleMessage("横屏拼接"),
    "henxinfangqi" : MessageLookupByLibrary.simpleMessage("狠心放弃"),
    "home_action_adventure" : MessageLookupByLibrary.simpleMessage("动作冒险"),
    "home_all_app" : MessageLookupByLibrary.simpleMessage("全部应用"),
    "home_all_categories" : MessageLookupByLibrary.simpleMessage("全部类别"),
    "home_all_prices" : MessageLookupByLibrary.simpleMessage("全部价格"),
    "home_all_types" : MessageLookupByLibrary.simpleMessage("全部类型"),
    "home_app" : MessageLookupByLibrary.simpleMessage("应用"),
    "home_app_info" : MessageLookupByLibrary.simpleMessage("应用信息"),
    "home_below_100" : MessageLookupByLibrary.simpleMessage("50-100元"),
    "home_below_50" : MessageLookupByLibrary.simpleMessage("50元以下"),
    "home_cartoon_animation" : MessageLookupByLibrary.simpleMessage("卡通动画"),
    "home_casual_creativity" : MessageLookupByLibrary.simpleMessage("休闲创意"),
    "home_comment" : MessageLookupByLibrary.simpleMessage("评论"),
    "home_cosplay" : MessageLookupByLibrary.simpleMessage("角色扮演"),
    "home_earliest_released" : MessageLookupByLibrary.simpleMessage("最早发布"),
    "home_free" : MessageLookupByLibrary.simpleMessage("免费"),
    "home_game" : MessageLookupByLibrary.simpleMessage("游戏"),
    "home_go_appcenter" : MessageLookupByLibrary.simpleMessage("没有找到合适的内容？去应用库看看吧"),
    "home_go_now" : MessageLookupByLibrary.simpleMessage("立即前往"),
    "home_highest_price" : MessageLookupByLibrary.simpleMessage("价格最高"),
    "home_hot_download" : MessageLookupByLibrary.simpleMessage("热门下载"),
    "home_lowest_price" : MessageLookupByLibrary.simpleMessage("价格最低"),
    "home_mark" : MessageLookupByLibrary.simpleMessage("评分"),
    "home_media_entertainment" : MessageLookupByLibrary.simpleMessage("影音娱乐"),
    "home_more_100" : MessageLookupByLibrary.simpleMessage("100元以上"),
    "home_multiplayer_game" : MessageLookupByLibrary.simpleMessage("多人游戏"),
    "home_my_app" : MessageLookupByLibrary.simpleMessage("我的应用"),
    "home_noti_center" : MessageLookupByLibrary.simpleMessage("通知中心"),
    "home_office_tools" : MessageLookupByLibrary.simpleMessage("办公工具"),
    "home_person_com" : m48,
    "home_recently_released" : MessageLookupByLibrary.simpleMessage("最近发布"),
    "home_recently_update" : MessageLookupByLibrary.simpleMessage("最近更新"),
    "home_recommended_sorting" : MessageLookupByLibrary.simpleMessage("推荐排序"),
    "home_search_history" : MessageLookupByLibrary.simpleMessage("搜索记录"),
    "home_search_plhd" : MessageLookupByLibrary.simpleMessage("输入关键词搜索"),
    "home_social_network" : MessageLookupByLibrary.simpleMessage("社交网络"),
    "home_star" : MessageLookupByLibrary.simpleMessage("星"),
    "home_stim_shot" : MessageLookupByLibrary.simpleMessage("刺激射击"),
    "home_thrilling" : MessageLookupByLibrary.simpleMessage("惊险恐怖"),
    "home_view_all" : MessageLookupByLibrary.simpleMessage("查看全部"),
    "home_view_all_comments" : MessageLookupByLibrary.simpleMessage("查看全部评价"),
    "houduanjiekouxujianr" : m49,
    "hulve" : MessageLookupByLibrary.simpleMessage("忽略"),
    "hunheluzhi" : MessageLookupByLibrary.simpleMessage("混合录制"),
    "huodong" : MessageLookupByLibrary.simpleMessage("活动"),
    "huodongID" : MessageLookupByLibrary.simpleMessage("活动ID："),
    "huodonganchanghuodon" : MessageLookupByLibrary.simpleMessage("活动广场"),
    "huodongbucunzai" : MessageLookupByLibrary.simpleMessage("活动不存在"),
    "huodongcanyushibaihu" : MessageLookupByLibrary.simpleMessage("活动参与失败"),
    "huodongchuangjianche" : MessageLookupByLibrary.simpleMessage("活动创建成功！"),
    "huodongchuangjianshi" : MessageLookupByLibrary.simpleMessage("活动创建失败！"),
    "huodongguize" : MessageLookupByLibrary.simpleMessage("活动规则"),
    "huodongjieshao" : MessageLookupByLibrary.simpleMessage("活动介绍"),
    "huodongshijian" : MessageLookupByLibrary.simpleMessage("活动时间："),
    "huodongshuimingxuant" : MessageLookupByLibrary.simpleMessage("活动说明（选填）"),
    "huodongxiangqing" : MessageLookupByLibrary.simpleMessage("活动详情"),
    "huodongxiugaichenggo" : MessageLookupByLibrary.simpleMessage("活动修改成功！"),
    "huodongxiugaishibai" : MessageLookupByLibrary.simpleMessage("活动修改失败！"),
    "huodongyiguoqi" : MessageLookupByLibrary.simpleMessage("活动已过期"),
    "huodongyijieshu" : MessageLookupByLibrary.simpleMessage("活动已结束"),
    "huodongyijieshuqings" : MessageLookupByLibrary.simpleMessage("活动已结束，请刷新后重试"),
    "huodongyijingguoqi" : MessageLookupByLibrary.simpleMessage("活动已经过期！"),
    "huodongyijingkaishim" : MessageLookupByLibrary.simpleMessage("活动已经开始，无法退出"),
    "huodongyikaishimofat" : MessageLookupByLibrary.simpleMessage("活动已开始，无法退出该活动！"),
    "huodongyikaishishanc" : MessageLookupByLibrary.simpleMessage("活动已开始，删除失败"),
    "huodongyixiaxian" : MessageLookupByLibrary.simpleMessage("活动已下线"),
    "huoquVRduanshipinshi" : MessageLookupByLibrary.simpleMessage("获取VR端视频失败"),
    "huoquYbishujushibai" : MessageLookupByLibrary.simpleMessage("获取Y币数据失败"),
    "huoquchongzhixinxish" : MessageLookupByLibrary.simpleMessage("获取充值信息失败"),
    "huoqushebeiliebiaosh" : MessageLookupByLibrary.simpleMessage("获取设备列表失败"),
    "huoqushijian" : MessageLookupByLibrary.simpleMessage("获取时间："),
    "identity_card_back" : MessageLookupByLibrary.simpleMessage("国徽页"),
    "identity_card_front" : MessageLookupByLibrary.simpleMessage("人像页"),
    "input_email" : MessageLookupByLibrary.simpleMessage("请输入邮箱地址"),
    "input_phone_num" : MessageLookupByLibrary.simpleMessage("请输入手机号"),
    "input_pwd" : MessageLookupByLibrary.simpleMessage("请输入密码"),
    "input_verify_code" : MessageLookupByLibrary.simpleMessage("请输入验证码"),
    "jiangliyifafangjiang" : MessageLookupByLibrary.simpleMessage("奖励已发放"),
    "jianyiVRdianliang40y" : MessageLookupByLibrary.simpleMessage("建议VR电量40%以上使用"),
    "jianyininzaizhuxiaoq" : MessageLookupByLibrary.simpleMessage("建议您在注销前自行备份注销账号的相关信息，并请确认与该账号相关的所有服务均已进行妥善处理。注销成功后，我们将按照《用户隐私政策》相关条款处理您的个人信息，或对其进行匿名化处理。"),
    "jiaru" : MessageLookupByLibrary.simpleMessage("加入"),
    "jiarushibaigaiyingyo" : MessageLookupByLibrary.simpleMessage("加入失败，该应用没有购买"),
    "jiazaishibaiqingchon" : MessageLookupByLibrary.simpleMessage("加载失败，请重试。"),
    "jiazaizhong" : MessageLookupByLibrary.simpleMessage("加载中..."),
    "jiebangzhongxiebangz" : MessageLookupByLibrary.simpleMessage("解绑中..."),
    "jiebinghelubingjiepi" : MessageLookupByLibrary.simpleMessage("截屏和录屏"),
    "jiebingyulubingjiepi" : MessageLookupByLibrary.simpleMessage("截屏与录屏"),
    "jieshaoyixianiziji" : MessageLookupByLibrary.simpleMessage("介绍一下你自己"),
    "jieshushijian" : MessageLookupByLibrary.simpleMessage("结束时间"),
    "jijiangshangxianjiqi" : MessageLookupByLibrary.simpleMessage("敬请期待"),
    "jingdong" : MessageLookupByLibrary.simpleMessage("京东"),
    "jingdongyonghuming" : MessageLookupByLibrary.simpleMessage("京东用户名"),
    "jingtoufanzhuan" : MessageLookupByLibrary.simpleMessage("镜头翻转"),
    "jinhangzhongjinhengz" : MessageLookupByLibrary.simpleMessage("进行中"),
    "jinqi" : MessageLookupByLibrary.simpleMessage("近期"),
    "jinrikeshiyongshidua" : MessageLookupByLibrary.simpleMessage("今日可使用时段"),
    "jintian" : MessageLookupByLibrary.simpleMessage("今天"),
    "jinzhichizhongwendax" : MessageLookupByLibrary.simpleMessage("仅支持中文、大小写字母和数字"),
    "jitacuowuqitacuowu" : MessageLookupByLibrary.simpleMessage("其它错误"),
    "jitajinen19999qitaji" : MessageLookupByLibrary.simpleMessage("其他金额\n1-9999"),
    "jitaqita" : MessageLookupByLibrary.simpleMessage("其他"),
    "jitongcuowuxitongcuo" : MessageLookupByLibrary.simpleMessage("系统错误"),
    "jitongtongzhixitongt" : MessageLookupByLibrary.simpleMessage("系统通知"),
    "jitongxiaoxixitongxi" : MessageLookupByLibrary.simpleMessage("系统消息"),
    "jubanzhe" : MessageLookupByLibrary.simpleMessage("举办者:"),
    "jubao" : MessageLookupByLibrary.simpleMessage("举报"),
    "jubaoshibai" : MessageLookupByLibrary.simpleMessage("举报失败！"),
    "kaiqihuodongqingqian" : MessageLookupByLibrary.simpleMessage("开启活动请前往VR端"),
    "kaiqiqingshaonianmos" : MessageLookupByLibrary.simpleMessage("开启青少年模式"),
    "kaishi" : MessageLookupByLibrary.simpleMessage("开始"),
    "kaishishijian" : MessageLookupByLibrary.simpleMessage("开始时间"),
    "keep_secret" : MessageLookupByLibrary.simpleMessage("保密"),
    "kenengrenshikenengre" : MessageLookupByLibrary.simpleMessage("可能认识"),
    "keshiyongshiduan" : MessageLookupByLibrary.simpleMessage("可使用时段"),
    "keshiyongyouhuquandu" : MessageLookupByLibrary.simpleMessage("可使用游戏券兑换"),
    "keyongshijianduan" : MessageLookupByLibrary.simpleMessage("可用时间段"),
    "keyongyouhuquanNzhan" : m50,
    "keyongyouhuquankeyon" : MessageLookupByLibrary.simpleMessage("可用游戏券"),
    "kongzhishichangshouh" : MessageLookupByLibrary.simpleMessage("控制时长、守护青少年在元宇宙中的健康成长"),
    "kuailaibaomingba" : MessageLookupByLibrary.simpleMessage("快来报名吧"),
    "kuaiquVRlitiyanSba" : m51,
    "kuaiquVRlitiyanSba_1" : m52,
    "kunbangbao" : MessageLookupByLibrary.simpleMessage("捆绑包"),
    "laheiS" : m53,
    "laheiyonghuyiweizhao" : MessageLookupByLibrary.simpleMessage("拉黑用户意味着对方无法：\n加你为好友\n邀请你加入聊天室\n以及搜索到你"),
    "lajiangaolajiguangga" : MessageLookupByLibrary.simpleMessage("垃圾广告"),
    "lanhelianjieshibaila" : MessageLookupByLibrary.simpleMessage("蓝牙连接失败！"),
    "lanheweikaiqixianggu" : MessageLookupByLibrary.simpleMessage("蓝牙未开启，相关功能将无法使用！"),
    "leave" : MessageLookupByLibrary.simpleMessage("退出"),
    "leijidakaNtianleijid" : m54,
    "lianjiefuwuqishibaiq" : MessageLookupByLibrary.simpleMessage("连接服务器失败，请重试"),
    "lianjieshebei" : MessageLookupByLibrary.simpleMessage("连接设备"),
    "lianjieshibai" : MessageLookupByLibrary.simpleMessage("连接失败"),
    "lijibaoming" : MessageLookupByLibrary.simpleMessage("立即报名"),
    "lijigenghuan" : MessageLookupByLibrary.simpleMessage("立即更换"),
    "lijigengxin" : MessageLookupByLibrary.simpleMessage("立即更新"),
    "lijiqianwang" : MessageLookupByLibrary.simpleMessage("立即前往"),
    "lijixiazai" : MessageLookupByLibrary.simpleMessage("立即下载"),
    "lingjiangxinxidijiao" : MessageLookupByLibrary.simpleMessage("领奖信息提交成功！"),
    "lingjiangxinxidijiao_1" : MessageLookupByLibrary.simpleMessage("领奖信息提交失败"),
    "lingjiangxinxitianxi" : MessageLookupByLibrary.simpleMessage("领奖信息填写页面"),
    "liuchengmaSqingyongy" : m55,
    "liuchengmayifuzhi" : MessageLookupByLibrary.simpleMessage("流程码已复制"),
    "login_network_error" : MessageLookupByLibrary.simpleMessage("网络不给力，请重新登录"),
    "login_successful" : MessageLookupByLibrary.simpleMessage("登录成功"),
    "luzhijijiangjieshuqi" : MessageLookupByLibrary.simpleMessage("录制即将结束，请注意时间"),
    "luzhinarongguodamofa" : MessageLookupByLibrary.simpleMessage("录制内容过大，无法保存"),
    "luzhishibaozhengbeil" : MessageLookupByLibrary.simpleMessage("录制时，保证被录者在画面中央"),
    "male" : MessageLookupByLibrary.simpleMessage("男"),
    "meiyoufugetiaojiande" : MessageLookupByLibrary.simpleMessage("没有符合条件的内容"),
    "meiyoukexuanshijiand" : MessageLookupByLibrary.simpleMessage("没有可选时间段"),
    "meiyouzhaodaofujinde" : MessageLookupByLibrary.simpleMessage("没有找到附近的VR眼镜"),
    "mianfeigoumaiwenfeig" : MessageLookupByLibrary.simpleMessage("免费购买"),
    "mianfeihuodewenfeihu" : MessageLookupByLibrary.simpleMessage("免费获得"),
    "mianfeiwenfei" : MessageLookupByLibrary.simpleMessage("免费"),
    "mimabuyizhiqingheshi" : MessageLookupByLibrary.simpleMessage("密码不一致，请核实后重新输入"),
    "mimacuowu" : MessageLookupByLibrary.simpleMessage("密码错误"),
    "mingtian" : MessageLookupByLibrary.simpleMessage("明天"),
    "modify_nickname" : MessageLookupByLibrary.simpleMessage("修改用户昵称"),
    "mofabaocunqingchongs" : MessageLookupByLibrary.simpleMessage("无法保存,请重试"),
    "mofabaocunqingfanhui" : MessageLookupByLibrary.simpleMessage("无法保存，请返回列表刷新"),
    "mofagoumaigaiyingyon" : MessageLookupByLibrary.simpleMessage("无法购买该应用"),
    "mofahuoqujielubingwe" : MessageLookupByLibrary.simpleMessage("无法获取截录屏文件，请检查手机与VR眼镜\n连接相同Wi-Fi，并且应用权限打开后重试"),
    "mofahuoquwifimingche" : MessageLookupByLibrary.simpleMessage("无法获取wifi名称"),
    "mofatianjiaVRyanjing" : MessageLookupByLibrary.simpleMessage("无法添加VR眼镜"),
    "mofatianjiaciVRyanji" : MessageLookupByLibrary.simpleMessage("无法添加此VR眼镜，请返回后重试。"),
    "mofatongbuwanglaowuf" : MessageLookupByLibrary.simpleMessage("无法同步网络"),
    "mofazuoweikaishishij" : MessageLookupByLibrary.simpleMessage("无法作为开始时间，请重新选择"),
    "mokeshiyongshiduanqi" : MessageLookupByLibrary.simpleMessage("无可使用时段，请前往时间锁设置"),
    "msg_accept_friend" : m56,
    "msg_after_half_hour" : MessageLookupByLibrary.simpleMessage("活动30分钟后开始"),
    "msg_delete" : MessageLookupByLibrary.simpleMessage("删除消息"),
    "msg_delete_confirm" : MessageLookupByLibrary.simpleMessage("删除消息后消息将永远消失，确定删除吗？"),
    "msg_evet_cancel" : MessageLookupByLibrary.simpleMessage("活动已取消"),
    "msg_evet_start" : MessageLookupByLibrary.simpleMessage("活动即将开始"),
    "msg_friend_pass" : MessageLookupByLibrary.simpleMessage("好友申请通过"),
    "msg_invite_event" : m57,
    "msg_invite_you" : MessageLookupByLibrary.simpleMessage("好友邀请参加活动"),
    "msg_req_friend" : MessageLookupByLibrary.simpleMessage("请求加你为好友。"),
    "nan" : MessageLookupByLibrary.simpleMessage("男"),
    "narongguodajianyizhi" : MessageLookupByLibrary.simpleMessage("内容过大，建议直接拷贝至您的电脑保存"),
    "nichenbukeweikonghuo" : MessageLookupByLibrary.simpleMessage("昵称不可为空或包含空格！"),
    "nichenbukeweikongzif" : MessageLookupByLibrary.simpleMessage("昵称不可为空字符或包含空格哦！"),
    "nickname" : MessageLookupByLibrary.simpleMessage("用户昵称"),
    "nihaimeiyougaiyingyo" : MessageLookupByLibrary.simpleMessage("你还没有该应用，快去购买体验吧"),
    "nihaimeiyoulianjiesh" : MessageLookupByLibrary.simpleMessage("你还没有连接设备，是否去连接设备？"),
    "nimenduzaiwanSnimend" : m58,
    "nindangqianyishizuix" : MessageLookupByLibrary.simpleMessage("您当前已是最新版本"),
    "nindeVRshebeixiangni" : m59,
    "nindebeijingtupiansh" : MessageLookupByLibrary.simpleMessage("您的背景图片审核不通过"),
    "nindegexingqianmings" : MessageLookupByLibrary.simpleMessage("您的个性签名审核不通过"),
    "nindegoumaiqudaonind" : MessageLookupByLibrary.simpleMessage("您的购买渠道"),
    "nindetouxiangtupians" : MessageLookupByLibrary.simpleMessage("可能包含令人不适的内容，请修改后重试"),
    "nindeyonghunichengsh" : MessageLookupByLibrary.simpleMessage("您的用户昵称审核不通过"),
    "nindezhanghaochuyuzh" : MessageLookupByLibrary.simpleMessage("3.您的账号处于正常使用状态，经玩出梦想平台安全检查无被盗风险，且不存在违法违规的情况。"),
    "nindezhanghaochuyuzh_1" : MessageLookupByLibrary.simpleMessage("3. 您的账号处于正常使用状态，经玩出梦想平台安全检查无被盗风险，且不存在违法违规的情况。"),
    "nindijiaodezifugeshi" : MessageLookupByLibrary.simpleMessage("您提交的字符格式不支持！"),
    "ninjiangfangqiweidao" : MessageLookupByLibrary.simpleMessage("1．您将放弃未到期或未使用的各类游戏角色下的虚拟货币和道具、您在玩出梦想平台各产品和/或服务中的各类身份权益、您在各产品和/或服务中已经购买的未到期的在线服务内容、您在购买的各产品和/或服务中获得的相关资产（例如Y币等）以及其他已经产生但未消耗完毕的权益或未来预期的利益；"),
    "ninjiangjiechugaizha" : MessageLookupByLibrary.simpleMessage("2．您将解除该账号与其他产品（例如视频或第三方平台）的绑定或授权登录关系；"),
    "ninjiangjixuwancheng" : MessageLookupByLibrary.simpleMessage("您将继续完成注销账号流程，该账号的数据将随此玩出梦想账号的注销一同删除"),
    "ninjiaruhuodonghouca" : MessageLookupByLibrary.simpleMessage("您加入活动后才可邀请好友！"),
    "ninshangchuandetouxi" : MessageLookupByLibrary.simpleMessage("您上传的头像违规，请重新上传！"),
    "ninshangchuandetouxi_1" : MessageLookupByLibrary.simpleMessage("您上传的头像违规！"),
    "ninshengyuhuodongtia" : MessageLookupByLibrary.simpleMessage("您剩余活动天数已无法满足领奖条件"),
    "ninshurudemingchenyi" : MessageLookupByLibrary.simpleMessage("您输入的名称已超过长度限制！"),
    "ninyiwanchengzhifunk" : MessageLookupByLibrary.simpleMessage("您已完成支付，\n快去你的VR设备上体验吧！"),
    "ninzaiyingyongshangd" : m60,
    "ninzaiyingyongshangd_1" : m61,
    "ninzaiyingyongshangd_2" : m62,
    "ninzaiyingyongshangd_3" : m63,
    "no_account" : MessageLookupByLibrary.simpleMessage("还没有注册账号？"),
    "nv" : MessageLookupByLibrary.simpleMessage("女"),
    "paizhao" : MessageLookupByLibrary.simpleMessage("拍照"),
    "payWayManage" : MessageLookupByLibrary.simpleMessage("支付方式管理"),
    "pingfenyidijiaopingf" : MessageLookupByLibrary.simpleMessage("评分已提交"),
    "pinglunnarongweiguip" : MessageLookupByLibrary.simpleMessage("评论内容违规"),
    "pinglunyidijiaoshenh" : MessageLookupByLibrary.simpleMessage("评价已提交审核，您\n可在【通知中心】查\n看审核结果"),
    "pingtaizhengce" : MessageLookupByLibrary.simpleMessage("平台政策"),
    "pinjie" : MessageLookupByLibrary.simpleMessage("拼接"),
    "pinjieluzhi" : MessageLookupByLibrary.simpleMessage("拼接录制"),
    "pinjieluzhiguochengz" : MessageLookupByLibrary.simpleMessage("拼接录制过程中请保持应用在前台"),
    "pinjiemoshi" : MessageLookupByLibrary.simpleMessage("拼接模式"),
    "please_add_dev" : MessageLookupByLibrary.simpleMessage("请先在VR中登录账号"),
    "plhd_no_event" : MessageLookupByLibrary.simpleMessage("暂无活动"),
    "plhd_no_more_data" : MessageLookupByLibrary.simpleMessage("已无更多数据！"),
    "plhd_no_msg" : MessageLookupByLibrary.simpleMessage("您还没有收到通知"),
    "point_rule_html_page_name" : MessageLookupByLibrary.simpleMessage("pointRule"),
    "privacy_policy" : MessageLookupByLibrary.simpleMessage("隐私政策"),
    "privacy_policy_html_page_name" : MessageLookupByLibrary.simpleMessage("protection"),
    "prod_alipay" : MessageLookupByLibrary.simpleMessage("支付宝"),
    "prod_authority" : MessageLookupByLibrary.simpleMessage("权限要求："),
    "prod_book" : MessageLookupByLibrary.simpleMessage("立即预约"),
    "prod_book_app_failed" : MessageLookupByLibrary.simpleMessage("预约失败，请重试"),
    "prod_book_app_succeeded" : MessageLookupByLibrary.simpleMessage("预约成功，上线时您将收到通知"),
    "prod_booked" : MessageLookupByLibrary.simpleMessage("已预约"),
    "prod_coming_time" : m64,
    "prod_detail" : MessageLookupByLibrary.simpleMessage("详情"),
    "prod_developer" : MessageLookupByLibrary.simpleMessage("开发者："),
    "prod_download_queue" : MessageLookupByLibrary.simpleMessage("前往VR眼镜体验"),
    "prod_experience_vr" : MessageLookupByLibrary.simpleMessage("您已完成支付，\n快去你的VR设备上体验吧"),
    "prod_fold" : MessageLookupByLibrary.simpleMessage("折叠"),
    "prod_input_comment" : MessageLookupByLibrary.simpleMessage("写下你的看法..."),
    "prod_install_alipay" : MessageLookupByLibrary.simpleMessage("请先安装支付宝应用"),
    "prod_install_wechat" : MessageLookupByLibrary.simpleMessage("请先安装微信应用"),
    "prod_language" : MessageLookupByLibrary.simpleMessage("语言："),
    "prod_my_comment" : MessageLookupByLibrary.simpleMessage("我的评价"),
    "prod_need_login" : MessageLookupByLibrary.simpleMessage("需要登录才能购买"),
    "prod_network" : MessageLookupByLibrary.simpleMessage("网络模式："),
    "prod_pay_fail" : MessageLookupByLibrary.simpleMessage("支付失败!"),
    "prod_pay_result" : MessageLookupByLibrary.simpleMessage("支付结果"),
    "prod_pay_success" : MessageLookupByLibrary.simpleMessage("支付成功!"),
    "prod_pay_way" : MessageLookupByLibrary.simpleMessage("选择支付方式"),
    "prod_policy" : MessageLookupByLibrary.simpleMessage("平台政策："),
    "prod_policy_title" : MessageLookupByLibrary.simpleMessage("玩出梦想退款政策"),
    "prod_publisher" : MessageLookupByLibrary.simpleMessage("发行商："),
    "prod_ram" : MessageLookupByLibrary.simpleMessage("所需空间："),
    "prod_ratings_reviews" : MessageLookupByLibrary.simpleMessage("评分与评价"),
    "prod_release_date" : MessageLookupByLibrary.simpleMessage("发布时间："),
    "prod_sofa" : MessageLookupByLibrary.simpleMessage("还没有评价，快去抢占沙发吧！"),
    "prod_unbook_app_failed" : MessageLookupByLibrary.simpleMessage("取消失败，请重试"),
    "prod_unfold" : MessageLookupByLibrary.simpleMessage("展开"),
    "prod_version" : MessageLookupByLibrary.simpleMessage("版本："),
    "prod_wechat" : MessageLookupByLibrary.simpleMessage("微信"),
    "profile_buy_history" : MessageLookupByLibrary.simpleMessage("购买记录"),
    "profile_cancel_feedback" : MessageLookupByLibrary.simpleMessage("放弃当前反馈"),
    "profile_change_pwd" : MessageLookupByLibrary.simpleMessage("修改登录密码"),
    "profile_check_version" : MessageLookupByLibrary.simpleMessage("检查更新"),
    "profile_confirm_password" : MessageLookupByLibrary.simpleMessage("确认密码"),
    "profile_connect_way" : MessageLookupByLibrary.simpleMessage("留下QQ或手机号以方便我们联系您"),
    "profile_contact_information" : MessageLookupByLibrary.simpleMessage("或拨打400-618-1160 客服在线时间：工作日9:00-18:00"),
    "profile_current_version" : MessageLookupByLibrary.simpleMessage("当前版本"),
    "profile_desc_problem" : MessageLookupByLibrary.simpleMessage("描述您遇到的问题，我们一定会及时处理"),
    "profile_edit" : MessageLookupByLibrary.simpleMessage("编辑个人资料"),
    "profile_enter_sms" : MessageLookupByLibrary.simpleMessage("输入短信验证码"),
    "profile_exchange_code" : MessageLookupByLibrary.simpleMessage("输入兑换码"),
    "profile_feedback" : MessageLookupByLibrary.simpleMessage("用户反馈"),
    "profile_health" : MessageLookupByLibrary.simpleMessage("健康与安全"),
    "profile_homepage" : MessageLookupByLibrary.simpleMessage("个人主页"),
    "profile_input_nick" : MessageLookupByLibrary.simpleMessage("请输入您的昵称"),
    "profile_is_feedback" : MessageLookupByLibrary.simpleMessage("是否放弃当前反馈？"),
    "profile_legal_info" : MessageLookupByLibrary.simpleMessage("用户协议"),
    "profile_new_password" : MessageLookupByLibrary.simpleMessage("新密码"),
    "profile_new_ver" : MessageLookupByLibrary.simpleMessage("最新版本"),
    "profile_not_update" : MessageLookupByLibrary.simpleMessage("暂不更新"),
    "profile_phone_num" : MessageLookupByLibrary.simpleMessage("手机号"),
    "profile_please_redemption" : MessageLookupByLibrary.simpleMessage("请输入您的兑换码"),
    "profile_question_type" : MessageLookupByLibrary.simpleMessage("问题类型"),
    "profile_redemption_code" : MessageLookupByLibrary.simpleMessage("兑换码"),
    "profile_refunded" : MessageLookupByLibrary.simpleMessage("已退款"),
    "profile_refunding" : MessageLookupByLibrary.simpleMessage("退款中"),
    "profile_request_refund" : MessageLookupByLibrary.simpleMessage("申请退款"),
    "profile_sel_feedback" : MessageLookupByLibrary.simpleMessage("请选择您的反馈类型"),
    "profile_set_password" : MessageLookupByLibrary.simpleMessage("设置新密码"),
    "profile_update_now" : MessageLookupByLibrary.simpleMessage("立即更新"),
    "profile_ver_size" : MessageLookupByLibrary.simpleMessage("版本大小"),
    "profile_ver_update" : MessageLookupByLibrary.simpleMessage("版本更新"),
    "profile_verify_identidy" : MessageLookupByLibrary.simpleMessage("验证身份"),
    "profile_verify_phone_num" : MessageLookupByLibrary.simpleMessage("验证手机号"),
    "pwd_input_again" : MessageLookupByLibrary.simpleMessage("再次输入密码"),
    "pwd_login" : MessageLookupByLibrary.simpleMessage("账号密码登录"),
    "pwd_rule" : MessageLookupByLibrary.simpleMessage("6-12位大小写字母/数字/符号"),
    "qianmingbunengchaogu" : MessageLookupByLibrary.simpleMessage("签名不能超过6行哦"),
    "qianmingbunenghanyou" : MessageLookupByLibrary.simpleMessage("签名不能含有空行，且最大行数为6行"),
    "qiehuanshipin" : MessageLookupByLibrary.simpleMessage("切换视频"),
    "qingduiyingyongSdafe" : m65,
    "qinggenjuxiafangzhiy" : MessageLookupByLibrary.simpleMessage("请根据下方指引填写领奖信息，提交后我们将尽快进行审核"),
    "qinglianjiezhixiangt" : MessageLookupByLibrary.simpleMessage("请连接至相同网络后，下拉刷新列表"),
    "qingniwubishenshenyu" : m66,
    "qingqianwangVRshebei" : MessageLookupByLibrary.simpleMessage("请前往VR设备中进行手动下载"),
    "qingqianwangVRyanjin" : MessageLookupByLibrary.simpleMessage("请前往VR眼镜内体验"),
    "qingqianwangYVRguanw" : MessageLookupByLibrary.simpleMessage("请前往玩出梦想官网进行修改"),
    "qingqiehuanshipindep" : MessageLookupByLibrary.simpleMessage("请切换视频的拼接模式"),
    "qingqiuchaoshi" : MessageLookupByLibrary.simpleMessage("请求超时"),
    "qingqiushibai" : MessageLookupByLibrary.simpleMessage("请求失败"),
    "qingquanchengshiyong" : MessageLookupByLibrary.simpleMessage("请全程使用竖屏录制"),
    "qingquebaoVRyanjingl" : MessageLookupByLibrary.simpleMessage("请确保VR眼镜亮屏并连接网络"),
    "qingquebaoVRzhongyid" : MessageLookupByLibrary.simpleMessage("请确保VR中已登录相同账号"),
    "qingshangchuanbaohan" : MessageLookupByLibrary.simpleMessage("请上传包含订单号&实际支付金额的平台订单截图"),
    "qingshangchuanshenfe" : MessageLookupByLibrary.simpleMessage("请上传身份证正反面照片（务必保持照片清晰完整）"),
    "qingshaonianmoshiqin" : MessageLookupByLibrary.simpleMessage("青少年模式"),
    "qingshaonianmoshiyig" : MessageLookupByLibrary.simpleMessage("青少年模式已关闭"),
    "qingshaonianmoshiyig_1" : MessageLookupByLibrary.simpleMessage("青少年模式已关闭，请打开后重试"),
    "qingshaonianmoshiyik" : MessageLookupByLibrary.simpleMessage("青少年模式已开"),
    "qingshaonianshouhugo" : MessageLookupByLibrary.simpleMessage("青少年守护工具"),
    "qingshiyongjitashaix" : MessageLookupByLibrary.simpleMessage("请使用其它筛选条件试试"),
    "qingshuru612weidaxia" : MessageLookupByLibrary.simpleMessage("请输入6-12位大小写字母/数字/符号"),
    "qingshuruNweishuzi" : m67,
    "qingshuruS" : m68,
    "qingshuruWiFimima" : MessageLookupByLibrary.simpleMessage("请输入Wi-Fi密码"),
    "qingshurufashenggeig" : MessageLookupByLibrary.simpleMessage("请输入发生给该账号绑定的手机号的验证码"),
    "qingshurumimayanzhen" : MessageLookupByLibrary.simpleMessage("请输入密码验证家长身份，进入青少年模式设置"),
    "qingshuruningoumaiqu" : MessageLookupByLibrary.simpleMessage("请输入您购买渠道的平台订单号（需与上传截图中的保持一致）"),
    "qingshurushoujihao" : MessageLookupByLibrary.simpleMessage("请输入手机号"),
    "qingshuruxinshoujiha" : MessageLookupByLibrary.simpleMessage("请输入新手机号"),
    "qingshuruyanzhengma" : MessageLookupByLibrary.simpleMessage("请输入验证码"),
    "qingshuruyouxiaoshou" : MessageLookupByLibrary.simpleMessage("请输入有效手机号"),
    "qingwanchengbitianxi" : MessageLookupByLibrary.simpleMessage("请完成必填项后再提交创建！"),
    "qingwutuichu" : MessageLookupByLibrary.simpleMessage("请勿退出"),
    "qingxiananzhuangweix" : MessageLookupByLibrary.simpleMessage("请先安装微信应用"),
    "qingxianbangdingnind" : MessageLookupByLibrary.simpleMessage("请先绑定您的VR设备"),
    "qingxiandakaiVRshebe" : MessageLookupByLibrary.simpleMessage("请先打开VR设备中的WiFi功能"),
    "qingxiandakaiVRyanji" : MessageLookupByLibrary.simpleMessage("请先打开VR眼镜中的WiFi功能"),
    "qingxiandakaifangwen" : MessageLookupByLibrary.simpleMessage("请打开本地网络权限，若权限打开后仍无法连接，请尝试切换至其他Wi-Fi"),
    "qingxiandakaiqingsha" : MessageLookupByLibrary.simpleMessage("请先打开青少年模式"),
    "qingxiandenglunindez" : MessageLookupByLibrary.simpleMessage("请先登录您的账号"),
    "qingxianguanbiqingsh" : MessageLookupByLibrary.simpleMessage("请先关闭青少年模式"),
    "qingxianjiangshoujil" : MessageLookupByLibrary.simpleMessage("请先将手机连接至Wi-Fi。若已连接，请开启玩出梦想的蓝牙与位置权限"),
    "qingxiankaiqiSquanxi" : m69,
    "qingxiankaiqibendexi" : MessageLookupByLibrary.simpleMessage("请先开启本地相册权限"),
    "qingxiankaiqixiangji" : MessageLookupByLibrary.simpleMessage("请先开启相机权限"),
    "qingxiankaiqixiangji_1" : MessageLookupByLibrary.simpleMessage("请先开启相机和麦克风权限"),
    "qingxianlianjieVRyan" : MessageLookupByLibrary.simpleMessage("请先连接VR眼镜"),
    "qingxiantongyiYVRfuw" : MessageLookupByLibrary.simpleMessage("请先同意玩出梦想《服务协议》和《隐私政策》"),
    "qingxianwanchengbiti" : MessageLookupByLibrary.simpleMessage("请先完成必填项"),
    "qingxianyuedubington" : MessageLookupByLibrary.simpleMessage("请先阅读并同意注销协议"),
    "qingxianyuedubington_1" : MessageLookupByLibrary.simpleMessage("请先阅读并同意Y币充值协议"),
    "qingxianzaiVRyanjing" : MessageLookupByLibrary.simpleMessage("请先在VR眼镜中登录您的账号！"),
    "qingxuanzeshipindepi" : MessageLookupByLibrary.simpleMessage("请选择视频的拼接模式"),
    "quanbu" : MessageLookupByLibrary.simpleMessage("全部"),
    "quanbuhuodong" : MessageLookupByLibrary.simpleMessage("全部活动"),
    "qucanjiaqucenjiaqusa" : MessageLookupByLibrary.simpleMessage("去参加"),
    "qudenglu" : MessageLookupByLibrary.simpleMessage("去登录"),
    "queding" : MessageLookupByLibrary.simpleMessage("确定"),
    "quedingfangqicaichan" : MessageLookupByLibrary.simpleMessage("确定放弃财产"),
    "quedingyaoshanchugai" : MessageLookupByLibrary.simpleMessage("确定要删除该好友吗？"),
    "quedingyaoshanchugai_1" : MessageLookupByLibrary.simpleMessage("确定要删除该活动吗？"),
    "quedingyaotuikuanmat" : MessageLookupByLibrary.simpleMessage("确定要退款吗？退款金额会在3个工作日后原路退还到你的支付账号中。退款期间你将不可使用该应用。"),
    "quedingzhuxiao" : MessageLookupByLibrary.simpleMessage("确定注销"),
    "quedingzhuxiaozhangh" : MessageLookupByLibrary.simpleMessage("确定注销账号"),
    "queren" : MessageLookupByLibrary.simpleMessage("确认"),
    "querenmima" : MessageLookupByLibrary.simpleMessage("确认密码"),
    "querenzhuxiaochongya" : MessageLookupByLibrary.simpleMessage("确认注销重要提醒"),
    "querenzhuxiaojiangwa" : MessageLookupByLibrary.simpleMessage("确认注销将完成注销账号申请，7天后系统将自动注销您的玩出梦想账号。如需撤回注销申请，请在7日内再次登录此账号。"),
    "querenzhuxiaozhangha" : MessageLookupByLibrary.simpleMessage("确认注销账号，七天后您的账号将被注销。如需撤回注销申请，请在7天内再次登录此账号。"),
    "qugoumai" : MessageLookupByLibrary.simpleMessage("去购买"),
    "qukaiqi" : MessageLookupByLibrary.simpleMessage("去开启"),
    "qulianjie" : MessageLookupByLibrary.simpleMessage("去连接"),
    "qushezhi" : MessageLookupByLibrary.simpleMessage("去设置"),
    "quxiao" : MessageLookupByLibrary.simpleMessage("取消"),
    "quxiaoluzhi" : MessageLookupByLibrary.simpleMessage("取消录制"),
    "quxiazai" : MessageLookupByLibrary.simpleMessage("去下载"),
    "recharge_service_agreement_url_dev" : MessageLookupByLibrary.simpleMessage("https://apitest.yvrdream.com/yvrdvcenter/#/rechargeagreement"),
    "recharge_service_agreement_url_release" : MessageLookupByLibrary.simpleMessage("https://developer.yvrdream.com/#/rechargeagreement"),
    "refundAvailable" : MessageLookupByLibrary.simpleMessage("支持退款申请："),
    "refund_policy_html_page_name" : MessageLookupByLibrary.simpleMessage("refundPolicy"),
    "register_account" : MessageLookupByLibrary.simpleMessage("注册账号"),
    "renxiangweizhu" : MessageLookupByLibrary.simpleMessage("人像为主"),
    "resend" : MessageLookupByLibrary.simpleMessage("重新发送"),
    "riqishijian" : MessageLookupByLibrary.simpleMessage("日期/时间"),
    "riyiersan" : MessageLookupByLibrary.simpleMessage("日,一,二,三,四,五,六"),
    "rizhidiaoshirizhitia" : MessageLookupByLibrary.simpleMessage("日志调试"),
    "runinrengxuanzejixuz" : MessageLookupByLibrary.simpleMessage("如您仍选择继续注销账号，则该账号内的虚拟权益等财产性利益（包括但不限于以下权益）视为您自动放弃，将无法继续使用。您理解并同意，我们无法协助您重新恢复上述服务。"),
    "ruxinxiqueloucuowuze" : MessageLookupByLibrary.simpleMessage("如信息缺漏、错误，则视为申领无效"),
    "ruyouqingshangchuang" : MessageLookupByLibrary.simpleMessage("如有，请上传购买玩出梦想的发票（非必填）"),
    "sanninzhuxiaobenYVRz" : MessageLookupByLibrary.simpleMessage("三、您注销本玩出梦想账号并不代表注销前该账号中的行为和相关责任得到减轻或豁免。"),
    "saomiaolanhexuyaonin" : MessageLookupByLibrary.simpleMessage("扫描蓝牙需要您在玩出梦想应用设置中先开启位置权限"),
    "sel_photograph" : MessageLookupByLibrary.simpleMessage("从相册选择"),
    "service_agreement" : MessageLookupByLibrary.simpleMessage("服务协议"),
    "service_agreement_html_page_name" : MessageLookupByLibrary.simpleMessage("server"),
    "shaixuantiaojian" : MessageLookupByLibrary.simpleMessage("筛选条件："),
    "shanchu" : MessageLookupByLibrary.simpleMessage("删除"),
    "shanchuS" : m70,
    "shanchuhaoyouchenggo" : MessageLookupByLibrary.simpleMessage("删除好友成功！"),
    "shanchuhuodong" : MessageLookupByLibrary.simpleMessage("删除活动"),
    "shanchushibai" : MessageLookupByLibrary.simpleMessage("删除失败"),
    "shanchuzhong" : MessageLookupByLibrary.simpleMessage("删除中"),
    "shangchuanshibaishan" : MessageLookupByLibrary.simpleMessage("上传失败"),
    "shangchuanwenjianwei" : MessageLookupByLibrary.simpleMessage("上传文件未通过智能审计"),
    "shangchuanzhongshang" : MessageLookupByLibrary.simpleMessage("上传中"),
    "shangwu" : MessageLookupByLibrary.simpleMessage("上午"),
    "shaohouchakanshaohou" : MessageLookupByLibrary.simpleMessage("稍后查看"),
    "shaohouqianwang" : MessageLookupByLibrary.simpleMessage("稍后前往"),
    "shaohouxiazai" : MessageLookupByLibrary.simpleMessage("稍后下载"),
    "shebeibucunzai" : MessageLookupByLibrary.simpleMessage("设备不存在"),
    "shenfenyanzheng" : MessageLookupByLibrary.simpleMessage("身份验证"),
    "shengjishibaiqingcho" : MessageLookupByLibrary.simpleMessage("升级失败，请重试"),
    "shengyuN0" : m71,
    "shengyuNxuzhifuN" : m72,
    "shenhebutongguoyuany" : MessageLookupByLibrary.simpleMessage("审核不通过，原因为：存在违规内容"),
    "shenheyitongguoshenh" : MessageLookupByLibrary.simpleMessage("，审核已通过"),
    "shenqingzhanghaozhux" : MessageLookupByLibrary.simpleMessage("申请账号注销"),
    "shezhichenggong" : MessageLookupByLibrary.simpleMessage("设置成功"),
    "shezhikeshiyongshidu" : MessageLookupByLibrary.simpleMessage("设置可使用时段，默认早上6时—晚上10时可使用VR眼镜"),
    "shezhimima" : MessageLookupByLibrary.simpleMessage("设置密码"),
    "shichangshizhang" : MessageLookupByLibrary.simpleMessage("时长"),
    "shichangshizhang_1" : MessageLookupByLibrary.simpleMessage("时长："),
    "shiduanyizhanyongqin" : MessageLookupByLibrary.simpleMessage("时段已占用，请选择其他时段"),
    "shifoufangqidangqian" : MessageLookupByLibrary.simpleMessage("是否放弃当前评价？"),
    "shifoulijixiazaindia" : MessageLookupByLibrary.simpleMessage("是否立即下载？\n点击立即下载，VR设备将在连网时自动下载该应用。"),
    "shifouquedingfangqid" : MessageLookupByLibrary.simpleMessage("是否确定放弃当前账号拥有的财产？"),
    "shifouquxiaobaomings" : MessageLookupByLibrary.simpleMessage("是否取消报名？"),
    "shijiansuoshezhi" : MessageLookupByLibrary.simpleMessage("时间锁设置"),
    "shijifukuan" : MessageLookupByLibrary.simpleMessage("实际付款"),
    "shipingeshicuowu" : MessageLookupByLibrary.simpleMessage("视频格式错误"),
    "shipinluzhiyizhongdu" : MessageLookupByLibrary.simpleMessage("视频录制已中断"),
    "shipinnuligechengzho" : MessageLookupByLibrary.simpleMessage("视频努力合成中..."),
    "shipinshili" : MessageLookupByLibrary.simpleMessage("视频示例"),
    "shiyonglanhehuliansh" : MessageLookupByLibrary.simpleMessage("使用蓝牙互联时，其他设备将看到此名称。"),
    "shiyongshuimingshiyo" : MessageLookupByLibrary.simpleMessage("使用说明"),
    "shoujianrenzhenshixi" : MessageLookupByLibrary.simpleMessage("收件人真实姓名"),
    "shoujinacunbuzuqingq" : MessageLookupByLibrary.simpleMessage("手机内存不足，请清理内存后再开始录制"),
    "shoujiyuVRyanjingwan" : MessageLookupByLibrary.simpleMessage("手机与VR眼镜网络不一致，请连接至相同网络后再重试"),
    "shouqi" : MessageLookupByLibrary.simpleMessage("收起"),
    "shuaxin" : MessageLookupByLibrary.simpleMessage("刷新"),
    "shuaxinguoyupinfanqi" : MessageLookupByLibrary.simpleMessage("刷新过于频繁，请稍后再试"),
    "shubingpinjieshuping" : MessageLookupByLibrary.simpleMessage("竖屏拼接"),
    "shubingshuping" : MessageLookupByLibrary.simpleMessage("竖屏"),
    "shuidianshenmebashuo" : MessageLookupByLibrary.simpleMessage("说点什么吧~"),
    "shujuqingqiuchaoshij" : MessageLookupByLibrary.simpleMessage("数据请求超时，加载失败"),
    "shuruhuodongmingchen" : MessageLookupByLibrary.simpleMessage("输入活动名称(14个字以内)"),
    "shurunindingchanzhon" : MessageLookupByLibrary.simpleMessage("输入您订单中的实际支付金额（需与上传截图中的保持一致）"),
    "shuruyanzhengma" : MessageLookupByLibrary.simpleMessage("输入验证码"),
    "sign_in" : MessageLookupByLibrary.simpleMessage("登录"),
    "sign_out" : MessageLookupByLibrary.simpleMessage("退出登录"),
    "sign_up" : MessageLookupByLibrary.simpleMessage("注册"),
    "social_activity" : MessageLookupByLibrary.simpleMessage("活动"),
    "social_add_friend" : MessageLookupByLibrary.simpleMessage("添加好友"),
    "social_add_req" : MessageLookupByLibrary.simpleMessage("请求添加你为好友"),
    "social_block" : MessageLookupByLibrary.simpleMessage("拉黑"),
    "social_block_list" : MessageLookupByLibrary.simpleMessage("黑名单"),
    "social_creat_event" : MessageLookupByLibrary.simpleMessage("创建活动"),
    "social_date" : MessageLookupByLibrary.simpleMessage("日期/时间"),
    "social_duration" : MessageLookupByLibrary.simpleMessage("时长"),
    "social_event_desc" : MessageLookupByLibrary.simpleMessage("活动说明（选填）"),
    "social_friend_req" : MessageLookupByLibrary.simpleMessage("好友申请"),
    "social_friends" : MessageLookupByLibrary.simpleMessage("好友"),
    "social_ignore" : MessageLookupByLibrary.simpleMessage("忽略"),
    "social_input_name" : MessageLookupByLibrary.simpleMessage("输入活动名称（14个字以内）"),
    "social_join_in" : MessageLookupByLibrary.simpleMessage("想参加"),
    "social_join_in_vr" : MessageLookupByLibrary.simpleMessage("在VR中加入"),
    "social_joined" : MessageLookupByLibrary.simpleMessage("已参加"),
    "social_know_more" : MessageLookupByLibrary.simpleMessage("添加好友可以了解更多"),
    "social_leave_event" : MessageLookupByLibrary.simpleMessage("退出活动"),
    "social_leave_event_desc" : MessageLookupByLibrary.simpleMessage("退出活动后，你将不会收到活动通知和动态更新，你仍可以再次参与活动。"),
    "social_minute" : MessageLookupByLibrary.simpleMessage("分钟"),
    "social_my_activity" : MessageLookupByLibrary.simpleMessage("我的活动"),
    "social_my_friends" : MessageLookupByLibrary.simpleMessage("我的好友"),
    "social_no_block" : MessageLookupByLibrary.simpleMessage("暂无黑名单"),
    "social_organizers" : MessageLookupByLibrary.simpleMessage("举办者"),
    "social_pass" : MessageLookupByLibrary.simpleMessage("通过"),
    "social_people_attend" : m73,
    "social_popular_activities" : MessageLookupByLibrary.simpleMessage("热门活动"),
    "social_recent_activity" : MessageLookupByLibrary.simpleMessage("最近活动"),
    "social_recommended" : MessageLookupByLibrary.simpleMessage("为你推荐"),
    "social_sel_app" : MessageLookupByLibrary.simpleMessage("选择应用"),
    "social_sendreq_yet" : MessageLookupByLibrary.simpleMessage("已发送请求"),
    "social_unfriend" : MessageLookupByLibrary.simpleMessage("删除"),
    "songNYbi" : m74,
    "sousuojieshu" : MessageLookupByLibrary.simpleMessage("搜索结束"),
    "sousuonichenheIDhaot" : MessageLookupByLibrary.simpleMessage("搜索昵称和ID号添加好友"),
    "sousuozaixianyouhuha" : MessageLookupByLibrary.simpleMessage("搜索在线游戏，好友名称，ID，应用"),
    "state_empty" : MessageLookupByLibrary.simpleMessage("空空如也"),
    "state_error" : MessageLookupByLibrary.simpleMessage("加载失败"),
    "state_load_fail" : MessageLookupByLibrary.simpleMessage("加载失败！点击重试！"),
    "state_load_more" : MessageLookupByLibrary.simpleMessage("松手,加载更多"),
    "state_network" : MessageLookupByLibrary.simpleMessage("网络不可用,请检查网络设置"),
    "state_refresh" : MessageLookupByLibrary.simpleMessage("刷新一下"),
    "state_retry" : MessageLookupByLibrary.simpleMessage("刷新页面"),
    "state_unauth" : MessageLookupByLibrary.simpleMessage("未登录"),
    "submit" : MessageLookupByLibrary.simpleMessage("提交"),
    "tabbar_device" : MessageLookupByLibrary.simpleMessage("设备"),
    "tabbar_home" : MessageLookupByLibrary.simpleMessage("商店"),
    "tabbar_profile" : MessageLookupByLibrary.simpleMessage("我的"),
    "tabbar_social" : MessageLookupByLibrary.simpleMessage("社区"),
    "take_picture" : MessageLookupByLibrary.simpleMessage("拍照"),
    "taobaozhanghuming" : MessageLookupByLibrary.simpleMessage("淘宝账户名"),
    "tianjia" : MessageLookupByLibrary.simpleMessage("添加"),
    "tianjiaVRyanjing" : MessageLookupByLibrary.simpleMessage("添加VR眼镜"),
    "tianjiaVRyanjingbeij" : MessageLookupByLibrary.simpleMessage("添加VR眼镜被拒绝，请返回后重新选择需要添加的VR眼镜。"),
    "tianjiaVRyanjingqian" : MessageLookupByLibrary.simpleMessage("添加VR眼镜前，请先开启蓝牙。"),
    "tianjiahaoyou" : MessageLookupByLibrary.simpleMessage("添加好友"),
    "tianjiahaoyoukeyilia" : MessageLookupByLibrary.simpleMessage("添加好友可以了解更多"),
    "tianjiashebei" : MessageLookupByLibrary.simpleMessage("添加设备"),
    "tianmao" : MessageLookupByLibrary.simpleMessage("天猫"),
    "tianqian" : MessageLookupByLibrary.simpleMessage("1天前"),
    "tianqian_1" : MessageLookupByLibrary.simpleMessage("2天前"),
    "tingzhitoubingtingzh" : MessageLookupByLibrary.simpleMessage("停止投屏"),
    "tips" : MessageLookupByLibrary.simpleMessage("温馨提示"),
    "toBind" : MessageLookupByLibrary.simpleMessage("去绑定"),
    "toast_already_commented" : MessageLookupByLibrary.simpleMessage("您已经评价过该应用"),
    "toast_app_purchased" : MessageLookupByLibrary.simpleMessage("应用已购买，请勿重复支付！"),
    "toast_avatar_fail" : MessageLookupByLibrary.simpleMessage("头像上传失败"),
    "toast_avatar_success" : MessageLookupByLibrary.simpleMessage("头像上传成功"),
    "toast_birthday" : MessageLookupByLibrary.simpleMessage("请先选择生日！"),
    "toast_dev_limit" : MessageLookupByLibrary.simpleMessage("登录的设备超过限制"),
    "toast_emoticon_icon" : MessageLookupByLibrary.simpleMessage("评价不支持表情输入哦！"),
    "toast_event_delete" : MessageLookupByLibrary.simpleMessage("活动已被创建者删除"),
    "toast_feedback_desc" : MessageLookupByLibrary.simpleMessage("感谢您的反馈，我们将在\n7个工作日内联系到您"),
    "toast_feedback_fail" : MessageLookupByLibrary.simpleMessage("问题反馈失败！"),
    "toast_gender" : MessageLookupByLibrary.simpleMessage("请先选择性别！"),
    "toast_inventory_shortage" : MessageLookupByLibrary.simpleMessage("应用库存不足！"),
    "toast_login_fail" : MessageLookupByLibrary.simpleMessage("登录账号异常"),
    "toast_msg_not_input" : MessageLookupByLibrary.simpleMessage("您有信息未填写！"),
    "toast_msg_update_fail" : MessageLookupByLibrary.simpleMessage("信息修改失败！"),
    "toast_need_login" : MessageLookupByLibrary.simpleMessage("请先登录后查看"),
    "toast_no_regist" : MessageLookupByLibrary.simpleMessage("输入的手机号未注册过！"),
    "toast_not_exist" : MessageLookupByLibrary.simpleMessage("手机号不存在"),
    "toast_order_fail" : MessageLookupByLibrary.simpleMessage("支付下单失败"),
    "toast_pwd_error" : MessageLookupByLibrary.simpleMessage("密码不正确"),
    "toast_redemption_fail" : MessageLookupByLibrary.simpleMessage("兑换失败"),
    "toast_refund_desc" : MessageLookupByLibrary.simpleMessage("退款请求已被受理，退款金额将在3个工作日后返还至你的支付账号中"),
    "toast_refund_fail" : MessageLookupByLibrary.simpleMessage("退款失败！"),
    "toast_refund_success" : MessageLookupByLibrary.simpleMessage("退款成功！"),
    "toast_regist_yet" : MessageLookupByLibrary.simpleMessage("手机号已经注册过账号"),
    "toast_unknown_error" : MessageLookupByLibrary.simpleMessage("未知错误！"),
    "toast_update_pwd" : MessageLookupByLibrary.simpleMessage("修改密码不能与原密码相同"),
    "toast_userinfo" : MessageLookupByLibrary.simpleMessage("个人信息设置成功"),
    "toast_vfcodeJ_send_fail" : MessageLookupByLibrary.simpleMessage("验证码发送失败"),
    "toast_vfcode_day" : MessageLookupByLibrary.simpleMessage("发送验证码超过一天允许的总量"),
    "toast_vfcode_error" : MessageLookupByLibrary.simpleMessage("验证码不正确"),
    "toast_vfcode_frequently" : MessageLookupByLibrary.simpleMessage("发送验证码过于频繁"),
    "tongbuqianqingxianqu" : MessageLookupByLibrary.simpleMessage("同步前请先确认蓝牙和位置权限开启。若Wi-Fi无密码，可直接点击确定。"),
    "tongbuwanglaotongbuw" : MessageLookupByLibrary.simpleMessage("同步网络"),
    "tongguo" : MessageLookupByLibrary.simpleMessage("通过"),
    "tongyi" : MessageLookupByLibrary.simpleMessage("同意"),
    "tongzhiVRxiazaishiba" : MessageLookupByLibrary.simpleMessage("通知VR下载失败，请前往VR设备中进行手动下载"),
    "toubingtouping" : MessageLookupByLibrary.simpleMessage("投屏"),
    "tuichuzhuxiao" : MessageLookupByLibrary.simpleMessage("退出注销"),
    "tuijian" : MessageLookupByLibrary.simpleMessage("推荐"),
    "tuikuan" : MessageLookupByLibrary.simpleMessage("退款"),
    "tuikuanshibai" : MessageLookupByLibrary.simpleMessage("退款失败"),
    "tuikuanshibaiyichaog" : MessageLookupByLibrary.simpleMessage("退款失败，已超过退款期限"),
    "tuikuanzhong" : MessageLookupByLibrary.simpleMessage("退款中"),
    "tupianshangchuanshib" : MessageLookupByLibrary.simpleMessage("图片上传失败请重试"),
    "tupianyasushibaitupi" : MessageLookupByLibrary.simpleMessage("图片压缩失败"),
    "unbind" : MessageLookupByLibrary.simpleMessage("解绑"),
    "unbindAlipay" : MessageLookupByLibrary.simpleMessage("确定要解绑支付宝免密支付吗？"),
    "unbindFail" : MessageLookupByLibrary.simpleMessage("解绑失败"),
    "unbindSuccess" : MessageLookupByLibrary.simpleMessage("解绑成功"),
    "understood" : MessageLookupByLibrary.simpleMessage("知道了"),
    "unknown" : MessageLookupByLibrary.simpleMessage("未知"),
    "user_info" : MessageLookupByLibrary.simpleMessage("个人信息"),
    "verify_code_login" : MessageLookupByLibrary.simpleMessage("验证码登录"),
    "vfcode_send" : MessageLookupByLibrary.simpleMessage("验证码已发送，注意查收！"),
    "wancheng" : MessageLookupByLibrary.simpleMessage("完成"),
    "wangjiliao" : MessageLookupByLibrary.simpleMessage("忘记了？"),
    "wanglaolianjieyiduan" : MessageLookupByLibrary.simpleMessage("网络连接已断开"),
    "wanglaoyichangqingsh" : MessageLookupByLibrary.simpleMessage("网络异常，请稍后再试"),
    "wanglaoyichangqingsh_1" : MessageLookupByLibrary.simpleMessage("网络异常，请稍后再试。"),
    "weibaocunpinjieshipi" : MessageLookupByLibrary.simpleMessage("未保存拼接视频"),
    "weibaozhengnindezhan" : MessageLookupByLibrary.simpleMessage("为保证您的账号安全，在您提交的注销申请生效前，需同时满足以下条件："),
    "weibaozhengxiaoguoqi" : MessageLookupByLibrary.simpleMessage("为保证效果，请竖握手机进行录制"),
    "weichengweihaoyou" : MessageLookupByLibrary.simpleMessage("未成为好友"),
    "weichuanruwangyelian" : MessageLookupByLibrary.simpleMessage("未传入网页链接，请检查跳转设置"),
    "weifaweigui" : MessageLookupByLibrary.simpleMessage("违法违规"),
    "weifaxianxiangyaodeV" : MessageLookupByLibrary.simpleMessage("未发现想要的VR眼镜，可“重新搜索”"),
    "weiguanzhugaihuodong" : MessageLookupByLibrary.simpleMessage("未关注该活动，不可取消关注"),
    "weiguinichenqingchon" : MessageLookupByLibrary.simpleMessage("违规昵称，请重新提交昵称"),
    "weikaiqidingweifuwub" : MessageLookupByLibrary.simpleMessage("未开启定位服务，部分功能不可用"),
    "weilianjie" : MessageLookupByLibrary.simpleMessage("未连接"),
    "weixin" : MessageLookupByLibrary.simpleMessage("微信"),
    "weixinzhifu" : MessageLookupByLibrary.simpleMessage("微信支付"),
    "weizhaodaoxiangguanh" : MessageLookupByLibrary.simpleMessage("未找到相关活动！"),
    "weizhi" : MessageLookupByLibrary.simpleMessage("未知"),
    "weizhicuowu" : MessageLookupByLibrary.simpleMessage("未知错误"),
    "welcome" : MessageLookupByLibrary.simpleMessage("欢迎来到玩出梦想"),
    "wenjianbucunzai" : MessageLookupByLibrary.simpleMessage("文件不存在"),
    "wenxindishiwenxintis" : MessageLookupByLibrary.simpleMessage("温馨提示"),
    "wodeVRyanjingwodiVRy" : MessageLookupByLibrary.simpleMessage("我的VR眼镜"),
    "wodehaoyouwodihaoyou" : MessageLookupByLibrary.simpleMessage("我的好友"),
    "wodeheimingdan" : MessageLookupByLibrary.simpleMessage("我的黑名单"),
    "wodehuodongwodihuodo" : MessageLookupByLibrary.simpleMessage("我的活动"),
    "wodeqianbaowodiqianb" : MessageLookupByLibrary.simpleMessage("我的钱包"),
    "woyiyuedubingtongyiS" : m75,
    "woyiyuedubingtongyiY" : m76,
    "wozhidaoliao" : MessageLookupByLibrary.simpleMessage("我知道了"),
    "xiangcanjiaxiangcenj" : MessageLookupByLibrary.simpleMessage("想参加"),
    "xiangce" : MessageLookupByLibrary.simpleMessage("相册"),
    "xiangguantiaokuanchu" : MessageLookupByLibrary.simpleMessage("相关条款处理您的个人信息，或对其进行匿名化处理。"),
    "xiangguanyingyong" : MessageLookupByLibrary.simpleMessage("相关应用"),
    "xiangji" : MessageLookupByLibrary.simpleMessage("相机"),
    "xiangjicuowu" : MessageLookupByLibrary.simpleMessage("相机错误"),
    "xiangxiangrenshinide" : MessageLookupByLibrary.simpleMessage("向想认识你的人，介绍一下自己吧"),
    "xiaofeishijian" : MessageLookupByLibrary.simpleMessage("消费时间："),
    "xiaofeixiangqing" : MessageLookupByLibrary.simpleMessage("消费详情"),
    "xiaoshi" : MessageLookupByLibrary.simpleMessage("1小时"),
    "xiaoshi_1" : MessageLookupByLibrary.simpleMessage("3小时"),
    "xiaoshi_2" : MessageLookupByLibrary.simpleMessage("4小时"),
    "xiaoshi_3" : MessageLookupByLibrary.simpleMessage("2小时"),
    "xiaoshi_4" : MessageLookupByLibrary.simpleMessage("5小时"),
    "xiawu" : MessageLookupByLibrary.simpleMessage("下午"),
    "xiayibu" : MessageLookupByLibrary.simpleMessage("下一步"),
    "xiazaiyingyong" : MessageLookupByLibrary.simpleMessage("下载应用"),
    "xing" : MessageLookupByLibrary.simpleMessage("4星"),
    "xing_1" : MessageLookupByLibrary.simpleMessage("1星"),
    "xing_2" : MessageLookupByLibrary.simpleMessage("3星"),
    "xing_3" : MessageLookupByLibrary.simpleMessage("2星"),
    "xing_4" : MessageLookupByLibrary.simpleMessage("5星"),
    "xinzengshijianduan" : MessageLookupByLibrary.simpleMessage("新增时间段"),
    "xiugaiqianqingxiangu" : MessageLookupByLibrary.simpleMessage("修改前请先关闭青少年模式"),
    "xiugaishoujihaobunen" : MessageLookupByLibrary.simpleMessage("修改手机号不能与旧手机号相同哦！"),
    "xuanzeshebeiqushebei" : MessageLookupByLibrary.simpleMessage("选择设备，去设备中体验应用"),
    "xuanzeshebeixuanzhai" : MessageLookupByLibrary.simpleMessage("选择设备"),
    "xuanzeyingyongxuanzh" : MessageLookupByLibrary.simpleMessage("选择应用"),
    "xuanzezhifufangshixu" : MessageLookupByLibrary.simpleMessage("选择支付方式"),
    "xuyaodenglucainengch" : MessageLookupByLibrary.simpleMessage("需要登录才能查看已下载应用！"),
    "xuyaodenglucainengch_1" : MessageLookupByLibrary.simpleMessage("需要登录才能查看您的应用！"),
    "xuyaodenglucainengch_2" : MessageLookupByLibrary.simpleMessage("需要登录才能查看消息中心！"),
    "xuyaodenglucainenggo" : MessageLookupByLibrary.simpleMessage("需要登录才能购买"),
    "xuyaodenglucainengju" : MessageLookupByLibrary.simpleMessage("需要登录才能举报"),
    "yCoinRecord" : MessageLookupByLibrary.simpleMessage("Y币记录"),
    "yanzhengguchangshenf" : MessageLookupByLibrary.simpleMessage("验证家长身份"),
    "yanzhengmabuzhengque" : MessageLookupByLibrary.simpleMessage("验证码不正确"),
    "yanzhengmacuowu" : MessageLookupByLibrary.simpleMessage("验证码错误"),
    "yanzhengshenfen" : MessageLookupByLibrary.simpleMessage("验证身份"),
    "yanzhengshibai" : MessageLookupByLibrary.simpleMessage("验证失败"),
    "yaocanjiahuodongnixu" : m77,
    "yaoqinghaoyou" : MessageLookupByLibrary.simpleMessage("邀请好友"),
    "yibaocun" : MessageLookupByLibrary.simpleMessage("已保存"),
    "yibaocun_1" : MessageLookupByLibrary.simpleMessage("已保存"),
    "yibaocunzhi" : MessageLookupByLibrary.simpleMessage("已保存至"),
    "yibaocunzhixiangce" : MessageLookupByLibrary.simpleMessage("已保存至相册"),
    "yibaoming" : MessageLookupByLibrary.simpleMessage("已报名"),
    "yicanjiagaihuodongyi" : MessageLookupByLibrary.simpleMessage("已参加该活动"),
    "yicanjiayicenjiayisa" : MessageLookupByLibrary.simpleMessage("已参加"),
    "yichenggongfachuyaoq" : MessageLookupByLibrary.simpleMessage("已成功发出邀请"),
    "yichenggongjiaruhuod" : MessageLookupByLibrary.simpleMessage("已成功加入活动！"),
    "yichu" : MessageLookupByLibrary.simpleMessage("移除"),
    "yichuciVRyanjing" : MessageLookupByLibrary.simpleMessage("移除此VR眼镜"),
    "yichuheimingchanchen" : MessageLookupByLibrary.simpleMessage("移出黑名单成功！"),
    "yichuheimingchanyich" : MessageLookupByLibrary.simpleMessage("移出黑名单"),
    "yichuheimingdan" : MessageLookupByLibrary.simpleMessage("移除黑名单"),
    "yichuhouVRjiangzaili" : MessageLookupByLibrary.simpleMessage("移除后，VR将在连网时自动退出\n原登录账号，是否继续移除？"),
    "yifasongqingqiuyifei" : MessageLookupByLibrary.simpleMessage("已发送请求"),
    "yifeichangweihanwome" : MessageLookupByLibrary.simpleMessage("一、非常遗憾我们无法继续为您提供服务。如您仍选择继续注销账号，则该账号内的虚拟权益等财产性利益（包括但不限于以下权益）视为您自动放弃，将无法继续使用。您理解并同意，我们无法协助您重新恢复上述服务。"),
    "yigoumai" : MessageLookupByLibrary.simpleMessage("已购买"),
    "yiguanbigexinghuanar" : MessageLookupByLibrary.simpleMessage("已关闭个性化内容推荐"),
    "yijiarugaihuodong" : MessageLookupByLibrary.simpleMessage("已加入该活动！"),
    "yijiaruheimingchanyi" : MessageLookupByLibrary.simpleMessage("已加入黑名单！"),
    "yijieshu" : MessageLookupByLibrary.simpleMessage("已结束"),
    "yijingguanzhugaihuod" : MessageLookupByLibrary.simpleMessage("已经关注该活动，不可重复关注"),
    "yilingjiang" : MessageLookupByLibrary.simpleMessage("已领奖"),
    "yimogengduowenjianyi" : MessageLookupByLibrary.simpleMessage("已无更多文件"),
    "yimogengduoyingyongy" : MessageLookupByLibrary.simpleMessage("已无更多应用！"),
    "yingyonggoumaichengg" : MessageLookupByLibrary.simpleMessage("应用购买成功"),
    "yingyongtuiandetongz" : MessageLookupByLibrary.simpleMessage("应用推广的通知"),
    "yingyongyixiajiahuob" : MessageLookupByLibrary.simpleMessage("应用已下架或不存在"),
    "yinsiguanli" : MessageLookupByLibrary.simpleMessage("隐私管理"),
    "yiquxiaogechengqingc" : MessageLookupByLibrary.simpleMessage("已取消合成，请重新录制"),
    "yituichugaihuodong" : MessageLookupByLibrary.simpleMessage("已退出该活动!"),
    "yituichugaihuodong_1" : MessageLookupByLibrary.simpleMessage("已退出该活动！"),
    "yiwan" : MessageLookupByLibrary.simpleMessage("已玩"),
    "yiwanNfenzhong" : m78,
    "yiwanNxiaoshi" : m79,
    "yiwanchengdakayiwanc" : MessageLookupByLibrary.simpleMessage("已完成打卡"),
    "yixiangVRyanjingfaso" : MessageLookupByLibrary.simpleMessage("已向VR眼镜发送添加请求，请前往VR眼镜中点击同意。"),
    "yonghuyinsizhengce" : MessageLookupByLibrary.simpleMessage("《用户隐私政策》"),
    "youhuquanNzhang3youh" : m80,
    "youhuquanfafangtongz" : MessageLookupByLibrary.simpleMessage("游戏券发放通知"),
    "youhuquanyouhuiquany" : MessageLookupByLibrary.simpleMessage("游戏券"),
    "youhuquanyouhuiquany_1" : MessageLookupByLibrary.simpleMessage("游戏券"),
    "youhushuyouhuishuyou" : MessageLookupByLibrary.simpleMessage("游戏数"),
    "youxiaoqizhi" : MessageLookupByLibrary.simpleMessage("有效期至："),
    "yuan10Ybi" : MessageLookupByLibrary.simpleMessage("1元=10Y币"),
    "yudiyicishurudemimab" : MessageLookupByLibrary.simpleMessage("与第一次输入的密码不一致"),
    "yujihaixuNmiao" : m81,
    "yulantupian" : MessageLookupByLibrary.simpleMessage("预览图片"),
    "yunxuyijiaruyonghuya" : MessageLookupByLibrary.simpleMessage("允许已加入用户邀请好友"),
    "yvr_protocol" : MessageLookupByLibrary.simpleMessage("使用手机号码注册代表你已经同意玩出梦想"),
    "yvr_protocol2" : m82,
    "zaiVRzhongjiaru" : MessageLookupByLibrary.simpleMessage("在VR中加入"),
    "zaininshenqingzhuxia" : MessageLookupByLibrary.simpleMessage("在您申请注销玩出梦想平台账号之前，请您仔细阅读并同意本《账号注销协议》。"),
    "zanbu" : MessageLookupByLibrary.simpleMessage("暂不"),
    "zanbugengxin" : MessageLookupByLibrary.simpleMessage("暂不更新"),
    "zanbushiyong" : MessageLookupByLibrary.simpleMessage("暂不使用"),
    "zanmogengduodongtaiz" : MessageLookupByLibrary.simpleMessage("暂无更多动态"),
    "zanmohaoyouzanwuhaoy" : MessageLookupByLibrary.simpleMessage("暂无好友！"),
    "zanmojiebinghelubing" : MessageLookupByLibrary.simpleMessage("暂无截屏和录屏文件"),
    "zanmojiluzanwujilu" : MessageLookupByLibrary.simpleMessage("暂无记录"),
    "zanmokaquanzanwukaqu" : MessageLookupByLibrary.simpleMessage("暂无卡券"),
    "zanmokeyongzanwukeyo" : MessageLookupByLibrary.simpleMessage("暂无可用"),
    "zanmoshiyongshuiming" : MessageLookupByLibrary.simpleMessage("暂无使用说明"),
    "zanshimofahuoqushebe" : MessageLookupByLibrary.simpleMessage("暂时无法获取设备绑定信息"),
    "zanweigoumaiyingyong" : MessageLookupByLibrary.simpleMessage("暂未购买应用"),
    "zhangchanzhangdanzha" : MessageLookupByLibrary.simpleMessage("账单"),
    "zhanghaobucunzai" : MessageLookupByLibrary.simpleMessage("账号不存在"),
    "zhanghaodenglu" : MessageLookupByLibrary.simpleMessage("账号登录"),
    "zhanghaoyishenqingzh" : MessageLookupByLibrary.simpleMessage("账号已申请注销"),
    "zhanghaoyishixiao" : MessageLookupByLibrary.simpleMessage("账号已失效"),
    "zhanghaoyizhuxiao" : MessageLookupByLibrary.simpleMessage("账号已注销"),
    "zhanghaoyuanquan" : MessageLookupByLibrary.simpleMessage("账号与安全"),
    "zhanghaozhuxiaoshiba" : MessageLookupByLibrary.simpleMessage("账号注销失败"),
    "zhanghuyuanquan" : MessageLookupByLibrary.simpleMessage("账户与安全"),
    "zhankai" : MessageLookupByLibrary.simpleMessage("展开"),
    "zhegeshishipindebiao" : MessageLookupByLibrary.simpleMessage("这个是视频的标题"),
    "zhengzaiYVRdatingzhe" : MessageLookupByLibrary.simpleMessage("正在YVR大厅"),
    "zhengzaibaocun" : MessageLookupByLibrary.simpleMessage("正在保存..."),
    "zhengzaichangshichon" : MessageLookupByLibrary.simpleMessage("正在尝试重新连接..."),
    "zhengzaichangshilian" : MessageLookupByLibrary.simpleMessage("正在尝试连接另一台设备"),
    "zhengzaidakaiyingyon" : MessageLookupByLibrary.simpleMessage("正在打开应用，请勿重复操作"),
    "zhengzaijinhangzhong" : MessageLookupByLibrary.simpleMessage("正在进行中"),
    "zhengzaijinhangzhong_1" : MessageLookupByLibrary.simpleMessage("正在进行中的活动无法删除哦！"),
    "zhengzailianjieVRyan" : MessageLookupByLibrary.simpleMessage("正在连接VR眼镜…"),
    "zhengzailianjiezhong" : MessageLookupByLibrary.simpleMessage("正在连接中..."),
    "zhengzaiqiehuanshebe" : MessageLookupByLibrary.simpleMessage("正在切换设备..."),
    "zhengzaiquanlisousuo" : MessageLookupByLibrary.simpleMessage("正在全力搜索VR眼镜…"),
    "zhengzaishiyongS" : m83,
    "zhengzaishiyongweizh" : MessageLookupByLibrary.simpleMessage("正在使用未知应用"),
    "zhengzaishiyongyingy" : m84,
    "zhengzaisousuo" : MessageLookupByLibrary.simpleMessage("正在搜索"),
    "zhengzaiweiVRyanjing" : MessageLookupByLibrary.simpleMessage("正在为VR眼镜连接\nWi-Fi，请稍后…"),
    "zhidaoliao" : MessageLookupByLibrary.simpleMessage("知道了"),
    "zhifubaozhanghaodesh" : MessageLookupByLibrary.simpleMessage("银行卡姓名（需和身份证上姓名一致）"),
    "zhifubaozhanghaoyong" : MessageLookupByLibrary.simpleMessage("银行卡号（用以接受返款，仅限储蓄卡）"),
    "zhifubaozhifu" : MessageLookupByLibrary.simpleMessage("支付宝支付"),
    "zhifuchenggong" : MessageLookupByLibrary.simpleMessage("支付成功"),
    "zhifufangshi" : MessageLookupByLibrary.simpleMessage("支付方式："),
    "zhifufangshi_1" : MessageLookupByLibrary.simpleMessage("支付方式"),
    "zhifujieguo" : MessageLookupByLibrary.simpleMessage("支付结果"),
    "zhifujine" : MessageLookupByLibrary.simpleMessage("支付金额："),
    "zhifushibai" : MessageLookupByLibrary.simpleMessage("支付失败"),
    "zhifushijian" : MessageLookupByLibrary.simpleMessage("支付时间："),
    "zhiyouhuodongchuangj" : MessageLookupByLibrary.simpleMessage("只有活动创建者拥有此权限哦！"),
    "zhouliuzhizhourizhou" : MessageLookupByLibrary.simpleMessage("周六至周日"),
    "zhouyizhizhouwu" : MessageLookupByLibrary.simpleMessage("周一至周五"),
    "zhuanti" : MessageLookupByLibrary.simpleMessage("专题"),
    "zhuantiyiguoqihuobuc" : MessageLookupByLibrary.simpleMessage("专题已过期或不存在"),
    "zhutiyiguoqihuobucun" : MessageLookupByLibrary.simpleMessage("主题已过期或不存在"),
    "zhuxiaochenggongshou" : MessageLookupByLibrary.simpleMessage("注销成功，手机与第\n三方已释放"),
    "zhuxiaoxieyi" : MessageLookupByLibrary.simpleMessage("注销协议"),
    "zhuxiaoxieyi_1" : MessageLookupByLibrary.simpleMessage("《注销协议》"),
    "zhuxiaozhanghao" : MessageLookupByLibrary.simpleMessage("注销账号"),
    "zhuyireshenhetongguo" : MessageLookupByLibrary.simpleMessage("1.请再次确认信息填写是否有误，审核中无法修改信息。\n2.提交后，将生成审核流程码，请联系原销售渠道员工/客服（8:00-24:00），进行最终核对。\n3.您所提交的所有信息我们将进行保密处理，继续提交将视为您已同意将信息授权给玩出梦想进行合法校验。"),
    "zijichuangjiandehuod" : MessageLookupByLibrary.simpleMessage("自己创建的活动无法退出哦！"),
    "zongyue" : MessageLookupByLibrary.simpleMessage("总余额"),
    "zuiduoyunxushangchua" : m85,
    "zuijinyizhou" : MessageLookupByLibrary.simpleMessage("最近一周"),
    "zuirehuodong" : MessageLookupByLibrary.simpleMessage("最热活动"),
    "zuixinhuodong" : MessageLookupByLibrary.simpleMessage("最新活动")
  };
}
