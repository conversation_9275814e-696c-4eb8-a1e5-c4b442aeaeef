// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static m0(date_0) => "Purchased at ${date_0}";

  static m1(str_0) => "你如何评价${str_0}？";

  static m2(str_0) => "我知悉本次参与的VR产品打卡试用活动将会获得不同激励金额，我已阅读并同意${str_0}，根据国家法律规定，YVR将为我申报个人所得税。";

  static m3(num_0, num_1, num_2) => " End after ${num_0}:${num_1}:${num_2}";

  static m4(num_0, num_1) => " End after ${num_0}:${num_1} ";

  static m5(num_0) => "${num_0} Y Coins";

  static m6(num_0) => "${num_0} Y Coins have been distributed. You can use them to redeem items in the store. Thank you for your support for YVR!";

  static m7(num_0) => "${num_0} min";

  static m8(num_0) => " End after ${num_0} minutes";

  static m9(num_0) => "${num_0} minutes ago";

  static m10(num_0) => "To end in ${num_0}s";

  static m11(num_0) => "${num_0} users have joined in.";

  static m12(num_0) => "Rated by ${num_0} users";

  static m13(num_0) => "Commented by ${num_0} users";

  static m14(num_0) => "${num_0} users have signed up.";

  static m15(num_0) => "${num_0} users have joined in.";

  static m16(num_0) => "${num_0}0% off";

  static m17(num_0) => "End ${num_0} days later";

  static m18(num_0) => "${num_0} h";

  static m19(num_0) => "End after ${num_0} hours";

  static m20(num_0) => "${num_0} hours ago";

  static m21(num_0) => "${num_0} star";

  static m22(num_0) => "${num_0} stars";

  static m23(num_0) => "${num_0} comments";

  static m24(num_0) => "${num_0}";

  static m25(num_0, str_0) => "${num_0} coupons for ${str_0} have been distributed and you can use them to exchange for items in the store. Thank you for your support and love for YVR!";

  static m26(num_0) => "${num_0} vouchers available.";

  static m27(num_0) => "Passwords error，You have ${num_0} chances left";

  static m28(str_0) => "${str_0} has been purchased. Now you can enjoy it on your VR device.";

  static m29(str_0) => "${str_0} This activity does not exist.";

  static m30(str_0) => "${str_0} has not checked in today.";

  static m31(str_0) => "${str_0} has claimed the reward.";

  static m32(str_0) => "${str_0} has checked in.";

  static m33(str_0) => "and ${str_0} are mutual friends.";

  static m34(str_0, str_1) => "${str_0} has not been installed in ${str_1}. Install it now and try it.";

  static m35(str_0) => "The comment in ${str_0} has been blocked by the system due to suspected violations. Do not make uncivil comments for the sake of a favorable community environment.";

  static m36(str_0, str_1) => "The comment in ${str_0} has been blocked by the system due to suspected ${str_1}. Do not make uncivil comments for the sake of a favorable community environment.";

  static m37(str_0) => "SN number: ${str_0}";

  static m38(str_0) => "1. VR app/game: ${str_0}";

  static m39(str_0) => "Kindly reminder: Account cancellation is an unrecoverable operation. Please read this reminder carefully, and proceed only if you are sure you want to cancel the YVR account and fully understand the effect of the operation. We recommend you back up your account information before proceeding and ensure that all services related to the account have been properly handled. Once your account is canceled, we will process or anonymize your personal information according to the relevant terms of ${str_0}.";

  static m40(num_0) => "2. Y Coins: ${num_0}";

  static m41(num_0) => "Not applicable coupons: ${num_0}";

  static m42(str_0) => "${str_0} opening timed out. Please try again.";

  static m43(num_0) => "${num_0} persons are online.";

  static m44(num_0) => "Row ${num_0}";

  static m45(num_0) => "Row ${num_0}";

  static m46(str_0) => "This account has been canceled and will be deleted in ${str_0}. If you continue to log in, account cancelation will be withdrawn.";

  static m47(str_0) => "Purchase ${str_0}";

  static m48(num_0) => "${num_0} comment(s)";

  static m49(str_0) => "The backend interface needs to be compatible with the ${str_0} field.";

  static m50(num_0) => "Applicable coupons: ${num_0}";

  static m51(str_0) => "Try ${str_0} in VR.";

  static m52(str_0) => "Try ${str_0} in the VR now.";

  static m53(str_0) => "Blacklist ${str_0}";

  static m54(num_0) => "Accumulated check-in days: ${num_0}";

  static m55(str_0) => "*Flow code: ${str_0}. Use the original purchase account to consult the customer service on the corresponding e-commerce platform for final redemption. Once your application is approved, the reward will be granted as soon as possible. Pay attention to the SMS notification.";

  static m56(num_0) => "${num_0} accepted your friend request.";

  static m57(num_0) => "${num_0} invites you to join an activity.";

  static m58(str_0) => "You are all playing ${str_0}";

  static m59(str_0) => "Your VR headset requests to purchase ${str_0}. Would you like to purchase it?";

  static m60(num_0, str_0) => "You have given a ${num_0}-star rating for ${str_0} in the App Store.";

  static m61(num_0, str_0) => "You have given a ${num_0}-star rating for ${str_0} in the App Store. Your review has been approved.";

  static m62(num_0, str_0) => "You have given a ${num_0}-star rating for ${str_0} in the App Store. Your review has been rejected due to illegal content.";

  static m63(num_0, str_0, str_1) => "You have given a ${num_0}-star rating for ${str_0} in the App Store. Your review has been rejected due to ${str_1} content.";

  static m64(str_0) => "Coming soon ${str_0}";

  static m65(str_0) => "Rate the ${str_0} app.";

  static m66(str_0, str_1) => "Please carefully read and fully understand all of the provisions in ${str_0} and ${str_1}. You can view, change, and delete your personal information and manage permissions in My. If you agree, tap Agree to accept our service.";

  static m67(num_0) => "Enter a ${num_0}-digit number.";

  static m68(str_0) => "Enter ${str_0}.";

  static m69(str_0) => "Enable ${str_0} permission";

  static m70(str_0) => "Delete${str_0}";

  static m71(num_0) => "(Remaining: ${num_0})";

  static m72(num_0, num_1) => "(Remaining: ${num_0}，; To-be-paid: ${num_1})";

  static m73(num_0) => "${num_0} user(s) joined";

  static m74(num_0) => "Extra ${num_0} Y Coins";

  static m75(str_0) => "I have read and agreed to the terms and conditions of the ${str_0}";

  static m76(str_0, str_1) => "I have read and agree to YVR${str_0} and ${str_1}.";

  static m77(str_0) => "To join this activity, you need to download ${str_0}.";

  static m78(num_0) => "Time played: ${num_0} minutes.";

  static m79(num_0) => " Time played: ${num_0} hours.";

  static m80(num_0) => "3. Coupons: ${num_0}";

  static m81(num_0) => "${num_0} seconds left";

  static m82(str_0, str_1) => "Registration by email means that you have agreed to YVR${str_0} and ${str_1}";

  static m83(str_0) => "is using ${str_0}.";

  static m84(str_0) => "is using ${str_0}.";

  static m85(num_0) => "A maximum of ${num_0} images are allowed.";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static _notInlinedMessages(_) => <String, Function> {
    "AddNewAddress" : MessageLookupByLibrary.simpleMessage("新建收货地址"),
    "AdditionalContent" : MessageLookupByLibrary.simpleMessage("附加内容"),
    "AgeAdvice" : MessageLookupByLibrary.simpleMessage("年龄建议："),
    "AllApps" : MessageLookupByLibrary.simpleMessage("全部应用"),
    "AllGames" : MessageLookupByLibrary.simpleMessage("全部游戏"),
    "Application" : MessageLookupByLibrary.simpleMessage("应用"),
    "Appointment" : MessageLookupByLibrary.simpleMessage("预约"),
    "Attention" : MessageLookupByLibrary.simpleMessage("注意"),
    "BackToCheck" : MessageLookupByLibrary.simpleMessage("返回检查"),
    "CheckInActivity" : MessageLookupByLibrary.simpleMessage("打卡签到活动"),
    "Comfort" : MessageLookupByLibrary.simpleMessage("舒适度："),
    "ComfortRating" : MessageLookupByLibrary.simpleMessage("舒适度评级"),
    "Comfortable" : MessageLookupByLibrary.simpleMessage("舒适"),
    "CommonQuestion" : MessageLookupByLibrary.simpleMessage("常见问题"),
    "CompatibilityRating" : MessageLookupByLibrary.simpleMessage("兼容性评级"),
    "ConfirmOrder" : MessageLookupByLibrary.simpleMessage("确认订单"),
    "Confrontation" : MessageLookupByLibrary.simpleMessage("对抗"),
    "ConnectNearbyDevicesPermission" : MessageLookupByLibrary.simpleMessage("扫描蓝牙需要您在玩出梦想应用设置中先开启连接附近的设备权限"),
    "ControlMode" : MessageLookupByLibrary.simpleMessage("控制方式："),
    "Cooperate" : MessageLookupByLibrary.simpleMessage("合作"),
    "CurrentlyAvailablePoints" : MessageLookupByLibrary.simpleMessage("当前可用积分"),
    "DataAccumulation" : MessageLookupByLibrary.simpleMessage("暂无评分，数据累积中"),
    "Dgoumai" : m0,
    "EditAddress" : MessageLookupByLibrary.simpleMessage("编辑收货地址"),
    "EmailAddressHasBeenRegistered" : MessageLookupByLibrary.simpleMessage("Email address registered"),
    "ExchangeRecord" : MessageLookupByLibrary.simpleMessage("兑换记录"),
    "ExchangeResult" : MessageLookupByLibrary.simpleMessage("兑换结果"),
    "Explore" : MessageLookupByLibrary.simpleMessage("探索"),
    "ExploreArea" : MessageLookupByLibrary.simpleMessage("探索专区的应用可能在开发或测试中，可参考兼容性评级和游玩建议进行体验"),
    "FailedToVerifyTheDigits" : MessageLookupByLibrary.simpleMessage("身份证号码位数验证失败"),
    "FitsMostPlayers" : MessageLookupByLibrary.simpleMessage("Not appropriate for a few players, and may become comfortable after adaptation."),
    "GameMode" : MessageLookupByLibrary.simpleMessage("游戏模式："),
    "Gamepad" : MessageLookupByLibrary.simpleMessage("游戏手柄"),
    "Games" : MessageLookupByLibrary.simpleMessage("游戏"),
    "GiftCard" : MessageLookupByLibrary.simpleMessage("礼品卡"),
    "HandTracking" : MessageLookupByLibrary.simpleMessage("手势追踪"),
    "HowDoYouEvaluate" : m1,
    "IAgreeThatYVRWill" : MessageLookupByLibrary.simpleMessage("我同意YVR使用所提交信息进行合法校验。YVR承诺对提供的信息严格保密。"),
    "IConfirmThatTheAbove" : MessageLookupByLibrary.simpleMessage("我确认以上信息无误。信息一旦提交将不能修改，如信息错误导致未能取得返现由本人承担责任。"),
    "IDCardError" : MessageLookupByLibrary.simpleMessage("身份证号码校验未通过"),
    "IKnownThisVRAcitivity" : m2,
    "IUnderstandAndAgree" : MessageLookupByLibrary.simpleMessage("我知悉并同意为返现活动提供的信息，且知悉以上信息包含个人信息。"),
    "IncorrectEmailAddress" : MessageLookupByLibrary.simpleMessage("Incorrect email address"),
    "Individual" : MessageLookupByLibrary.simpleMessage("单人"),
    "KeepTimeSynchronized" : MessageLookupByLibrary.simpleMessage("Please keep the phone and device time synchronized"),
    "Keyboard" : MessageLookupByLibrary.simpleMessage("键盘"),
    "Latest" : MessageLookupByLibrary.simpleMessage("Latest"),
    "LoginFailure" : MessageLookupByLibrary.simpleMessage("Login failure"),
    "LoginPasswordRules" : MessageLookupByLibrary.simpleMessage("8-12 digits, with at least 3 digits in numbers, letters, and symbols"),
    "MajorUpdate" : MessageLookupByLibrary.simpleMessage("Major Update"),
    "ManyPeople" : MessageLookupByLibrary.simpleMessage("多人"),
    "Moderate" : MessageLookupByLibrary.simpleMessage("适中"),
    "MyPoints" : MessageLookupByLibrary.simpleMessage("我的积分"),
    "NNNhoujieshu" : m3,
    "NNhoujieshu" : m4,
    "NYbi" : m5,
    "NYbiyifafangkezaisha" : m6,
    "Nervous" : MessageLookupByLibrary.simpleMessage("紧张"),
    "Nfenzhong" : m7,
    "Nfenzhonghoujieshu" : m8,
    "Nfenzhongqian" : m9,
    "Nmiaohoujieshu" : m10,
    "NoPointDetailsYet" : MessageLookupByLibrary.simpleMessage("暂无积分明细哦"),
    "NoRatingYet" : MessageLookupByLibrary.simpleMessage("暂无评分"),
    "NotJoinPunch" : MessageLookupByLibrary.simpleMessage("暂未参加任何打卡活动"),
    "NotPossess" : MessageLookupByLibrary.simpleMessage("未拥有"),
    "NrencanjiaNrencenjia" : m11,
    "Nrenpingfen" : m12,
    "Nrenpinglun" : m13,
    "Nrenyibaoming" : m14,
    "NrenyicanjiaNrenyice" : m15,
    "NsheNzhe" : m16,
    "Ntianhoujieshu" : m17,
    "Nxiaoshi" : m18,
    "Nxiaoshihoujieshu" : m19,
    "Nxiaoshiqian" : m20,
    "Nxing" : m21,
    "Nxing_2" : m22,
    "Nzepinglun" : m23,
    "Nzhang" : m24,
    "NzhangSyouhuquanyifa" : m25,
    "Nzhangkeyong" : m26,
    "OrderDetails" : MessageLookupByLibrary.simpleMessage("订单详情页"),
    "PageNotFound" : MessageLookupByLibrary.simpleMessage("请求页面不存在"),
    "PasswordsError" : m27,
    "PayPal" : MessageLookupByLibrary.simpleMessage("PayPal"),
    "PlatformServiceAgreement" : MessageLookupByLibrary.simpleMessage("《平台服务协议》"),
    "PlayForDream" : MessageLookupByLibrary.simpleMessage("PLAY FOR DREAM"),
    "PleaseEnterAtLeastFive" : MessageLookupByLibrary.simpleMessage("应用评价至少为5个字"),
    "PleaseFillInCorrectly" : MessageLookupByLibrary.simpleMessage("请正确填写，信息缺漏、错误，则视为申领无效"),
    "PleaseFillInTheIDNumber" : MessageLookupByLibrary.simpleMessage("请填写银行卡号实名认证人的身份证号码（根据相关财税规定，请务必正确填写身份证号码，且年满18周岁，信息仅用于校验，我们将对您的信息保密）"),
    "PleaseFillInYourPurchase" : MessageLookupByLibrary.simpleMessage("请填写您的购买渠道"),
    "PleaseInstallAppAndExperience" : MessageLookupByLibrary.simpleMessage("请先安装应用并体验5分钟再来评价吧！"),
    "PleaseTickTheAboveAgreement" : MessageLookupByLibrary.simpleMessage("请先勾选以上协议"),
    "PleaseUploadAPaymentScreenshot" : MessageLookupByLibrary.simpleMessage("请上传付款截图（支付宝/微信截图）"),
    "PointsMall" : MessageLookupByLibrary.simpleMessage("积分商城"),
    "PointsMallIsUnderConstruction" : MessageLookupByLibrary.simpleMessage("积分商城预计7月上线，敬请期待"),
    "PointsRules" : MessageLookupByLibrary.simpleMessage("积分规则"),
    "Popular" : MessageLookupByLibrary.simpleMessage("Popular"),
    "PrivacySetting" : MessageLookupByLibrary.simpleMessage("隐私设置"),
    "ProductDetails" : MessageLookupByLibrary.simpleMessage("商品详情"),
    "PublicScope" : MessageLookupByLibrary.simpleMessage("公开范围"),
    "Recommend" : MessageLookupByLibrary.simpleMessage("推荐"),
    "RelatedActivity" : MessageLookupByLibrary.simpleMessage("相关活动"),
    "RelatedContentHasBeenRemoved" : MessageLookupByLibrary.simpleMessage("相关内容已下架"),
    "RemoteShooting" : MessageLookupByLibrary.simpleMessage("遥控拍摄"),
    "SafePay" : MessageLookupByLibrary.simpleMessage("支付安全"),
    "SelectGames" : MessageLookupByLibrary.simpleMessage("选择游戏"),
    "Sgoumaichenggongkuai" : m28,
    "Share" : MessageLookupByLibrary.simpleMessage("分享"),
    "ShippingAddress" : MessageLookupByLibrary.simpleMessage("收货地址"),
    "Shuodongbucunzai" : m29,
    "SjinriweidakaSjinriw" : m30,
    "SomePlayersAreNotSuitable" : MessageLookupByLibrary.simpleMessage("Some players may experience discomfort such as dizziness, so players who are new to VR should exercise caution when trying it out."),
    "SportsPoster" : MessageLookupByLibrary.simpleMessage("运动海报"),
    "StarAll" : MessageLookupByLibrary.simpleMessage("All"),
    "Star_1" : MessageLookupByLibrary.simpleMessage("1 star"),
    "Star_2" : MessageLookupByLibrary.simpleMessage("3 stars"),
    "Star_3" : MessageLookupByLibrary.simpleMessage("2 stars"),
    "Star_4" : MessageLookupByLibrary.simpleMessage("5 stars"),
    "Star_5" : MessageLookupByLibrary.simpleMessage("4 stars"),
    "Stores" : MessageLookupByLibrary.simpleMessage("线下门店"),
    "SuitableForAllPlayers" : MessageLookupByLibrary.simpleMessage("Appropriate for most players."),
    "SupportedPlatforms" : MessageLookupByLibrary.simpleMessage("支持平台："),
    "Syilingjiang" : m31,
    "SyiwanchengdakaSyiwa" : m32,
    "SyuSshigongtonghaoyo" : m33,
    "SzaiSzhongweian" : m34,
    "Szhongdehuifuyinshex" : m35,
    "Szhongdehuifuyinshex_1" : m36,
    "TheAccountHasBeenLocked" : MessageLookupByLibrary.simpleMessage("The account has been locked. Please login again after 5 minutes."),
    "TheComfortOfVRPlay" : MessageLookupByLibrary.simpleMessage("Comfort in VR depends on factors such as game optimization, gameplay mode, and player adaptability. Please refer to the comfort rating to choose a suitable game."),
    "TheEvaluationIsUnderReview" : MessageLookupByLibrary.simpleMessage("评价将在审核通过后展示"),
    "TheEvaluationYouSubmit" : MessageLookupByLibrary.simpleMessage("您提交的评价会经过平台审核后展示"),
    "TipszaiVRyanjingzhon" : MessageLookupByLibrary.simpleMessage("Tips: You can add it by logging in to the same account in the VR headset. You can return to the list directly without repeating the operation. You can find the SN number of device by choosing [Settings] > [General] > [About] in the VR headset. If you cannot add the VR headset, check whether relevant permissions are enabled in the Play For Dream."),
    "Today" : MessageLookupByLibrary.simpleMessage("Today"),
    "Tomorrow" : MessageLookupByLibrary.simpleMessage("Tomorrow"),
    "VRController" : MessageLookupByLibrary.simpleMessage("VR手柄"),
    "VRduanyizhongzhitoub" : MessageLookupByLibrary.simpleMessage("Projection terminated in the VR side."),
    "VRnarongzhiyushangfa" : MessageLookupByLibrary.simpleMessage("VR content at the top. \nMobile phone recording at the bottom."),
    "VRnarongzhiyuzuocens" : MessageLookupByLibrary.simpleMessage("VR content on the left. Mobile phone recording on the right."),
    "VRshebeigoumaiqingqi" : MessageLookupByLibrary.simpleMessage("VR device purchase request"),
    "VRshebeijiangzailian" : MessageLookupByLibrary.simpleMessage("The VR headset will automatically download this app when it is connected to the network."),
    "VRshipinchuanshuzhon" : MessageLookupByLibrary.simpleMessage("Transmitting the VR video..."),
    "VRweizhu" : MessageLookupByLibrary.simpleMessage("More in VR"),
    "VRyanjingSNhaoS" : m37,
    "VRyanjingchuyuqingsh" : MessageLookupByLibrary.simpleMessage("The VR headset is in teen mode and cannot be added again."),
    "VRyanjingduankailian" : MessageLookupByLibrary.simpleMessage("The VR headset is disconnected. Please connect again."),
    "VRyanjingjiangtongbu" : MessageLookupByLibrary.simpleMessage("This file will also be deleted and cannot be restored. Are you sure you want to continue?"),
    "VRyanjinglianjiechen" : MessageLookupByLibrary.simpleMessage("VR headset connected."),
    "VRyanjingmofalianjie" : MessageLookupByLibrary.simpleMessage("The VR headset failed to connect to the Wi-Fi. Please try again."),
    "VRyanjingnacunbuzuqi" : MessageLookupByLibrary.simpleMessage("Insufficient storage of the VR headset. Please free up some space before recording."),
    "VRyanjingnacunbuzuqi_1" : MessageLookupByLibrary.simpleMessage("Insufficient storage of the VR headset. Please free up some space before using the dual recording function."),
    "VRyanjingtianjiachen" : MessageLookupByLibrary.simpleMessage("The VR headset is added."),
    "VRyanjingtoubingshib" : MessageLookupByLibrary.simpleMessage("Failed to initiate projection by VR headset."),
    "VRyanjingyizhongzhip" : MessageLookupByLibrary.simpleMessage("The VR headset has terminated dual recording."),
    "VRyanjingyizhongzhit" : MessageLookupByLibrary.simpleMessage("The VR headset has terminated projection."),
    "VRyanjingyushoujiwan" : MessageLookupByLibrary.simpleMessage("The network of the VR headset and the mobile phone are inconsistent"),
    "VRyingyongyouhuS1VRy" : m38,
    "WiFibuyizhibufengong" : MessageLookupByLibrary.simpleMessage("The Wi-Fi is inconsistent"),
    "WriteReviewAndMoreFeedback" : MessageLookupByLibrary.simpleMessage("写评价留下更多反馈吧！"),
    "YVRGoSport" : MessageLookupByLibrary.simpleMessage("YVR GO运动"),
    "YVRRefundPolicy" : MessageLookupByLibrary.simpleMessage("Play For Dream Refund Policy"),
    "YVRshebeihaoSNhao" : MessageLookupByLibrary.simpleMessage("SN number of YVR device"),
    "YVRzaicishanyidixing" : MessageLookupByLibrary.simpleMessage("Kindly reminder: Account cancellation is an unrecoverable operation. After the account is canceled, you will no longer be able to use the account or retrieve any content or information you have purchased, browsed or collected (even if you use the same mobile number to register again and use the YVR platform)."),
    "YVRzaicishanyidixing_1" : m39,
    "YVRzhanghao" : MessageLookupByLibrary.simpleMessage("YVR account"),
    "Ybi" : MessageLookupByLibrary.simpleMessage("Y Coins"),
    "YbiNge" : m40,
    "Ybichongzhi" : MessageLookupByLibrary.simpleMessage("Y Coins Recharge"),
    "Ybichongzhixieyi" : MessageLookupByLibrary.simpleMessage("YVR Top-up Service Agreement"),
    "YbifafangtongzhiYbif" : MessageLookupByLibrary.simpleMessage("Notification of Y Coins Distribution"),
    "Ybihuoqu" : MessageLookupByLibrary.simpleMessage("Obtain Y Coins"),
    "Ybishouzhimingxi" : MessageLookupByLibrary.simpleMessage("Y Coins Transaction Details"),
    "Ybixiaofei" : MessageLookupByLibrary.simpleMessage("Consume Y Coins"),
    "Ybiyifafangkezaishan" : MessageLookupByLibrary.simpleMessage("Y Coins have been distributed and you can use them to exchange for items in the store. Thank you for your support and love for YVR!"),
    "Ybiyuebuzu" : MessageLookupByLibrary.simpleMessage("Insufficient Y Coins balance."),
    "Ybiyuebuzuqingchongz" : MessageLookupByLibrary.simpleMessage("Insufficient Y Coins balance"),
    "Ybizhifu" : MessageLookupByLibrary.simpleMessage("By Y Coins"),
    "account_cancellation_agreement_url_dev" : MessageLookupByLibrary.simpleMessage("https://apitest.yvrdream.com/yvrdvcenter/#/cancellationagreementen"),
    "account_cancellation_agreement_url_release" : MessageLookupByLibrary.simpleMessage("https://developer.yvrdream.com/#/cancellationagreementen"),
    "account_yet" : MessageLookupByLibrary.simpleMessage("Already registered? "),
    "alipayPwdFree" : MessageLookupByLibrary.simpleMessage("支付宝免密支付"),
    "anzhuangyingyong" : MessageLookupByLibrary.simpleMessage("Install app"),
    "banben" : MessageLookupByLibrary.simpleMessage("Version"),
    "banbengengxin" : MessageLookupByLibrary.simpleMessage("Version Update"),
    "baocun" : MessageLookupByLibrary.simpleMessage("Save"),
    "baocunchenggong" : MessageLookupByLibrary.simpleMessage("Saved."),
    "baocunzhong" : MessageLookupByLibrary.simpleMessage("Saving..."),
    "baocunzhong_1" : MessageLookupByLibrary.simpleMessage("Saving"),
    "baohan" : MessageLookupByLibrary.simpleMessage("Include:"),
    "baohanyingyong" : MessageLookupByLibrary.simpleMessage("Included Apps"),
    "baomi" : MessageLookupByLibrary.simpleMessage("Secret"),
    "baoqianmeiyouzhaodao" : MessageLookupByLibrary.simpleMessage("Sorry, no VR headset found nearby. Please ensure the Bluetooth of the VR headset is enabled and try again."),
    "bendexiangcebendixia" : MessageLookupByLibrary.simpleMessage("local album"),
    "bianjihuodong" : MessageLookupByLibrary.simpleMessage("Edit Activity"),
    "bianjisirenhuodong" : MessageLookupByLibrary.simpleMessage("Edit Personal Activity"),
    "bianjiziliao" : MessageLookupByLibrary.simpleMessage("Edit Info"),
    "bindAlipay" : MessageLookupByLibrary.simpleMessage("确定要开通支付宝免密支付吗？"),
    "bindFail" : MessageLookupByLibrary.simpleMessage("绑定失败"),
    "bindSuccess" : MessageLookupByLibrary.simpleMessage("绑定成功"),
    "birthday" : MessageLookupByLibrary.simpleMessage("Birthday"),
    "bukeyongyouhuquanNzh" : m41,
    "bushidangnarongzaoch" : MessageLookupByLibrary.simpleMessage("Harassment"),
    "buzhichiduobushoujit" : MessageLookupByLibrary.simpleMessage("You cannot project multiple mobile phones at the same time."),
    "buzhichigaiiPhonexin" : MessageLookupByLibrary.simpleMessage("This iPhone model is not supported."),
    "caijian" : MessageLookupByLibrary.simpleMessage("Crop"),
    "cancel" : MessageLookupByLibrary.simpleMessage("Cancel"),
    "canjiabingwanchengda" : MessageLookupByLibrary.simpleMessage("Complete the Daily Check-In Reward activity and you can get 50% of the purchase amount as cashback. For more information, see the activity details."),
    "canshubuduiwenjianwe" : MessageLookupByLibrary.simpleMessage("Incorrect parameter or empty file."),
    "canshucuowucenshucuo" : MessageLookupByLibrary.simpleMessage("Parameter error."),
    "canyushijiancenyushi" : MessageLookupByLibrary.simpleMessage("Accumulated time:"),
    "cardsForCN" : MessageLookupByLibrary.simpleMessage(" "),
    "chakanquanbuzhakanqu" : MessageLookupByLibrary.simpleMessage("View All"),
    "chakanxiangqingzhaka" : MessageLookupByLibrary.simpleMessage("View Details"),
    "chongxinluzhizhongxi" : MessageLookupByLibrary.simpleMessage("Re-record"),
    "chongyaozhongyao" : MessageLookupByLibrary.simpleMessage("Important"),
    "chongzhimimazhongzhi" : MessageLookupByLibrary.simpleMessage("Reset password."),
    "chongzhishibai" : MessageLookupByLibrary.simpleMessage("Recharge failed."),
    "chuangjian" : MessageLookupByLibrary.simpleMessage("Create"),
    "chuangjianhuodong" : MessageLookupByLibrary.simpleMessage("Create Activity"),
    "chuangjiansirenhuodo" : MessageLookupByLibrary.simpleMessage("Create Personal Activity"),
    "chuliyuanyuzhouzhong" : MessageLookupByLibrary.simpleMessage("Help teens grow healthily in metaverse."),
    "cimimaweiguanbiqings" : MessageLookupByLibrary.simpleMessage("This is a common password for disabling teen mode and setting time lock."),
    "cizhanghaokenengbeid" : MessageLookupByLibrary.simpleMessage("Might be stolen"),
    "confirm" : MessageLookupByLibrary.simpleMessage("OK"),
    "confirmTheContract" : MessageLookupByLibrary.simpleMessage("确认签约"),
    "confirm_leave" : MessageLookupByLibrary.simpleMessage("Are you sure you want to exit?"),
    "congxiangcezhongxuan" : MessageLookupByLibrary.simpleMessage("Select from album"),
    "consume_time" : MessageLookupByLibrary.simpleMessage("Purchase Time"),
    "coupon" : MessageLookupByLibrary.simpleMessage("优惠券"),
    "cunchu" : MessageLookupByLibrary.simpleMessage("Store"),
    "cundaoxiangce" : MessageLookupByLibrary.simpleMessage("Save"),
    "cunzaijitaqinquanhan" : MessageLookupByLibrary.simpleMessage("Infringement"),
    "cuxiaohuodongfasheng" : MessageLookupByLibrary.simpleMessage("The sales promotion has changed. Please refresh and try again."),
    "daifabudaifeibu" : MessageLookupByLibrary.simpleMessage("To be released"),
    "dakafanxiandaqiafanx" : MessageLookupByLibrary.simpleMessage("Daily Check-In Reward"),
    "dakahuo50goujikuanxi" : MessageLookupByLibrary.simpleMessage("Join in Daily Check-In Reward to get 50% of the purchase amount as cashback"),
    "dakai" : MessageLookupByLibrary.simpleMessage("Enable"),
    "dakaiSchaoshiqingcho" : m42,
    "dakailianjieshibai" : MessageLookupByLibrary.simpleMessage("Failed to open link"),
    "dakaiyingyong" : MessageLookupByLibrary.simpleMessage("Open the app"),
    "dakashujuyouyidingya" : MessageLookupByLibrary.simpleMessage("The check-in data may have delay. Ensure that the VR headset is connected to the network."),
    "dakatianshumeiyoudad" : MessageLookupByLibrary.simpleMessage("The number of check-in days does not reach the threshold for daily check-in reward."),
    "dangVRyanjingzaifuji" : MessageLookupByLibrary.simpleMessage("When the VR headset is nearby, enable the Bluetooth of the mobile phone and the VR headset, and ensure that the Bluetooth and location permissions of the Play For Dream are enabled. When the connection fails, tap the Refresh button on the upper part of the screen to reconnect."),
    "dangqianVRyanjingdia" : MessageLookupByLibrary.simpleMessage("The VR headset is projecting the screen to a TV. Please stop projecting and try again."),
    "dangqianVRyanjinglub" : MessageLookupByLibrary.simpleMessage("The VR headset is recording the screen. Please stop recording and try again."),
    "dangqianVRyanjingtou" : MessageLookupByLibrary.simpleMessage("The VR headset is projecting the screen. Please stop projecting and try again."),
    "dangqianVRyanjingyid" : MessageLookupByLibrary.simpleMessage("You have logged in with the same account as the mobile phone. Now you can experience the interaction between the mobile phone and the VR headset."),
    "dangqianquanbuzhichi" : MessageLookupByLibrary.simpleMessage("The current coupon is not supported."),
    "dangqianshebeichuyuq" : MessageLookupByLibrary.simpleMessage("The VR headset is in teen mode. You need to disable the mode in the Device tab of the app before you can try the device."),
    "dangqianshebeiyilian" : MessageLookupByLibrary.simpleMessage("The current device has been connected."),
    "dangqianwanglaobukey" : MessageLookupByLibrary.simpleMessage("The current network is unavailable. Please check your network settings."),
    "dangqianweibangdingV" : MessageLookupByLibrary.simpleMessage("No VR headset is bound."),
    "dangqianyingyongyixi" : MessageLookupByLibrary.simpleMessage("The app has been removed."),
    "dangqianzaixianrensh" : m43,
    "dangqianzhanghaoyong" : MessageLookupByLibrary.simpleMessage("The virtual assets under this account include but not limited to:"),
    "dangqianzhengzaibian" : MessageLookupByLibrary.simpleMessage("An activity is being edited. If you exit, the edited content will be lost."),
    "dangqianzhengzaichua" : MessageLookupByLibrary.simpleMessage("An activity is being created. If you exit, all content will be lost."),
    "datingdaiting" : MessageLookupByLibrary.simpleMessage("Hall"),
    "denglu" : MessageLookupByLibrary.simpleMessage("Log In"),
    "denglumimaxiugaichen" : MessageLookupByLibrary.simpleMessage("The login password has been changed."),
    "denglumimaxiugaishib" : MessageLookupByLibrary.simpleMessage("Failed to change the login password."),
    "denglushibai" : MessageLookupByLibrary.simpleMessage("Login failed."),
    "dev_add_dev" : MessageLookupByLibrary.simpleMessage("Add device"),
    "dev_add_remind" : MessageLookupByLibrary.simpleMessage("1. Tap \"Search for Nearby VR Headset\" down below.\n\n2. Tap a VR headset name. \n\n3. After the selected VR headset is successfully added, it will automatically log in to the current account, and you can use projection, screenshot, screen recording and other features."),
    "dev_ble_lead" : MessageLookupByLibrary.simpleMessage("Pairing guide"),
    "dev_can_connect" : MessageLookupByLibrary.simpleMessage("Devices available for pairing"),
    "dev_connected" : MessageLookupByLibrary.simpleMessage("Connected"),
    "dev_connecting" : MessageLookupByLibrary.simpleMessage("Connecting..."),
    "dev_modify" : MessageLookupByLibrary.simpleMessage("Change device name"),
    "dev_my_vr" : MessageLookupByLibrary.simpleMessage("My VR devices"),
    "dev_nearby" : MessageLookupByLibrary.simpleMessage("Nearby"),
    "dev_need_vr" : MessageLookupByLibrary.simpleMessage("Connect to VR glasses before you can use the feature."),
    "dev_not_connected" : MessageLookupByLibrary.simpleMessage("Not connected"),
    "dev_open_bluetooth" : MessageLookupByLibrary.simpleMessage("Bluetooth not enabled. Failed to find VR glasses."),
    "dev_proj" : MessageLookupByLibrary.simpleMessage("Projection"),
    "dev_proj_desc" : MessageLookupByLibrary.simpleMessage("1. If all projection attempts failed, check whether the VR headset is connected to Wi-Fi and try again."),
    "dev_proj_from" : MessageLookupByLibrary.simpleMessage("Projected From"),
    "dev_proj_to" : MessageLookupByLibrary.simpleMessage("Projected To"),
    "dev_research" : MessageLookupByLibrary.simpleMessage("Search again"),
    "dev_search_ble" : MessageLookupByLibrary.simpleMessage("Search for devices nearby"),
    "dev_searching" : MessageLookupByLibrary.simpleMessage("Searching for devices nearby..."),
    "dev_sel_dev" : MessageLookupByLibrary.simpleMessage("Select device"),
    "dev_setting" : MessageLookupByLibrary.simpleMessage("Set"),
    "dev_unavailable_func" : MessageLookupByLibrary.simpleMessage("The feature is unavailable."),
    "diNhangdiNhengdiNxin" : m44,
    "diNhangdiNhengdiNxin_1" : m45,
    "dianjichakanhuodongx" : MessageLookupByLibrary.simpleMessage("Click to view activity details."),
    "dianjidengluzhuce" : MessageLookupByLibrary.simpleMessage("Click Log On/Sign Up"),
    "dijiaotijiao" : MessageLookupByLibrary.simpleMessage("同意并提交"),
    "dingchangoumaishijia" : MessageLookupByLibrary.simpleMessage("Order placement time"),
    "dingchanhaodingdanha" : MessageLookupByLibrary.simpleMessage("Order No.:"),
    "dingchanhaodingdanha_1" : MessageLookupByLibrary.simpleMessage("Order No."),
    "direct_login" : MessageLookupByLibrary.simpleMessage("Log on directly"),
    "dishitishi" : MessageLookupByLibrary.simpleMessage("Prompt"),
    "disuruma" : MessageLookupByLibrary.simpleMessage("Obscene and profane content"),
    "do_it_later" : MessageLookupByLibrary.simpleMessage("Later"),
    "douyin" : MessageLookupByLibrary.simpleMessage("Douyin"),
    "douyinhao" : MessageLookupByLibrary.simpleMessage("Douyin ID"),
    "downloadNow" : MessageLookupByLibrary.simpleMessage("Download now?"),
    "downloadText" : MessageLookupByLibrary.simpleMessage("If you tap Download Now, the VR headset will automatically download this app when it is connected to the network."),
    "duihuan" : MessageLookupByLibrary.simpleMessage("Redeem"),
    "duihuanchenggong" : MessageLookupByLibrary.simpleMessage("Redeemed."),
    "email_address" : MessageLookupByLibrary.simpleMessage("Email address"),
    "erweiliaobangchuninw" : MessageLookupByLibrary.simpleMessage("II. To help you complete account cancelation, you should undertake to YVR that:"),
    "fabiaofeibiao" : MessageLookupByLibrary.simpleMessage("Send"),
    "fabiaoyixianidekanfa" : MessageLookupByLibrary.simpleMessage("Share your comments."),
    "fangchenmikongzhishi" : MessageLookupByLibrary.simpleMessage("Anti-addiction by limiting usage period. "),
    "fangqibaocundangqian" : MessageLookupByLibrary.simpleMessage("Do not save the spliced video."),
    "fangqidangqianpinglu" : MessageLookupByLibrary.simpleMessage("Discard this comment."),
    "fanhuishangyiye" : MessageLookupByLibrary.simpleMessage("Previous"),
    "faqituikuanshibaifei" : MessageLookupByLibrary.simpleMessage("Failed to initiate a refund."),
    "faxiancuowuqingzaish" : MessageLookupByLibrary.simpleMessage("An error occurred. Please add the error code in the upper part."),
    "faxiantongxingcuhaoy" : MessageLookupByLibrary.simpleMessage("Find friends with similar interests."),
    "feishiyongshiduanVRy" : MessageLookupByLibrary.simpleMessage("During the unavailable period, the VR headset will be locked. You can modify the available period of the current day on the teen guardian tool page."),
    "female" : MessageLookupByLibrary.simpleMessage("Female"),
    "fenzhong" : MessageLookupByLibrary.simpleMessage("30 min"),
    "fenzhong_1" : MessageLookupByLibrary.simpleMessage("90 min"),
    "forget_pwd" : MessageLookupByLibrary.simpleMessage("Forgot password?"),
    "fuwuqiqingqiushibai" : MessageLookupByLibrary.simpleMessage("Server request failed."),
    "fuwuxieyiheyinsizhen" : MessageLookupByLibrary.simpleMessage("Service Agreement and Privacy Policy"),
    "gaishebeibucunzai" : MessageLookupByLibrary.simpleMessage("This device does not exist."),
    "gaishebeiyijingshenq" : MessageLookupByLibrary.simpleMessage("You have already applied for daily check-in reward. Do not repeat the operation."),
    "gaixuanzejinyingxian" : MessageLookupByLibrary.simpleMessage("This affects only the display mode of the final splicing video."),
    "gaiyingyongmeiyougou" : MessageLookupByLibrary.simpleMessage("This app has not been purchased yet."),
    "gaiyingyongtuikuanzh" : MessageLookupByLibrary.simpleMessage("This app is being refunded and cannot be opened."),
    "gaizhanghaodehaoyouj" : MessageLookupByLibrary.simpleMessage("4. Your friends under this account will not be able to find you through this account."),
    "gaizhanghaodequanbug" : MessageLookupByLibrary.simpleMessage("3. All personal data and historical information of the account (including but not limited to avatar, user name, published content, browsing records, following records, and favorites) will be unrecoverable."),
    "gaizhanghaojitongguo" : MessageLookupByLibrary.simpleMessage("1. This account is registered through the official YVR channel for your own use."),
    "gaizhanghaojitongton" : MessageLookupByLibrary.simpleMessage("1. This account is registered through the official YVR channel for your own use."),
    "gaizhanghaoleijidede" : MessageLookupByLibrary.simpleMessage("5. The levels, points and benefits you have earned under the account will be cleared."),
    "gaizhanghaonamofachu" : MessageLookupByLibrary.simpleMessage("2. There are no unfinished transactions in the account."),
    "gaizhanghaonamoweich" : MessageLookupByLibrary.simpleMessage("2. There are no unfinished transactions in the account."),
    "gaizhanghaoyishenqin" : m46,
    "ganggang" : MessageLookupByLibrary.simpleMessage("Just now"),
    "ganxienindezhichinwo" : MessageLookupByLibrary.simpleMessage("Thanks for your support. We will handle your report as soon as possible."),
    "gechengshibaiqingcho" : MessageLookupByLibrary.simpleMessage("Synthesis failed. Please record again."),
    "gender" : MessageLookupByLibrary.simpleMessage("Gender"),
    "gengduo" : MessageLookupByLibrary.simpleMessage("Expand"),
    "genghuanbeijing" : MessageLookupByLibrary.simpleMessage("Change Background"),
    "genghuanshoujihao" : MessageLookupByLibrary.simpleMessage("Change Mobile Number"),
    "get_verify_code" : MessageLookupByLibrary.simpleMessage("Request verification code"),
    "get_vfcode_failed" : MessageLookupByLibrary.simpleMessage("Failed to request the verification code."),
    "gexinghuanarongtuiji" : MessageLookupByLibrary.simpleMessage("Personalized recommendation"),
    "gexingqianming" : MessageLookupByLibrary.simpleMessage("Bio"),
    "gongtonghaoyou" : MessageLookupByLibrary.simpleMessage("Mutual friend"),
    "gongxininjinridakach" : MessageLookupByLibrary.simpleMessage("You have checked in today."),
    "gongxininnindedakahu" : MessageLookupByLibrary.simpleMessage("Congratulations. Your daily check-in reward has been granted based on the information you entered in the Play For Dream. For any doubt, contact the customer service or dial ************."),
    "goumai" : MessageLookupByLibrary.simpleMessage("Purchase"),
    "goumaiS" : m47,
    "goumaiyingyong" : MessageLookupByLibrary.simpleMessage("Purchase App"),
    "goumaiyingyongqianqi" : MessageLookupByLibrary.simpleMessage("Before purchasing an app, go to Device to add a YVR device."),
    "guanbi" : MessageLookupByLibrary.simpleMessage("Disable"),
    "guanbihoujiangmofash" : MessageLookupByLibrary.simpleMessage("If disabled, you will not receive personalized recommendations. We recommend you enable it so that you can access more interesting content."),
    "guanfang" : MessageLookupByLibrary.simpleMessage("Official"),
    "guanfanghuodong" : MessageLookupByLibrary.simpleMessage("Official Activity"),
    "guanfanghuodongbucun" : MessageLookupByLibrary.simpleMessage("The official activity does not exist."),
    "guanlishiyongshijian" : MessageLookupByLibrary.simpleMessage("Manage the available period to prevent gaming addiction."),
    "guizexiangqing" : MessageLookupByLibrary.simpleMessage("Rule Details"),
    "haimeiyouhaoyoukuaiq" : MessageLookupByLibrary.simpleMessage("No friend yet. Add some now."),
    "haimeiyourenhedongta" : MessageLookupByLibrary.simpleMessage("No dynamics yet."),
    "haoyou" : MessageLookupByLibrary.simpleMessage("Friend"),
    "haoyouyijingtianjia" : MessageLookupByLibrary.simpleMessage("Friend added."),
    "hechenghengpingshipi" : MessageLookupByLibrary.simpleMessage("Form 16:9 video"),
    "hechengshibai" : MessageLookupByLibrary.simpleMessage("Synthesis failed. Please record again."),
    "hechengshupingshipin" : MessageLookupByLibrary.simpleMessage("Form 9:16 video"),
    "hehuhuo" : MessageLookupByLibrary.simpleMessage("and"),
    "heimingchanheimingda" : MessageLookupByLibrary.simpleMessage("Blacklist"),
    "henbaoqianmeiyouzhao" : MessageLookupByLibrary.simpleMessage("Sorry. The app you are searching for does not exist."),
    "hengbinghengping" : MessageLookupByLibrary.simpleMessage("Landscape"),
    "hengbingpinjiehengpi" : MessageLookupByLibrary.simpleMessage("Landscape Splicing"),
    "henxinfangqi" : MessageLookupByLibrary.simpleMessage("Yes"),
    "home_action_adventure" : MessageLookupByLibrary.simpleMessage("Action"),
    "home_all_app" : MessageLookupByLibrary.simpleMessage("All apps"),
    "home_all_categories" : MessageLookupByLibrary.simpleMessage("All types"),
    "home_all_prices" : MessageLookupByLibrary.simpleMessage("All prices"),
    "home_all_types" : MessageLookupByLibrary.simpleMessage("All types"),
    "home_app" : MessageLookupByLibrary.simpleMessage("App"),
    "home_app_info" : MessageLookupByLibrary.simpleMessage("App info"),
    "home_below_100" : MessageLookupByLibrary.simpleMessage("50-100"),
    "home_below_50" : MessageLookupByLibrary.simpleMessage("Under 50"),
    "home_cartoon_animation" : MessageLookupByLibrary.simpleMessage("Cartoon"),
    "home_casual_creativity" : MessageLookupByLibrary.simpleMessage("Leisure"),
    "home_comment" : MessageLookupByLibrary.simpleMessage("Comment"),
    "home_cosplay" : MessageLookupByLibrary.simpleMessage("Role-play"),
    "home_earliest_released" : MessageLookupByLibrary.simpleMessage("Earliest release"),
    "home_free" : MessageLookupByLibrary.simpleMessage("Free"),
    "home_game" : MessageLookupByLibrary.simpleMessage("Game"),
    "home_go_appcenter" : MessageLookupByLibrary.simpleMessage("Did not find a suitable application, go to the application center to have a look"),
    "home_go_now" : MessageLookupByLibrary.simpleMessage("Go now"),
    "home_highest_price" : MessageLookupByLibrary.simpleMessage("Most expensive"),
    "home_hot_download" : MessageLookupByLibrary.simpleMessage("Popular downloads"),
    "home_lowest_price" : MessageLookupByLibrary.simpleMessage("Cheapest"),
    "home_mark" : MessageLookupByLibrary.simpleMessage("Rating"),
    "home_media_entertainment" : MessageLookupByLibrary.simpleMessage("Multimedia"),
    "home_more_100" : MessageLookupByLibrary.simpleMessage("Above 100"),
    "home_multiplayer_game" : MessageLookupByLibrary.simpleMessage("Multiplayer"),
    "home_my_app" : MessageLookupByLibrary.simpleMessage("My Applications"),
    "home_noti_center" : MessageLookupByLibrary.simpleMessage("Notification Center"),
    "home_office_tools" : MessageLookupByLibrary.simpleMessage("Office tool"),
    "home_person_com" : m48,
    "home_recently_released" : MessageLookupByLibrary.simpleMessage("Latest release"),
    "home_recently_update" : MessageLookupByLibrary.simpleMessage("Latest update"),
    "home_recommended_sorting" : MessageLookupByLibrary.simpleMessage("Recommended"),
    "home_search_history" : MessageLookupByLibrary.simpleMessage("Search records"),
    "home_search_plhd" : MessageLookupByLibrary.simpleMessage("Enter keyword search"),
    "home_social_network" : MessageLookupByLibrary.simpleMessage("Social network"),
    "home_star" : MessageLookupByLibrary.simpleMessage("Star"),
    "home_stim_shot" : MessageLookupByLibrary.simpleMessage("Shooting"),
    "home_thrilling" : MessageLookupByLibrary.simpleMessage("Horror"),
    "home_view_all" : MessageLookupByLibrary.simpleMessage("View all"),
    "home_view_all_comments" : MessageLookupByLibrary.simpleMessage("View all comments"),
    "houduanjiekouxujianr" : m49,
    "hulve" : MessageLookupByLibrary.simpleMessage("Ignore"),
    "hunheluzhi" : MessageLookupByLibrary.simpleMessage("MRC"),
    "huodong" : MessageLookupByLibrary.simpleMessage("Activity"),
    "huodongID" : MessageLookupByLibrary.simpleMessage("Activity ID:"),
    "huodonganchanghuodon" : MessageLookupByLibrary.simpleMessage("Activity Mall"),
    "huodongbucunzai" : MessageLookupByLibrary.simpleMessage("This activity does not exist."),
    "huodongcanyushibaihu" : MessageLookupByLibrary.simpleMessage("Failed to join in the activity."),
    "huodongchuangjianche" : MessageLookupByLibrary.simpleMessage("Activity created."),
    "huodongchuangjianshi" : MessageLookupByLibrary.simpleMessage("Failed to create the activity."),
    "huodongguize" : MessageLookupByLibrary.simpleMessage("Activity Rules"),
    "huodongjieshao" : MessageLookupByLibrary.simpleMessage("活动介绍"),
    "huodongshijian" : MessageLookupByLibrary.simpleMessage("Activity time:"),
    "huodongshuimingxuant" : MessageLookupByLibrary.simpleMessage("(Optional) Activity Description"),
    "huodongxiangqing" : MessageLookupByLibrary.simpleMessage("Activity Details"),
    "huodongxiugaichenggo" : MessageLookupByLibrary.simpleMessage("Activity modified."),
    "huodongxiugaishibai" : MessageLookupByLibrary.simpleMessage("Failed to modify the activity."),
    "huodongyiguoqi" : MessageLookupByLibrary.simpleMessage("This activity has expired."),
    "huodongyijieshu" : MessageLookupByLibrary.simpleMessage("This activity has ended."),
    "huodongyijieshuqings" : MessageLookupByLibrary.simpleMessage("The activity has ended. Please refresh and try again."),
    "huodongyijingguoqi" : MessageLookupByLibrary.simpleMessage("This activity has expired."),
    "huodongyijingkaishim" : MessageLookupByLibrary.simpleMessage("This activity has started and you cannot exit."),
    "huodongyikaishimofat" : MessageLookupByLibrary.simpleMessage("This activity has started and you cannot exit."),
    "huodongyikaishishanc" : MessageLookupByLibrary.simpleMessage("This activity has started and cannot be deleted."),
    "huodongyixiaxian" : MessageLookupByLibrary.simpleMessage("This activity has ended."),
    "huoquVRduanshipinshi" : MessageLookupByLibrary.simpleMessage("Failed to obtain the videos from the VR side."),
    "huoquYbishujushibai" : MessageLookupByLibrary.simpleMessage("Failed to obtain Y Coins data."),
    "huoquchongzhixinxish" : MessageLookupByLibrary.simpleMessage("Failed to obtain recharge information."),
    "huoqushebeiliebiaosh" : MessageLookupByLibrary.simpleMessage("Failed to obtain the device list."),
    "huoqushijian" : MessageLookupByLibrary.simpleMessage("Obtained At:"),
    "identity_card_back" : MessageLookupByLibrary.simpleMessage("Back"),
    "identity_card_front" : MessageLookupByLibrary.simpleMessage("Front"),
    "input_email" : MessageLookupByLibrary.simpleMessage("Please enter email address"),
    "input_phone_num" : MessageLookupByLibrary.simpleMessage("Enter the mobile number"),
    "input_pwd" : MessageLookupByLibrary.simpleMessage("Enter the password"),
    "input_verify_code" : MessageLookupByLibrary.simpleMessage("Enter the verification code"),
    "jiangliyifafangjiang" : MessageLookupByLibrary.simpleMessage("Reward granted."),
    "jianyiVRdianliang40y" : MessageLookupByLibrary.simpleMessage("We recommend you use the VR headset when the battery level is more than 40%."),
    "jianyininzaizhuxiaoq" : MessageLookupByLibrary.simpleMessage("We recommend you back up your account information before proceeding, and ensure that all services related to the account have been properly handled. Once your account is canceled, we will process or anonymize your personal information according to the relevant terms of the User Privacy Policy."),
    "jiaru" : MessageLookupByLibrary.simpleMessage("Join"),
    "jiarushibaigaiyingyo" : MessageLookupByLibrary.simpleMessage("Failed to add this app because it has not been purchased."),
    "jiazaishibaiqingchon" : MessageLookupByLibrary.simpleMessage("Loading failed. Please try again."),
    "jiazaizhong" : MessageLookupByLibrary.simpleMessage("Loading..."),
    "jiebangzhongxiebangz" : MessageLookupByLibrary.simpleMessage("Unbinding..."),
    "jiebinghelubingjiepi" : MessageLookupByLibrary.simpleMessage("Media"),
    "jiebingyulubingjiepi" : MessageLookupByLibrary.simpleMessage("Media"),
    "jieshaoyixianiziji" : MessageLookupByLibrary.simpleMessage("Introduce yourself."),
    "jieshushijian" : MessageLookupByLibrary.simpleMessage("End Time"),
    "jijiangshangxianjiqi" : MessageLookupByLibrary.simpleMessage("Coming soon"),
    "jingdong" : MessageLookupByLibrary.simpleMessage("JD.com"),
    "jingdongyonghuming" : MessageLookupByLibrary.simpleMessage("JD.com ID"),
    "jingtoufanzhuan" : MessageLookupByLibrary.simpleMessage("Switch"),
    "jinhangzhongjinhengz" : MessageLookupByLibrary.simpleMessage("Ongoing"),
    "jinqi" : MessageLookupByLibrary.simpleMessage("Recent"),
    "jinrikeshiyongshidua" : MessageLookupByLibrary.simpleMessage("Available period today"),
    "jintian" : MessageLookupByLibrary.simpleMessage("Today"),
    "jinzhichizhongwendax" : MessageLookupByLibrary.simpleMessage("Only Chinese characters, English letters, and digits are supported."),
    "jitacuowuqitacuowu" : MessageLookupByLibrary.simpleMessage("Other error."),
    "jitajinen19999qitaji" : MessageLookupByLibrary.simpleMessage("Other amount 1-9,999"),
    "jitaqita" : MessageLookupByLibrary.simpleMessage("Other"),
    "jitongcuowuxitongcuo" : MessageLookupByLibrary.simpleMessage("System error."),
    "jitongtongzhixitongt" : MessageLookupByLibrary.simpleMessage("System Notification"),
    "jitongxiaoxixitongxi" : MessageLookupByLibrary.simpleMessage("System message"),
    "jubanzhe" : MessageLookupByLibrary.simpleMessage("Organizer:"),
    "jubao" : MessageLookupByLibrary.simpleMessage("Report"),
    "jubaoshibai" : MessageLookupByLibrary.simpleMessage("Reporting failed."),
    "kaiqihuodongqingqian" : MessageLookupByLibrary.simpleMessage("Go to the VR headset to join in the activity."),
    "kaiqiqingshaonianmos" : MessageLookupByLibrary.simpleMessage("Enable teen mode"),
    "kaishi" : MessageLookupByLibrary.simpleMessage("Start"),
    "kaishishijian" : MessageLookupByLibrary.simpleMessage("Start Time"),
    "keep_secret" : MessageLookupByLibrary.simpleMessage("Confidential"),
    "kenengrenshikenengre" : MessageLookupByLibrary.simpleMessage("Users you may know"),
    "keshiyongshiduan" : MessageLookupByLibrary.simpleMessage("Available period"),
    "keshiyongyouhuquandu" : MessageLookupByLibrary.simpleMessage("You can redeem coupons for it."),
    "keyongshijianduan" : MessageLookupByLibrary.simpleMessage("Available period"),
    "keyongyouhuquanNzhan" : m50,
    "keyongyouhuquankeyon" : MessageLookupByLibrary.simpleMessage("Valid coupons"),
    "kongzhishichangshouh" : MessageLookupByLibrary.simpleMessage("Anti-addiction control helps teens grow healthily in metaverse."),
    "kuailaibaomingba" : MessageLookupByLibrary.simpleMessage("Sign up now."),
    "kuaiquVRlitiyanSba" : m51,
    "kuaiquVRlitiyanSba_1" : m52,
    "kunbangbao" : MessageLookupByLibrary.simpleMessage("Bundle"),
    "laheiS" : m53,
    "laheiyonghuyiweizhao" : MessageLookupByLibrary.simpleMessage("If you blacklist a user, the user cannot: \nadd you as a friend,\ninvite you to a chatroom, or\nfind you using search."),
    "lajiangaolajiguangga" : MessageLookupByLibrary.simpleMessage("Spam advertisement"),
    "lanhelianjieshibaila" : MessageLookupByLibrary.simpleMessage("Bluetooth connection failed."),
    "lanheweikaiqixianggu" : MessageLookupByLibrary.simpleMessage("Bluetooth is not enabled. The related functions cannot be used."),
    "leave" : MessageLookupByLibrary.simpleMessage("Exit"),
    "leijidakaNtianleijid" : m54,
    "lianjiefuwuqishibaiq" : MessageLookupByLibrary.simpleMessage("Failed to connect to the server. Please try again."),
    "lianjieshebei" : MessageLookupByLibrary.simpleMessage("Connect device"),
    "lianjieshibai" : MessageLookupByLibrary.simpleMessage("Connection failed."),
    "lijibaoming" : MessageLookupByLibrary.simpleMessage("Sign up Now"),
    "lijigenghuan" : MessageLookupByLibrary.simpleMessage("Change Now"),
    "lijigengxin" : MessageLookupByLibrary.simpleMessage("Update Now"),
    "lijiqianwang" : MessageLookupByLibrary.simpleMessage("Now"),
    "lijixiazai" : MessageLookupByLibrary.simpleMessage("Download Now"),
    "lingjiangxinxidijiao" : MessageLookupByLibrary.simpleMessage("Submitted."),
    "lingjiangxinxidijiao_1" : MessageLookupByLibrary.simpleMessage("Failed to submit the information."),
    "lingjiangxinxitianxi" : MessageLookupByLibrary.simpleMessage("Page for information required for reward collection"),
    "liuchengmaSqingyongy" : m55,
    "liuchengmayifuzhi" : MessageLookupByLibrary.simpleMessage("Flow code copied."),
    "login_network_error" : MessageLookupByLibrary.simpleMessage("Network error. Please log in again."),
    "login_successful" : MessageLookupByLibrary.simpleMessage("Logged on successfully."),
    "luzhijijiangjieshuqi" : MessageLookupByLibrary.simpleMessage("The recording is about to end"),
    "luzhinarongguodamofa" : MessageLookupByLibrary.simpleMessage("The recorded content is too large to save."),
    "luzhishibaozhengbeil" : MessageLookupByLibrary.simpleMessage("Only capture the center"),
    "male" : MessageLookupByLibrary.simpleMessage("Male"),
    "meiyoufugetiaojiande" : MessageLookupByLibrary.simpleMessage("No content satisfies the condition."),
    "meiyoukexuanshijiand" : MessageLookupByLibrary.simpleMessage("No period is available."),
    "meiyouzhaodaofujinde" : MessageLookupByLibrary.simpleMessage("No VR headset found nearby."),
    "mianfeigoumaiwenfeig" : MessageLookupByLibrary.simpleMessage("Free"),
    "mianfeihuodewenfeihu" : MessageLookupByLibrary.simpleMessage("Free"),
    "mianfeiwenfei" : MessageLookupByLibrary.simpleMessage("Free"),
    "mimabuyizhiqingheshi" : MessageLookupByLibrary.simpleMessage("The passwords do not match. Please check and enter them again."),
    "mimacuowu" : MessageLookupByLibrary.simpleMessage("Incorrect password."),
    "mingtian" : MessageLookupByLibrary.simpleMessage("Tomorrow"),
    "modify_nickname" : MessageLookupByLibrary.simpleMessage("Change nickname"),
    "mofabaocunqingchongs" : MessageLookupByLibrary.simpleMessage("Failed to save. Please try again."),
    "mofabaocunqingfanhui" : MessageLookupByLibrary.simpleMessage("Saving failed. Go back to the list to refresh."),
    "mofagoumaigaiyingyon" : MessageLookupByLibrary.simpleMessage("The app cannot be purchased."),
    "mofahuoqujielubingwe" : MessageLookupByLibrary.simpleMessage("Unable to obtain the media file. Please ensure that the mobile phone and the VR headset are connected to the same Wi-Fi and the app permission is enabled, and then try again."),
    "mofahuoquwifimingche" : MessageLookupByLibrary.simpleMessage("Failed to obtain the Wi-Fi name."),
    "mofatianjiaVRyanjing" : MessageLookupByLibrary.simpleMessage("Failed to add the VR headset."),
    "mofatianjiaciVRyanji" : MessageLookupByLibrary.simpleMessage("Failed to add the VR headset. Please go back and try again."),
    "mofatongbuwanglaowuf" : MessageLookupByLibrary.simpleMessage("Failed to synchronize the network."),
    "mofazuoweikaishishij" : MessageLookupByLibrary.simpleMessage("This is not a valid start time. Please select again."),
    "mokeshiyongshiduanqi" : MessageLookupByLibrary.simpleMessage("No period available. Go to the time lock setting."),
    "msg_accept_friend" : m56,
    "msg_after_half_hour" : MessageLookupByLibrary.simpleMessage("This activity will start 30 minutes later."),
    "msg_delete" : MessageLookupByLibrary.simpleMessage("Delete"),
    "msg_delete_confirm" : MessageLookupByLibrary.simpleMessage("Deleted messages cannot be recovered. Are you sure you want to delete it?"),
    "msg_evet_cancel" : MessageLookupByLibrary.simpleMessage("This activity has been canceled."),
    "msg_evet_start" : MessageLookupByLibrary.simpleMessage("The activity is about to start."),
    "msg_friend_pass" : MessageLookupByLibrary.simpleMessage("Your friend request has been accepted."),
    "msg_invite_event" : m57,
    "msg_invite_you" : MessageLookupByLibrary.simpleMessage("Your friend invites you to join an activity."),
    "msg_req_friend" : MessageLookupByLibrary.simpleMessage("sent you a friend request."),
    "nan" : MessageLookupByLibrary.simpleMessage("Male"),
    "narongguodajianyizhi" : MessageLookupByLibrary.simpleMessage("The content is too large. We recommend you directly copy it to your PC."),
    "nichenbukeweikonghuo" : MessageLookupByLibrary.simpleMessage("The nickname cannot be empty or contain spaces."),
    "nichenbukeweikongzif" : MessageLookupByLibrary.simpleMessage("The nickname cannot be empty or contain spaces."),
    "nickname" : MessageLookupByLibrary.simpleMessage("Nickname"),
    "nihaimeiyougaiyingyo" : MessageLookupByLibrary.simpleMessage("You do not have this app yet. Go to purchase it."),
    "nihaimeiyoulianjiesh" : MessageLookupByLibrary.simpleMessage("The device is not connected. Do you want to connect it?"),
    "nimenduzaiwanSnimend" : m58,
    "nindangqianyishizuix" : MessageLookupByLibrary.simpleMessage("It is already the latest version."),
    "nindeVRshebeixiangni" : m59,
    "nindebeijingtupiansh" : MessageLookupByLibrary.simpleMessage("Your background image has not been approved"),
    "nindegexingqianmings" : MessageLookupByLibrary.simpleMessage("Your bio review failed"),
    "nindegoumaiqudaonind" : MessageLookupByLibrary.simpleMessage("Your purchase channel"),
    "nindetouxiangtupians" : MessageLookupByLibrary.simpleMessage("May contain disturbing content, please modify and try again."),
    "nindeyonghunichengsh" : MessageLookupByLibrary.simpleMessage("Your nickname review failed"),
    "nindezhanghaochuyuzh" : MessageLookupByLibrary.simpleMessage("3. The account is in normal use, has passed the security inspection by YVR and bears no risk of theft, and has never committed any violation of laws and regulations."),
    "nindezhanghaochuyuzh_1" : MessageLookupByLibrary.simpleMessage("3. The account is in normal use, has passed the security inspection by YVR and bears no risk of theft, and has never committed any violation of laws and regulations."),
    "nindijiaodezifugeshi" : MessageLookupByLibrary.simpleMessage("The character format is not supported."),
    "ninjiangfangqiweidao" : MessageLookupByLibrary.simpleMessage("1. You will give up your unexpired or unused virtual currency and props under various game characters, your various identity rights and interests in various products and/or services on the YVR platform, the unexpired online service content you have purchased in various products and/or services, the related assets (such as Y Coins) you have obtained in various products and/or services you have purchased, and other rights and interests that have been generated but not used up yet or expected benefits in the future."),
    "ninjiangjiechugaizha" : MessageLookupByLibrary.simpleMessage("2. You will disassociate this account from other products (such as video or third-party platforms), for example, remove the SSO."),
    "ninjiangjixuwancheng" : MessageLookupByLibrary.simpleMessage("The account cancelation process will proceed and the data of this account will also be deleted once the YVR account is canceled."),
    "ninjiaruhuodonghouca" : MessageLookupByLibrary.simpleMessage("You can invite friends only after you join in an activity."),
    "ninshangchuandetouxi" : MessageLookupByLibrary.simpleMessage("The avatar you uploaded does not meet the requirements."),
    "ninshangchuandetouxi_1" : MessageLookupByLibrary.simpleMessage("The avatar you uploaded does not meet the requirements."),
    "ninshengyuhuodongtia" : MessageLookupByLibrary.simpleMessage("Your remaining activity days cannot satisfy the condition for receiving the reward."),
    "ninshurudemingchenyi" : MessageLookupByLibrary.simpleMessage("The name you entered has exceeded the length limit."),
    "ninyiwanchengzhifunk" : MessageLookupByLibrary.simpleMessage("Payment complete. Now you can enjoy it on your VR headset."),
    "ninzaiyingyongshangd" : m60,
    "ninzaiyingyongshangd_1" : m61,
    "ninzaiyingyongshangd_2" : m62,
    "ninzaiyingyongshangd_3" : m63,
    "no_account" : MessageLookupByLibrary.simpleMessage("New user?"),
    "nv" : MessageLookupByLibrary.simpleMessage("Female"),
    "paizhao" : MessageLookupByLibrary.simpleMessage("Take photo"),
    "payWayManage" : MessageLookupByLibrary.simpleMessage("支付方式管理"),
    "pingfenyidijiaopingf" : MessageLookupByLibrary.simpleMessage("Your rating has been submitted."),
    "pinglunnarongweiguip" : MessageLookupByLibrary.simpleMessage("Illegal content"),
    "pinglunyidijiaoshenh" : MessageLookupByLibrary.simpleMessage("The comment has been submitted for review. You can check the review results in [Notification Center]."),
    "pingtaizhengce" : MessageLookupByLibrary.simpleMessage("Platform Policy"),
    "pinjie" : MessageLookupByLibrary.simpleMessage("Splice"),
    "pinjieluzhi" : MessageLookupByLibrary.simpleMessage("Dual Recording"),
    "pinjieluzhiguochengz" : MessageLookupByLibrary.simpleMessage("Ensure that the app is on the frontend during dual recording."),
    "pinjiemoshi" : MessageLookupByLibrary.simpleMessage("Mode"),
    "please_add_dev" : MessageLookupByLibrary.simpleMessage("Please log in to your account in VR first"),
    "plhd_no_event" : MessageLookupByLibrary.simpleMessage("No activity yet."),
    "plhd_no_more_data" : MessageLookupByLibrary.simpleMessage("No more data."),
    "plhd_no_msg" : MessageLookupByLibrary.simpleMessage("No new messages."),
    "point_rule_html_page_name" : MessageLookupByLibrary.simpleMessage("pointRuleen"),
    "privacy_policy" : MessageLookupByLibrary.simpleMessage("Privacy Policy"),
    "privacy_policy_html_page_name" : MessageLookupByLibrary.simpleMessage("protectionen"),
    "prod_alipay" : MessageLookupByLibrary.simpleMessage("Alipay"),
    "prod_authority" : MessageLookupByLibrary.simpleMessage("Required permission: "),
    "prod_book" : MessageLookupByLibrary.simpleMessage("Book"),
    "prod_book_app_failed" : MessageLookupByLibrary.simpleMessage("Reservation failed, please try again."),
    "prod_book_app_succeeded" : MessageLookupByLibrary.simpleMessage("Reservation successful, you will be notified when it goes live."),
    "prod_booked" : MessageLookupByLibrary.simpleMessage("Booked"),
    "prod_coming_time" : m64,
    "prod_detail" : MessageLookupByLibrary.simpleMessage("Details"),
    "prod_developer" : MessageLookupByLibrary.simpleMessage("Developer: "),
    "prod_download_queue" : MessageLookupByLibrary.simpleMessage("Added to the download list."),
    "prod_experience_vr" : MessageLookupByLibrary.simpleMessage("You have paid for the app.\nEnjoy it on your VR device."),
    "prod_fold" : MessageLookupByLibrary.simpleMessage("Collapse"),
    "prod_input_comment" : MessageLookupByLibrary.simpleMessage("Enter your review."),
    "prod_install_alipay" : MessageLookupByLibrary.simpleMessage("Install Alipay first."),
    "prod_install_wechat" : MessageLookupByLibrary.simpleMessage("Install WeChat first."),
    "prod_language" : MessageLookupByLibrary.simpleMessage("Language: "),
    "prod_my_comment" : MessageLookupByLibrary.simpleMessage("My review"),
    "prod_need_login" : MessageLookupByLibrary.simpleMessage("Please log on to buy."),
    "prod_network" : MessageLookupByLibrary.simpleMessage("Network mode: "),
    "prod_pay_fail" : MessageLookupByLibrary.simpleMessage("Payment failed."),
    "prod_pay_result" : MessageLookupByLibrary.simpleMessage("Payment status"),
    "prod_pay_success" : MessageLookupByLibrary.simpleMessage("Paid successfully."),
    "prod_pay_way" : MessageLookupByLibrary.simpleMessage("Select payment method"),
    "prod_policy" : MessageLookupByLibrary.simpleMessage("Platform policy:"),
    "prod_policy_title" : MessageLookupByLibrary.simpleMessage("Platform policy"),
    "prod_publisher" : MessageLookupByLibrary.simpleMessage("publisher: "),
    "prod_ram" : MessageLookupByLibrary.simpleMessage("Required ROM: "),
    "prod_ratings_reviews" : MessageLookupByLibrary.simpleMessage("Rating and reviews"),
    "prod_release_date" : MessageLookupByLibrary.simpleMessage("Release date: "),
    "prod_sofa" : MessageLookupByLibrary.simpleMessage("No review yet. Be the first reviewer!"),
    "prod_unbook_app_failed" : MessageLookupByLibrary.simpleMessage("Cancellation failed, please try again."),
    "prod_unfold" : MessageLookupByLibrary.simpleMessage("Expand"),
    "prod_version" : MessageLookupByLibrary.simpleMessage("Version: "),
    "prod_wechat" : MessageLookupByLibrary.simpleMessage("WeChat"),
    "profile_buy_history" : MessageLookupByLibrary.simpleMessage("Purchase records"),
    "profile_cancel_feedback" : MessageLookupByLibrary.simpleMessage("Cancel feedback"),
    "profile_change_pwd" : MessageLookupByLibrary.simpleMessage("Change password"),
    "profile_check_version" : MessageLookupByLibrary.simpleMessage("Check the version"),
    "profile_confirm_password" : MessageLookupByLibrary.simpleMessage("Confirm password"),
    "profile_connect_way" : MessageLookupByLibrary.simpleMessage("Email address"),
    "profile_contact_information" : MessageLookupByLibrary.simpleMessage("Call ************ Customer service online time: 9:00-18:00 on working days"),
    "profile_current_version" : MessageLookupByLibrary.simpleMessage("Current version"),
    "profile_desc_problem" : MessageLookupByLibrary.simpleMessage("Describe the problem you have encountered. We will handle it in time."),
    "profile_edit" : MessageLookupByLibrary.simpleMessage("Edit profile"),
    "profile_enter_sms" : MessageLookupByLibrary.simpleMessage("Enter SMS verification code"),
    "profile_exchange_code" : MessageLookupByLibrary.simpleMessage("Enter exchange code"),
    "profile_feedback" : MessageLookupByLibrary.simpleMessage("User feedback"),
    "profile_health" : MessageLookupByLibrary.simpleMessage("Health and Safety"),
    "profile_homepage" : MessageLookupByLibrary.simpleMessage("Homepage"),
    "profile_input_nick" : MessageLookupByLibrary.simpleMessage("Enter your nickname."),
    "profile_is_feedback" : MessageLookupByLibrary.simpleMessage("Are you sure you want to cancel the feedback?"),
    "profile_legal_info" : MessageLookupByLibrary.simpleMessage("Legal information"),
    "profile_new_password" : MessageLookupByLibrary.simpleMessage("New password"),
    "profile_new_ver" : MessageLookupByLibrary.simpleMessage("Latest version"),
    "profile_not_update" : MessageLookupByLibrary.simpleMessage("Later"),
    "profile_phone_num" : MessageLookupByLibrary.simpleMessage("Phone number"),
    "profile_please_redemption" : MessageLookupByLibrary.simpleMessage("Enter your redemption code."),
    "profile_question_type" : MessageLookupByLibrary.simpleMessage("Problem type"),
    "profile_redemption_code" : MessageLookupByLibrary.simpleMessage("Redemption code"),
    "profile_refunded" : MessageLookupByLibrary.simpleMessage("Refunded"),
    "profile_refunding" : MessageLookupByLibrary.simpleMessage("Refunding"),
    "profile_request_refund" : MessageLookupByLibrary.simpleMessage("Request refund"),
    "profile_sel_feedback" : MessageLookupByLibrary.simpleMessage("Select your feedback type."),
    "profile_set_password" : MessageLookupByLibrary.simpleMessage("Set new password"),
    "profile_update_now" : MessageLookupByLibrary.simpleMessage("Update now"),
    "profile_ver_size" : MessageLookupByLibrary.simpleMessage("Version size"),
    "profile_ver_update" : MessageLookupByLibrary.simpleMessage("Version update"),
    "profile_verify_identidy" : MessageLookupByLibrary.simpleMessage("Verify identification"),
    "profile_verify_phone_num" : MessageLookupByLibrary.simpleMessage("Verify mobile number"),
    "pwd_input_again" : MessageLookupByLibrary.simpleMessage("Confirm the password"),
    "pwd_login" : MessageLookupByLibrary.simpleMessage("Log on by password"),
    "pwd_rule" : MessageLookupByLibrary.simpleMessage("6-12 characters (letters & digits)"),
    "qianmingbunengchaogu" : MessageLookupByLibrary.simpleMessage("The bio cannot exceed 6 lines."),
    "qianmingbunenghanyou" : MessageLookupByLibrary.simpleMessage("The bio cannot contain blank lines and should have no more than 6 lines."),
    "qiehuanshipin" : MessageLookupByLibrary.simpleMessage("Switch Video"),
    "qingduiyingyongSdafe" : m65,
    "qinggenjuxiafangzhiy" : MessageLookupByLibrary.simpleMessage("Fill in the reward collection information following the instructions below. We will review the information you provide as soon as possible."),
    "qinglianjiezhixiangt" : MessageLookupByLibrary.simpleMessage("After connecting to the same network, swipe down to refresh the list."),
    "qingniwubishenshenyu" : m66,
    "qingqianwangVRshebei" : MessageLookupByLibrary.simpleMessage("Go to the VR headset to manually download the app."),
    "qingqianwangVRyanjin" : MessageLookupByLibrary.simpleMessage("Try it in the VR headset."),
    "qingqianwangYVRguanw" : MessageLookupByLibrary.simpleMessage("Please visit https://yvr.cn"),
    "qingqiehuanshipindep" : MessageLookupByLibrary.simpleMessage("Switch the video splicing mode."),
    "qingqiuchaoshi" : MessageLookupByLibrary.simpleMessage("Request timed out."),
    "qingqiushibai" : MessageLookupByLibrary.simpleMessage("Request failed."),
    "qingquanchengshiyong" : MessageLookupByLibrary.simpleMessage("Hold your phone vertically during recording"),
    "qingquebaoVRyanjingl" : MessageLookupByLibrary.simpleMessage("Ensure that the VR headset is connected to the network and the screen is on."),
    "qingquebaoVRzhongyid" : MessageLookupByLibrary.simpleMessage("Ensure that you have logged in with the same account."),
    "qingshangchuanbaohan" : MessageLookupByLibrary.simpleMessage("Upload the screenshot of the platform order containing the order No. and actual payment amount."),
    "qingshangchuanshenfe" : MessageLookupByLibrary.simpleMessage("Upload the photos of the front and back of your ID card (must be clear and complete.)"),
    "qingshaonianmoshiqin" : MessageLookupByLibrary.simpleMessage("Teen mode"),
    "qingshaonianmoshiyig" : MessageLookupByLibrary.simpleMessage("Teen mode is off."),
    "qingshaonianmoshiyig_1" : MessageLookupByLibrary.simpleMessage("The teen mode is disabled. Enable it and retry."),
    "qingshaonianmoshiyik" : MessageLookupByLibrary.simpleMessage("Teen mode enabled."),
    "qingshaonianshouhugo" : MessageLookupByLibrary.simpleMessage("Teen guardian tool"),
    "qingshiyongjitashaix" : MessageLookupByLibrary.simpleMessage("Try other filter criteria."),
    "qingshuru612weidaxia" : MessageLookupByLibrary.simpleMessage("Enter a string of 6 to 12 letters, digits, or special characters."),
    "qingshuruNweishuzi" : m67,
    "qingshuruS" : m68,
    "qingshuruWiFimima" : MessageLookupByLibrary.simpleMessage("Enter the Wi-Fi password."),
    "qingshurufashenggeig" : MessageLookupByLibrary.simpleMessage("Enter the verification code that is sent to the mobile number bound to the account."),
    "qingshurumimayanzhen" : MessageLookupByLibrary.simpleMessage("Enter the password to authenticate the parent\'s identity and enter the teen mode setting."),
    "qingshuruningoumaiqu" : MessageLookupByLibrary.simpleMessage("Enter the platform order No. (must be consistent with that in the uploaded screenshot) of your purchase channel."),
    "qingshurushoujihao" : MessageLookupByLibrary.simpleMessage("Enter a mobile number."),
    "qingshuruxinshoujiha" : MessageLookupByLibrary.simpleMessage("Enter a new mobile number."),
    "qingshuruyanzhengma" : MessageLookupByLibrary.simpleMessage("Enter the verification code."),
    "qingshuruyouxiaoshou" : MessageLookupByLibrary.simpleMessage("Enter a valid mobile number."),
    "qingwanchengbitianxi" : MessageLookupByLibrary.simpleMessage("Complete all required items before submission."),
    "qingwutuichu" : MessageLookupByLibrary.simpleMessage("Do not exit"),
    "qingxiananzhuangweix" : MessageLookupByLibrary.simpleMessage("Please install the WeChat app first."),
    "qingxianbangdingnind" : MessageLookupByLibrary.simpleMessage("Please bind your VR headset first."),
    "qingxiandakaiVRshebe" : MessageLookupByLibrary.simpleMessage("Enable the Wi-Fi function of the VR headset first."),
    "qingxiandakaiVRyanji" : MessageLookupByLibrary.simpleMessage("Enable the Wi-Fi function of the VR headset first."),
    "qingxiandakaifangwen" : MessageLookupByLibrary.simpleMessage("Please enable the permission to access the local network."),
    "qingxiandakaiqingsha" : MessageLookupByLibrary.simpleMessage("Enable teen mode first."),
    "qingxiandenglunindez" : MessageLookupByLibrary.simpleMessage("Log in first."),
    "qingxianguanbiqingsh" : MessageLookupByLibrary.simpleMessage("Turn off teen mode first."),
    "qingxianjiangshoujil" : MessageLookupByLibrary.simpleMessage("Connect the mobile phone to Wi-Fi first. If it is connected, enable the Bluetooth and location permissions of the Play For Dream first."),
    "qingxiankaiqiSquanxi" : m69,
    "qingxiankaiqibendexi" : MessageLookupByLibrary.simpleMessage("Please enable the permission to access the local album."),
    "qingxiankaiqixiangji" : MessageLookupByLibrary.simpleMessage("Enable camera permission"),
    "qingxiankaiqixiangji_1" : MessageLookupByLibrary.simpleMessage("Enable the camera and microphone permissions first."),
    "qingxianlianjieVRyan" : MessageLookupByLibrary.simpleMessage("Connect the VR headset first."),
    "qingxiantongyiYVRfuw" : MessageLookupByLibrary.simpleMessage("You need to agree to the Service Agreement and the Privacy Policy of YVR."),
    "qingxianwanchengbiti" : MessageLookupByLibrary.simpleMessage("Complete the required items first."),
    "qingxianyuedubington" : MessageLookupByLibrary.simpleMessage("Please read and agree to the terms and conditions of the Account Cancelation Agreement first."),
    "qingxianyuedubington_1" : MessageLookupByLibrary.simpleMessage("Please read and agree to YVR Top-up Service Agreement"),
    "qingxianzaiVRyanjing" : MessageLookupByLibrary.simpleMessage("Log in first in the VR headset."),
    "qingxuanzeshipindepi" : MessageLookupByLibrary.simpleMessage("Select a video splicing mode."),
    "quanbu" : MessageLookupByLibrary.simpleMessage("All"),
    "quanbuhuodong" : MessageLookupByLibrary.simpleMessage("All activities"),
    "qucanjiaqucenjiaqusa" : MessageLookupByLibrary.simpleMessage("Join In Now"),
    "qudenglu" : MessageLookupByLibrary.simpleMessage("Log In Now"),
    "queding" : MessageLookupByLibrary.simpleMessage("OK"),
    "quedingfangqicaichan" : MessageLookupByLibrary.simpleMessage("Give Up Account Assets"),
    "quedingyaoshanchugai" : MessageLookupByLibrary.simpleMessage("Are you sure you want to delete this friend?"),
    "quedingyaoshanchugai_1" : MessageLookupByLibrary.simpleMessage("Are you sure you want to delete this activity?"),
    "quedingyaotuikuanmat" : MessageLookupByLibrary.simpleMessage("Are you sure you want to refund? The refund amount will be returned to your payment account in three working days. You cannot use this app during the refund period."),
    "quedingzhuxiao" : MessageLookupByLibrary.simpleMessage("Yes"),
    "quedingzhuxiaozhangh" : MessageLookupByLibrary.simpleMessage("Confirm Account Cancelation"),
    "queren" : MessageLookupByLibrary.simpleMessage("OK"),
    "querenmima" : MessageLookupByLibrary.simpleMessage("Confirm password"),
    "querenzhuxiaochongya" : MessageLookupByLibrary.simpleMessage("Important reminders for account cancelation"),
    "querenzhuxiaojiangwa" : MessageLookupByLibrary.simpleMessage("If you confirm the cancelation, your request for account cancelation will be completed, and the system will automatically cancel your YVR account 7 days later. To withdraw the request, log in to this account again within 7 days."),
    "querenzhuxiaozhangha" : MessageLookupByLibrary.simpleMessage("After you confirm account cancelation, your account will be automatically canceled 7 days later. To withdraw the request, log in to this account again within 7 days."),
    "qugoumai" : MessageLookupByLibrary.simpleMessage("Purchase Now"),
    "qukaiqi" : MessageLookupByLibrary.simpleMessage("Enable Now"),
    "qulianjie" : MessageLookupByLibrary.simpleMessage("Connect"),
    "qushezhi" : MessageLookupByLibrary.simpleMessage("Disable Now"),
    "quxiao" : MessageLookupByLibrary.simpleMessage("Cancel"),
    "quxiaoluzhi" : MessageLookupByLibrary.simpleMessage("Cancel"),
    "quxiazai" : MessageLookupByLibrary.simpleMessage("Download Now"),
    "recharge_service_agreement_url_dev" : MessageLookupByLibrary.simpleMessage("https://apitest.yvrdream.com/yvrdvcenter/#/rechargeagreementen"),
    "recharge_service_agreement_url_release" : MessageLookupByLibrary.simpleMessage("https://developer.yvrdream.com/#/rechargeagreementen"),
    "refundAvailable" : MessageLookupByLibrary.simpleMessage("Refund Available: "),
    "refund_policy_html_page_name" : MessageLookupByLibrary.simpleMessage("refundPolicyen"),
    "register_account" : MessageLookupByLibrary.simpleMessage("Register"),
    "renxiangweizhu" : MessageLookupByLibrary.simpleMessage("More in Camera"),
    "resend" : MessageLookupByLibrary.simpleMessage("Resend"),
    "riqishijian" : MessageLookupByLibrary.simpleMessage("Date/Time"),
    "riyiersan" : MessageLookupByLibrary.simpleMessage("Sun,Mon,Tue,Wed,Thur,Fri,Sat"),
    "rizhidiaoshirizhitia" : MessageLookupByLibrary.simpleMessage("Log debugging"),
    "runinrengxuanzejixuz" : MessageLookupByLibrary.simpleMessage("If you still choose to cancel your account, you are deemed to automatically give up your virtual assets (including but not limited to the following rights and interests) under the account. You understand and agree that we cannot assist you in restoring the preceding services."),
    "ruxinxiqueloucuowuze" : MessageLookupByLibrary.simpleMessage("If the information is missing or wrong, the application will be deemed invalid."),
    "ruyouqingshangchuang" : MessageLookupByLibrary.simpleMessage("(Optional) Upload the YVR invoice, if any."),
    "sanninzhuxiaobenYVRz" : MessageLookupByLibrary.simpleMessage("III. Your cancelation of this YVR account does not mean your relevant responsibilities under this account will be diminished or exempted before cancelation."),
    "saomiaolanhexuyaonin" : MessageLookupByLibrary.simpleMessage("To search for nearby Bluetooth devices, please enable location permission for Play For Dream in Settings."),
    "sel_photograph" : MessageLookupByLibrary.simpleMessage("Select from album"),
    "service_agreement" : MessageLookupByLibrary.simpleMessage("Terms of Service"),
    "service_agreement_html_page_name" : MessageLookupByLibrary.simpleMessage("serveren"),
    "shaixuantiaojian" : MessageLookupByLibrary.simpleMessage("Filter criterion:"),
    "shanchu" : MessageLookupByLibrary.simpleMessage("Yes"),
    "shanchuS" : m70,
    "shanchuhaoyouchenggo" : MessageLookupByLibrary.simpleMessage("Deleted."),
    "shanchuhuodong" : MessageLookupByLibrary.simpleMessage("Delete Activity"),
    "shanchushibai" : MessageLookupByLibrary.simpleMessage("Deletion failed."),
    "shanchuzhong" : MessageLookupByLibrary.simpleMessage("Deleting..."),
    "shangchuanshibaishan" : MessageLookupByLibrary.simpleMessage("Upload failure."),
    "shangchuanwenjianwei" : MessageLookupByLibrary.simpleMessage("The uploaded file fails the intelligent audit."),
    "shangchuanzhongshang" : MessageLookupByLibrary.simpleMessage("Uploading..."),
    "shangwu" : MessageLookupByLibrary.simpleMessage("a.m."),
    "shaohouchakanshaohou" : MessageLookupByLibrary.simpleMessage("View Later"),
    "shaohouqianwang" : MessageLookupByLibrary.simpleMessage("Later"),
    "shaohouxiazai" : MessageLookupByLibrary.simpleMessage("Maybe Later"),
    "shebeibucunzai" : MessageLookupByLibrary.simpleMessage("No device found."),
    "shenfenyanzheng" : MessageLookupByLibrary.simpleMessage("Identity authentication"),
    "shengjishibaiqingcho" : MessageLookupByLibrary.simpleMessage("Upgrade failed. Please try again."),
    "shengyuN0" : m71,
    "shengyuNxuzhifuN" : m72,
    "shenhebutongguoyuany" : MessageLookupByLibrary.simpleMessage("Rejected. Illegal content found."),
    "shenheyitongguoshenh" : MessageLookupByLibrary.simpleMessage(",Approved."),
    "shenqingzhanghaozhux" : MessageLookupByLibrary.simpleMessage("Request Account Cancelation"),
    "shezhichenggong" : MessageLookupByLibrary.simpleMessage("Set."),
    "shezhikeshiyongshidu" : MessageLookupByLibrary.simpleMessage("Available to set the usage period, which defaults to 6:00-22:00 each day."),
    "shezhimima" : MessageLookupByLibrary.simpleMessage("Set password"),
    "shichangshizhang" : MessageLookupByLibrary.simpleMessage("Duration"),
    "shichangshizhang_1" : MessageLookupByLibrary.simpleMessage("Duration:"),
    "shiduanyizhanyongqin" : MessageLookupByLibrary.simpleMessage("This time slot has been occupied. Select another one."),
    "shifoufangqidangqian" : MessageLookupByLibrary.simpleMessage("Are you sure you want to discard?"),
    "shifoulijixiazaindia" : MessageLookupByLibrary.simpleMessage("Download now? If you tap Download Now, the VR headset will automatically download this app when it is connected to the network."),
    "shifouquedingfangqid" : MessageLookupByLibrary.simpleMessage("Are you sure you want to give up your virtual assets under this account?"),
    "shifouquxiaobaomings" : MessageLookupByLibrary.simpleMessage("Do you want to cancel sign-up?"),
    "shijiansuoshezhi" : MessageLookupByLibrary.simpleMessage("Time lock setting"),
    "shijifukuan" : MessageLookupByLibrary.simpleMessage("Actual payment"),
    "shipingeshicuowu" : MessageLookupByLibrary.simpleMessage("Incorrect video format."),
    "shipinluzhiyizhongdu" : MessageLookupByLibrary.simpleMessage("Video recording has been interrupted."),
    "shipinnuligechengzho" : MessageLookupByLibrary.simpleMessage("Synthesizing..."),
    "shipinshili" : MessageLookupByLibrary.simpleMessage("Video Example"),
    "shiyonglanhehuliansh" : MessageLookupByLibrary.simpleMessage("When Bluetooth is used for interconnection, this name will be displayed on other devices."),
    "shiyongshuimingshiyo" : MessageLookupByLibrary.simpleMessage("Instruction"),
    "shoujianrenzhenshixi" : MessageLookupByLibrary.simpleMessage("Real name of the recipient"),
    "shoujinacunbuzuqingq" : MessageLookupByLibrary.simpleMessage("Insufficient storage of the mobile phone. Please free up some space before recording."),
    "shoujiyuVRyanjingwan" : MessageLookupByLibrary.simpleMessage("The network of the mobile phone and the VR headset are inconsistent"),
    "shouqi" : MessageLookupByLibrary.simpleMessage("Collapse"),
    "shuaxin" : MessageLookupByLibrary.simpleMessage("Refresh"),
    "shuaxinguoyupinfanqi" : MessageLookupByLibrary.simpleMessage("You are refreshing too often. Please try later."),
    "shubingpinjieshuping" : MessageLookupByLibrary.simpleMessage("Portrait Splicing"),
    "shubingshuping" : MessageLookupByLibrary.simpleMessage("Portrait"),
    "shuidianshenmebashuo" : MessageLookupByLibrary.simpleMessage("Any comments are welcome."),
    "shujuqingqiuchaoshij" : MessageLookupByLibrary.simpleMessage("Data request timed out. Loading failed."),
    "shuruhuodongmingchen" : MessageLookupByLibrary.simpleMessage("Enter the activity name (no more than 14 characters)."),
    "shurunindingchanzhon" : MessageLookupByLibrary.simpleMessage("Enter the actual payment amount (must be consistent with that in the uploaded screenshot) of your order."),
    "shuruyanzhengma" : MessageLookupByLibrary.simpleMessage("Enter the verification code."),
    "sign_in" : MessageLookupByLibrary.simpleMessage("Log on"),
    "sign_out" : MessageLookupByLibrary.simpleMessage("Log out"),
    "sign_up" : MessageLookupByLibrary.simpleMessage("Register"),
    "social_activity" : MessageLookupByLibrary.simpleMessage("Activity"),
    "social_add_friend" : MessageLookupByLibrary.simpleMessage("Add friend"),
    "social_add_req" : MessageLookupByLibrary.simpleMessage("requested to add you as a friend."),
    "social_block" : MessageLookupByLibrary.simpleMessage("Blacklist"),
    "social_block_list" : MessageLookupByLibrary.simpleMessage("Blacklist"),
    "social_creat_event" : MessageLookupByLibrary.simpleMessage("Create activity"),
    "social_date" : MessageLookupByLibrary.simpleMessage("Date/Time"),
    "social_duration" : MessageLookupByLibrary.simpleMessage("Length"),
    "social_event_desc" : MessageLookupByLibrary.simpleMessage("Activity description (optional)"),
    "social_friend_req" : MessageLookupByLibrary.simpleMessage("New friends"),
    "social_friends" : MessageLookupByLibrary.simpleMessage("Friends"),
    "social_ignore" : MessageLookupByLibrary.simpleMessage("Ignore"),
    "social_input_name" : MessageLookupByLibrary.simpleMessage("Enter the activity name (within 14 characters)."),
    "social_join_in" : MessageLookupByLibrary.simpleMessage("Interested"),
    "social_join_in_vr" : MessageLookupByLibrary.simpleMessage("Add to VR"),
    "social_joined" : MessageLookupByLibrary.simpleMessage("Joined"),
    "social_know_more" : MessageLookupByLibrary.simpleMessage("Add friend to learn more."),
    "social_leave_event" : MessageLookupByLibrary.simpleMessage("Quit Activity"),
    "social_leave_event_desc" : MessageLookupByLibrary.simpleMessage("If you quit the activity, you will not receive any activity notifications or updates, but you can join the activity again."),
    "social_minute" : MessageLookupByLibrary.simpleMessage("Minutes"),
    "social_my_activity" : MessageLookupByLibrary.simpleMessage("My activities"),
    "social_my_friends" : MessageLookupByLibrary.simpleMessage("My friends"),
    "social_no_block" : MessageLookupByLibrary.simpleMessage("The blacklist is empty."),
    "social_organizers" : MessageLookupByLibrary.simpleMessage("Host"),
    "social_pass" : MessageLookupByLibrary.simpleMessage("Pass"),
    "social_people_attend" : m73,
    "social_popular_activities" : MessageLookupByLibrary.simpleMessage("Hot activities"),
    "social_recent_activity" : MessageLookupByLibrary.simpleMessage("Recent activities"),
    "social_recommended" : MessageLookupByLibrary.simpleMessage("Recommended"),
    "social_sel_app" : MessageLookupByLibrary.simpleMessage("Select app"),
    "social_sendreq_yet" : MessageLookupByLibrary.simpleMessage("The request is sent."),
    "social_unfriend" : MessageLookupByLibrary.simpleMessage("Delete"),
    "songNYbi" : m74,
    "sousuojieshu" : MessageLookupByLibrary.simpleMessage("Search ended."),
    "sousuonichenheIDhaot" : MessageLookupByLibrary.simpleMessage("Search for the nickname and ID to add a friend."),
    "sousuozaixianyouhuha" : MessageLookupByLibrary.simpleMessage("Search for an online game, friend name, ID, or app."),
    "state_empty" : MessageLookupByLibrary.simpleMessage("Nothing Found"),
    "state_error" : MessageLookupByLibrary.simpleMessage("Load Failed"),
    "state_load_fail" : MessageLookupByLibrary.simpleMessage("Loading failed. Tap to try again."),
    "state_load_more" : MessageLookupByLibrary.simpleMessage("Release to load more."),
    "state_network" : MessageLookupByLibrary.simpleMessage("Load Failed, Check network "),
    "state_refresh" : MessageLookupByLibrary.simpleMessage("Refresh"),
    "state_retry" : MessageLookupByLibrary.simpleMessage("Retry"),
    "state_unauth" : MessageLookupByLibrary.simpleMessage("Not sign in yet"),
    "submit" : MessageLookupByLibrary.simpleMessage("Submit"),
    "tabbar_device" : MessageLookupByLibrary.simpleMessage("Device"),
    "tabbar_home" : MessageLookupByLibrary.simpleMessage("Store"),
    "tabbar_profile" : MessageLookupByLibrary.simpleMessage("Me"),
    "tabbar_social" : MessageLookupByLibrary.simpleMessage("Community"),
    "take_picture" : MessageLookupByLibrary.simpleMessage("Photo"),
    "taobaozhanghuming" : MessageLookupByLibrary.simpleMessage("Taobao ID"),
    "tianjia" : MessageLookupByLibrary.simpleMessage("Add"),
    "tianjiaVRyanjing" : MessageLookupByLibrary.simpleMessage("Add VR headset"),
    "tianjiaVRyanjingbeij" : MessageLookupByLibrary.simpleMessage("The request has been rejected. Please go back and reselect the desired VR headset."),
    "tianjiaVRyanjingqian" : MessageLookupByLibrary.simpleMessage("Before adding a VR headset, enable Bluetooth."),
    "tianjiahaoyou" : MessageLookupByLibrary.simpleMessage("Add Friend"),
    "tianjiahaoyoukeyilia" : MessageLookupByLibrary.simpleMessage("Add friends to learn more."),
    "tianjiashebei" : MessageLookupByLibrary.simpleMessage("Add"),
    "tianmao" : MessageLookupByLibrary.simpleMessage("Tmall"),
    "tianqian" : MessageLookupByLibrary.simpleMessage("1 day ago"),
    "tianqian_1" : MessageLookupByLibrary.simpleMessage("2 days ago"),
    "tingzhitoubingtingzh" : MessageLookupByLibrary.simpleMessage("Stop Projection"),
    "tips" : MessageLookupByLibrary.simpleMessage("Note"),
    "toBind" : MessageLookupByLibrary.simpleMessage("去绑定"),
    "toast_already_commented" : MessageLookupByLibrary.simpleMessage("You have already reviewed the app."),
    "toast_app_purchased" : MessageLookupByLibrary.simpleMessage("You have bought the app already. Do not pay repeatedly."),
    "toast_avatar_fail" : MessageLookupByLibrary.simpleMessage("Failed to upload the avatar."),
    "toast_avatar_success" : MessageLookupByLibrary.simpleMessage("Failed to upload the avatar."),
    "toast_birthday" : MessageLookupByLibrary.simpleMessage("Select birth date first."),
    "toast_dev_limit" : MessageLookupByLibrary.simpleMessage("The number of devices logged on has reached the limit."),
    "toast_emoticon_icon" : MessageLookupByLibrary.simpleMessage("Review content does not support stickers."),
    "toast_event_delete" : MessageLookupByLibrary.simpleMessage("This activity has been deleted by the creator."),
    "toast_feedback_desc" : MessageLookupByLibrary.simpleMessage("Thanks for your feedback. We will\ncontact you within seven workdays."),
    "toast_feedback_fail" : MessageLookupByLibrary.simpleMessage("Failed to submit feedback."),
    "toast_gender" : MessageLookupByLibrary.simpleMessage("Select a gender first."),
    "toast_inventory_shortage" : MessageLookupByLibrary.simpleMessage("Insufficient app stock."),
    "toast_login_fail" : MessageLookupByLibrary.simpleMessage("An exception occurred."),
    "toast_msg_not_input" : MessageLookupByLibrary.simpleMessage("Required information is missing."),
    "toast_msg_update_fail" : MessageLookupByLibrary.simpleMessage("Failed to modify information."),
    "toast_need_login" : MessageLookupByLibrary.simpleMessage("Log in first."),
    "toast_no_regist" : MessageLookupByLibrary.simpleMessage("The account is not registered yet."),
    "toast_not_exist" : MessageLookupByLibrary.simpleMessage("The mobile number does not exist."),
    "toast_order_fail" : MessageLookupByLibrary.simpleMessage("Failed to place the order."),
    "toast_pwd_error" : MessageLookupByLibrary.simpleMessage("Incorrect password"),
    "toast_redemption_fail" : MessageLookupByLibrary.simpleMessage("Redemption failed."),
    "toast_refund_desc" : MessageLookupByLibrary.simpleMessage("Your refund request is accepted. The refund will be sent to the account used to purchase the app in 3 workdays."),
    "toast_refund_fail" : MessageLookupByLibrary.simpleMessage("Refund failed."),
    "toast_refund_success" : MessageLookupByLibrary.simpleMessage("Refunded."),
    "toast_regist_yet" : MessageLookupByLibrary.simpleMessage("The mobile number is already registered."),
    "toast_unknown_error" : MessageLookupByLibrary.simpleMessage("Unknown error."),
    "toast_update_pwd" : MessageLookupByLibrary.simpleMessage("The new and original password cannot be the same."),
    "toast_userinfo" : MessageLookupByLibrary.simpleMessage("User info updated."),
    "toast_vfcodeJ_send_fail" : MessageLookupByLibrary.simpleMessage("Failed to request the verification code."),
    "toast_vfcode_day" : MessageLookupByLibrary.simpleMessage("Your requests for the verification code have exceeded the daily limit."),
    "toast_vfcode_error" : MessageLookupByLibrary.simpleMessage("Incorrect verification code."),
    "toast_vfcode_frequently" : MessageLookupByLibrary.simpleMessage("Too many requests for the verification code."),
    "tongbuqianqingxianqu" : MessageLookupByLibrary.simpleMessage("Before network synchronization, ensure that the Bluetooth and location permissions are enabled. If the Wi-Fi has no password, directly tap OK."),
    "tongbuwanglaotongbuw" : MessageLookupByLibrary.simpleMessage("Synchronize"),
    "tongguo" : MessageLookupByLibrary.simpleMessage("Accept"),
    "tongyi" : MessageLookupByLibrary.simpleMessage("Agree"),
    "tongzhiVRxiazaishiba" : MessageLookupByLibrary.simpleMessage("Download failed. Go to the VR headset to manually download it."),
    "toubingtouping" : MessageLookupByLibrary.simpleMessage("Projection"),
    "tuichuzhuxiao" : MessageLookupByLibrary.simpleMessage("Exit"),
    "tuijian" : MessageLookupByLibrary.simpleMessage("Recommend"),
    "tuikuan" : MessageLookupByLibrary.simpleMessage("Refund"),
    "tuikuanshibai" : MessageLookupByLibrary.simpleMessage("Refund failed."),
    "tuikuanshibaiyichaog" : MessageLookupByLibrary.simpleMessage("Refund failed. The refund period has expired."),
    "tuikuanzhong" : MessageLookupByLibrary.simpleMessage("Refunding..."),
    "tupianshangchuanshib" : MessageLookupByLibrary.simpleMessage("Failed to upload the image. Please try again."),
    "tupianyasushibaitupi" : MessageLookupByLibrary.simpleMessage("Image compression failed."),
    "unbind" : MessageLookupByLibrary.simpleMessage("解除绑定"),
    "unbindAlipay" : MessageLookupByLibrary.simpleMessage("确定要解绑支付宝免密支付吗？"),
    "unbindFail" : MessageLookupByLibrary.simpleMessage("解绑失败"),
    "unbindSuccess" : MessageLookupByLibrary.simpleMessage("解绑成功"),
    "understood" : MessageLookupByLibrary.simpleMessage("OK"),
    "unknown" : MessageLookupByLibrary.simpleMessage("Unknown"),
    "user_info" : MessageLookupByLibrary.simpleMessage("Personal info"),
    "verify_code_login" : MessageLookupByLibrary.simpleMessage("Log on by verification code"),
    "vfcode_send" : MessageLookupByLibrary.simpleMessage("Verification code sent. Please check."),
    "wancheng" : MessageLookupByLibrary.simpleMessage("Complete"),
    "wangjiliao" : MessageLookupByLibrary.simpleMessage("Forgot?"),
    "wanglaolianjieyiduan" : MessageLookupByLibrary.simpleMessage("The network is disconnected."),
    "wanglaoyichangqingsh" : MessageLookupByLibrary.simpleMessage("Network error. Please try again later."),
    "wanglaoyichangqingsh_1" : MessageLookupByLibrary.simpleMessage("Network error. Please try again later."),
    "weibaocunpinjieshipi" : MessageLookupByLibrary.simpleMessage("The splicing video is not saved."),
    "weibaozhengnindezhan" : MessageLookupByLibrary.simpleMessage("To ensure the security of your account, the following conditions must be met before the cancelation takes effect:"),
    "weibaozhengxiaoguoqi" : MessageLookupByLibrary.simpleMessage("To ensure the recording effect, please hold your phone vertically during recording."),
    "weichengweihaoyou" : MessageLookupByLibrary.simpleMessage("You are not friends yet."),
    "weichuanruwangyelian" : MessageLookupByLibrary.simpleMessage("No webpage link is found. Please check the jump settings."),
    "weifaweigui" : MessageLookupByLibrary.simpleMessage("Illegal content"),
    "weifaxianxiangyaodeV" : MessageLookupByLibrary.simpleMessage("If the desired VR headset is not found, tap Search Again."),
    "weiguanzhugaihuodong" : MessageLookupByLibrary.simpleMessage("You have not followed this activity and cannot unfollow it."),
    "weiguinichenqingchon" : MessageLookupByLibrary.simpleMessage("Invalid nickname. Please create a new one and re-submit."),
    "weikaiqidingweifuwub" : MessageLookupByLibrary.simpleMessage("The location service is not enabled. Some functions are unavailable."),
    "weilianjie" : MessageLookupByLibrary.simpleMessage("Not connected."),
    "weixin" : MessageLookupByLibrary.simpleMessage("WeChat"),
    "weixinzhifu" : MessageLookupByLibrary.simpleMessage("By WeChat"),
    "weizhaodaoxiangguanh" : MessageLookupByLibrary.simpleMessage("No related activity is found."),
    "weizhi" : MessageLookupByLibrary.simpleMessage("Unknown"),
    "weizhicuowu" : MessageLookupByLibrary.simpleMessage("Unknown error."),
    "welcome" : MessageLookupByLibrary.simpleMessage("Welcome to Play for Dream"),
    "wenjianbucunzai" : MessageLookupByLibrary.simpleMessage("The file does not exist."),
    "wenxindishiwenxintis" : MessageLookupByLibrary.simpleMessage("Kindly reminder"),
    "wodeVRyanjingwodiVRy" : MessageLookupByLibrary.simpleMessage("My headset"),
    "wodehaoyouwodihaoyou" : MessageLookupByLibrary.simpleMessage("My friend"),
    "wodeheimingdan" : MessageLookupByLibrary.simpleMessage("Blacklist"),
    "wodehuodongwodihuodo" : MessageLookupByLibrary.simpleMessage("My Activity"),
    "wodeqianbaowodiqianb" : MessageLookupByLibrary.simpleMessage("My Wallet"),
    "woyiyuedubingtongyiS" : m75,
    "woyiyuedubingtongyiY" : m76,
    "wozhidaoliao" : MessageLookupByLibrary.simpleMessage("OK"),
    "xiangcanjiaxiangcenj" : MessageLookupByLibrary.simpleMessage("I\'m interested"),
    "xiangce" : MessageLookupByLibrary.simpleMessage("Album"),
    "xiangguantiaokuanchu" : MessageLookupByLibrary.simpleMessage("according to the relevant terms of the User Privacy Policy."),
    "xiangguanyingyong" : MessageLookupByLibrary.simpleMessage("Related apps"),
    "xiangji" : MessageLookupByLibrary.simpleMessage("Camera"),
    "xiangjicuowu" : MessageLookupByLibrary.simpleMessage("camera error"),
    "xiangxiangrenshinide" : MessageLookupByLibrary.simpleMessage("Introduce yourself to people interested in you."),
    "xiaofeishijian" : MessageLookupByLibrary.simpleMessage("Transaction time:"),
    "xiaofeixiangqing" : MessageLookupByLibrary.simpleMessage("Transaction Details"),
    "xiaoshi" : MessageLookupByLibrary.simpleMessage("1 h"),
    "xiaoshi_1" : MessageLookupByLibrary.simpleMessage("3 h"),
    "xiaoshi_2" : MessageLookupByLibrary.simpleMessage("4 h"),
    "xiaoshi_3" : MessageLookupByLibrary.simpleMessage("2 h"),
    "xiaoshi_4" : MessageLookupByLibrary.simpleMessage("5 h"),
    "xiawu" : MessageLookupByLibrary.simpleMessage("p.m."),
    "xiayibu" : MessageLookupByLibrary.simpleMessage("Next"),
    "xiazaiyingyong" : MessageLookupByLibrary.simpleMessage("Download App"),
    "xing" : MessageLookupByLibrary.simpleMessage("4 stars"),
    "xing_1" : MessageLookupByLibrary.simpleMessage("1 star"),
    "xing_2" : MessageLookupByLibrary.simpleMessage("3 stars"),
    "xing_3" : MessageLookupByLibrary.simpleMessage("2 stars"),
    "xing_4" : MessageLookupByLibrary.simpleMessage("5 stars"),
    "xinzengshijianduan" : MessageLookupByLibrary.simpleMessage("Add period"),
    "xiugaiqianqingxiangu" : MessageLookupByLibrary.simpleMessage("Disable teen mode before modification."),
    "xiugaishoujihaobunen" : MessageLookupByLibrary.simpleMessage("The new mobile number cannot be the same as the old one."),
    "xuanzeshebeiqushebei" : MessageLookupByLibrary.simpleMessage("Select a device and try the app on the device."),
    "xuanzeshebeixuanzhai" : MessageLookupByLibrary.simpleMessage("Select Device"),
    "xuanzeyingyongxuanzh" : MessageLookupByLibrary.simpleMessage("Select App"),
    "xuanzezhifufangshixu" : MessageLookupByLibrary.simpleMessage("Select payment method"),
    "xuyaodenglucainengch" : MessageLookupByLibrary.simpleMessage("You can view downloaded apps only after you log in."),
    "xuyaodenglucainengch_1" : MessageLookupByLibrary.simpleMessage("You can view your app only after you log in."),
    "xuyaodenglucainengch_2" : MessageLookupByLibrary.simpleMessage("You can enter the notification center only after you log in."),
    "xuyaodenglucainenggo" : MessageLookupByLibrary.simpleMessage("You can purchase it only after you log in."),
    "xuyaodenglucainengju" : MessageLookupByLibrary.simpleMessage("You can report it only after you log in."),
    "yCoinRecord" : MessageLookupByLibrary.simpleMessage("Y币记录"),
    "yanzhengguchangshenf" : MessageLookupByLibrary.simpleMessage("Authenticate parent identity"),
    "yanzhengmabuzhengque" : MessageLookupByLibrary.simpleMessage("Incorrect verification code."),
    "yanzhengmacuowu" : MessageLookupByLibrary.simpleMessage("Incorrect verification code."),
    "yanzhengshenfen" : MessageLookupByLibrary.simpleMessage("Verify Identity"),
    "yanzhengshibai" : MessageLookupByLibrary.simpleMessage("Authentication failed."),
    "yaocanjiahuodongnixu" : m77,
    "yaoqinghaoyou" : MessageLookupByLibrary.simpleMessage("Invite Friend"),
    "yibaocun" : MessageLookupByLibrary.simpleMessage("Saved."),
    "yibaocun_1" : MessageLookupByLibrary.simpleMessage("Saved"),
    "yibaocunzhi" : MessageLookupByLibrary.simpleMessage("Saved to"),
    "yibaocunzhixiangce" : MessageLookupByLibrary.simpleMessage("Saved to the album."),
    "yibaoming" : MessageLookupByLibrary.simpleMessage("Already signed up"),
    "yicanjiagaihuodongyi" : MessageLookupByLibrary.simpleMessage("Joined in this activity"),
    "yicanjiayicenjiayisa" : MessageLookupByLibrary.simpleMessage("Already joined in"),
    "yichenggongfachuyaoq" : MessageLookupByLibrary.simpleMessage("Invitation sent."),
    "yichenggongjiaruhuod" : MessageLookupByLibrary.simpleMessage("You have joined in this activity."),
    "yichu" : MessageLookupByLibrary.simpleMessage("Delete"),
    "yichuciVRyanjing" : MessageLookupByLibrary.simpleMessage(" Delete VR Headset"),
    "yichuheimingchanchen" : MessageLookupByLibrary.simpleMessage("Unblocked."),
    "yichuheimingchanyich" : MessageLookupByLibrary.simpleMessage("Remove from Blacklist"),
    "yichuheimingdan" : MessageLookupByLibrary.simpleMessage("Remove from Blacklist"),
    "yichuhouVRjiangzaili" : MessageLookupByLibrary.simpleMessage("After this VR headset is deleted, it will automatically log out of \nthe original login account when connected to network. Are you sure you want to proceed?"),
    "yifasongqingqiuyifei" : MessageLookupByLibrary.simpleMessage("Request sent."),
    "yifeichangweihanwome" : MessageLookupByLibrary.simpleMessage("I. We regret that we will no longer provide services for you. If you still choose to cancel your account, you are deemed to automatically give up your virtual assets (including but not limited to the following rights and interests) under the account. You understand and agree that we cannot assist you in restoring the preceding services."),
    "yigoumai" : MessageLookupByLibrary.simpleMessage("Purchased"),
    "yiguanbigexinghuanar" : MessageLookupByLibrary.simpleMessage("Personalized recommendation has been disabled."),
    "yijiarugaihuodong" : MessageLookupByLibrary.simpleMessage("You have joined in this activity."),
    "yijiaruheimingchanyi" : MessageLookupByLibrary.simpleMessage("Blocked."),
    "yijieshu" : MessageLookupByLibrary.simpleMessage("Already ended"),
    "yijingguanzhugaihuod" : MessageLookupByLibrary.simpleMessage("You have already followed this activity and cannot follow it again."),
    "yilingjiang" : MessageLookupByLibrary.simpleMessage("You have received the reward."),
    "yimogengduowenjianyi" : MessageLookupByLibrary.simpleMessage("No more files."),
    "yimogengduoyingyongy" : MessageLookupByLibrary.simpleMessage("No more apps."),
    "yingyonggoumaichengg" : MessageLookupByLibrary.simpleMessage("Purchased."),
    "yingyongtuiandetongz" : MessageLookupByLibrary.simpleMessage("Notification of App Promotion"),
    "yingyongyixiajiahuob" : MessageLookupByLibrary.simpleMessage("The app has been removed or does not exist."),
    "yinsiguanli" : MessageLookupByLibrary.simpleMessage("Privacy management"),
    "yiquxiaogechengqingc" : MessageLookupByLibrary.simpleMessage("Synthesis canceled. Please record again."),
    "yituichugaihuodong" : MessageLookupByLibrary.simpleMessage("You have exited this activity."),
    "yituichugaihuodong_1" : MessageLookupByLibrary.simpleMessage("You have exited this activity."),
    "yiwan" : MessageLookupByLibrary.simpleMessage("Played"),
    "yiwanNfenzhong" : m78,
    "yiwanNxiaoshi" : m79,
    "yiwanchengdakayiwanc" : MessageLookupByLibrary.simpleMessage("You have checked in."),
    "yixiangVRyanjingfaso" : MessageLookupByLibrary.simpleMessage("A request for adding the VR headset has been sent. Go to the VR headset and click Agree."),
    "yonghuyinsizhengce" : MessageLookupByLibrary.simpleMessage("User Privacy Policy"),
    "youhuquanNzhang3youh" : m80,
    "youhuquanfafangtongz" : MessageLookupByLibrary.simpleMessage("Notification of Coupons Distribution"),
    "youhuquanyouhuiquany" : MessageLookupByLibrary.simpleMessage("coupons"),
    "youhuquanyouhuiquany_1" : MessageLookupByLibrary.simpleMessage("Coupons"),
    "youhushuyouhuishuyou" : MessageLookupByLibrary.simpleMessage("Games Owned"),
    "youxiaoqizhi" : MessageLookupByLibrary.simpleMessage("Valid until:"),
    "yuan10Ybi" : MessageLookupByLibrary.simpleMessage("CNY 1 = 10 Y Coins"),
    "yudiyicishurudemimab" : MessageLookupByLibrary.simpleMessage("Passwords do not match."),
    "yujihaixuNmiao" : m81,
    "yulantupian" : MessageLookupByLibrary.simpleMessage("Preview Image"),
    "yunxuyijiaruyonghuya" : MessageLookupByLibrary.simpleMessage("Allow joined users to invite friends"),
    "yvr_protocol" : MessageLookupByLibrary.simpleMessage("Registration by mobile number means that you have agreed to YVR"),
    "yvr_protocol2" : m82,
    "zaiVRzhongjiaru" : MessageLookupByLibrary.simpleMessage("Join in from VR"),
    "zaininshenqingzhuxia" : MessageLookupByLibrary.simpleMessage("Before you request account cancelation, please carefully read and agree to this Account Cancellation Agreement."),
    "zanbu" : MessageLookupByLibrary.simpleMessage("No"),
    "zanbugengxin" : MessageLookupByLibrary.simpleMessage("Maybe Later"),
    "zanbushiyong" : MessageLookupByLibrary.simpleMessage("Later"),
    "zanmogengduodongtaiz" : MessageLookupByLibrary.simpleMessage("No more posts."),
    "zanmohaoyouzanwuhaoy" : MessageLookupByLibrary.simpleMessage("No friend found."),
    "zanmojiebinghelubing" : MessageLookupByLibrary.simpleMessage("No screenshot or recording file available."),
    "zanmojiluzanwujilu" : MessageLookupByLibrary.simpleMessage("No record available."),
    "zanmokaquanzanwukaqu" : MessageLookupByLibrary.simpleMessage("No voucher available."),
    "zanmokeyongzanwukeyo" : MessageLookupByLibrary.simpleMessage("No voucher available."),
    "zanmoshiyongshuiming" : MessageLookupByLibrary.simpleMessage("No usage instruction is available."),
    "zanshimofahuoqushebe" : MessageLookupByLibrary.simpleMessage("Unable to obtain the device binding information."),
    "zanweigoumaiyingyong" : MessageLookupByLibrary.simpleMessage("No app has been purchased yet."),
    "zhangchanzhangdanzha" : MessageLookupByLibrary.simpleMessage("Bill"),
    "zhanghaobucunzai" : MessageLookupByLibrary.simpleMessage("The account does not exist."),
    "zhanghaodenglu" : MessageLookupByLibrary.simpleMessage("Account login"),
    "zhanghaoyishenqingzh" : MessageLookupByLibrary.simpleMessage("The account has been canceled."),
    "zhanghaoyishixiao" : MessageLookupByLibrary.simpleMessage("The account is invalid."),
    "zhanghaoyizhuxiao" : MessageLookupByLibrary.simpleMessage("The account has been canceled."),
    "zhanghaoyuanquan" : MessageLookupByLibrary.simpleMessage("Account & Security"),
    "zhanghaozhuxiaoshiba" : MessageLookupByLibrary.simpleMessage("Account cancelation failed."),
    "zhanghuyuanquan" : MessageLookupByLibrary.simpleMessage("Account & Security"),
    "zhankai" : MessageLookupByLibrary.simpleMessage("Expand"),
    "zhegeshishipindebiao" : MessageLookupByLibrary.simpleMessage("It is the video title."),
    "zhengzaiYVRdatingzhe" : MessageLookupByLibrary.simpleMessage("is in the YVR hall."),
    "zhengzaibaocun" : MessageLookupByLibrary.simpleMessage("Saving..."),
    "zhengzaichangshichon" : MessageLookupByLibrary.simpleMessage("Trying to reconnect..."),
    "zhengzaichangshilian" : MessageLookupByLibrary.simpleMessage("Trying to connect another device..."),
    "zhengzaidakaiyingyon" : MessageLookupByLibrary.simpleMessage("The app is being opened. Do not repeat this operation."),
    "zhengzaijinhangzhong" : MessageLookupByLibrary.simpleMessage("Ongoing"),
    "zhengzaijinhangzhong_1" : MessageLookupByLibrary.simpleMessage("An ongoing activity cannot be deleted."),
    "zhengzailianjieVRyan" : MessageLookupByLibrary.simpleMessage("Connecting VR headset..."),
    "zhengzailianjiezhong" : MessageLookupByLibrary.simpleMessage("Connecting..."),
    "zhengzaiqiehuanshebe" : MessageLookupByLibrary.simpleMessage("Switching device..."),
    "zhengzaiquanlisousuo" : MessageLookupByLibrary.simpleMessage("Searching for the VR headset..."),
    "zhengzaishiyongS" : m83,
    "zhengzaishiyongweizh" : MessageLookupByLibrary.simpleMessage("is using an unknown app."),
    "zhengzaishiyongyingy" : m84,
    "zhengzaisousuo" : MessageLookupByLibrary.simpleMessage("Searching..."),
    "zhengzaiweiVRyanjing" : MessageLookupByLibrary.simpleMessage("Connecting the VR headset to Wi-Fi... Please wait..."),
    "zhidaoliao" : MessageLookupByLibrary.simpleMessage("OK"),
    "zhifubaozhanghaodesh" : MessageLookupByLibrary.simpleMessage("Authenticated real name for the Bank account"),
    "zhifubaozhanghaoyong" : MessageLookupByLibrary.simpleMessage("Bank account (for receiving cashback)"),
    "zhifubaozhifu" : MessageLookupByLibrary.simpleMessage("By Alipay"),
    "zhifuchenggong" : MessageLookupByLibrary.simpleMessage("Paid."),
    "zhifufangshi" : MessageLookupByLibrary.simpleMessage("Payment method:"),
    "zhifufangshi_1" : MessageLookupByLibrary.simpleMessage("Payment method"),
    "zhifujieguo" : MessageLookupByLibrary.simpleMessage("Payment result"),
    "zhifujine" : MessageLookupByLibrary.simpleMessage("Payment amount:"),
    "zhifushibai" : MessageLookupByLibrary.simpleMessage("Payment failed."),
    "zhifushijian" : MessageLookupByLibrary.simpleMessage("Payment time:"),
    "zhiyouhuodongchuangj" : MessageLookupByLibrary.simpleMessage("Only the activity creator has this permission."),
    "zhouliuzhizhourizhou" : MessageLookupByLibrary.simpleMessage("Saturday to Sunday"),
    "zhouyizhizhouwu" : MessageLookupByLibrary.simpleMessage("Monday to Friday"),
    "zhuanti" : MessageLookupByLibrary.simpleMessage("Topic"),
    "zhuantiyiguoqihuobuc" : MessageLookupByLibrary.simpleMessage("The topic has expired or does not exist."),
    "zhutiyiguoqihuobucun" : MessageLookupByLibrary.simpleMessage("The subject has expired or does not exist."),
    "zhuxiaochenggongshou" : MessageLookupByLibrary.simpleMessage("Canceled. Your mobile phone has been unbound with the third party."),
    "zhuxiaoxieyi" : MessageLookupByLibrary.simpleMessage("Account Cancelation Agreement"),
    "zhuxiaoxieyi_1" : MessageLookupByLibrary.simpleMessage("Account Cancelation Agreement"),
    "zhuxiaozhanghao" : MessageLookupByLibrary.simpleMessage("Cancel account"),
    "zhuyireshenhetongguo" : MessageLookupByLibrary.simpleMessage("1.请再次确认信息填写是否有误，审核中无法修改信息。\n2.提交后，将生成返现流程码，请联系原销售渠道员工/客服，进行最终核对。\n3.您所提交的所有信息我们将信息保密处理，不与第三方共享，继续提交将视为您已同意向信息授权给YVR进行合法校验。"),
    "zijichuangjiandehuod" : MessageLookupByLibrary.simpleMessage("You cannot exit an activity created by yourself."),
    "zongyue" : MessageLookupByLibrary.simpleMessage("Total balance"),
    "zuiduoyunxushangchua" : m85,
    "zuijinyizhou" : MessageLookupByLibrary.simpleMessage("Recent week"),
    "zuirehuodong" : MessageLookupByLibrary.simpleMessage("Hot"),
    "zuixinhuodong" : MessageLookupByLibrary.simpleMessage("New")
  };
}
