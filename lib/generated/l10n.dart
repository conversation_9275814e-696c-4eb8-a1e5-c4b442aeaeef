// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values

class YLocal {
  YLocal();
  
  static YLocal current;
  
  static const AppLocalizationDelegate delegate =
    AppLocalizationDelegate();

  static Future<YLocal> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false) ? locale.languageCode : locale.toString();
    final localeName = Intl.canonicalizedLocale(name); 
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      YLocal.current = YLocal();
      
      return YLocal.current;
    });
  } 

  static YLocal of(BuildContext context) {
    return Localizations.of<YLocal>(context, YLocal);
  }

  /// `Purchased at {date_0}`
  String Dgoumai(Object date_0) {
    return Intl.message(
      'Purchased at $date_0',
      name: 'Dgoumai',
      desc: '',
      args: [date_0],
    );
  }

  /// ` End after {num_0}:{num_1}:{num_2}`
  String NNNhoujieshu(Object num_0, Object num_1, Object num_2) {
    return Intl.message(
      ' End after $num_0:$num_1:$num_2',
      name: 'NNNhoujieshu',
      desc: '',
      args: [num_0, num_1, num_2],
    );
  }

  /// ` End after {num_0}:{num_1} `
  String NNhoujieshu(Object num_0, Object num_1) {
    return Intl.message(
      ' End after $num_0:$num_1 ',
      name: 'NNhoujieshu',
      desc: '',
      args: [num_0, num_1],
    );
  }

  /// `{num_0} Y Coins`
  String NYbi(Object num_0) {
    return Intl.message(
      '$num_0 Y Coins',
      name: 'NYbi',
      desc: '',
      args: [num_0],
    );
  }

  /// `{num_0} Y Coins have been distributed. You can use them to redeem items in the store. Thank you for your support for YVR!`
  String NYbiyifafangkezaisha(Object num_0) {
    return Intl.message(
      '$num_0 Y Coins have been distributed. You can use them to redeem items in the store. Thank you for your support for YVR!',
      name: 'NYbiyifafangkezaisha',
      desc: '',
      args: [num_0],
    );
  }

  /// `{num_0} min`
  String Nfenzhong(Object num_0) {
    return Intl.message(
      '$num_0 min',
      name: 'Nfenzhong',
      desc: '',
      args: [num_0],
    );
  }

  /// ` End after {num_0} minutes`
  String Nfenzhonghoujieshu(Object num_0) {
    return Intl.message(
      ' End after $num_0 minutes',
      name: 'Nfenzhonghoujieshu',
      desc: '',
      args: [num_0],
    );
  }

  /// `{num_0} minutes ago`
  String Nfenzhongqian(Object num_0) {
    return Intl.message(
      '$num_0 minutes ago',
      name: 'Nfenzhongqian',
      desc: '',
      args: [num_0],
    );
  }

  /// `To end in {num_0}s`
  String Nmiaohoujieshu(Object num_0) {
    return Intl.message(
      'To end in ${num_0}s',
      name: 'Nmiaohoujieshu',
      desc: '',
      args: [num_0],
    );
  }

  /// `{num_0} users have joined in.`
  String NrencanjiaNrencenjia(Object num_0) {
    return Intl.message(
      '$num_0 users have joined in.',
      name: 'NrencanjiaNrencenjia',
      desc: '',
      args: [num_0],
    );
  }

  /// `Rated by {num_0} users`
  String Nrenpingfen(Object num_0) {
    return Intl.message(
      'Rated by $num_0 users',
      name: 'Nrenpingfen',
      desc: '',
      args: [num_0],
    );
  }

  /// `Commented by {num_0} users`
  String Nrenpinglun(Object num_0) {
    return Intl.message(
      'Commented by $num_0 users',
      name: 'Nrenpinglun',
      desc: '',
      args: [num_0],
    );
  }

  /// `{num_0} users have signed up.`
  String Nrenyibaoming(Object num_0) {
    return Intl.message(
      '$num_0 users have signed up.',
      name: 'Nrenyibaoming',
      desc: '',
      args: [num_0],
    );
  }

  /// `{num_0} users have joined in.`
  String NrenyicanjiaNrenyice(Object num_0) {
    return Intl.message(
      '$num_0 users have joined in.',
      name: 'NrenyicanjiaNrenyice',
      desc: '',
      args: [num_0],
    );
  }

  /// `{num_0}0% off`
  String NsheNzhe(Object num_0) {
    return Intl.message(
      '${num_0}0% off',
      name: 'NsheNzhe',
      desc: '',
      args: [num_0],
    );
  }

  /// `End {num_0} days later`
  String Ntianhoujieshu(Object num_0) {
    return Intl.message(
      'End $num_0 days later',
      name: 'Ntianhoujieshu',
      desc: '',
      args: [num_0],
    );
  }

  /// `{num_0} h`
  String Nxiaoshi(Object num_0) {
    return Intl.message(
      '$num_0 h',
      name: 'Nxiaoshi',
      desc: '',
      args: [num_0],
    );
  }

  /// `End after {num_0} hours`
  String Nxiaoshihoujieshu(Object num_0) {
    return Intl.message(
      'End after $num_0 hours',
      name: 'Nxiaoshihoujieshu',
      desc: '',
      args: [num_0],
    );
  }

  /// `{num_0} hours ago`
  String Nxiaoshiqian(Object num_0) {
    return Intl.message(
      '$num_0 hours ago',
      name: 'Nxiaoshiqian',
      desc: '',
      args: [num_0],
    );
  }

  /// `{num_0} star`
  String Nxing(Object num_0) {
    return Intl.message(
      '$num_0 star',
      name: 'Nxing',
      desc: '',
      args: [num_0],
    );
  }

  /// `{num_0} stars`
  String Nxing_2(Object num_0) {
    return Intl.message(
      '$num_0 stars',
      name: 'Nxing_2',
      desc: '',
      args: [num_0],
    );
  }

  /// `{num_0} comments`
  String Nzepinglun(Object num_0) {
    return Intl.message(
      '$num_0 comments',
      name: 'Nzepinglun',
      desc: '',
      args: [num_0],
    );
  }

  /// `{num_0}`
  String Nzhang(Object num_0) {
    return Intl.message(
      '$num_0',
      name: 'Nzhang',
      desc: '',
      args: [num_0],
    );
  }

  /// `{num_0} coupons for {str_0} have been distributed and you can use them to exchange for items in the store. Thank you for your support and love for YVR!`
  String NzhangSyouhuquanyifa(Object num_0, Object str_0) {
    return Intl.message(
      '$num_0 coupons for $str_0 have been distributed and you can use them to exchange for items in the store. Thank you for your support and love for YVR!',
      name: 'NzhangSyouhuquanyifa',
      desc: '',
      args: [num_0, str_0],
    );
  }

  /// `{num_0} vouchers available.`
  String Nzhangkeyong(Object num_0) {
    return Intl.message(
      '$num_0 vouchers available.',
      name: 'Nzhangkeyong',
      desc: '',
      args: [num_0],
    );
  }

  /// `PayPal`
  String get PayPal {
    return Intl.message(
      'PayPal',
      name: 'PayPal',
      desc: '',
      args: [],
    );
  }

  /// `{str_0} has been purchased. Now you can enjoy it on your VR device.`
  String Sgoumaichenggongkuai(Object str_0) {
    return Intl.message(
      '$str_0 has been purchased. Now you can enjoy it on your VR device.',
      name: 'Sgoumaichenggongkuai',
      desc: '',
      args: [str_0],
    );
  }

  /// `{str_0} This activity does not exist.`
  String Shuodongbucunzai(Object str_0) {
    return Intl.message(
      '$str_0 This activity does not exist.',
      name: 'Shuodongbucunzai',
      desc: '',
      args: [str_0],
    );
  }

  /// `{str_0} has not checked in today.`
  String SjinriweidakaSjinriw(Object str_0) {
    return Intl.message(
      '$str_0 has not checked in today.',
      name: 'SjinriweidakaSjinriw',
      desc: '',
      args: [str_0],
    );
  }

  /// `{str_0} has claimed the reward.`
  String Syilingjiang(Object str_0) {
    return Intl.message(
      '$str_0 has claimed the reward.',
      name: 'Syilingjiang',
      desc: '',
      args: [str_0],
    );
  }

  /// `{str_0} has checked in.`
  String SyiwanchengdakaSyiwa(Object str_0) {
    return Intl.message(
      '$str_0 has checked in.',
      name: 'SyiwanchengdakaSyiwa',
      desc: '',
      args: [str_0],
    );
  }

  /// `and {str_0} are mutual friends.`
  String SyuSshigongtonghaoyo(Object str_0) {
    return Intl.message(
      'and $str_0 are mutual friends.',
      name: 'SyuSshigongtonghaoyo',
      desc: '',
      args: [str_0],
    );
  }

  /// `{str_0} has not been installed in {str_1}. Install it now and try it.`
  String SzaiSzhongweian(Object str_0, Object str_1) {
    return Intl.message(
      '$str_0 has not been installed in $str_1. Install it now and try it.',
      name: 'SzaiSzhongweian',
      desc: '',
      args: [str_0, str_1],
    );
  }

  /// `The comment in {str_0} has been blocked by the system due to suspected violations. Do not make uncivil comments for the sake of a favorable community environment.`
  String Szhongdehuifuyinshex(Object str_0) {
    return Intl.message(
      'The comment in $str_0 has been blocked by the system due to suspected violations. Do not make uncivil comments for the sake of a favorable community environment.',
      name: 'Szhongdehuifuyinshex',
      desc: '',
      args: [str_0],
    );
  }

  /// `The comment in {str_0} has been blocked by the system due to suspected {str_1}. Do not make uncivil comments for the sake of a favorable community environment.`
  String Szhongdehuifuyinshex_1(Object str_0, Object str_1) {
    return Intl.message(
      'The comment in $str_0 has been blocked by the system due to suspected $str_1. Do not make uncivil comments for the sake of a favorable community environment.',
      name: 'Szhongdehuifuyinshex_1',
      desc: '',
      args: [str_0, str_1],
    );
  }

  /// `Tips: You can add it by logging in to the same account in the VR headset. You can return to the list directly without repeating the operation. You can find the SN number of device by choosing [Settings] > [General] > [About] in the VR headset. If you cannot add the VR headset, check whether relevant permissions are enabled in the Play For Dream.`
  String get TipszaiVRyanjingzhon {
    return Intl.message(
      'Tips: You can add it by logging in to the same account in the VR headset. You can return to the list directly without repeating the operation. You can find the SN number of device by choosing [Settings] > [General] > [About] in the VR headset. If you cannot add the VR headset, check whether relevant permissions are enabled in the Play For Dream.',
      name: 'TipszaiVRyanjingzhon',
      desc: '',
      args: [],
    );
  }

  /// `Projection terminated in the VR side.`
  String get VRduanyizhongzhitoub {
    return Intl.message(
      'Projection terminated in the VR side.',
      name: 'VRduanyizhongzhitoub',
      desc: '',
      args: [],
    );
  }

  /// `VR content at the top. \nMobile phone recording at the bottom.`
  String get VRnarongzhiyushangfa {
    return Intl.message(
      'VR content at the top. \nMobile phone recording at the bottom.',
      name: 'VRnarongzhiyushangfa',
      desc: '',
      args: [],
    );
  }

  /// `VR content on the left. Mobile phone recording on the right.`
  String get VRnarongzhiyuzuocens {
    return Intl.message(
      'VR content on the left. Mobile phone recording on the right.',
      name: 'VRnarongzhiyuzuocens',
      desc: '',
      args: [],
    );
  }

  /// `VR device purchase request`
  String get VRshebeigoumaiqingqi {
    return Intl.message(
      'VR device purchase request',
      name: 'VRshebeigoumaiqingqi',
      desc: '',
      args: [],
    );
  }

  /// `The VR headset will automatically download this app when it is connected to the network.`
  String get VRshebeijiangzailian {
    return Intl.message(
      'The VR headset will automatically download this app when it is connected to the network.',
      name: 'VRshebeijiangzailian',
      desc: '',
      args: [],
    );
  }

  /// `Transmitting the VR video...`
  String get VRshipinchuanshuzhon {
    return Intl.message(
      'Transmitting the VR video...',
      name: 'VRshipinchuanshuzhon',
      desc: '',
      args: [],
    );
  }

  /// `More in VR`
  String get VRweizhu {
    return Intl.message(
      'More in VR',
      name: 'VRweizhu',
      desc: '',
      args: [],
    );
  }

  /// `SN number: {str_0}`
  String VRyanjingSNhaoS(Object str_0) {
    return Intl.message(
      'SN number: $str_0',
      name: 'VRyanjingSNhaoS',
      desc: '',
      args: [str_0],
    );
  }

  /// `The VR headset is in teen mode and cannot be added again.`
  String get VRyanjingchuyuqingsh {
    return Intl.message(
      'The VR headset is in teen mode and cannot be added again.',
      name: 'VRyanjingchuyuqingsh',
      desc: '',
      args: [],
    );
  }

  /// `The VR headset is disconnected. Please connect again.`
  String get VRyanjingduankailian {
    return Intl.message(
      'The VR headset is disconnected. Please connect again.',
      name: 'VRyanjingduankailian',
      desc: '',
      args: [],
    );
  }

  /// `This file will also be deleted and cannot be restored. Are you sure you want to continue?`
  String get VRyanjingjiangtongbu {
    return Intl.message(
      'This file will also be deleted and cannot be restored. Are you sure you want to continue?',
      name: 'VRyanjingjiangtongbu',
      desc: '',
      args: [],
    );
  }

  /// `VR headset connected.`
  String get VRyanjinglianjiechen {
    return Intl.message(
      'VR headset connected.',
      name: 'VRyanjinglianjiechen',
      desc: '',
      args: [],
    );
  }

  /// `The VR headset failed to connect to the Wi-Fi. Please try again.`
  String get VRyanjingmofalianjie {
    return Intl.message(
      'The VR headset failed to connect to the Wi-Fi. Please try again.',
      name: 'VRyanjingmofalianjie',
      desc: '',
      args: [],
    );
  }

  /// `Insufficient storage of the VR headset. Please free up some space before recording.`
  String get VRyanjingnacunbuzuqi {
    return Intl.message(
      'Insufficient storage of the VR headset. Please free up some space before recording.',
      name: 'VRyanjingnacunbuzuqi',
      desc: '',
      args: [],
    );
  }

  /// `Insufficient storage of the VR headset. Please free up some space before using the dual recording function.`
  String get VRyanjingnacunbuzuqi_1 {
    return Intl.message(
      'Insufficient storage of the VR headset. Please free up some space before using the dual recording function.',
      name: 'VRyanjingnacunbuzuqi_1',
      desc: '',
      args: [],
    );
  }

  /// `The VR headset is added.`
  String get VRyanjingtianjiachen {
    return Intl.message(
      'The VR headset is added.',
      name: 'VRyanjingtianjiachen',
      desc: '',
      args: [],
    );
  }

  /// `Failed to initiate projection by VR headset.`
  String get VRyanjingtoubingshib {
    return Intl.message(
      'Failed to initiate projection by VR headset.',
      name: 'VRyanjingtoubingshib',
      desc: '',
      args: [],
    );
  }

  /// `The VR headset has terminated dual recording.`
  String get VRyanjingyizhongzhip {
    return Intl.message(
      'The VR headset has terminated dual recording.',
      name: 'VRyanjingyizhongzhip',
      desc: '',
      args: [],
    );
  }

  /// `The VR headset has terminated projection.`
  String get VRyanjingyizhongzhit {
    return Intl.message(
      'The VR headset has terminated projection.',
      name: 'VRyanjingyizhongzhit',
      desc: '',
      args: [],
    );
  }

  /// `The network of the VR headset and the mobile phone are inconsistent`
  String get VRyanjingyushoujiwan {
    return Intl.message(
      'The network of the VR headset and the mobile phone are inconsistent',
      name: 'VRyanjingyushoujiwan',
      desc: '',
      args: [],
    );
  }

  /// `1. VR app/game: {str_0}`
  String VRyingyongyouhuS1VRy(Object str_0) {
    return Intl.message(
      '1. VR app/game: $str_0',
      name: 'VRyingyongyouhuS1VRy',
      desc: '',
      args: [str_0],
    );
  }

  /// `The Wi-Fi is inconsistent`
  String get WiFibuyizhibufengong {
    return Intl.message(
      'The Wi-Fi is inconsistent',
      name: 'WiFibuyizhibufengong',
      desc: '',
      args: [],
    );
  }

  /// `Play For Dream Refund Policy`
  String get YVRRefundPolicy {
    return Intl.message(
      'Play For Dream Refund Policy',
      name: 'YVRRefundPolicy',
      desc: '',
      args: [],
    );
  }

  /// `SN number of YVR device`
  String get YVRshebeihaoSNhao {
    return Intl.message(
      'SN number of YVR device',
      name: 'YVRshebeihaoSNhao',
      desc: '',
      args: [],
    );
  }

  /// `Kindly reminder: Account cancellation is an unrecoverable operation. After the account is canceled, you will no longer be able to use the account or retrieve any content or information you have purchased, browsed or collected (even if you use the same mobile number to register again and use the YVR platform).`
  String get YVRzaicishanyidixing {
    return Intl.message(
      'Kindly reminder: Account cancellation is an unrecoverable operation. After the account is canceled, you will no longer be able to use the account or retrieve any content or information you have purchased, browsed or collected (even if you use the same mobile number to register again and use the YVR platform).',
      name: 'YVRzaicishanyidixing',
      desc: '',
      args: [],
    );
  }

  /// `Kindly reminder: Account cancellation is an unrecoverable operation. Please read this reminder carefully, and proceed only if you are sure you want to cancel the YVR account and fully understand the effect of the operation. We recommend you back up your account information before proceeding and ensure that all services related to the account have been properly handled. Once your account is canceled, we will process or anonymize your personal information according to the relevant terms of {str_0}.`
  String YVRzaicishanyidixing_1(Object str_0) {
    return Intl.message(
      'Kindly reminder: Account cancellation is an unrecoverable operation. Please read this reminder carefully, and proceed only if you are sure you want to cancel the YVR account and fully understand the effect of the operation. We recommend you back up your account information before proceeding and ensure that all services related to the account have been properly handled. Once your account is canceled, we will process or anonymize your personal information according to the relevant terms of $str_0.',
      name: 'YVRzaicishanyidixing_1',
      desc: '',
      args: [str_0],
    );
  }

  /// `YVR account`
  String get YVRzhanghao {
    return Intl.message(
      'YVR account',
      name: 'YVRzhanghao',
      desc: '',
      args: [],
    );
  }

  /// `Y Coins`
  String get Ybi {
    return Intl.message(
      'Y Coins',
      name: 'Ybi',
      desc: '',
      args: [],
    );
  }

  /// `2. Y Coins: {num_0}`
  String YbiNge(Object num_0) {
    return Intl.message(
      '2. Y Coins: $num_0',
      name: 'YbiNge',
      desc: '',
      args: [num_0],
    );
  }

  /// `Y Coins Recharge`
  String get Ybichongzhi {
    return Intl.message(
      'Y Coins Recharge',
      name: 'Ybichongzhi',
      desc: '',
      args: [],
    );
  }

  /// `YVR Top-up Service Agreement`
  String get Ybichongzhixieyi {
    return Intl.message(
      'YVR Top-up Service Agreement',
      name: 'Ybichongzhixieyi',
      desc: '',
      args: [],
    );
  }

  /// `Notification of Y Coins Distribution`
  String get YbifafangtongzhiYbif {
    return Intl.message(
      'Notification of Y Coins Distribution',
      name: 'YbifafangtongzhiYbif',
      desc: '',
      args: [],
    );
  }

  /// `Obtain Y Coins`
  String get Ybihuoqu {
    return Intl.message(
      'Obtain Y Coins',
      name: 'Ybihuoqu',
      desc: '',
      args: [],
    );
  }

  /// `Y Coins Transaction Details`
  String get Ybishouzhimingxi {
    return Intl.message(
      'Y Coins Transaction Details',
      name: 'Ybishouzhimingxi',
      desc: '',
      args: [],
    );
  }

  /// `Consume Y Coins`
  String get Ybixiaofei {
    return Intl.message(
      'Consume Y Coins',
      name: 'Ybixiaofei',
      desc: '',
      args: [],
    );
  }

  /// `Y Coins have been distributed and you can use them to exchange for items in the store. Thank you for your support and love for YVR!`
  String get Ybiyifafangkezaishan {
    return Intl.message(
      'Y Coins have been distributed and you can use them to exchange for items in the store. Thank you for your support and love for YVR!',
      name: 'Ybiyifafangkezaishan',
      desc: '',
      args: [],
    );
  }

  /// `Insufficient Y Coins balance.`
  String get Ybiyuebuzu {
    return Intl.message(
      'Insufficient Y Coins balance.',
      name: 'Ybiyuebuzu',
      desc: '',
      args: [],
    );
  }

  /// `Insufficient Y Coins balance`
  String get Ybiyuebuzuqingchongz {
    return Intl.message(
      'Insufficient Y Coins balance',
      name: 'Ybiyuebuzuqingchongz',
      desc: '',
      args: [],
    );
  }

  /// `By Y Coins`
  String get Ybizhifu {
    return Intl.message(
      'By Y Coins',
      name: 'Ybizhifu',
      desc: '',
      args: [],
    );
  }

  /// `https://apitest.yvrdream.com/yvrdvcenter/#/cancellationagreementen`
  String get account_cancellation_agreement_url_dev {
    return Intl.message(
      'https://apitest.yvrdream.com/yvrdvcenter/#/cancellationagreementen',
      name: 'account_cancellation_agreement_url_dev',
      desc: '',
      args: [],
    );
  }

  /// `https://developer.yvrdream.com/#/cancellationagreementen`
  String get account_cancellation_agreement_url_release {
    return Intl.message(
      'https://developer.yvrdream.com/#/cancellationagreementen',
      name: 'account_cancellation_agreement_url_release',
      desc: '',
      args: [],
    );
  }

  /// `Already registered? `
  String get account_yet {
    return Intl.message(
      'Already registered? ',
      name: 'account_yet',
      desc: '',
      args: [],
    );
  }

  /// `Install app`
  String get anzhuangyingyong {
    return Intl.message(
      'Install app',
      name: 'anzhuangyingyong',
      desc: '',
      args: [],
    );
  }

  /// `Version`
  String get banben {
    return Intl.message(
      'Version',
      name: 'banben',
      desc: '',
      args: [],
    );
  }

  /// `Version Update`
  String get banbengengxin {
    return Intl.message(
      'Version Update',
      name: 'banbengengxin',
      desc: '',
      args: [],
    );
  }

  /// `Save`
  String get baocun {
    return Intl.message(
      'Save',
      name: 'baocun',
      desc: '',
      args: [],
    );
  }

  /// `Saved.`
  String get baocunchenggong {
    return Intl.message(
      'Saved.',
      name: 'baocunchenggong',
      desc: '',
      args: [],
    );
  }

  /// `Saving...`
  String get baocunzhong {
    return Intl.message(
      'Saving...',
      name: 'baocunzhong',
      desc: '',
      args: [],
    );
  }

  /// `Saving`
  String get baocunzhong_1 {
    return Intl.message(
      'Saving',
      name: 'baocunzhong_1',
      desc: '',
      args: [],
    );
  }

  /// `Include:`
  String get baohan {
    return Intl.message(
      'Include:',
      name: 'baohan',
      desc: '',
      args: [],
    );
  }

  /// `Included Apps`
  String get baohanyingyong {
    return Intl.message(
      'Included Apps',
      name: 'baohanyingyong',
      desc: '',
      args: [],
    );
  }

  /// `Secret`
  String get baomi {
    return Intl.message(
      'Secret',
      name: 'baomi',
      desc: '',
      args: [],
    );
  }

  /// `Sorry, no VR headset found nearby. Please ensure the Bluetooth of the VR headset is enabled and try again.`
  String get baoqianmeiyouzhaodao {
    return Intl.message(
      'Sorry, no VR headset found nearby. Please ensure the Bluetooth of the VR headset is enabled and try again.',
      name: 'baoqianmeiyouzhaodao',
      desc: '',
      args: [],
    );
  }

  /// `local album`
  String get bendexiangcebendixia {
    return Intl.message(
      'local album',
      name: 'bendexiangcebendixia',
      desc: '',
      args: [],
    );
  }

  /// `Edit Activity`
  String get bianjihuodong {
    return Intl.message(
      'Edit Activity',
      name: 'bianjihuodong',
      desc: '',
      args: [],
    );
  }

  /// `Edit Personal Activity`
  String get bianjisirenhuodong {
    return Intl.message(
      'Edit Personal Activity',
      name: 'bianjisirenhuodong',
      desc: '',
      args: [],
    );
  }

  /// `Edit Info`
  String get bianjiziliao {
    return Intl.message(
      'Edit Info',
      name: 'bianjiziliao',
      desc: '',
      args: [],
    );
  }

  /// `Birthday`
  String get birthday {
    return Intl.message(
      'Birthday',
      name: 'birthday',
      desc: '',
      args: [],
    );
  }

  /// `Not applicable coupons: {num_0}`
  String bukeyongyouhuquanNzh(Object num_0) {
    return Intl.message(
      'Not applicable coupons: $num_0',
      name: 'bukeyongyouhuquanNzh',
      desc: '',
      args: [num_0],
    );
  }

  /// `Harassment`
  String get bushidangnarongzaoch {
    return Intl.message(
      'Harassment',
      name: 'bushidangnarongzaoch',
      desc: '',
      args: [],
    );
  }

  /// `You cannot project multiple mobile phones at the same time.`
  String get buzhichiduobushoujit {
    return Intl.message(
      'You cannot project multiple mobile phones at the same time.',
      name: 'buzhichiduobushoujit',
      desc: '',
      args: [],
    );
  }

  /// `This iPhone model is not supported.`
  String get buzhichigaiiPhonexin {
    return Intl.message(
      'This iPhone model is not supported.',
      name: 'buzhichigaiiPhonexin',
      desc: '',
      args: [],
    );
  }

  /// `Crop`
  String get caijian {
    return Intl.message(
      'Crop',
      name: 'caijian',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get cancel {
    return Intl.message(
      'Cancel',
      name: 'cancel',
      desc: '',
      args: [],
    );
  }

  /// `Complete the Daily Check-In Reward activity and you can get 50% of the purchase amount as cashback. For more information, see the activity details.`
  String get canjiabingwanchengda {
    return Intl.message(
      'Complete the Daily Check-In Reward activity and you can get 50% of the purchase amount as cashback. For more information, see the activity details.',
      name: 'canjiabingwanchengda',
      desc: '',
      args: [],
    );
  }

  /// `Incorrect parameter or empty file.`
  String get canshubuduiwenjianwe {
    return Intl.message(
      'Incorrect parameter or empty file.',
      name: 'canshubuduiwenjianwe',
      desc: '',
      args: [],
    );
  }

  /// `Parameter error.`
  String get canshucuowucenshucuo {
    return Intl.message(
      'Parameter error.',
      name: 'canshucuowucenshucuo',
      desc: '',
      args: [],
    );
  }

  /// `Accumulated time:`
  String get canyushijiancenyushi {
    return Intl.message(
      'Accumulated time:',
      name: 'canyushijiancenyushi',
      desc: '',
      args: [],
    );
  }

  /// `View All`
  String get chakanquanbuzhakanqu {
    return Intl.message(
      'View All',
      name: 'chakanquanbuzhakanqu',
      desc: '',
      args: [],
    );
  }

  /// `View Details`
  String get chakanxiangqingzhaka {
    return Intl.message(
      'View Details',
      name: 'chakanxiangqingzhaka',
      desc: '',
      args: [],
    );
  }

  /// `Re-record`
  String get chongxinluzhizhongxi {
    return Intl.message(
      'Re-record',
      name: 'chongxinluzhizhongxi',
      desc: '',
      args: [],
    );
  }

  /// `Important`
  String get chongyaozhongyao {
    return Intl.message(
      'Important',
      name: 'chongyaozhongyao',
      desc: '',
      args: [],
    );
  }

  /// `Reset password.`
  String get chongzhimimazhongzhi {
    return Intl.message(
      'Reset password.',
      name: 'chongzhimimazhongzhi',
      desc: '',
      args: [],
    );
  }

  /// `Recharge failed.`
  String get chongzhishibai {
    return Intl.message(
      'Recharge failed.',
      name: 'chongzhishibai',
      desc: '',
      args: [],
    );
  }

  /// `Create`
  String get chuangjian {
    return Intl.message(
      'Create',
      name: 'chuangjian',
      desc: '',
      args: [],
    );
  }

  /// `Create Activity`
  String get chuangjianhuodong {
    return Intl.message(
      'Create Activity',
      name: 'chuangjianhuodong',
      desc: '',
      args: [],
    );
  }

  /// `Create Personal Activity`
  String get chuangjiansirenhuodo {
    return Intl.message(
      'Create Personal Activity',
      name: 'chuangjiansirenhuodo',
      desc: '',
      args: [],
    );
  }

  /// `Help teens grow healthily in metaverse.`
  String get chuliyuanyuzhouzhong {
    return Intl.message(
      'Help teens grow healthily in metaverse.',
      name: 'chuliyuanyuzhouzhong',
      desc: '',
      args: [],
    );
  }

  /// `This is a common password for disabling teen mode and setting time lock.`
  String get cimimaweiguanbiqings {
    return Intl.message(
      'This is a common password for disabling teen mode and setting time lock.',
      name: 'cimimaweiguanbiqings',
      desc: '',
      args: [],
    );
  }

  /// `Might be stolen`
  String get cizhanghaokenengbeid {
    return Intl.message(
      'Might be stolen',
      name: 'cizhanghaokenengbeid',
      desc: '',
      args: [],
    );
  }

  /// `OK`
  String get confirm {
    return Intl.message(
      'OK',
      name: 'confirm',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to exit?`
  String get confirm_leave {
    return Intl.message(
      'Are you sure you want to exit?',
      name: 'confirm_leave',
      desc: '',
      args: [],
    );
  }

  /// `Select from album`
  String get congxiangcezhongxuan {
    return Intl.message(
      'Select from album',
      name: 'congxiangcezhongxuan',
      desc: '',
      args: [],
    );
  }

  /// `Purchase Time`
  String get consume_time {
    return Intl.message(
      'Purchase Time',
      name: 'consume_time',
      desc: '',
      args: [],
    );
  }

  /// `Store`
  String get cunchu {
    return Intl.message(
      'Store',
      name: 'cunchu',
      desc: '',
      args: [],
    );
  }

  /// `Save`
  String get cundaoxiangce {
    return Intl.message(
      'Save',
      name: 'cundaoxiangce',
      desc: '',
      args: [],
    );
  }

  /// `Infringement`
  String get cunzaijitaqinquanhan {
    return Intl.message(
      'Infringement',
      name: 'cunzaijitaqinquanhan',
      desc: '',
      args: [],
    );
  }

  /// `The sales promotion has changed. Please refresh and try again.`
  String get cuxiaohuodongfasheng {
    return Intl.message(
      'The sales promotion has changed. Please refresh and try again.',
      name: 'cuxiaohuodongfasheng',
      desc: '',
      args: [],
    );
  }

  /// `To be released`
  String get daifabudaifeibu {
    return Intl.message(
      'To be released',
      name: 'daifabudaifeibu',
      desc: '',
      args: [],
    );
  }

  /// `Daily Check-In Reward`
  String get dakafanxiandaqiafanx {
    return Intl.message(
      'Daily Check-In Reward',
      name: 'dakafanxiandaqiafanx',
      desc: '',
      args: [],
    );
  }

  /// `Join in Daily Check-In Reward to get 50% of the purchase amount as cashback`
  String get dakahuo50goujikuanxi {
    return Intl.message(
      'Join in Daily Check-In Reward to get 50% of the purchase amount as cashback',
      name: 'dakahuo50goujikuanxi',
      desc: '',
      args: [],
    );
  }

  /// `Enable`
  String get dakai {
    return Intl.message(
      'Enable',
      name: 'dakai',
      desc: '',
      args: [],
    );
  }

  /// `{str_0} opening timed out. Please try again.`
  String dakaiSchaoshiqingcho(Object str_0) {
    return Intl.message(
      '$str_0 opening timed out. Please try again.',
      name: 'dakaiSchaoshiqingcho',
      desc: '',
      args: [str_0],
    );
  }

  /// `Failed to open link`
  String get dakailianjieshibai {
    return Intl.message(
      'Failed to open link',
      name: 'dakailianjieshibai',
      desc: '',
      args: [],
    );
  }

  /// `Open the app`
  String get dakaiyingyong {
    return Intl.message(
      'Open the app',
      name: 'dakaiyingyong',
      desc: '',
      args: [],
    );
  }

  /// `The check-in data may have delay. Ensure that the VR headset is connected to the network.`
  String get dakashujuyouyidingya {
    return Intl.message(
      'The check-in data may have delay. Ensure that the VR headset is connected to the network.',
      name: 'dakashujuyouyidingya',
      desc: '',
      args: [],
    );
  }

  /// `The number of check-in days does not reach the threshold for daily check-in reward.`
  String get dakatianshumeiyoudad {
    return Intl.message(
      'The number of check-in days does not reach the threshold for daily check-in reward.',
      name: 'dakatianshumeiyoudad',
      desc: '',
      args: [],
    );
  }

  /// `When the VR headset is nearby, enable the Bluetooth of the mobile phone and the VR headset, and ensure that the Bluetooth and location permissions of the Play For Dream are enabled. When the connection fails, tap the Refresh button on the upper part of the screen to reconnect.`
  String get dangVRyanjingzaifuji {
    return Intl.message(
      'When the VR headset is nearby, enable the Bluetooth of the mobile phone and the VR headset, and ensure that the Bluetooth and location permissions of the Play For Dream are enabled. When the connection fails, tap the Refresh button on the upper part of the screen to reconnect.',
      name: 'dangVRyanjingzaifuji',
      desc: '',
      args: [],
    );
  }

  /// `The VR headset is projecting the screen to a TV. Please stop projecting and try again.`
  String get dangqianVRyanjingdia {
    return Intl.message(
      'The VR headset is projecting the screen to a TV. Please stop projecting and try again.',
      name: 'dangqianVRyanjingdia',
      desc: '',
      args: [],
    );
  }

  /// `The VR headset is recording the screen. Please stop recording and try again.`
  String get dangqianVRyanjinglub {
    return Intl.message(
      'The VR headset is recording the screen. Please stop recording and try again.',
      name: 'dangqianVRyanjinglub',
      desc: '',
      args: [],
    );
  }

  /// `The VR headset is projecting the screen. Please stop projecting and try again.`
  String get dangqianVRyanjingtou {
    return Intl.message(
      'The VR headset is projecting the screen. Please stop projecting and try again.',
      name: 'dangqianVRyanjingtou',
      desc: '',
      args: [],
    );
  }

  /// `You have logged in with the same account as the mobile phone. Now you can experience the interaction between the mobile phone and the VR headset.`
  String get dangqianVRyanjingyid {
    return Intl.message(
      'You have logged in with the same account as the mobile phone. Now you can experience the interaction between the mobile phone and the VR headset.',
      name: 'dangqianVRyanjingyid',
      desc: '',
      args: [],
    );
  }

  /// `The current coupon is not supported.`
  String get dangqianquanbuzhichi {
    return Intl.message(
      'The current coupon is not supported.',
      name: 'dangqianquanbuzhichi',
      desc: '',
      args: [],
    );
  }

  /// `The VR headset is in teen mode. You need to disable the mode in the Device tab of the app before you can try the device.`
  String get dangqianshebeichuyuq {
    return Intl.message(
      'The VR headset is in teen mode. You need to disable the mode in the Device tab of the app before you can try the device.',
      name: 'dangqianshebeichuyuq',
      desc: '',
      args: [],
    );
  }

  /// `The current device has been connected.`
  String get dangqianshebeiyilian {
    return Intl.message(
      'The current device has been connected.',
      name: 'dangqianshebeiyilian',
      desc: '',
      args: [],
    );
  }

  /// `The current network is unavailable. Please check your network settings.`
  String get dangqianwanglaobukey {
    return Intl.message(
      'The current network is unavailable. Please check your network settings.',
      name: 'dangqianwanglaobukey',
      desc: '',
      args: [],
    );
  }

  /// `No VR headset is bound.`
  String get dangqianweibangdingV {
    return Intl.message(
      'No VR headset is bound.',
      name: 'dangqianweibangdingV',
      desc: '',
      args: [],
    );
  }

  /// `The app has been removed.`
  String get dangqianyingyongyixi {
    return Intl.message(
      'The app has been removed.',
      name: 'dangqianyingyongyixi',
      desc: '',
      args: [],
    );
  }

  /// `{num_0} persons are online.`
  String dangqianzaixianrensh(Object num_0) {
    return Intl.message(
      '$num_0 persons are online.',
      name: 'dangqianzaixianrensh',
      desc: '',
      args: [num_0],
    );
  }

  /// `The virtual assets under this account include but not limited to:`
  String get dangqianzhanghaoyong {
    return Intl.message(
      'The virtual assets under this account include but not limited to:',
      name: 'dangqianzhanghaoyong',
      desc: '',
      args: [],
    );
  }

  /// `An activity is being edited. If you exit, the edited content will be lost.`
  String get dangqianzhengzaibian {
    return Intl.message(
      'An activity is being edited. If you exit, the edited content will be lost.',
      name: 'dangqianzhengzaibian',
      desc: '',
      args: [],
    );
  }

  /// `An activity is being created. If you exit, all content will be lost.`
  String get dangqianzhengzaichua {
    return Intl.message(
      'An activity is being created. If you exit, all content will be lost.',
      name: 'dangqianzhengzaichua',
      desc: '',
      args: [],
    );
  }

  /// `Hall`
  String get datingdaiting {
    return Intl.message(
      'Hall',
      name: 'datingdaiting',
      desc: '',
      args: [],
    );
  }

  /// `Log In`
  String get denglu {
    return Intl.message(
      'Log In',
      name: 'denglu',
      desc: '',
      args: [],
    );
  }

  /// `The login password has been changed.`
  String get denglumimaxiugaichen {
    return Intl.message(
      'The login password has been changed.',
      name: 'denglumimaxiugaichen',
      desc: '',
      args: [],
    );
  }

  /// `Failed to change the login password.`
  String get denglumimaxiugaishib {
    return Intl.message(
      'Failed to change the login password.',
      name: 'denglumimaxiugaishib',
      desc: '',
      args: [],
    );
  }

  /// `Login failed.`
  String get denglushibai {
    return Intl.message(
      'Login failed.',
      name: 'denglushibai',
      desc: '',
      args: [],
    );
  }

  /// `Add device`
  String get dev_add_dev {
    return Intl.message(
      'Add device',
      name: 'dev_add_dev',
      desc: '',
      args: [],
    );
  }

  /// `1. Tap "Search for Nearby VR Headset" down below.\n\n2. Tap a VR headset name. \n\n3. After the selected VR headset is successfully added, it will automatically log in to the current account, and you can use projection, screenshot, screen recording and other features.`
  String get dev_add_remind {
    return Intl.message(
      '1. Tap "Search for Nearby VR Headset" down below.\n\n2. Tap a VR headset name. \n\n3. After the selected VR headset is successfully added, it will automatically log in to the current account, and you can use projection, screenshot, screen recording and other features.',
      name: 'dev_add_remind',
      desc: '',
      args: [],
    );
  }

  /// `Pairing guide`
  String get dev_ble_lead {
    return Intl.message(
      'Pairing guide',
      name: 'dev_ble_lead',
      desc: '',
      args: [],
    );
  }

  /// `Please log in to your account in VR first`
  String get please_add_dev {
    return Intl.message(
      'Please log in to your account in VR first',
      name: 'please_add_dev',
      desc: '',
      args: [],
    );
  }

  /// `Devices available for pairing`
  String get dev_can_connect {
    return Intl.message(
      'Devices available for pairing',
      name: 'dev_can_connect',
      desc: '',
      args: [],
    );
  }

  /// `Connected`
  String get dev_connected {
    return Intl.message(
      'Connected',
      name: 'dev_connected',
      desc: '',
      args: [],
    );
  }

  /// `Connecting...`
  String get dev_connecting {
    return Intl.message(
      'Connecting...',
      name: 'dev_connecting',
      desc: '',
      args: [],
    );
  }

  /// `Change device name`
  String get dev_modify {
    return Intl.message(
      'Change device name',
      name: 'dev_modify',
      desc: '',
      args: [],
    );
  }

  /// `My VR devices`
  String get dev_my_vr {
    return Intl.message(
      'My VR devices',
      name: 'dev_my_vr',
      desc: '',
      args: [],
    );
  }

  /// `Nearby`
  String get dev_nearby {
    return Intl.message(
      'Nearby',
      name: 'dev_nearby',
      desc: '',
      args: [],
    );
  }

  /// `Connect to VR glasses before you can use the feature.`
  String get dev_need_vr {
    return Intl.message(
      'Connect to VR glasses before you can use the feature.',
      name: 'dev_need_vr',
      desc: '',
      args: [],
    );
  }

  /// `Not connected`
  String get dev_not_connected {
    return Intl.message(
      'Not connected',
      name: 'dev_not_connected',
      desc: '',
      args: [],
    );
  }

  /// `Bluetooth not enabled. Failed to find VR glasses.`
  String get dev_open_bluetooth {
    return Intl.message(
      'Bluetooth not enabled. Failed to find VR glasses.',
      name: 'dev_open_bluetooth',
      desc: '',
      args: [],
    );
  }

  /// `Projection`
  String get dev_proj {
    return Intl.message(
      'Projection',
      name: 'dev_proj',
      desc: '',
      args: [],
    );
  }

  /// `1. If all projection attempts failed, check whether the VR headset is connected to Wi-Fi and try again.`
  String get dev_proj_desc {
    return Intl.message(
      '1. If all projection attempts failed, check whether the VR headset is connected to Wi-Fi and try again.',
      name: 'dev_proj_desc',
      desc: '',
      args: [],
    );
  }

  /// `Projected From`
  String get dev_proj_from {
    return Intl.message(
      'Projected From',
      name: 'dev_proj_from',
      desc: '',
      args: [],
    );
  }

  /// `Projected To`
  String get dev_proj_to {
    return Intl.message(
      'Projected To',
      name: 'dev_proj_to',
      desc: '',
      args: [],
    );
  }

  /// `Search again`
  String get dev_research {
    return Intl.message(
      'Search again',
      name: 'dev_research',
      desc: '',
      args: [],
    );
  }

  /// `Search for devices nearby`
  String get dev_search_ble {
    return Intl.message(
      'Search for devices nearby',
      name: 'dev_search_ble',
      desc: '',
      args: [],
    );
  }

  /// `Searching for devices nearby...`
  String get dev_searching {
    return Intl.message(
      'Searching for devices nearby...',
      name: 'dev_searching',
      desc: '',
      args: [],
    );
  }

  /// `Select device`
  String get dev_sel_dev {
    return Intl.message(
      'Select device',
      name: 'dev_sel_dev',
      desc: '',
      args: [],
    );
  }

  /// `Set`
  String get dev_setting {
    return Intl.message(
      'Set',
      name: 'dev_setting',
      desc: '',
      args: [],
    );
  }

  /// `The feature is unavailable.`
  String get dev_unavailable_func {
    return Intl.message(
      'The feature is unavailable.',
      name: 'dev_unavailable_func',
      desc: '',
      args: [],
    );
  }

  /// `Row {num_0}`
  String diNhangdiNhengdiNxin(Object num_0) {
    return Intl.message(
      'Row $num_0',
      name: 'diNhangdiNhengdiNxin',
      desc: '',
      args: [num_0],
    );
  }

  /// `Row {num_0}`
  String diNhangdiNhengdiNxin_1(Object num_0) {
    return Intl.message(
      'Row $num_0',
      name: 'diNhangdiNhengdiNxin_1',
      desc: '',
      args: [num_0],
    );
  }

  /// `Click to view activity details.`
  String get dianjichakanhuodongx {
    return Intl.message(
      'Click to view activity details.',
      name: 'dianjichakanhuodongx',
      desc: '',
      args: [],
    );
  }

  /// `Click Log On/Sign Up`
  String get dianjidengluzhuce {
    return Intl.message(
      'Click Log On/Sign Up',
      name: 'dianjidengluzhuce',
      desc: '',
      args: [],
    );
  }

  /// `同意并提交`
  String get dijiaotijiao {
    return Intl.message(
      '同意并提交',
      name: 'dijiaotijiao',
      desc: '',
      args: [],
    );
  }

  /// `Order placement time`
  String get dingchangoumaishijia {
    return Intl.message(
      'Order placement time',
      name: 'dingchangoumaishijia',
      desc: '',
      args: [],
    );
  }

  /// `Order No.:`
  String get dingchanhaodingdanha {
    return Intl.message(
      'Order No.:',
      name: 'dingchanhaodingdanha',
      desc: '',
      args: [],
    );
  }

  /// `Order No.`
  String get dingchanhaodingdanha_1 {
    return Intl.message(
      'Order No.',
      name: 'dingchanhaodingdanha_1',
      desc: '',
      args: [],
    );
  }

  /// `Log on directly`
  String get direct_login {
    return Intl.message(
      'Log on directly',
      name: 'direct_login',
      desc: '',
      args: [],
    );
  }

  /// `Prompt`
  String get dishitishi {
    return Intl.message(
      'Prompt',
      name: 'dishitishi',
      desc: '',
      args: [],
    );
  }

  /// `Obscene and profane content`
  String get disuruma {
    return Intl.message(
      'Obscene and profane content',
      name: 'disuruma',
      desc: '',
      args: [],
    );
  }

  /// `Later`
  String get do_it_later {
    return Intl.message(
      'Later',
      name: 'do_it_later',
      desc: '',
      args: [],
    );
  }

  /// `Douyin`
  String get douyin {
    return Intl.message(
      'Douyin',
      name: 'douyin',
      desc: '',
      args: [],
    );
  }

  /// `Douyin ID`
  String get douyinhao {
    return Intl.message(
      'Douyin ID',
      name: 'douyinhao',
      desc: '',
      args: [],
    );
  }

  /// `Download now?`
  String get downloadNow {
    return Intl.message(
      'Download now?',
      name: 'downloadNow',
      desc: '',
      args: [],
    );
  }

  /// `If you tap Download Now, the VR headset will automatically download this app when it is connected to the network.`
  String get downloadText {
    return Intl.message(
      'If you tap Download Now, the VR headset will automatically download this app when it is connected to the network.',
      name: 'downloadText',
      desc: '',
      args: [],
    );
  }

  /// `Redeem`
  String get duihuan {
    return Intl.message(
      'Redeem',
      name: 'duihuan',
      desc: '',
      args: [],
    );
  }

  /// `Redeemed.`
  String get duihuanchenggong {
    return Intl.message(
      'Redeemed.',
      name: 'duihuanchenggong',
      desc: '',
      args: [],
    );
  }

  /// `Email address`
  String get email_address {
    return Intl.message(
      'Email address',
      name: 'email_address',
      desc: '',
      args: [],
    );
  }

  /// `II. To help you complete account cancelation, you should undertake to YVR that:`
  String get erweiliaobangchuninw {
    return Intl.message(
      'II. To help you complete account cancelation, you should undertake to YVR that:',
      name: 'erweiliaobangchuninw',
      desc: '',
      args: [],
    );
  }

  /// `Send`
  String get fabiaofeibiao {
    return Intl.message(
      'Send',
      name: 'fabiaofeibiao',
      desc: '',
      args: [],
    );
  }

  /// `Share your comments.`
  String get fabiaoyixianidekanfa {
    return Intl.message(
      'Share your comments.',
      name: 'fabiaoyixianidekanfa',
      desc: '',
      args: [],
    );
  }

  /// `Anti-addiction by limiting usage period. `
  String get fangchenmikongzhishi {
    return Intl.message(
      'Anti-addiction by limiting usage period. ',
      name: 'fangchenmikongzhishi',
      desc: '',
      args: [],
    );
  }

  /// `Do not save the spliced video.`
  String get fangqibaocundangqian {
    return Intl.message(
      'Do not save the spliced video.',
      name: 'fangqibaocundangqian',
      desc: '',
      args: [],
    );
  }

  /// `Discard this comment.`
  String get fangqidangqianpinglu {
    return Intl.message(
      'Discard this comment.',
      name: 'fangqidangqianpinglu',
      desc: '',
      args: [],
    );
  }

  /// `Previous`
  String get fanhuishangyiye {
    return Intl.message(
      'Previous',
      name: 'fanhuishangyiye',
      desc: '',
      args: [],
    );
  }

  /// `Failed to initiate a refund.`
  String get faqituikuanshibaifei {
    return Intl.message(
      'Failed to initiate a refund.',
      name: 'faqituikuanshibaifei',
      desc: '',
      args: [],
    );
  }

  /// `An error occurred. Please add the error code in the upper part.`
  String get faxiancuowuqingzaish {
    return Intl.message(
      'An error occurred. Please add the error code in the upper part.',
      name: 'faxiancuowuqingzaish',
      desc: '',
      args: [],
    );
  }

  /// `Find friends with similar interests.`
  String get faxiantongxingcuhaoy {
    return Intl.message(
      'Find friends with similar interests.',
      name: 'faxiantongxingcuhaoy',
      desc: '',
      args: [],
    );
  }

  /// `During the unavailable period, the VR headset will be locked. You can modify the available period of the current day on the teen guardian tool page.`
  String get feishiyongshiduanVRy {
    return Intl.message(
      'During the unavailable period, the VR headset will be locked. You can modify the available period of the current day on the teen guardian tool page.',
      name: 'feishiyongshiduanVRy',
      desc: '',
      args: [],
    );
  }

  /// `Female`
  String get female {
    return Intl.message(
      'Female',
      name: 'female',
      desc: '',
      args: [],
    );
  }

  /// `30 min`
  String get fenzhong {
    return Intl.message(
      '30 min',
      name: 'fenzhong',
      desc: '',
      args: [],
    );
  }

  /// `90 min`
  String get fenzhong_1 {
    return Intl.message(
      '90 min',
      name: 'fenzhong_1',
      desc: '',
      args: [],
    );
  }

  /// `Forgot password?`
  String get forget_pwd {
    return Intl.message(
      'Forgot password?',
      name: 'forget_pwd',
      desc: '',
      args: [],
    );
  }

  /// `Server request failed.`
  String get fuwuqiqingqiushibai {
    return Intl.message(
      'Server request failed.',
      name: 'fuwuqiqingqiushibai',
      desc: '',
      args: [],
    );
  }

  /// `Service Agreement and Privacy Policy`
  String get fuwuxieyiheyinsizhen {
    return Intl.message(
      'Service Agreement and Privacy Policy',
      name: 'fuwuxieyiheyinsizhen',
      desc: '',
      args: [],
    );
  }

  /// `This device does not exist.`
  String get gaishebeibucunzai {
    return Intl.message(
      'This device does not exist.',
      name: 'gaishebeibucunzai',
      desc: '',
      args: [],
    );
  }

  /// `You have already applied for daily check-in reward. Do not repeat the operation.`
  String get gaishebeiyijingshenq {
    return Intl.message(
      'You have already applied for daily check-in reward. Do not repeat the operation.',
      name: 'gaishebeiyijingshenq',
      desc: '',
      args: [],
    );
  }

  /// `This affects only the display mode of the final splicing video.`
  String get gaixuanzejinyingxian {
    return Intl.message(
      'This affects only the display mode of the final splicing video.',
      name: 'gaixuanzejinyingxian',
      desc: '',
      args: [],
    );
  }

  /// `This app has not been purchased yet.`
  String get gaiyingyongmeiyougou {
    return Intl.message(
      'This app has not been purchased yet.',
      name: 'gaiyingyongmeiyougou',
      desc: '',
      args: [],
    );
  }

  /// `This app is being refunded and cannot be opened.`
  String get gaiyingyongtuikuanzh {
    return Intl.message(
      'This app is being refunded and cannot be opened.',
      name: 'gaiyingyongtuikuanzh',
      desc: '',
      args: [],
    );
  }

  /// `4. Your friends under this account will not be able to find you through this account.`
  String get gaizhanghaodehaoyouj {
    return Intl.message(
      '4. Your friends under this account will not be able to find you through this account.',
      name: 'gaizhanghaodehaoyouj',
      desc: '',
      args: [],
    );
  }

  /// `3. All personal data and historical information of the account (including but not limited to avatar, user name, published content, browsing records, following records, and favorites) will be unrecoverable.`
  String get gaizhanghaodequanbug {
    return Intl.message(
      '3. All personal data and historical information of the account (including but not limited to avatar, user name, published content, browsing records, following records, and favorites) will be unrecoverable.',
      name: 'gaizhanghaodequanbug',
      desc: '',
      args: [],
    );
  }

  /// `1. This account is registered through the official YVR channel for your own use.`
  String get gaizhanghaojitongguo {
    return Intl.message(
      '1. This account is registered through the official YVR channel for your own use.',
      name: 'gaizhanghaojitongguo',
      desc: '',
      args: [],
    );
  }

  /// `1. This account is registered through the official YVR channel for your own use.`
  String get gaizhanghaojitongton {
    return Intl.message(
      '1. This account is registered through the official YVR channel for your own use.',
      name: 'gaizhanghaojitongton',
      desc: '',
      args: [],
    );
  }

  /// `5. The levels, points and benefits you have earned under the account will be cleared.`
  String get gaizhanghaoleijidede {
    return Intl.message(
      '5. The levels, points and benefits you have earned under the account will be cleared.',
      name: 'gaizhanghaoleijidede',
      desc: '',
      args: [],
    );
  }

  /// `2. There are no unfinished transactions in the account.`
  String get gaizhanghaonamofachu {
    return Intl.message(
      '2. There are no unfinished transactions in the account.',
      name: 'gaizhanghaonamofachu',
      desc: '',
      args: [],
    );
  }

  /// `2. There are no unfinished transactions in the account.`
  String get gaizhanghaonamoweich {
    return Intl.message(
      '2. There are no unfinished transactions in the account.',
      name: 'gaizhanghaonamoweich',
      desc: '',
      args: [],
    );
  }

  /// `This account has been canceled and will be deleted in {str_0}. If you continue to log in, account cancelation will be withdrawn.`
  String gaizhanghaoyishenqin(Object str_0) {
    return Intl.message(
      'This account has been canceled and will be deleted in $str_0. If you continue to log in, account cancelation will be withdrawn.',
      name: 'gaizhanghaoyishenqin',
      desc: '',
      args: [str_0],
    );
  }

  /// `Just now`
  String get ganggang {
    return Intl.message(
      'Just now',
      name: 'ganggang',
      desc: '',
      args: [],
    );
  }

  /// `Thanks for your support. We will handle your report as soon as possible.`
  String get ganxienindezhichinwo {
    return Intl.message(
      'Thanks for your support. We will handle your report as soon as possible.',
      name: 'ganxienindezhichinwo',
      desc: '',
      args: [],
    );
  }

  /// `Synthesis failed. Please record again.`
  String get hechengshibai {
    return Intl.message(
      'Synthesis failed. Please record again.',
      name: 'hechengshibai',
      desc: '',
      args: [],
    );
  }

  /// `Gender`
  String get gender {
    return Intl.message(
      'Gender',
      name: 'gender',
      desc: '',
      args: [],
    );
  }

  /// `Expand`
  String get gengduo {
    return Intl.message(
      'Expand',
      name: 'gengduo',
      desc: '',
      args: [],
    );
  }

  /// `Change Background`
  String get genghuanbeijing {
    return Intl.message(
      'Change Background',
      name: 'genghuanbeijing',
      desc: '',
      args: [],
    );
  }

  /// `Change Mobile Number`
  String get genghuanshoujihao {
    return Intl.message(
      'Change Mobile Number',
      name: 'genghuanshoujihao',
      desc: '',
      args: [],
    );
  }

  /// `Request verification code`
  String get get_verify_code {
    return Intl.message(
      'Request verification code',
      name: 'get_verify_code',
      desc: '',
      args: [],
    );
  }

  /// `Failed to request the verification code.`
  String get get_vfcode_failed {
    return Intl.message(
      'Failed to request the verification code.',
      name: 'get_vfcode_failed',
      desc: '',
      args: [],
    );
  }

  /// `Personalized recommendation`
  String get gexinghuanarongtuiji {
    return Intl.message(
      'Personalized recommendation',
      name: 'gexinghuanarongtuiji',
      desc: '',
      args: [],
    );
  }

  /// `Bio`
  String get gexingqianming {
    return Intl.message(
      'Bio',
      name: 'gexingqianming',
      desc: '',
      args: [],
    );
  }

  /// `Mutual friend`
  String get gongtonghaoyou {
    return Intl.message(
      'Mutual friend',
      name: 'gongtonghaoyou',
      desc: '',
      args: [],
    );
  }

  /// `You have checked in today.`
  String get gongxininjinridakach {
    return Intl.message(
      'You have checked in today.',
      name: 'gongxininjinridakach',
      desc: '',
      args: [],
    );
  }

  /// `Congratulations. Your daily check-in reward has been granted based on the information you entered in the Play For Dream. For any doubt, contact the customer service or dial 400-618-1160.`
  String get gongxininnindedakahu {
    return Intl.message(
      'Congratulations. Your daily check-in reward has been granted based on the information you entered in the Play For Dream. For any doubt, contact the customer service or dial 400-618-1160.',
      name: 'gongxininnindedakahu',
      desc: '',
      args: [],
    );
  }

  /// `Purchase`
  String get goumai {
    return Intl.message(
      'Purchase',
      name: 'goumai',
      desc: '',
      args: [],
    );
  }

  /// `Purchase {str_0}`
  String goumaiS(Object str_0) {
    return Intl.message(
      'Purchase $str_0',
      name: 'goumaiS',
      desc: '',
      args: [str_0],
    );
  }

  /// `Purchase App`
  String get goumaiyingyong {
    return Intl.message(
      'Purchase App',
      name: 'goumaiyingyong',
      desc: '',
      args: [],
    );
  }

  /// `Before purchasing an app, go to Device to add a YVR device.`
  String get goumaiyingyongqianqi {
    return Intl.message(
      'Before purchasing an app, go to Device to add a YVR device.',
      name: 'goumaiyingyongqianqi',
      desc: '',
      args: [],
    );
  }

  /// `Disable`
  String get guanbi {
    return Intl.message(
      'Disable',
      name: 'guanbi',
      desc: '',
      args: [],
    );
  }

  /// `If disabled, you will not receive personalized recommendations. We recommend you enable it so that you can access more interesting content.`
  String get guanbihoujiangmofash {
    return Intl.message(
      'If disabled, you will not receive personalized recommendations. We recommend you enable it so that you can access more interesting content.',
      name: 'guanbihoujiangmofash',
      desc: '',
      args: [],
    );
  }

  /// `Official`
  String get guanfang {
    return Intl.message(
      'Official',
      name: 'guanfang',
      desc: '',
      args: [],
    );
  }

  /// `Official Activity`
  String get guanfanghuodong {
    return Intl.message(
      'Official Activity',
      name: 'guanfanghuodong',
      desc: '',
      args: [],
    );
  }

  /// `The official activity does not exist.`
  String get guanfanghuodongbucun {
    return Intl.message(
      'The official activity does not exist.',
      name: 'guanfanghuodongbucun',
      desc: '',
      args: [],
    );
  }

  /// `Manage the available period to prevent gaming addiction.`
  String get guanlishiyongshijian {
    return Intl.message(
      'Manage the available period to prevent gaming addiction.',
      name: 'guanlishiyongshijian',
      desc: '',
      args: [],
    );
  }

  /// `Rule Details`
  String get guizexiangqing {
    return Intl.message(
      'Rule Details',
      name: 'guizexiangqing',
      desc: '',
      args: [],
    );
  }

  /// `No friend yet. Add some now.`
  String get haimeiyouhaoyoukuaiq {
    return Intl.message(
      'No friend yet. Add some now.',
      name: 'haimeiyouhaoyoukuaiq',
      desc: '',
      args: [],
    );
  }

  /// `No dynamics yet.`
  String get haimeiyourenhedongta {
    return Intl.message(
      'No dynamics yet.',
      name: 'haimeiyourenhedongta',
      desc: '',
      args: [],
    );
  }

  /// `Friend`
  String get haoyou {
    return Intl.message(
      'Friend',
      name: 'haoyou',
      desc: '',
      args: [],
    );
  }

  /// `Friend added.`
  String get haoyouyijingtianjia {
    return Intl.message(
      'Friend added.',
      name: 'haoyouyijingtianjia',
      desc: '',
      args: [],
    );
  }

  /// `Form 16:9 video`
  String get hechenghengpingshipi {
    return Intl.message(
      'Form 16:9 video',
      name: 'hechenghengpingshipi',
      desc: '',
      args: [],
    );
  }

  /// `Form 9:16 video`
  String get hechengshupingshipin {
    return Intl.message(
      'Form 9:16 video',
      name: 'hechengshupingshipin',
      desc: '',
      args: [],
    );
  }

  /// `and`
  String get hehuhuo {
    return Intl.message(
      'and',
      name: 'hehuhuo',
      desc: '',
      args: [],
    );
  }

  /// `Blacklist`
  String get heimingchanheimingda {
    return Intl.message(
      'Blacklist',
      name: 'heimingchanheimingda',
      desc: '',
      args: [],
    );
  }

  /// `Sorry. The app you are searching for does not exist.`
  String get henbaoqianmeiyouzhao {
    return Intl.message(
      'Sorry. The app you are searching for does not exist.',
      name: 'henbaoqianmeiyouzhao',
      desc: '',
      args: [],
    );
  }

  /// `Landscape`
  String get hengbinghengping {
    return Intl.message(
      'Landscape',
      name: 'hengbinghengping',
      desc: '',
      args: [],
    );
  }

  /// `Landscape Splicing`
  String get hengbingpinjiehengpi {
    return Intl.message(
      'Landscape Splicing',
      name: 'hengbingpinjiehengpi',
      desc: '',
      args: [],
    );
  }

  /// `Yes`
  String get henxinfangqi {
    return Intl.message(
      'Yes',
      name: 'henxinfangqi',
      desc: '',
      args: [],
    );
  }

  /// `Action`
  String get home_action_adventure {
    return Intl.message(
      'Action',
      name: 'home_action_adventure',
      desc: '',
      args: [],
    );
  }

  /// `All apps`
  String get home_all_app {
    return Intl.message(
      'All apps',
      name: 'home_all_app',
      desc: '',
      args: [],
    );
  }

  /// `All types`
  String get home_all_categories {
    return Intl.message(
      'All types',
      name: 'home_all_categories',
      desc: '',
      args: [],
    );
  }

  /// `All prices`
  String get home_all_prices {
    return Intl.message(
      'All prices',
      name: 'home_all_prices',
      desc: '',
      args: [],
    );
  }

  /// `All types`
  String get home_all_types {
    return Intl.message(
      'All types',
      name: 'home_all_types',
      desc: '',
      args: [],
    );
  }

  /// `App`
  String get home_app {
    return Intl.message(
      'App',
      name: 'home_app',
      desc: '',
      args: [],
    );
  }

  /// `App info`
  String get home_app_info {
    return Intl.message(
      'App info',
      name: 'home_app_info',
      desc: '',
      args: [],
    );
  }

  /// `50-100`
  String get home_below_100 {
    return Intl.message(
      '50-100',
      name: 'home_below_100',
      desc: '',
      args: [],
    );
  }

  /// `Under 50`
  String get home_below_50 {
    return Intl.message(
      'Under 50',
      name: 'home_below_50',
      desc: '',
      args: [],
    );
  }

  /// `Cartoon`
  String get home_cartoon_animation {
    return Intl.message(
      'Cartoon',
      name: 'home_cartoon_animation',
      desc: '',
      args: [],
    );
  }

  /// `Leisure`
  String get home_casual_creativity {
    return Intl.message(
      'Leisure',
      name: 'home_casual_creativity',
      desc: '',
      args: [],
    );
  }

  /// `Role-play`
  String get home_cosplay {
    return Intl.message(
      'Role-play',
      name: 'home_cosplay',
      desc: '',
      args: [],
    );
  }

  /// `Recommended`
  String get home_recommended_sorting {
    return Intl.message(
      'Recommended',
      name: 'home_recommended_sorting',
      desc: '',
      args: [],
    );
  }

  /// `Free`
  String get home_free {
    return Intl.message(
      'Free',
      name: 'home_free',
      desc: '',
      args: [],
    );
  }

  /// `Game`
  String get home_game {
    return Intl.message(
      'Game',
      name: 'home_game',
      desc: '',
      args: [],
    );
  }

  /// `Did not find a suitable application, go to the application center to have a look`
  String get home_go_appcenter {
    return Intl.message(
      'Did not find a suitable application, go to the application center to have a look',
      name: 'home_go_appcenter',
      desc: '',
      args: [],
    );
  }

  /// `Go now`
  String get home_go_now {
    return Intl.message(
      'Go now',
      name: 'home_go_now',
      desc: '',
      args: [],
    );
  }

  /// `Most expensive`
  String get home_highest_price {
    return Intl.message(
      'Most expensive',
      name: 'home_highest_price',
      desc: '',
      args: [],
    );
  }

  /// `Popular downloads`
  String get home_hot_download {
    return Intl.message(
      'Popular downloads',
      name: 'home_hot_download',
      desc: '',
      args: [],
    );
  }

  /// `Cheapest`
  String get home_lowest_price {
    return Intl.message(
      'Cheapest',
      name: 'home_lowest_price',
      desc: '',
      args: [],
    );
  }

  /// `Rating`
  String get home_mark {
    return Intl.message(
      'Rating',
      name: 'home_mark',
      desc: '',
      args: [],
    );
  }

  /// `Multimedia`
  String get home_media_entertainment {
    return Intl.message(
      'Multimedia',
      name: 'home_media_entertainment',
      desc: '',
      args: [],
    );
  }

  /// `Above 100`
  String get home_more_100 {
    return Intl.message(
      'Above 100',
      name: 'home_more_100',
      desc: '',
      args: [],
    );
  }

  /// `Multiplayer`
  String get home_multiplayer_game {
    return Intl.message(
      'Multiplayer',
      name: 'home_multiplayer_game',
      desc: '',
      args: [],
    );
  }

  /// `My Applications`
  String get home_my_app {
    return Intl.message(
      'My Applications',
      name: 'home_my_app',
      desc: '',
      args: [],
    );
  }

  /// `Notification Center`
  String get home_noti_center {
    return Intl.message(
      'Notification Center',
      name: 'home_noti_center',
      desc: '',
      args: [],
    );
  }

  /// `Office tool`
  String get home_office_tools {
    return Intl.message(
      'Office tool',
      name: 'home_office_tools',
      desc: '',
      args: [],
    );
  }

  /// `{num_0} comment(s)`
  String home_person_com(Object num_0) {
    return Intl.message(
      '$num_0 comment(s)',
      name: 'home_person_com',
      desc: '',
      args: [num_0],
    );
  }

  /// `Latest release`
  String get home_recently_released {
    return Intl.message(
      'Latest release',
      name: 'home_recently_released',
      desc: '',
      args: [],
    );
  }

  /// `Latest update`
  String get home_recently_update {
    return Intl.message(
      'Latest update',
      name: 'home_recently_update',
      desc: '',
      args: [],
    );
  }

  /// `Search records`
  String get home_search_history {
    return Intl.message(
      'Search records',
      name: 'home_search_history',
      desc: '',
      args: [],
    );
  }

  /// `Enter keyword search`
  String get home_search_plhd {
    return Intl.message(
      'Enter keyword search',
      name: 'home_search_plhd',
      desc: '',
      args: [],
    );
  }

  /// `Social network`
  String get home_social_network {
    return Intl.message(
      'Social network',
      name: 'home_social_network',
      desc: '',
      args: [],
    );
  }

  /// `Star`
  String get home_star {
    return Intl.message(
      'Star',
      name: 'home_star',
      desc: '',
      args: [],
    );
  }

  /// `Shooting`
  String get home_stim_shot {
    return Intl.message(
      'Shooting',
      name: 'home_stim_shot',
      desc: '',
      args: [],
    );
  }

  /// `Horror`
  String get home_thrilling {
    return Intl.message(
      'Horror',
      name: 'home_thrilling',
      desc: '',
      args: [],
    );
  }

  /// `View all`
  String get home_view_all {
    return Intl.message(
      'View all',
      name: 'home_view_all',
      desc: '',
      args: [],
    );
  }

  /// `View all comments`
  String get home_view_all_comments {
    return Intl.message(
      'View all comments',
      name: 'home_view_all_comments',
      desc: '',
      args: [],
    );
  }

  /// `The backend interface needs to be compatible with the {str_0} field.`
  String houduanjiekouxujianr(Object str_0) {
    return Intl.message(
      'The backend interface needs to be compatible with the $str_0 field.',
      name: 'houduanjiekouxujianr',
      desc: '',
      args: [str_0],
    );
  }

  /// `Ignore`
  String get hulve {
    return Intl.message(
      'Ignore',
      name: 'hulve',
      desc: '',
      args: [],
    );
  }

  /// `MRC`
  String get hunheluzhi {
    return Intl.message(
      'MRC',
      name: 'hunheluzhi',
      desc: '',
      args: [],
    );
  }

  /// `Activity`
  String get huodong {
    return Intl.message(
      'Activity',
      name: 'huodong',
      desc: '',
      args: [],
    );
  }

  /// `Activity ID:`
  String get huodongID {
    return Intl.message(
      'Activity ID:',
      name: 'huodongID',
      desc: '',
      args: [],
    );
  }

  /// `Activity Mall`
  String get huodonganchanghuodon {
    return Intl.message(
      'Activity Mall',
      name: 'huodonganchanghuodon',
      desc: '',
      args: [],
    );
  }

  /// `This activity does not exist.`
  String get huodongbucunzai {
    return Intl.message(
      'This activity does not exist.',
      name: 'huodongbucunzai',
      desc: '',
      args: [],
    );
  }

  /// `Failed to join in the activity.`
  String get huodongcanyushibaihu {
    return Intl.message(
      'Failed to join in the activity.',
      name: 'huodongcanyushibaihu',
      desc: '',
      args: [],
    );
  }

  /// `Activity created.`
  String get huodongchuangjianche {
    return Intl.message(
      'Activity created.',
      name: 'huodongchuangjianche',
      desc: '',
      args: [],
    );
  }

  /// `Failed to create the activity.`
  String get huodongchuangjianshi {
    return Intl.message(
      'Failed to create the activity.',
      name: 'huodongchuangjianshi',
      desc: '',
      args: [],
    );
  }

  /// `Activity Rules`
  String get huodongguize {
    return Intl.message(
      'Activity Rules',
      name: 'huodongguize',
      desc: '',
      args: [],
    );
  }

  /// `活动介绍`
  String get huodongjieshao {
    return Intl.message(
      '活动介绍',
      name: 'huodongjieshao',
      desc: '',
      args: [],
    );
  }

  /// `Activity time:`
  String get huodongshijian {
    return Intl.message(
      'Activity time:',
      name: 'huodongshijian',
      desc: '',
      args: [],
    );
  }

  /// `(Optional) Activity Description`
  String get huodongshuimingxuant {
    return Intl.message(
      '(Optional) Activity Description',
      name: 'huodongshuimingxuant',
      desc: '',
      args: [],
    );
  }

  /// `Activity Details`
  String get huodongxiangqing {
    return Intl.message(
      'Activity Details',
      name: 'huodongxiangqing',
      desc: '',
      args: [],
    );
  }

  /// `Activity modified.`
  String get huodongxiugaichenggo {
    return Intl.message(
      'Activity modified.',
      name: 'huodongxiugaichenggo',
      desc: '',
      args: [],
    );
  }

  /// `Failed to modify the activity.`
  String get huodongxiugaishibai {
    return Intl.message(
      'Failed to modify the activity.',
      name: 'huodongxiugaishibai',
      desc: '',
      args: [],
    );
  }

  /// `This activity has expired.`
  String get huodongyiguoqi {
    return Intl.message(
      'This activity has expired.',
      name: 'huodongyiguoqi',
      desc: '',
      args: [],
    );
  }

  /// `This activity has ended.`
  String get huodongyijieshu {
    return Intl.message(
      'This activity has ended.',
      name: 'huodongyijieshu',
      desc: '',
      args: [],
    );
  }

  /// `The activity has ended. Please refresh and try again.`
  String get huodongyijieshuqings {
    return Intl.message(
      'The activity has ended. Please refresh and try again.',
      name: 'huodongyijieshuqings',
      desc: '',
      args: [],
    );
  }

  /// `This activity has expired.`
  String get huodongyijingguoqi {
    return Intl.message(
      'This activity has expired.',
      name: 'huodongyijingguoqi',
      desc: '',
      args: [],
    );
  }

  /// `This activity has started and you cannot exit.`
  String get huodongyijingkaishim {
    return Intl.message(
      'This activity has started and you cannot exit.',
      name: 'huodongyijingkaishim',
      desc: '',
      args: [],
    );
  }

  /// `This activity has started and you cannot exit.`
  String get huodongyikaishimofat {
    return Intl.message(
      'This activity has started and you cannot exit.',
      name: 'huodongyikaishimofat',
      desc: '',
      args: [],
    );
  }

  /// `This activity has started and cannot be deleted.`
  String get huodongyikaishishanc {
    return Intl.message(
      'This activity has started and cannot be deleted.',
      name: 'huodongyikaishishanc',
      desc: '',
      args: [],
    );
  }

  /// `This activity has ended.`
  String get huodongyixiaxian {
    return Intl.message(
      'This activity has ended.',
      name: 'huodongyixiaxian',
      desc: '',
      args: [],
    );
  }

  /// `Failed to obtain the videos from the VR side.`
  String get huoquVRduanshipinshi {
    return Intl.message(
      'Failed to obtain the videos from the VR side.',
      name: 'huoquVRduanshipinshi',
      desc: '',
      args: [],
    );
  }

  /// `Failed to obtain Y Coins data.`
  String get huoquYbishujushibai {
    return Intl.message(
      'Failed to obtain Y Coins data.',
      name: 'huoquYbishujushibai',
      desc: '',
      args: [],
    );
  }

  /// `Failed to obtain recharge information.`
  String get huoquchongzhixinxish {
    return Intl.message(
      'Failed to obtain recharge information.',
      name: 'huoquchongzhixinxish',
      desc: '',
      args: [],
    );
  }

  /// `Failed to obtain the device list.`
  String get huoqushebeiliebiaosh {
    return Intl.message(
      'Failed to obtain the device list.',
      name: 'huoqushebeiliebiaosh',
      desc: '',
      args: [],
    );
  }

  /// `Obtained At:`
  String get huoqushijian {
    return Intl.message(
      'Obtained At:',
      name: 'huoqushijian',
      desc: '',
      args: [],
    );
  }

  /// `Please enter email address`
  String get input_email {
    return Intl.message(
      'Please enter email address',
      name: 'input_email',
      desc: '',
      args: [],
    );
  }

  /// `Enter the mobile number`
  String get input_phone_num {
    return Intl.message(
      'Enter the mobile number',
      name: 'input_phone_num',
      desc: '',
      args: [],
    );
  }

  /// `Enter the password`
  String get input_pwd {
    return Intl.message(
      'Enter the password',
      name: 'input_pwd',
      desc: '',
      args: [],
    );
  }

  /// `Enter the verification code`
  String get input_verify_code {
    return Intl.message(
      'Enter the verification code',
      name: 'input_verify_code',
      desc: '',
      args: [],
    );
  }

  /// `Reward granted.`
  String get jiangliyifafangjiang {
    return Intl.message(
      'Reward granted.',
      name: 'jiangliyifafangjiang',
      desc: '',
      args: [],
    );
  }

  /// `We recommend you use the VR headset when the battery level is more than 40%.`
  String get jianyiVRdianliang40y {
    return Intl.message(
      'We recommend you use the VR headset when the battery level is more than 40%.',
      name: 'jianyiVRdianliang40y',
      desc: '',
      args: [],
    );
  }

  /// `We recommend you back up your account information before proceeding, and ensure that all services related to the account have been properly handled. Once your account is canceled, we will process or anonymize your personal information according to the relevant terms of the User Privacy Policy.`
  String get jianyininzaizhuxiaoq {
    return Intl.message(
      'We recommend you back up your account information before proceeding, and ensure that all services related to the account have been properly handled. Once your account is canceled, we will process or anonymize your personal information according to the relevant terms of the User Privacy Policy.',
      name: 'jianyininzaizhuxiaoq',
      desc: '',
      args: [],
    );
  }

  /// `Join`
  String get jiaru {
    return Intl.message(
      'Join',
      name: 'jiaru',
      desc: '',
      args: [],
    );
  }

  /// `Failed to add this app because it has not been purchased.`
  String get jiarushibaigaiyingyo {
    return Intl.message(
      'Failed to add this app because it has not been purchased.',
      name: 'jiarushibaigaiyingyo',
      desc: '',
      args: [],
    );
  }

  /// `Loading failed. Please try again.`
  String get jiazaishibaiqingchon {
    return Intl.message(
      'Loading failed. Please try again.',
      name: 'jiazaishibaiqingchon',
      desc: '',
      args: [],
    );
  }

  /// `Loading...`
  String get jiazaizhong {
    return Intl.message(
      'Loading...',
      name: 'jiazaizhong',
      desc: '',
      args: [],
    );
  }

  /// `Unbinding...`
  String get jiebangzhongxiebangz {
    return Intl.message(
      'Unbinding...',
      name: 'jiebangzhongxiebangz',
      desc: '',
      args: [],
    );
  }

  /// `Media`
  String get jiebinghelubingjiepi {
    return Intl.message(
      'Media',
      name: 'jiebinghelubingjiepi',
      desc: '',
      args: [],
    );
  }

  /// `Media`
  String get jiebingyulubingjiepi {
    return Intl.message(
      'Media',
      name: 'jiebingyulubingjiepi',
      desc: '',
      args: [],
    );
  }

  /// `Introduce yourself.`
  String get jieshaoyixianiziji {
    return Intl.message(
      'Introduce yourself.',
      name: 'jieshaoyixianiziji',
      desc: '',
      args: [],
    );
  }

  /// `End Time`
  String get jieshushijian {
    return Intl.message(
      'End Time',
      name: 'jieshushijian',
      desc: '',
      args: [],
    );
  }

  /// `Coming soon`
  String get jijiangshangxianjiqi {
    return Intl.message(
      'Coming soon',
      name: 'jijiangshangxianjiqi',
      desc: '',
      args: [],
    );
  }

  /// `JD.com`
  String get jingdong {
    return Intl.message(
      'JD.com',
      name: 'jingdong',
      desc: '',
      args: [],
    );
  }

  /// `JD.com ID`
  String get jingdongyonghuming {
    return Intl.message(
      'JD.com ID',
      name: 'jingdongyonghuming',
      desc: '',
      args: [],
    );
  }

  /// `Switch`
  String get jingtoufanzhuan {
    return Intl.message(
      'Switch',
      name: 'jingtoufanzhuan',
      desc: '',
      args: [],
    );
  }

  /// `Ongoing`
  String get jinhangzhongjinhengz {
    return Intl.message(
      'Ongoing',
      name: 'jinhangzhongjinhengz',
      desc: '',
      args: [],
    );
  }

  /// `Recent`
  String get jinqi {
    return Intl.message(
      'Recent',
      name: 'jinqi',
      desc: '',
      args: [],
    );
  }

  /// `Available period today`
  String get jinrikeshiyongshidua {
    return Intl.message(
      'Available period today',
      name: 'jinrikeshiyongshidua',
      desc: '',
      args: [],
    );
  }

  /// `Today`
  String get jintian {
    return Intl.message(
      'Today',
      name: 'jintian',
      desc: '',
      args: [],
    );
  }

  /// `Only Chinese characters, English letters, and digits are supported.`
  String get jinzhichizhongwendax {
    return Intl.message(
      'Only Chinese characters, English letters, and digits are supported.',
      name: 'jinzhichizhongwendax',
      desc: '',
      args: [],
    );
  }

  /// `Other error.`
  String get jitacuowuqitacuowu {
    return Intl.message(
      'Other error.',
      name: 'jitacuowuqitacuowu',
      desc: '',
      args: [],
    );
  }

  /// `Other amount 1-9,999`
  String get jitajinen19999qitaji {
    return Intl.message(
      'Other amount 1-9,999',
      name: 'jitajinen19999qitaji',
      desc: '',
      args: [],
    );
  }

  /// `Other`
  String get jitaqita {
    return Intl.message(
      'Other',
      name: 'jitaqita',
      desc: '',
      args: [],
    );
  }

  /// `System error.`
  String get jitongcuowuxitongcuo {
    return Intl.message(
      'System error.',
      name: 'jitongcuowuxitongcuo',
      desc: '',
      args: [],
    );
  }

  /// `System Notification`
  String get jitongtongzhixitongt {
    return Intl.message(
      'System Notification',
      name: 'jitongtongzhixitongt',
      desc: '',
      args: [],
    );
  }

  /// `System message`
  String get jitongxiaoxixitongxi {
    return Intl.message(
      'System message',
      name: 'jitongxiaoxixitongxi',
      desc: '',
      args: [],
    );
  }

  /// `Organizer:`
  String get jubanzhe {
    return Intl.message(
      'Organizer:',
      name: 'jubanzhe',
      desc: '',
      args: [],
    );
  }

  /// `Report`
  String get jubao {
    return Intl.message(
      'Report',
      name: 'jubao',
      desc: '',
      args: [],
    );
  }

  /// `Reporting failed.`
  String get jubaoshibai {
    return Intl.message(
      'Reporting failed.',
      name: 'jubaoshibai',
      desc: '',
      args: [],
    );
  }

  /// `Go to the VR headset to join in the activity.`
  String get kaiqihuodongqingqian {
    return Intl.message(
      'Go to the VR headset to join in the activity.',
      name: 'kaiqihuodongqingqian',
      desc: '',
      args: [],
    );
  }

  /// `Enable teen mode`
  String get kaiqiqingshaonianmos {
    return Intl.message(
      'Enable teen mode',
      name: 'kaiqiqingshaonianmos',
      desc: '',
      args: [],
    );
  }

  /// `Start`
  String get kaishi {
    return Intl.message(
      'Start',
      name: 'kaishi',
      desc: '',
      args: [],
    );
  }

  /// `Start Time`
  String get kaishishijian {
    return Intl.message(
      'Start Time',
      name: 'kaishishijian',
      desc: '',
      args: [],
    );
  }

  /// `Confidential`
  String get keep_secret {
    return Intl.message(
      'Confidential',
      name: 'keep_secret',
      desc: '',
      args: [],
    );
  }

  /// `Users you may know`
  String get kenengrenshikenengre {
    return Intl.message(
      'Users you may know',
      name: 'kenengrenshikenengre',
      desc: '',
      args: [],
    );
  }

  /// `Available period`
  String get keshiyongshiduan {
    return Intl.message(
      'Available period',
      name: 'keshiyongshiduan',
      desc: '',
      args: [],
    );
  }

  /// `You can redeem coupons for it.`
  String get keshiyongyouhuquandu {
    return Intl.message(
      'You can redeem coupons for it.',
      name: 'keshiyongyouhuquandu',
      desc: '',
      args: [],
    );
  }

  /// `Available period`
  String get keyongshijianduan {
    return Intl.message(
      'Available period',
      name: 'keyongshijianduan',
      desc: '',
      args: [],
    );
  }

  /// `Applicable coupons: {num_0}`
  String keyongyouhuquanNzhan(Object num_0) {
    return Intl.message(
      'Applicable coupons: $num_0',
      name: 'keyongyouhuquanNzhan',
      desc: '',
      args: [num_0],
    );
  }

  /// `Valid coupons`
  String get keyongyouhuquankeyon {
    return Intl.message(
      'Valid coupons',
      name: 'keyongyouhuquankeyon',
      desc: '',
      args: [],
    );
  }

  /// `Anti-addiction control helps teens grow healthily in metaverse.`
  String get kongzhishichangshouh {
    return Intl.message(
      'Anti-addiction control helps teens grow healthily in metaverse.',
      name: 'kongzhishichangshouh',
      desc: '',
      args: [],
    );
  }

  /// `Sign up now.`
  String get kuailaibaomingba {
    return Intl.message(
      'Sign up now.',
      name: 'kuailaibaomingba',
      desc: '',
      args: [],
    );
  }

  /// `Try {str_0} in VR.`
  String kuaiquVRlitiyanSba(Object str_0) {
    return Intl.message(
      'Try $str_0 in VR.',
      name: 'kuaiquVRlitiyanSba',
      desc: '',
      args: [str_0],
    );
  }

  /// `Try {str_0} in the VR now.`
  String kuaiquVRlitiyanSba_1(Object str_0) {
    return Intl.message(
      'Try $str_0 in the VR now.',
      name: 'kuaiquVRlitiyanSba_1',
      desc: '',
      args: [str_0],
    );
  }

  /// `Bundle`
  String get kunbangbao {
    return Intl.message(
      'Bundle',
      name: 'kunbangbao',
      desc: '',
      args: [],
    );
  }

  /// `Blacklist {str_0}`
  String laheiS(Object str_0) {
    return Intl.message(
      'Blacklist $str_0',
      name: 'laheiS',
      desc: '',
      args: [str_0],
    );
  }

  /// `If you blacklist a user, the user cannot: \nadd you as a friend,\ninvite you to a chatroom, or\nfind you using search.`
  String get laheiyonghuyiweizhao {
    return Intl.message(
      'If you blacklist a user, the user cannot: \nadd you as a friend,\ninvite you to a chatroom, or\nfind you using search.',
      name: 'laheiyonghuyiweizhao',
      desc: '',
      args: [],
    );
  }

  /// `Spam advertisement`
  String get lajiangaolajiguangga {
    return Intl.message(
      'Spam advertisement',
      name: 'lajiangaolajiguangga',
      desc: '',
      args: [],
    );
  }

  /// `Bluetooth connection failed.`
  String get lanhelianjieshibaila {
    return Intl.message(
      'Bluetooth connection failed.',
      name: 'lanhelianjieshibaila',
      desc: '',
      args: [],
    );
  }

  /// `Bluetooth is not enabled. The related functions cannot be used.`
  String get lanheweikaiqixianggu {
    return Intl.message(
      'Bluetooth is not enabled. The related functions cannot be used.',
      name: 'lanheweikaiqixianggu',
      desc: '',
      args: [],
    );
  }

  /// `Exit`
  String get leave {
    return Intl.message(
      'Exit',
      name: 'leave',
      desc: '',
      args: [],
    );
  }

  /// `Accumulated check-in days: {num_0}`
  String leijidakaNtianleijid(Object num_0) {
    return Intl.message(
      'Accumulated check-in days: $num_0',
      name: 'leijidakaNtianleijid',
      desc: '',
      args: [num_0],
    );
  }

  /// `Failed to connect to the server. Please try again.`
  String get lianjiefuwuqishibaiq {
    return Intl.message(
      'Failed to connect to the server. Please try again.',
      name: 'lianjiefuwuqishibaiq',
      desc: '',
      args: [],
    );
  }

  /// `Connect device`
  String get lianjieshebei {
    return Intl.message(
      'Connect device',
      name: 'lianjieshebei',
      desc: '',
      args: [],
    );
  }

  /// `Connection failed.`
  String get lianjieshibai {
    return Intl.message(
      'Connection failed.',
      name: 'lianjieshibai',
      desc: '',
      args: [],
    );
  }

  /// `Sign up Now`
  String get lijibaoming {
    return Intl.message(
      'Sign up Now',
      name: 'lijibaoming',
      desc: '',
      args: [],
    );
  }

  /// `Change Now`
  String get lijigenghuan {
    return Intl.message(
      'Change Now',
      name: 'lijigenghuan',
      desc: '',
      args: [],
    );
  }

  /// `Update Now`
  String get lijigengxin {
    return Intl.message(
      'Update Now',
      name: 'lijigengxin',
      desc: '',
      args: [],
    );
  }

  /// `Now`
  String get lijiqianwang {
    return Intl.message(
      'Now',
      name: 'lijiqianwang',
      desc: '',
      args: [],
    );
  }

  /// `Download Now`
  String get lijixiazai {
    return Intl.message(
      'Download Now',
      name: 'lijixiazai',
      desc: '',
      args: [],
    );
  }

  /// `Submitted.`
  String get lingjiangxinxidijiao {
    return Intl.message(
      'Submitted.',
      name: 'lingjiangxinxidijiao',
      desc: '',
      args: [],
    );
  }

  /// `Failed to submit the information.`
  String get lingjiangxinxidijiao_1 {
    return Intl.message(
      'Failed to submit the information.',
      name: 'lingjiangxinxidijiao_1',
      desc: '',
      args: [],
    );
  }

  /// `Page for information required for reward collection`
  String get lingjiangxinxitianxi {
    return Intl.message(
      'Page for information required for reward collection',
      name: 'lingjiangxinxitianxi',
      desc: '',
      args: [],
    );
  }

  /// `*Flow code: {str_0}. Use the original purchase account to consult the customer service on the corresponding e-commerce platform for final redemption. Once your application is approved, the reward will be granted as soon as possible. Pay attention to the SMS notification.`
  String liuchengmaSqingyongy(Object str_0) {
    return Intl.message(
      '*Flow code: $str_0. Use the original purchase account to consult the customer service on the corresponding e-commerce platform for final redemption. Once your application is approved, the reward will be granted as soon as possible. Pay attention to the SMS notification.',
      name: 'liuchengmaSqingyongy',
      desc: '',
      args: [str_0],
    );
  }

  /// `Flow code copied.`
  String get liuchengmayifuzhi {
    return Intl.message(
      'Flow code copied.',
      name: 'liuchengmayifuzhi',
      desc: '',
      args: [],
    );
  }

  /// `Network error. Please log in again.`
  String get login_network_error {
    return Intl.message(
      'Network error. Please log in again.',
      name: 'login_network_error',
      desc: '',
      args: [],
    );
  }

  /// `Logged on successfully.`
  String get login_successful {
    return Intl.message(
      'Logged on successfully.',
      name: 'login_successful',
      desc: '',
      args: [],
    );
  }

  /// `The recording is about to end`
  String get luzhijijiangjieshuqi {
    return Intl.message(
      'The recording is about to end',
      name: 'luzhijijiangjieshuqi',
      desc: '',
      args: [],
    );
  }

  /// `The recorded content is too large to save.`
  String get luzhinarongguodamofa {
    return Intl.message(
      'The recorded content is too large to save.',
      name: 'luzhinarongguodamofa',
      desc: '',
      args: [],
    );
  }

  /// `Only capture the center`
  String get luzhishibaozhengbeil {
    return Intl.message(
      'Only capture the center',
      name: 'luzhishibaozhengbeil',
      desc: '',
      args: [],
    );
  }

  /// `Male`
  String get male {
    return Intl.message(
      'Male',
      name: 'male',
      desc: '',
      args: [],
    );
  }

  /// `No content satisfies the condition.`
  String get meiyoufugetiaojiande {
    return Intl.message(
      'No content satisfies the condition.',
      name: 'meiyoufugetiaojiande',
      desc: '',
      args: [],
    );
  }

  /// `No period is available.`
  String get meiyoukexuanshijiand {
    return Intl.message(
      'No period is available.',
      name: 'meiyoukexuanshijiand',
      desc: '',
      args: [],
    );
  }

  /// `No VR headset found nearby.`
  String get meiyouzhaodaofujinde {
    return Intl.message(
      'No VR headset found nearby.',
      name: 'meiyouzhaodaofujinde',
      desc: '',
      args: [],
    );
  }

  /// `Free`
  String get mianfeigoumaiwenfeig {
    return Intl.message(
      'Free',
      name: 'mianfeigoumaiwenfeig',
      desc: '',
      args: [],
    );
  }

  /// `Free`
  String get mianfeihuodewenfeihu {
    return Intl.message(
      'Free',
      name: 'mianfeihuodewenfeihu',
      desc: '',
      args: [],
    );
  }

  /// `The passwords do not match. Please check and enter them again.`
  String get mimabuyizhiqingheshi {
    return Intl.message(
      'The passwords do not match. Please check and enter them again.',
      name: 'mimabuyizhiqingheshi',
      desc: '',
      args: [],
    );
  }

  /// `Incorrect password.`
  String get mimacuowu {
    return Intl.message(
      'Incorrect password.',
      name: 'mimacuowu',
      desc: '',
      args: [],
    );
  }

  /// `Tomorrow`
  String get mingtian {
    return Intl.message(
      'Tomorrow',
      name: 'mingtian',
      desc: '',
      args: [],
    );
  }

  /// `Change nickname`
  String get modify_nickname {
    return Intl.message(
      'Change nickname',
      name: 'modify_nickname',
      desc: '',
      args: [],
    );
  }

  /// `Failed to save. Please try again.`
  String get mofabaocunqingchongs {
    return Intl.message(
      'Failed to save. Please try again.',
      name: 'mofabaocunqingchongs',
      desc: '',
      args: [],
    );
  }

  /// `Saving failed. Go back to the list to refresh.`
  String get mofabaocunqingfanhui {
    return Intl.message(
      'Saving failed. Go back to the list to refresh.',
      name: 'mofabaocunqingfanhui',
      desc: '',
      args: [],
    );
  }

  /// `The app cannot be purchased.`
  String get mofagoumaigaiyingyon {
    return Intl.message(
      'The app cannot be purchased.',
      name: 'mofagoumaigaiyingyon',
      desc: '',
      args: [],
    );
  }

  /// `Unable to obtain the media file. Please ensure that the mobile phone and the VR headset are connected to the same Wi-Fi and the app permission is enabled, and then try again.`
  String get mofahuoqujielubingwe {
    return Intl.message(
      'Unable to obtain the media file. Please ensure that the mobile phone and the VR headset are connected to the same Wi-Fi and the app permission is enabled, and then try again.',
      name: 'mofahuoqujielubingwe',
      desc: '',
      args: [],
    );
  }

  /// `Failed to obtain the Wi-Fi name.`
  String get mofahuoquwifimingche {
    return Intl.message(
      'Failed to obtain the Wi-Fi name.',
      name: 'mofahuoquwifimingche',
      desc: '',
      args: [],
    );
  }

  /// `Failed to add the VR headset.`
  String get mofatianjiaVRyanjing {
    return Intl.message(
      'Failed to add the VR headset.',
      name: 'mofatianjiaVRyanjing',
      desc: '',
      args: [],
    );
  }

  /// `Failed to add the VR headset. Please go back and try again.`
  String get mofatianjiaciVRyanji {
    return Intl.message(
      'Failed to add the VR headset. Please go back and try again.',
      name: 'mofatianjiaciVRyanji',
      desc: '',
      args: [],
    );
  }

  /// `Failed to synchronize the network.`
  String get mofatongbuwanglaowuf {
    return Intl.message(
      'Failed to synchronize the network.',
      name: 'mofatongbuwanglaowuf',
      desc: '',
      args: [],
    );
  }

  /// `This is not a valid start time. Please select again.`
  String get mofazuoweikaishishij {
    return Intl.message(
      'This is not a valid start time. Please select again.',
      name: 'mofazuoweikaishishij',
      desc: '',
      args: [],
    );
  }

  /// `No period available. Go to the time lock setting.`
  String get mokeshiyongshiduanqi {
    return Intl.message(
      'No period available. Go to the time lock setting.',
      name: 'mokeshiyongshiduanqi',
      desc: '',
      args: [],
    );
  }

  /// `{num_0} accepted your friend request.`
  String msg_accept_friend(Object num_0) {
    return Intl.message(
      '$num_0 accepted your friend request.',
      name: 'msg_accept_friend',
      desc: '',
      args: [num_0],
    );
  }

  /// `This activity will start 30 minutes later.`
  String get msg_after_half_hour {
    return Intl.message(
      'This activity will start 30 minutes later.',
      name: 'msg_after_half_hour',
      desc: '',
      args: [],
    );
  }

  /// `Delete`
  String get msg_delete {
    return Intl.message(
      'Delete',
      name: 'msg_delete',
      desc: '',
      args: [],
    );
  }

  /// `Deleted messages cannot be recovered. Are you sure you want to delete it?`
  String get msg_delete_confirm {
    return Intl.message(
      'Deleted messages cannot be recovered. Are you sure you want to delete it?',
      name: 'msg_delete_confirm',
      desc: '',
      args: [],
    );
  }

  /// `This activity has been canceled.`
  String get msg_evet_cancel {
    return Intl.message(
      'This activity has been canceled.',
      name: 'msg_evet_cancel',
      desc: '',
      args: [],
    );
  }

  /// `The activity is about to start.`
  String get msg_evet_start {
    return Intl.message(
      'The activity is about to start.',
      name: 'msg_evet_start',
      desc: '',
      args: [],
    );
  }

  /// `Your friend request has been accepted.`
  String get msg_friend_pass {
    return Intl.message(
      'Your friend request has been accepted.',
      name: 'msg_friend_pass',
      desc: '',
      args: [],
    );
  }

  /// `{num_0} invites you to join an activity.`
  String msg_invite_event(Object num_0) {
    return Intl.message(
      '$num_0 invites you to join an activity.',
      name: 'msg_invite_event',
      desc: '',
      args: [num_0],
    );
  }

  /// `Your friend invites you to join an activity.`
  String get msg_invite_you {
    return Intl.message(
      'Your friend invites you to join an activity.',
      name: 'msg_invite_you',
      desc: '',
      args: [],
    );
  }

  /// `sent you a friend request.`
  String get msg_req_friend {
    return Intl.message(
      'sent you a friend request.',
      name: 'msg_req_friend',
      desc: '',
      args: [],
    );
  }

  /// `Male`
  String get nan {
    return Intl.message(
      'Male',
      name: 'nan',
      desc: '',
      args: [],
    );
  }

  /// `The content is too large. We recommend you directly copy it to your PC.`
  String get narongguodajianyizhi {
    return Intl.message(
      'The content is too large. We recommend you directly copy it to your PC.',
      name: 'narongguodajianyizhi',
      desc: '',
      args: [],
    );
  }

  /// `The nickname cannot be empty or contain spaces.`
  String get nichenbukeweikonghuo {
    return Intl.message(
      'The nickname cannot be empty or contain spaces.',
      name: 'nichenbukeweikonghuo',
      desc: '',
      args: [],
    );
  }

  /// `The nickname cannot be empty or contain spaces.`
  String get nichenbukeweikongzif {
    return Intl.message(
      'The nickname cannot be empty or contain spaces.',
      name: 'nichenbukeweikongzif',
      desc: '',
      args: [],
    );
  }

  /// `Nickname`
  String get nickname {
    return Intl.message(
      'Nickname',
      name: 'nickname',
      desc: '',
      args: [],
    );
  }

  /// `You do not have this app yet. Go to purchase it.`
  String get nihaimeiyougaiyingyo {
    return Intl.message(
      'You do not have this app yet. Go to purchase it.',
      name: 'nihaimeiyougaiyingyo',
      desc: '',
      args: [],
    );
  }

  /// `The device is not connected. Do you want to connect it?`
  String get nihaimeiyoulianjiesh {
    return Intl.message(
      'The device is not connected. Do you want to connect it?',
      name: 'nihaimeiyoulianjiesh',
      desc: '',
      args: [],
    );
  }

  /// `You are all playing {str_0}`
  String nimenduzaiwanSnimend(Object str_0) {
    return Intl.message(
      'You are all playing $str_0',
      name: 'nimenduzaiwanSnimend',
      desc: '',
      args: [str_0],
    );
  }

  /// `It is already the latest version.`
  String get nindangqianyishizuix {
    return Intl.message(
      'It is already the latest version.',
      name: 'nindangqianyishizuix',
      desc: '',
      args: [],
    );
  }

  /// `Your VR headset requests to purchase {str_0}. Would you like to purchase it?`
  String nindeVRshebeixiangni(Object str_0) {
    return Intl.message(
      'Your VR headset requests to purchase $str_0. Would you like to purchase it?',
      name: 'nindeVRshebeixiangni',
      desc: '',
      args: [str_0],
    );
  }

  /// `Your background image has not been approved`
  String get nindebeijingtupiansh {
    return Intl.message(
      'Your background image has not been approved',
      name: 'nindebeijingtupiansh',
      desc: '',
      args: [],
    );
  }

  /// `Your bio review failed`
  String get nindegexingqianmings {
    return Intl.message(
      'Your bio review failed',
      name: 'nindegexingqianmings',
      desc: '',
      args: [],
    );
  }

  /// `Your purchase channel`
  String get nindegoumaiqudaonind {
    return Intl.message(
      'Your purchase channel',
      name: 'nindegoumaiqudaonind',
      desc: '',
      args: [],
    );
  }

  /// `May contain disturbing content, please modify and try again.`
  String get nindetouxiangtupians {
    return Intl.message(
      'May contain disturbing content, please modify and try again.',
      name: 'nindetouxiangtupians',
      desc: '',
      args: [],
    );
  }

  /// `Your nickname review failed`
  String get nindeyonghunichengsh {
    return Intl.message(
      'Your nickname review failed',
      name: 'nindeyonghunichengsh',
      desc: '',
      args: [],
    );
  }

  /// `3. The account is in normal use, has passed the security inspection by YVR and bears no risk of theft, and has never committed any violation of laws and regulations.`
  String get nindezhanghaochuyuzh {
    return Intl.message(
      '3. The account is in normal use, has passed the security inspection by YVR and bears no risk of theft, and has never committed any violation of laws and regulations.',
      name: 'nindezhanghaochuyuzh',
      desc: '',
      args: [],
    );
  }

  /// `3. The account is in normal use, has passed the security inspection by YVR and bears no risk of theft, and has never committed any violation of laws and regulations.`
  String get nindezhanghaochuyuzh_1 {
    return Intl.message(
      '3. The account is in normal use, has passed the security inspection by YVR and bears no risk of theft, and has never committed any violation of laws and regulations.',
      name: 'nindezhanghaochuyuzh_1',
      desc: '',
      args: [],
    );
  }

  /// `The character format is not supported.`
  String get nindijiaodezifugeshi {
    return Intl.message(
      'The character format is not supported.',
      name: 'nindijiaodezifugeshi',
      desc: '',
      args: [],
    );
  }

  /// `1. You will give up your unexpired or unused virtual currency and props under various game characters, your various identity rights and interests in various products and/or services on the YVR platform, the unexpired online service content you have purchased in various products and/or services, the related assets (such as Y Coins) you have obtained in various products and/or services you have purchased, and other rights and interests that have been generated but not used up yet or expected benefits in the future.`
  String get ninjiangfangqiweidao {
    return Intl.message(
      '1. You will give up your unexpired or unused virtual currency and props under various game characters, your various identity rights and interests in various products and/or services on the YVR platform, the unexpired online service content you have purchased in various products and/or services, the related assets (such as Y Coins) you have obtained in various products and/or services you have purchased, and other rights and interests that have been generated but not used up yet or expected benefits in the future.',
      name: 'ninjiangfangqiweidao',
      desc: '',
      args: [],
    );
  }

  /// `2. You will disassociate this account from other products (such as video or third-party platforms), for example, remove the SSO.`
  String get ninjiangjiechugaizha {
    return Intl.message(
      '2. You will disassociate this account from other products (such as video or third-party platforms), for example, remove the SSO.',
      name: 'ninjiangjiechugaizha',
      desc: '',
      args: [],
    );
  }

  /// `The account cancelation process will proceed and the data of this account will also be deleted once the YVR account is canceled.`
  String get ninjiangjixuwancheng {
    return Intl.message(
      'The account cancelation process will proceed and the data of this account will also be deleted once the YVR account is canceled.',
      name: 'ninjiangjixuwancheng',
      desc: '',
      args: [],
    );
  }

  /// `You can invite friends only after you join in an activity.`
  String get ninjiaruhuodonghouca {
    return Intl.message(
      'You can invite friends only after you join in an activity.',
      name: 'ninjiaruhuodonghouca',
      desc: '',
      args: [],
    );
  }

  /// `The avatar you uploaded does not meet the requirements.`
  String get ninshangchuandetouxi {
    return Intl.message(
      'The avatar you uploaded does not meet the requirements.',
      name: 'ninshangchuandetouxi',
      desc: '',
      args: [],
    );
  }

  /// `The avatar you uploaded does not meet the requirements.`
  String get ninshangchuandetouxi_1 {
    return Intl.message(
      'The avatar you uploaded does not meet the requirements.',
      name: 'ninshangchuandetouxi_1',
      desc: '',
      args: [],
    );
  }

  /// `Your remaining activity days cannot satisfy the condition for receiving the reward.`
  String get ninshengyuhuodongtia {
    return Intl.message(
      'Your remaining activity days cannot satisfy the condition for receiving the reward.',
      name: 'ninshengyuhuodongtia',
      desc: '',
      args: [],
    );
  }

  /// `The name you entered has exceeded the length limit.`
  String get ninshurudemingchenyi {
    return Intl.message(
      'The name you entered has exceeded the length limit.',
      name: 'ninshurudemingchenyi',
      desc: '',
      args: [],
    );
  }

  /// `Payment complete. Now you can enjoy it on your VR headset.`
  String get ninyiwanchengzhifunk {
    return Intl.message(
      'Payment complete. Now you can enjoy it on your VR headset.',
      name: 'ninyiwanchengzhifunk',
      desc: '',
      args: [],
    );
  }

  /// `You have given a {num_0}-star rating for {str_0} in the App Store.`
  String ninzaiyingyongshangd(Object num_0, Object str_0) {
    return Intl.message(
      'You have given a $num_0-star rating for $str_0 in the App Store.',
      name: 'ninzaiyingyongshangd',
      desc: '',
      args: [num_0, str_0],
    );
  }

  /// `You have given a {num_0}-star rating for {str_0} in the App Store. Your review has been approved.`
  String ninzaiyingyongshangd_1(Object num_0, Object str_0) {
    return Intl.message(
      'You have given a $num_0-star rating for $str_0 in the App Store. Your review has been approved.',
      name: 'ninzaiyingyongshangd_1',
      desc: '',
      args: [num_0, str_0],
    );
  }

  /// `You have given a {num_0}-star rating for {str_0} in the App Store. Your review has been rejected due to illegal content.`
  String ninzaiyingyongshangd_2(Object num_0, Object str_0) {
    return Intl.message(
      'You have given a $num_0-star rating for $str_0 in the App Store. Your review has been rejected due to illegal content.',
      name: 'ninzaiyingyongshangd_2',
      desc: '',
      args: [num_0, str_0],
    );
  }

  /// `You have given a {num_0}-star rating for {str_0} in the App Store. Your review has been rejected due to {str_1} content.`
  String ninzaiyingyongshangd_3(Object num_0, Object str_0, Object str_1) {
    return Intl.message(
      'You have given a $num_0-star rating for $str_0 in the App Store. Your review has been rejected due to $str_1 content.',
      name: 'ninzaiyingyongshangd_3',
      desc: '',
      args: [num_0, str_0, str_1],
    );
  }

  /// `New user?`
  String get no_account {
    return Intl.message(
      'New user?',
      name: 'no_account',
      desc: '',
      args: [],
    );
  }

  /// `Female`
  String get nv {
    return Intl.message(
      'Female',
      name: 'nv',
      desc: '',
      args: [],
    );
  }

  /// `Take photo`
  String get paizhao {
    return Intl.message(
      'Take photo',
      name: 'paizhao',
      desc: '',
      args: [],
    );
  }

  /// `Your rating has been submitted.`
  String get pingfenyidijiaopingf {
    return Intl.message(
      'Your rating has been submitted.',
      name: 'pingfenyidijiaopingf',
      desc: '',
      args: [],
    );
  }

  /// `The comment has been submitted for review. You can check the review results in [Notification Center].`
  String get pinglunyidijiaoshenh {
    return Intl.message(
      'The comment has been submitted for review. You can check the review results in [Notification Center].',
      name: 'pinglunyidijiaoshenh',
      desc: '',
      args: [],
    );
  }

  /// `Splice`
  String get pinjie {
    return Intl.message(
      'Splice',
      name: 'pinjie',
      desc: '',
      args: [],
    );
  }

  /// `Dual Recording`
  String get pinjieluzhi {
    return Intl.message(
      'Dual Recording',
      name: 'pinjieluzhi',
      desc: '',
      args: [],
    );
  }

  /// `Ensure that the app is on the frontend during dual recording.`
  String get pinjieluzhiguochengz {
    return Intl.message(
      'Ensure that the app is on the frontend during dual recording.',
      name: 'pinjieluzhiguochengz',
      desc: '',
      args: [],
    );
  }

  /// `Mode`
  String get pinjiemoshi {
    return Intl.message(
      'Mode',
      name: 'pinjiemoshi',
      desc: '',
      args: [],
    );
  }

  /// `No activity yet.`
  String get plhd_no_event {
    return Intl.message(
      'No activity yet.',
      name: 'plhd_no_event',
      desc: '',
      args: [],
    );
  }

  /// `No more data.`
  String get plhd_no_more_data {
    return Intl.message(
      'No more data.',
      name: 'plhd_no_more_data',
      desc: '',
      args: [],
    );
  }

  /// `No new messages.`
  String get plhd_no_msg {
    return Intl.message(
      'No new messages.',
      name: 'plhd_no_msg',
      desc: '',
      args: [],
    );
  }

  /// `Privacy Policy`
  String get privacy_policy {
    return Intl.message(
      'Privacy Policy',
      name: 'privacy_policy',
      desc: '',
      args: [],
    );
  }

  /// `protectionen`
  String get privacy_policy_html_page_name {
    return Intl.message(
      'protectionen',
      name: 'privacy_policy_html_page_name',
      desc: '',
      args: [],
    );
  }

  /// `Alipay`
  String get prod_alipay {
    return Intl.message(
      'Alipay',
      name: 'prod_alipay',
      desc: '',
      args: [],
    );
  }

  /// `Required permission: `
  String get prod_authority {
    return Intl.message(
      'Required permission: ',
      name: 'prod_authority',
      desc: '',
      args: [],
    );
  }

  /// `Details`
  String get prod_detail {
    return Intl.message(
      'Details',
      name: 'prod_detail',
      desc: '',
      args: [],
    );
  }

  /// `Developer: `
  String get prod_developer {
    return Intl.message(
      'Developer: ',
      name: 'prod_developer',
      desc: '',
      args: [],
    );
  }

  /// `Added to the download list.`
  String get prod_download_queue {
    return Intl.message(
      'Added to the download list.',
      name: 'prod_download_queue',
      desc: '',
      args: [],
    );
  }

  /// `You have paid for the app.\nEnjoy it on your VR device.`
  String get prod_experience_vr {
    return Intl.message(
      'You have paid for the app.\nEnjoy it on your VR device.',
      name: 'prod_experience_vr',
      desc: '',
      args: [],
    );
  }

  /// `Collapse`
  String get prod_fold {
    return Intl.message(
      'Collapse',
      name: 'prod_fold',
      desc: '',
      args: [],
    );
  }

  /// `Enter your review.`
  String get prod_input_comment {
    return Intl.message(
      'Enter your review.',
      name: 'prod_input_comment',
      desc: '',
      args: [],
    );
  }

  /// `Install Alipay first.`
  String get prod_install_alipay {
    return Intl.message(
      'Install Alipay first.',
      name: 'prod_install_alipay',
      desc: '',
      args: [],
    );
  }

  /// `Install WeChat first.`
  String get prod_install_wechat {
    return Intl.message(
      'Install WeChat first.',
      name: 'prod_install_wechat',
      desc: '',
      args: [],
    );
  }

  /// `Language: `
  String get prod_language {
    return Intl.message(
      'Language: ',
      name: 'prod_language',
      desc: '',
      args: [],
    );
  }

  /// `My review`
  String get prod_my_comment {
    return Intl.message(
      'My review',
      name: 'prod_my_comment',
      desc: '',
      args: [],
    );
  }

  /// `Please log on to buy.`
  String get prod_need_login {
    return Intl.message(
      'Please log on to buy.',
      name: 'prod_need_login',
      desc: '',
      args: [],
    );
  }

  /// `Network mode: `
  String get prod_network {
    return Intl.message(
      'Network mode: ',
      name: 'prod_network',
      desc: '',
      args: [],
    );
  }

  /// `Payment failed.`
  String get prod_pay_fail {
    return Intl.message(
      'Payment failed.',
      name: 'prod_pay_fail',
      desc: '',
      args: [],
    );
  }

  /// `Payment status`
  String get prod_pay_result {
    return Intl.message(
      'Payment status',
      name: 'prod_pay_result',
      desc: '',
      args: [],
    );
  }

  /// `Paid successfully.`
  String get prod_pay_success {
    return Intl.message(
      'Paid successfully.',
      name: 'prod_pay_success',
      desc: '',
      args: [],
    );
  }

  /// `Select payment method`
  String get prod_pay_way {
    return Intl.message(
      'Select payment method',
      name: 'prod_pay_way',
      desc: '',
      args: [],
    );
  }

  /// `Platform policy:`
  String get prod_policy {
    return Intl.message(
      'Platform policy:',
      name: 'prod_policy',
      desc: '',
      args: [],
    );
  }

  /// `Platform policy`
  String get prod_policy_title {
    return Intl.message(
      'Platform policy',
      name: 'prod_policy_title',
      desc: '',
      args: [],
    );
  }

  /// `publisher: `
  String get prod_publisher {
    return Intl.message(
      'publisher: ',
      name: 'prod_publisher',
      desc: '',
      args: [],
    );
  }

  /// `Required ROM: `
  String get prod_ram {
    return Intl.message(
      'Required ROM: ',
      name: 'prod_ram',
      desc: '',
      args: [],
    );
  }

  /// `Rating and reviews`
  String get prod_ratings_reviews {
    return Intl.message(
      'Rating and reviews',
      name: 'prod_ratings_reviews',
      desc: '',
      args: [],
    );
  }

  /// `Release date: `
  String get prod_release_date {
    return Intl.message(
      'Release date: ',
      name: 'prod_release_date',
      desc: '',
      args: [],
    );
  }

  /// `No review yet. Be the first reviewer!`
  String get prod_sofa {
    return Intl.message(
      'No review yet. Be the first reviewer!',
      name: 'prod_sofa',
      desc: '',
      args: [],
    );
  }

  /// `Expand`
  String get prod_unfold {
    return Intl.message(
      'Expand',
      name: 'prod_unfold',
      desc: '',
      args: [],
    );
  }

  /// `Version: `
  String get prod_version {
    return Intl.message(
      'Version: ',
      name: 'prod_version',
      desc: '',
      args: [],
    );
  }

  /// `WeChat`
  String get prod_wechat {
    return Intl.message(
      'WeChat',
      name: 'prod_wechat',
      desc: '',
      args: [],
    );
  }

  /// `Book`
  String get prod_book {
    return Intl.message(
      'Book',
      name: 'prod_book',
      desc: '',
      args: [],
    );
  }

  /// `Booked`
  String get prod_booked {
    return Intl.message(
      'Booked',
      name: 'prod_booked',
      desc: '',
      args: [],
    );
  }

  /// `Reservation successful, you will be notified when it goes live.`
  String get prod_book_app_succeeded {
    return Intl.message(
      'Reservation successful, you will be notified when it goes live.',
      name: 'prod_book_app_succeeded',
      desc: '',
      args: [],
    );
  }

  /// `Reservation failed, please try again.`
  String get prod_book_app_failed {
    return Intl.message(
      'Reservation failed, please try again.',
      name: 'prod_book_app_failed',
      desc: '',
      args: [],
    );
  }

  /// `Cancellation failed, please try again.`
  String get prod_unbook_app_failed {
    return Intl.message(
      'Cancellation failed, please try again.',
      name: 'prod_unbook_app_failed',
      desc: '',
      args: [],
    );
  }

  /// `Coming soon {str_0}`
  String prod_coming_time(Object str_0) {
    return Intl.message(
      'Coming soon $str_0',
      name: 'prod_coming_time',
      desc: '',
      args: [str_0],
    );
  }

  /// `Purchase records`
  String get profile_buy_history {
    return Intl.message(
      'Purchase records',
      name: 'profile_buy_history',
      desc: '',
      args: [],
    );
  }

  /// `Cancel feedback`
  String get profile_cancel_feedback {
    return Intl.message(
      'Cancel feedback',
      name: 'profile_cancel_feedback',
      desc: '',
      args: [],
    );
  }

  /// `Change password`
  String get profile_change_pwd {
    return Intl.message(
      'Change password',
      name: 'profile_change_pwd',
      desc: '',
      args: [],
    );
  }

  /// `Check the version`
  String get profile_check_version {
    return Intl.message(
      'Check the version',
      name: 'profile_check_version',
      desc: '',
      args: [],
    );
  }

  /// `Confirm password`
  String get profile_confirm_password {
    return Intl.message(
      'Confirm password',
      name: 'profile_confirm_password',
      desc: '',
      args: [],
    );
  }

  /// `Email address`
  String get profile_connect_way {
    return Intl.message(
      'Email address',
      name: 'profile_connect_way',
      desc: '',
      args: [],
    );
  }

  /// `Call 400-618-1160 Customer service online time: 9:00-18:00 on working days`
  String get profile_contact_information {
    return Intl.message(
      'Call 400-618-1160 Customer service online time: 9:00-18:00 on working days',
      name: 'profile_contact_information',
      desc: '',
      args: [],
    );
  }

  /// `Current version`
  String get profile_current_version {
    return Intl.message(
      'Current version',
      name: 'profile_current_version',
      desc: '',
      args: [],
    );
  }

  /// `Describe the problem you have encountered. We will handle it in time.`
  String get profile_desc_problem {
    return Intl.message(
      'Describe the problem you have encountered. We will handle it in time.',
      name: 'profile_desc_problem',
      desc: '',
      args: [],
    );
  }

  /// `Edit profile`
  String get profile_edit {
    return Intl.message(
      'Edit profile',
      name: 'profile_edit',
      desc: '',
      args: [],
    );
  }

  /// `Enter SMS verification code`
  String get profile_enter_sms {
    return Intl.message(
      'Enter SMS verification code',
      name: 'profile_enter_sms',
      desc: '',
      args: [],
    );
  }

  /// `Enter exchange code`
  String get profile_exchange_code {
    return Intl.message(
      'Enter exchange code',
      name: 'profile_exchange_code',
      desc: '',
      args: [],
    );
  }

  /// `User feedback`
  String get profile_feedback {
    return Intl.message(
      'User feedback',
      name: 'profile_feedback',
      desc: '',
      args: [],
    );
  }

  /// `Health and Safety`
  String get profile_health {
    return Intl.message(
      'Health and Safety',
      name: 'profile_health',
      desc: '',
      args: [],
    );
  }

  /// `Homepage`
  String get profile_homepage {
    return Intl.message(
      'Homepage',
      name: 'profile_homepage',
      desc: '',
      args: [],
    );
  }

  /// `Enter your nickname.`
  String get profile_input_nick {
    return Intl.message(
      'Enter your nickname.',
      name: 'profile_input_nick',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to cancel the feedback?`
  String get profile_is_feedback {
    return Intl.message(
      'Are you sure you want to cancel the feedback?',
      name: 'profile_is_feedback',
      desc: '',
      args: [],
    );
  }

  /// `Legal information`
  String get profile_legal_info {
    return Intl.message(
      'Legal information',
      name: 'profile_legal_info',
      desc: '',
      args: [],
    );
  }

  /// `New password`
  String get profile_new_password {
    return Intl.message(
      'New password',
      name: 'profile_new_password',
      desc: '',
      args: [],
    );
  }

  /// `Latest version`
  String get profile_new_ver {
    return Intl.message(
      'Latest version',
      name: 'profile_new_ver',
      desc: '',
      args: [],
    );
  }

  /// `Later`
  String get profile_not_update {
    return Intl.message(
      'Later',
      name: 'profile_not_update',
      desc: '',
      args: [],
    );
  }

  /// `Phone number`
  String get profile_phone_num {
    return Intl.message(
      'Phone number',
      name: 'profile_phone_num',
      desc: '',
      args: [],
    );
  }

  /// `Enter your redemption code.`
  String get profile_please_redemption {
    return Intl.message(
      'Enter your redemption code.',
      name: 'profile_please_redemption',
      desc: '',
      args: [],
    );
  }

  /// `Problem type`
  String get profile_question_type {
    return Intl.message(
      'Problem type',
      name: 'profile_question_type',
      desc: '',
      args: [],
    );
  }

  /// `Redemption code`
  String get profile_redemption_code {
    return Intl.message(
      'Redemption code',
      name: 'profile_redemption_code',
      desc: '',
      args: [],
    );
  }

  /// `Refunded`
  String get profile_refunded {
    return Intl.message(
      'Refunded',
      name: 'profile_refunded',
      desc: '',
      args: [],
    );
  }

  /// `Refunding`
  String get profile_refunding {
    return Intl.message(
      'Refunding',
      name: 'profile_refunding',
      desc: '',
      args: [],
    );
  }

  /// `Request refund`
  String get profile_request_refund {
    return Intl.message(
      'Request refund',
      name: 'profile_request_refund',
      desc: '',
      args: [],
    );
  }

  /// `Select your feedback type.`
  String get profile_sel_feedback {
    return Intl.message(
      'Select your feedback type.',
      name: 'profile_sel_feedback',
      desc: '',
      args: [],
    );
  }

  /// `Set new password`
  String get profile_set_password {
    return Intl.message(
      'Set new password',
      name: 'profile_set_password',
      desc: '',
      args: [],
    );
  }

  /// `Update now`
  String get profile_update_now {
    return Intl.message(
      'Update now',
      name: 'profile_update_now',
      desc: '',
      args: [],
    );
  }

  /// `Version size`
  String get profile_ver_size {
    return Intl.message(
      'Version size',
      name: 'profile_ver_size',
      desc: '',
      args: [],
    );
  }

  /// `Version update`
  String get profile_ver_update {
    return Intl.message(
      'Version update',
      name: 'profile_ver_update',
      desc: '',
      args: [],
    );
  }

  /// `Verify identification`
  String get profile_verify_identidy {
    return Intl.message(
      'Verify identification',
      name: 'profile_verify_identidy',
      desc: '',
      args: [],
    );
  }

  /// `Verify mobile number`
  String get profile_verify_phone_num {
    return Intl.message(
      'Verify mobile number',
      name: 'profile_verify_phone_num',
      desc: '',
      args: [],
    );
  }

  /// `Confirm the password`
  String get pwd_input_again {
    return Intl.message(
      'Confirm the password',
      name: 'pwd_input_again',
      desc: '',
      args: [],
    );
  }

  /// `Log on by password`
  String get pwd_login {
    return Intl.message(
      'Log on by password',
      name: 'pwd_login',
      desc: '',
      args: [],
    );
  }

  /// `The bio cannot exceed 6 lines.`
  String get qianmingbunengchaogu {
    return Intl.message(
      'The bio cannot exceed 6 lines.',
      name: 'qianmingbunengchaogu',
      desc: '',
      args: [],
    );
  }

  /// `The bio cannot contain blank lines and should have no more than 6 lines.`
  String get qianmingbunenghanyou {
    return Intl.message(
      'The bio cannot contain blank lines and should have no more than 6 lines.',
      name: 'qianmingbunenghanyou',
      desc: '',
      args: [],
    );
  }

  /// `Switch Video`
  String get qiehuanshipin {
    return Intl.message(
      'Switch Video',
      name: 'qiehuanshipin',
      desc: '',
      args: [],
    );
  }

  /// `Rate the {str_0} app.`
  String qingduiyingyongSdafe(Object str_0) {
    return Intl.message(
      'Rate the $str_0 app.',
      name: 'qingduiyingyongSdafe',
      desc: '',
      args: [str_0],
    );
  }

  /// `Fill in the reward collection information following the instructions below. We will review the information you provide as soon as possible.`
  String get qinggenjuxiafangzhiy {
    return Intl.message(
      'Fill in the reward collection information following the instructions below. We will review the information you provide as soon as possible.',
      name: 'qinggenjuxiafangzhiy',
      desc: '',
      args: [],
    );
  }

  /// `After connecting to the same network, swipe down to refresh the list.`
  String get qinglianjiezhixiangt {
    return Intl.message(
      'After connecting to the same network, swipe down to refresh the list.',
      name: 'qinglianjiezhixiangt',
      desc: '',
      args: [],
    );
  }

  /// `Please carefully read and fully understand all of the provisions in {str_0} and {str_1}. You can view, change, and delete your personal information and manage permissions in My. If you agree, tap Agree to accept our service.`
  String qingniwubishenshenyu(Object str_0, Object str_1) {
    return Intl.message(
      'Please carefully read and fully understand all of the provisions in $str_0 and $str_1. You can view, change, and delete your personal information and manage permissions in My. If you agree, tap Agree to accept our service.',
      name: 'qingniwubishenshenyu',
      desc: '',
      args: [str_0, str_1],
    );
  }

  /// `Go to the VR headset to manually download the app.`
  String get qingqianwangVRshebei {
    return Intl.message(
      'Go to the VR headset to manually download the app.',
      name: 'qingqianwangVRshebei',
      desc: '',
      args: [],
    );
  }

  /// `Try it in the VR headset.`
  String get qingqianwangVRyanjin {
    return Intl.message(
      'Try it in the VR headset.',
      name: 'qingqianwangVRyanjin',
      desc: '',
      args: [],
    );
  }

  /// `Please visit https://yvr.cn`
  String get qingqianwangYVRguanw {
    return Intl.message(
      'Please visit https://yvr.cn',
      name: 'qingqianwangYVRguanw',
      desc: '',
      args: [],
    );
  }

  /// `Switch the video splicing mode.`
  String get qingqiehuanshipindep {
    return Intl.message(
      'Switch the video splicing mode.',
      name: 'qingqiehuanshipindep',
      desc: '',
      args: [],
    );
  }

  /// `Request timed out.`
  String get qingqiuchaoshi {
    return Intl.message(
      'Request timed out.',
      name: 'qingqiuchaoshi',
      desc: '',
      args: [],
    );
  }

  /// `Request failed.`
  String get qingqiushibai {
    return Intl.message(
      'Request failed.',
      name: 'qingqiushibai',
      desc: '',
      args: [],
    );
  }

  /// `Hold your phone vertically during recording`
  String get qingquanchengshiyong {
    return Intl.message(
      'Hold your phone vertically during recording',
      name: 'qingquanchengshiyong',
      desc: '',
      args: [],
    );
  }

  /// `Ensure that the VR headset is connected to the network and the screen is on.`
  String get qingquebaoVRyanjingl {
    return Intl.message(
      'Ensure that the VR headset is connected to the network and the screen is on.',
      name: 'qingquebaoVRyanjingl',
      desc: '',
      args: [],
    );
  }

  /// `Ensure that you have logged in with the same account.`
  String get qingquebaoVRzhongyid {
    return Intl.message(
      'Ensure that you have logged in with the same account.',
      name: 'qingquebaoVRzhongyid',
      desc: '',
      args: [],
    );
  }

  /// `Upload the screenshot of the platform order containing the order No. and actual payment amount.`
  String get qingshangchuanbaohan {
    return Intl.message(
      'Upload the screenshot of the platform order containing the order No. and actual payment amount.',
      name: 'qingshangchuanbaohan',
      desc: '',
      args: [],
    );
  }

  /// `Upload the photos of the front and back of your ID card (must be clear and complete.)`
  String get qingshangchuanshenfe {
    return Intl.message(
      'Upload the photos of the front and back of your ID card (must be clear and complete.)',
      name: 'qingshangchuanshenfe',
      desc: '',
      args: [],
    );
  }

  /// `Teen mode`
  String get qingshaonianmoshiqin {
    return Intl.message(
      'Teen mode',
      name: 'qingshaonianmoshiqin',
      desc: '',
      args: [],
    );
  }

  /// `Teen mode is off.`
  String get qingshaonianmoshiyig {
    return Intl.message(
      'Teen mode is off.',
      name: 'qingshaonianmoshiyig',
      desc: '',
      args: [],
    );
  }

  /// `The teen mode is disabled. Enable it and retry.`
  String get qingshaonianmoshiyig_1 {
    return Intl.message(
      'The teen mode is disabled. Enable it and retry.',
      name: 'qingshaonianmoshiyig_1',
      desc: '',
      args: [],
    );
  }

  /// `Teen mode enabled.`
  String get qingshaonianmoshiyik {
    return Intl.message(
      'Teen mode enabled.',
      name: 'qingshaonianmoshiyik',
      desc: '',
      args: [],
    );
  }

  /// `Teen guardian tool`
  String get qingshaonianshouhugo {
    return Intl.message(
      'Teen guardian tool',
      name: 'qingshaonianshouhugo',
      desc: '',
      args: [],
    );
  }

  /// `Try other filter criteria.`
  String get qingshiyongjitashaix {
    return Intl.message(
      'Try other filter criteria.',
      name: 'qingshiyongjitashaix',
      desc: '',
      args: [],
    );
  }

  /// `Enter a {num_0}-digit number.`
  String qingshuruNweishuzi(Object num_0) {
    return Intl.message(
      'Enter a $num_0-digit number.',
      name: 'qingshuruNweishuzi',
      desc: '',
      args: [num_0],
    );
  }

  /// `Enter {str_0}.`
  String qingshuruS(Object str_0) {
    return Intl.message(
      'Enter $str_0.',
      name: 'qingshuruS',
      desc: '',
      args: [str_0],
    );
  }

  /// `Enter the Wi-Fi password.`
  String get qingshuruWiFimima {
    return Intl.message(
      'Enter the Wi-Fi password.',
      name: 'qingshuruWiFimima',
      desc: '',
      args: [],
    );
  }

  /// `Enter the verification code that is sent to the mobile number bound to the account.`
  String get qingshurufashenggeig {
    return Intl.message(
      'Enter the verification code that is sent to the mobile number bound to the account.',
      name: 'qingshurufashenggeig',
      desc: '',
      args: [],
    );
  }

  /// `Enter the password to authenticate the parent's identity and enter the teen mode setting.`
  String get qingshurumimayanzhen {
    return Intl.message(
      'Enter the password to authenticate the parent\'s identity and enter the teen mode setting.',
      name: 'qingshurumimayanzhen',
      desc: '',
      args: [],
    );
  }

  /// `Enter the platform order No. (must be consistent with that in the uploaded screenshot) of your purchase channel.`
  String get qingshuruningoumaiqu {
    return Intl.message(
      'Enter the platform order No. (must be consistent with that in the uploaded screenshot) of your purchase channel.',
      name: 'qingshuruningoumaiqu',
      desc: '',
      args: [],
    );
  }

  /// `Enter a mobile number.`
  String get qingshurushoujihao {
    return Intl.message(
      'Enter a mobile number.',
      name: 'qingshurushoujihao',
      desc: '',
      args: [],
    );
  }

  /// `Enter a new mobile number.`
  String get qingshuruxinshoujiha {
    return Intl.message(
      'Enter a new mobile number.',
      name: 'qingshuruxinshoujiha',
      desc: '',
      args: [],
    );
  }

  /// `Enter the verification code.`
  String get qingshuruyanzhengma {
    return Intl.message(
      'Enter the verification code.',
      name: 'qingshuruyanzhengma',
      desc: '',
      args: [],
    );
  }

  /// `Enter a valid mobile number.`
  String get qingshuruyouxiaoshou {
    return Intl.message(
      'Enter a valid mobile number.',
      name: 'qingshuruyouxiaoshou',
      desc: '',
      args: [],
    );
  }

  /// `Complete all required items before submission.`
  String get qingwanchengbitianxi {
    return Intl.message(
      'Complete all required items before submission.',
      name: 'qingwanchengbitianxi',
      desc: '',
      args: [],
    );
  }

  /// `Do not exit`
  String get qingwutuichu {
    return Intl.message(
      'Do not exit',
      name: 'qingwutuichu',
      desc: '',
      args: [],
    );
  }

  /// `Please install the WeChat app first.`
  String get qingxiananzhuangweix {
    return Intl.message(
      'Please install the WeChat app first.',
      name: 'qingxiananzhuangweix',
      desc: '',
      args: [],
    );
  }

  /// `Please bind your VR headset first.`
  String get qingxianbangdingnind {
    return Intl.message(
      'Please bind your VR headset first.',
      name: 'qingxianbangdingnind',
      desc: '',
      args: [],
    );
  }

  /// `Enable the Wi-Fi function of the VR headset first.`
  String get qingxiandakaiVRshebe {
    return Intl.message(
      'Enable the Wi-Fi function of the VR headset first.',
      name: 'qingxiandakaiVRshebe',
      desc: '',
      args: [],
    );
  }

  /// `Enable the Wi-Fi function of the VR headset first.`
  String get qingxiandakaiVRyanji {
    return Intl.message(
      'Enable the Wi-Fi function of the VR headset first.',
      name: 'qingxiandakaiVRyanji',
      desc: '',
      args: [],
    );
  }

  /// `Please enable the permission to access the local network.`
  String get qingxiandakaifangwen {
    return Intl.message(
      'Please enable the permission to access the local network.',
      name: 'qingxiandakaifangwen',
      desc: '',
      args: [],
    );
  }

  /// `Enable teen mode first.`
  String get qingxiandakaiqingsha {
    return Intl.message(
      'Enable teen mode first.',
      name: 'qingxiandakaiqingsha',
      desc: '',
      args: [],
    );
  }

  /// `Log in first.`
  String get qingxiandenglunindez {
    return Intl.message(
      'Log in first.',
      name: 'qingxiandenglunindez',
      desc: '',
      args: [],
    );
  }

  /// `Turn off teen mode first.`
  String get qingxianguanbiqingsh {
    return Intl.message(
      'Turn off teen mode first.',
      name: 'qingxianguanbiqingsh',
      desc: '',
      args: [],
    );
  }

  /// `Connect the mobile phone to Wi-Fi first. If it is connected, enable the Bluetooth and location permissions of the Play For Dream first.`
  String get qingxianjiangshoujil {
    return Intl.message(
      'Connect the mobile phone to Wi-Fi first. If it is connected, enable the Bluetooth and location permissions of the Play For Dream first.',
      name: 'qingxianjiangshoujil',
      desc: '',
      args: [],
    );
  }

  /// `Enable {str_0} permission`
  String qingxiankaiqiSquanxi(Object str_0) {
    return Intl.message(
      'Enable $str_0 permission',
      name: 'qingxiankaiqiSquanxi',
      desc: '',
      args: [str_0],
    );
  }

  /// `Please enable the permission to access the local album.`
  String get qingxiankaiqibendexi {
    return Intl.message(
      'Please enable the permission to access the local album.',
      name: 'qingxiankaiqibendexi',
      desc: '',
      args: [],
    );
  }

  /// `Enable camera permission`
  String get qingxiankaiqixiangji {
    return Intl.message(
      'Enable camera permission',
      name: 'qingxiankaiqixiangji',
      desc: '',
      args: [],
    );
  }

  /// `Enable the camera and microphone permissions first.`
  String get qingxiankaiqixiangji_1 {
    return Intl.message(
      'Enable the camera and microphone permissions first.',
      name: 'qingxiankaiqixiangji_1',
      desc: '',
      args: [],
    );
  }

  /// `Connect the VR headset first.`
  String get qingxianlianjieVRyan {
    return Intl.message(
      'Connect the VR headset first.',
      name: 'qingxianlianjieVRyan',
      desc: '',
      args: [],
    );
  }

  /// `You need to agree to the Service Agreement and the Privacy Policy of YVR.`
  String get qingxiantongyiYVRfuw {
    return Intl.message(
      'You need to agree to the Service Agreement and the Privacy Policy of YVR.',
      name: 'qingxiantongyiYVRfuw',
      desc: '',
      args: [],
    );
  }

  /// `Complete the required items first.`
  String get qingxianwanchengbiti {
    return Intl.message(
      'Complete the required items first.',
      name: 'qingxianwanchengbiti',
      desc: '',
      args: [],
    );
  }

  /// `Please read and agree to the terms and conditions of the Account Cancelation Agreement first.`
  String get qingxianyuedubington {
    return Intl.message(
      'Please read and agree to the terms and conditions of the Account Cancelation Agreement first.',
      name: 'qingxianyuedubington',
      desc: '',
      args: [],
    );
  }

  /// `Please read and agree to YVR Top-up Service Agreement`
  String get qingxianyuedubington_1 {
    return Intl.message(
      'Please read and agree to YVR Top-up Service Agreement',
      name: 'qingxianyuedubington_1',
      desc: '',
      args: [],
    );
  }

  /// `Log in first in the VR headset.`
  String get qingxianzaiVRyanjing {
    return Intl.message(
      'Log in first in the VR headset.',
      name: 'qingxianzaiVRyanjing',
      desc: '',
      args: [],
    );
  }

  /// `Select a video splicing mode.`
  String get qingxuanzeshipindepi {
    return Intl.message(
      'Select a video splicing mode.',
      name: 'qingxuanzeshipindepi',
      desc: '',
      args: [],
    );
  }

  /// `Front`
  String get identity_card_front {
    return Intl.message(
      'Front',
      name: 'identity_card_front',
      desc: '',
      args: [],
    );
  }

  /// `Back`
  String get identity_card_back {
    return Intl.message(
      'Back',
      name: 'identity_card_back',
      desc: '',
      args: [],
    );
  }

  /// `All`
  String get quanbu {
    return Intl.message(
      'All',
      name: 'quanbu',
      desc: '',
      args: [],
    );
  }

  /// `All activities`
  String get quanbuhuodong {
    return Intl.message(
      'All activities',
      name: 'quanbuhuodong',
      desc: '',
      args: [],
    );
  }

  /// `Join In Now`
  String get qucanjiaqucenjiaqusa {
    return Intl.message(
      'Join In Now',
      name: 'qucanjiaqucenjiaqusa',
      desc: '',
      args: [],
    );
  }

  /// `Log In Now`
  String get qudenglu {
    return Intl.message(
      'Log In Now',
      name: 'qudenglu',
      desc: '',
      args: [],
    );
  }

  /// `OK`
  String get queding {
    return Intl.message(
      'OK',
      name: 'queding',
      desc: '',
      args: [],
    );
  }

  /// `Give Up Account Assets`
  String get quedingfangqicaichan {
    return Intl.message(
      'Give Up Account Assets',
      name: 'quedingfangqicaichan',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this friend?`
  String get quedingyaoshanchugai {
    return Intl.message(
      'Are you sure you want to delete this friend?',
      name: 'quedingyaoshanchugai',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this activity?`
  String get quedingyaoshanchugai_1 {
    return Intl.message(
      'Are you sure you want to delete this activity?',
      name: 'quedingyaoshanchugai_1',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to refund? The refund amount will be returned to your payment account in three working days. You cannot use this app during the refund period.`
  String get quedingyaotuikuanmat {
    return Intl.message(
      'Are you sure you want to refund? The refund amount will be returned to your payment account in three working days. You cannot use this app during the refund period.',
      name: 'quedingyaotuikuanmat',
      desc: '',
      args: [],
    );
  }

  /// `Yes`
  String get quedingzhuxiao {
    return Intl.message(
      'Yes',
      name: 'quedingzhuxiao',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Account Cancelation`
  String get quedingzhuxiaozhangh {
    return Intl.message(
      'Confirm Account Cancelation',
      name: 'quedingzhuxiaozhangh',
      desc: '',
      args: [],
    );
  }

  /// `OK`
  String get queren {
    return Intl.message(
      'OK',
      name: 'queren',
      desc: '',
      args: [],
    );
  }

  /// `Confirm password`
  String get querenmima {
    return Intl.message(
      'Confirm password',
      name: 'querenmima',
      desc: '',
      args: [],
    );
  }

  /// `Important reminders for account cancelation`
  String get querenzhuxiaochongya {
    return Intl.message(
      'Important reminders for account cancelation',
      name: 'querenzhuxiaochongya',
      desc: '',
      args: [],
    );
  }

  /// `If you confirm the cancelation, your request for account cancelation will be completed, and the system will automatically cancel your YVR account 7 days later. To withdraw the request, log in to this account again within 7 days.`
  String get querenzhuxiaojiangwa {
    return Intl.message(
      'If you confirm the cancelation, your request for account cancelation will be completed, and the system will automatically cancel your YVR account 7 days later. To withdraw the request, log in to this account again within 7 days.',
      name: 'querenzhuxiaojiangwa',
      desc: '',
      args: [],
    );
  }

  /// `After you confirm account cancelation, your account will be automatically canceled 7 days later. To withdraw the request, log in to this account again within 7 days.`
  String get querenzhuxiaozhangha {
    return Intl.message(
      'After you confirm account cancelation, your account will be automatically canceled 7 days later. To withdraw the request, log in to this account again within 7 days.',
      name: 'querenzhuxiaozhangha',
      desc: '',
      args: [],
    );
  }

  /// `Purchase Now`
  String get qugoumai {
    return Intl.message(
      'Purchase Now',
      name: 'qugoumai',
      desc: '',
      args: [],
    );
  }

  /// `Enable Now`
  String get qukaiqi {
    return Intl.message(
      'Enable Now',
      name: 'qukaiqi',
      desc: '',
      args: [],
    );
  }

  /// `Connect`
  String get qulianjie {
    return Intl.message(
      'Connect',
      name: 'qulianjie',
      desc: '',
      args: [],
    );
  }

  /// `Disable Now`
  String get qushezhi {
    return Intl.message(
      'Disable Now',
      name: 'qushezhi',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get quxiao {
    return Intl.message(
      'Cancel',
      name: 'quxiao',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get quxiaoluzhi {
    return Intl.message(
      'Cancel',
      name: 'quxiaoluzhi',
      desc: '',
      args: [],
    );
  }

  /// `Download Now`
  String get quxiazai {
    return Intl.message(
      'Download Now',
      name: 'quxiazai',
      desc: '',
      args: [],
    );
  }

  /// `https://apitest.yvrdream.com/yvrdvcenter/#/rechargeagreementen`
  String get recharge_service_agreement_url_dev {
    return Intl.message(
      'https://apitest.yvrdream.com/yvrdvcenter/#/rechargeagreementen',
      name: 'recharge_service_agreement_url_dev',
      desc: '',
      args: [],
    );
  }

  /// `https://developer.yvrdream.com/#/rechargeagreementen`
  String get recharge_service_agreement_url_release {
    return Intl.message(
      'https://developer.yvrdream.com/#/rechargeagreementen',
      name: 'recharge_service_agreement_url_release',
      desc: '',
      args: [],
    );
  }

  /// `Refund Available: `
  String get refundAvailable {
    return Intl.message(
      'Refund Available: ',
      name: 'refundAvailable',
      desc: '',
      args: [],
    );
  }

  /// `refundPolicyen`
  String get refund_policy_html_page_name {
    return Intl.message(
      'refundPolicyen',
      name: 'refund_policy_html_page_name',
      desc: '',
      args: [],
    );
  }

  /// `Register`
  String get register_account {
    return Intl.message(
      'Register',
      name: 'register_account',
      desc: '',
      args: [],
    );
  }

  /// `More in Camera`
  String get renxiangweizhu {
    return Intl.message(
      'More in Camera',
      name: 'renxiangweizhu',
      desc: '',
      args: [],
    );
  }

  /// `Resend`
  String get resend {
    return Intl.message(
      'Resend',
      name: 'resend',
      desc: '',
      args: [],
    );
  }

  /// `Date/Time`
  String get riqishijian {
    return Intl.message(
      'Date/Time',
      name: 'riqishijian',
      desc: '',
      args: [],
    );
  }

  /// `Sun,Mon,Tue,Wed,Thur,Fri,Sat`
  String get riyiersan {
    return Intl.message(
      'Sun,Mon,Tue,Wed,Thur,Fri,Sat',
      name: 'riyiersan',
      desc: '',
      args: [],
    );
  }

  /// `Log debugging`
  String get rizhidiaoshirizhitia {
    return Intl.message(
      'Log debugging',
      name: 'rizhidiaoshirizhitia',
      desc: '',
      args: [],
    );
  }

  /// `If you still choose to cancel your account, you are deemed to automatically give up your virtual assets (including but not limited to the following rights and interests) under the account. You understand and agree that we cannot assist you in restoring the preceding services.`
  String get runinrengxuanzejixuz {
    return Intl.message(
      'If you still choose to cancel your account, you are deemed to automatically give up your virtual assets (including but not limited to the following rights and interests) under the account. You understand and agree that we cannot assist you in restoring the preceding services.',
      name: 'runinrengxuanzejixuz',
      desc: '',
      args: [],
    );
  }

  /// `If the information is missing or wrong, the application will be deemed invalid.`
  String get ruxinxiqueloucuowuze {
    return Intl.message(
      'If the information is missing or wrong, the application will be deemed invalid.',
      name: 'ruxinxiqueloucuowuze',
      desc: '',
      args: [],
    );
  }

  /// `(Optional) Upload the YVR invoice, if any.`
  String get ruyouqingshangchuang {
    return Intl.message(
      '(Optional) Upload the YVR invoice, if any.',
      name: 'ruyouqingshangchuang',
      desc: '',
      args: [],
    );
  }

  /// `III. Your cancelation of this YVR account does not mean your relevant responsibilities under this account will be diminished or exempted before cancelation.`
  String get sanninzhuxiaobenYVRz {
    return Intl.message(
      'III. Your cancelation of this YVR account does not mean your relevant responsibilities under this account will be diminished or exempted before cancelation.',
      name: 'sanninzhuxiaobenYVRz',
      desc: '',
      args: [],
    );
  }

  /// `To search for nearby Bluetooth devices, please enable location permission for Play For Dream in Settings.`
  String get saomiaolanhexuyaonin {
    return Intl.message(
      'To search for nearby Bluetooth devices, please enable location permission for Play For Dream in Settings.',
      name: 'saomiaolanhexuyaonin',
      desc: '',
      args: [],
    );
  }

  /// `Select from album`
  String get sel_photograph {
    return Intl.message(
      'Select from album',
      name: 'sel_photograph',
      desc: '',
      args: [],
    );
  }

  /// `Terms of Service`
  String get service_agreement {
    return Intl.message(
      'Terms of Service',
      name: 'service_agreement',
      desc: '',
      args: [],
    );
  }

  /// `serveren`
  String get service_agreement_html_page_name {
    return Intl.message(
      'serveren',
      name: 'service_agreement_html_page_name',
      desc: '',
      args: [],
    );
  }

  /// `Filter criterion:`
  String get shaixuantiaojian {
    return Intl.message(
      'Filter criterion:',
      name: 'shaixuantiaojian',
      desc: '',
      args: [],
    );
  }

  /// `Yes`
  String get shanchu {
    return Intl.message(
      'Yes',
      name: 'shanchu',
      desc: '',
      args: [],
    );
  }

  /// `Delete{str_0}`
  String shanchuS(Object str_0) {
    return Intl.message(
      'Delete$str_0',
      name: 'shanchuS',
      desc: '',
      args: [str_0],
    );
  }

  /// `Deleted.`
  String get shanchuhaoyouchenggo {
    return Intl.message(
      'Deleted.',
      name: 'shanchuhaoyouchenggo',
      desc: '',
      args: [],
    );
  }

  /// `Delete Activity`
  String get shanchuhuodong {
    return Intl.message(
      'Delete Activity',
      name: 'shanchuhuodong',
      desc: '',
      args: [],
    );
  }

  /// `Deletion failed.`
  String get shanchushibai {
    return Intl.message(
      'Deletion failed.',
      name: 'shanchushibai',
      desc: '',
      args: [],
    );
  }

  /// `Deleting...`
  String get shanchuzhong {
    return Intl.message(
      'Deleting...',
      name: 'shanchuzhong',
      desc: '',
      args: [],
    );
  }

  /// `Upload failure.`
  String get shangchuanshibaishan {
    return Intl.message(
      'Upload failure.',
      name: 'shangchuanshibaishan',
      desc: '',
      args: [],
    );
  }

  /// `The uploaded file fails the intelligent audit.`
  String get shangchuanwenjianwei {
    return Intl.message(
      'The uploaded file fails the intelligent audit.',
      name: 'shangchuanwenjianwei',
      desc: '',
      args: [],
    );
  }

  /// `Uploading...`
  String get shangchuanzhongshang {
    return Intl.message(
      'Uploading...',
      name: 'shangchuanzhongshang',
      desc: '',
      args: [],
    );
  }

  /// `a.m.`
  String get shangwu {
    return Intl.message(
      'a.m.',
      name: 'shangwu',
      desc: '',
      args: [],
    );
  }

  /// `View Later`
  String get shaohouchakanshaohou {
    return Intl.message(
      'View Later',
      name: 'shaohouchakanshaohou',
      desc: '',
      args: [],
    );
  }

  /// `Later`
  String get shaohouqianwang {
    return Intl.message(
      'Later',
      name: 'shaohouqianwang',
      desc: '',
      args: [],
    );
  }

  /// `Maybe Later`
  String get shaohouxiazai {
    return Intl.message(
      'Maybe Later',
      name: 'shaohouxiazai',
      desc: '',
      args: [],
    );
  }

  /// `No device found.`
  String get shebeibucunzai {
    return Intl.message(
      'No device found.',
      name: 'shebeibucunzai',
      desc: '',
      args: [],
    );
  }

  /// `Identity authentication`
  String get shenfenyanzheng {
    return Intl.message(
      'Identity authentication',
      name: 'shenfenyanzheng',
      desc: '',
      args: [],
    );
  }

  /// `Upgrade failed. Please try again.`
  String get shengjishibaiqingcho {
    return Intl.message(
      'Upgrade failed. Please try again.',
      name: 'shengjishibaiqingcho',
      desc: '',
      args: [],
    );
  }

  /// `(Remaining: {num_0})`
  String shengyuN0(Object num_0) {
    return Intl.message(
      '(Remaining: $num_0)',
      name: 'shengyuN0',
      desc: '',
      args: [num_0],
    );
  }

  /// `(Remaining: {num_0}，; To-be-paid: {num_1})`
  String shengyuNxuzhifuN(Object num_0, Object num_1) {
    return Intl.message(
      '(Remaining: $num_0，; To-be-paid: $num_1)',
      name: 'shengyuNxuzhifuN',
      desc: '',
      args: [num_0, num_1],
    );
  }

  /// `Rejected. Illegal content found.`
  String get shenhebutongguoyuany {
    return Intl.message(
      'Rejected. Illegal content found.',
      name: 'shenhebutongguoyuany',
      desc: '',
      args: [],
    );
  }

  /// `,Approved.`
  String get shenheyitongguoshenh {
    return Intl.message(
      ',Approved.',
      name: 'shenheyitongguoshenh',
      desc: '',
      args: [],
    );
  }

  /// `Request Account Cancelation`
  String get shenqingzhanghaozhux {
    return Intl.message(
      'Request Account Cancelation',
      name: 'shenqingzhanghaozhux',
      desc: '',
      args: [],
    );
  }

  /// `Set.`
  String get shezhichenggong {
    return Intl.message(
      'Set.',
      name: 'shezhichenggong',
      desc: '',
      args: [],
    );
  }

  /// `Available to set the usage period, which defaults to 6:00-22:00 each day.`
  String get shezhikeshiyongshidu {
    return Intl.message(
      'Available to set the usage period, which defaults to 6:00-22:00 each day.',
      name: 'shezhikeshiyongshidu',
      desc: '',
      args: [],
    );
  }

  /// `Set password`
  String get shezhimima {
    return Intl.message(
      'Set password',
      name: 'shezhimima',
      desc: '',
      args: [],
    );
  }

  /// `Duration`
  String get shichangshizhang {
    return Intl.message(
      'Duration',
      name: 'shichangshizhang',
      desc: '',
      args: [],
    );
  }

  /// `Duration:`
  String get shichangshizhang_1 {
    return Intl.message(
      'Duration:',
      name: 'shichangshizhang_1',
      desc: '',
      args: [],
    );
  }

  /// `This time slot has been occupied. Select another one.`
  String get shiduanyizhanyongqin {
    return Intl.message(
      'This time slot has been occupied. Select another one.',
      name: 'shiduanyizhanyongqin',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to discard?`
  String get shifoufangqidangqian {
    return Intl.message(
      'Are you sure you want to discard?',
      name: 'shifoufangqidangqian',
      desc: '',
      args: [],
    );
  }

  /// `Download now? If you tap Download Now, the VR headset will automatically download this app when it is connected to the network.`
  String get shifoulijixiazaindia {
    return Intl.message(
      'Download now? If you tap Download Now, the VR headset will automatically download this app when it is connected to the network.',
      name: 'shifoulijixiazaindia',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to give up your virtual assets under this account?`
  String get shifouquedingfangqid {
    return Intl.message(
      'Are you sure you want to give up your virtual assets under this account?',
      name: 'shifouquedingfangqid',
      desc: '',
      args: [],
    );
  }

  /// `Do you want to cancel sign-up?`
  String get shifouquxiaobaomings {
    return Intl.message(
      'Do you want to cancel sign-up?',
      name: 'shifouquxiaobaomings',
      desc: '',
      args: [],
    );
  }

  /// `Time lock setting`
  String get shijiansuoshezhi {
    return Intl.message(
      'Time lock setting',
      name: 'shijiansuoshezhi',
      desc: '',
      args: [],
    );
  }

  /// `Actual payment`
  String get shijifukuan {
    return Intl.message(
      'Actual payment',
      name: 'shijifukuan',
      desc: '',
      args: [],
    );
  }

  /// `Incorrect video format.`
  String get shipingeshicuowu {
    return Intl.message(
      'Incorrect video format.',
      name: 'shipingeshicuowu',
      desc: '',
      args: [],
    );
  }

  /// `Video recording has been interrupted.`
  String get shipinluzhiyizhongdu {
    return Intl.message(
      'Video recording has been interrupted.',
      name: 'shipinluzhiyizhongdu',
      desc: '',
      args: [],
    );
  }

  /// `Synthesizing...`
  String get shipinnuligechengzho {
    return Intl.message(
      'Synthesizing...',
      name: 'shipinnuligechengzho',
      desc: '',
      args: [],
    );
  }

  /// `Video Example`
  String get shipinshili {
    return Intl.message(
      'Video Example',
      name: 'shipinshili',
      desc: '',
      args: [],
    );
  }

  /// `When Bluetooth is used for interconnection, this name will be displayed on other devices.`
  String get shiyonglanhehuliansh {
    return Intl.message(
      'When Bluetooth is used for interconnection, this name will be displayed on other devices.',
      name: 'shiyonglanhehuliansh',
      desc: '',
      args: [],
    );
  }

  /// `Instruction`
  String get shiyongshuimingshiyo {
    return Intl.message(
      'Instruction',
      name: 'shiyongshuimingshiyo',
      desc: '',
      args: [],
    );
  }

  /// `Real name of the recipient`
  String get shoujianrenzhenshixi {
    return Intl.message(
      'Real name of the recipient',
      name: 'shoujianrenzhenshixi',
      desc: '',
      args: [],
    );
  }

  /// `Insufficient storage of the mobile phone. Please free up some space before recording.`
  String get shoujinacunbuzuqingq {
    return Intl.message(
      'Insufficient storage of the mobile phone. Please free up some space before recording.',
      name: 'shoujinacunbuzuqingq',
      desc: '',
      args: [],
    );
  }

  /// `The network of the mobile phone and the VR headset are inconsistent`
  String get shoujiyuVRyanjingwan {
    return Intl.message(
      'The network of the mobile phone and the VR headset are inconsistent',
      name: 'shoujiyuVRyanjingwan',
      desc: '',
      args: [],
    );
  }

  /// `Collapse`
  String get shouqi {
    return Intl.message(
      'Collapse',
      name: 'shouqi',
      desc: '',
      args: [],
    );
  }

  /// `Refresh`
  String get shuaxin {
    return Intl.message(
      'Refresh',
      name: 'shuaxin',
      desc: '',
      args: [],
    );
  }

  /// `You are refreshing too often. Please try later.`
  String get shuaxinguoyupinfanqi {
    return Intl.message(
      'You are refreshing too often. Please try later.',
      name: 'shuaxinguoyupinfanqi',
      desc: '',
      args: [],
    );
  }

  /// `Portrait Splicing`
  String get shubingpinjieshuping {
    return Intl.message(
      'Portrait Splicing',
      name: 'shubingpinjieshuping',
      desc: '',
      args: [],
    );
  }

  /// `Portrait`
  String get shubingshuping {
    return Intl.message(
      'Portrait',
      name: 'shubingshuping',
      desc: '',
      args: [],
    );
  }

  /// `Any comments are welcome.`
  String get shuidianshenmebashuo {
    return Intl.message(
      'Any comments are welcome.',
      name: 'shuidianshenmebashuo',
      desc: '',
      args: [],
    );
  }

  /// `Data request timed out. Loading failed.`
  String get shujuqingqiuchaoshij {
    return Intl.message(
      'Data request timed out. Loading failed.',
      name: 'shujuqingqiuchaoshij',
      desc: '',
      args: [],
    );
  }

  /// `Enter the activity name (no more than 14 characters).`
  String get shuruhuodongmingchen {
    return Intl.message(
      'Enter the activity name (no more than 14 characters).',
      name: 'shuruhuodongmingchen',
      desc: '',
      args: [],
    );
  }

  /// `Enter the actual payment amount (must be consistent with that in the uploaded screenshot) of your order.`
  String get shurunindingchanzhon {
    return Intl.message(
      'Enter the actual payment amount (must be consistent with that in the uploaded screenshot) of your order.',
      name: 'shurunindingchanzhon',
      desc: '',
      args: [],
    );
  }

  /// `Enter the verification code.`
  String get shuruyanzhengma {
    return Intl.message(
      'Enter the verification code.',
      name: 'shuruyanzhengma',
      desc: '',
      args: [],
    );
  }

  /// `Log on`
  String get sign_in {
    return Intl.message(
      'Log on',
      name: 'sign_in',
      desc: '',
      args: [],
    );
  }

  /// `Log out`
  String get sign_out {
    return Intl.message(
      'Log out',
      name: 'sign_out',
      desc: '',
      args: [],
    );
  }

  /// `Register`
  String get sign_up {
    return Intl.message(
      'Register',
      name: 'sign_up',
      desc: '',
      args: [],
    );
  }

  /// `Activity`
  String get social_activity {
    return Intl.message(
      'Activity',
      name: 'social_activity',
      desc: '',
      args: [],
    );
  }

  /// `Add friend`
  String get social_add_friend {
    return Intl.message(
      'Add friend',
      name: 'social_add_friend',
      desc: '',
      args: [],
    );
  }

  /// `requested to add you as a friend.`
  String get social_add_req {
    return Intl.message(
      'requested to add you as a friend.',
      name: 'social_add_req',
      desc: '',
      args: [],
    );
  }

  /// `Blacklist`
  String get social_block {
    return Intl.message(
      'Blacklist',
      name: 'social_block',
      desc: '',
      args: [],
    );
  }

  /// `Blacklist`
  String get social_block_list {
    return Intl.message(
      'Blacklist',
      name: 'social_block_list',
      desc: '',
      args: [],
    );
  }

  /// `Create activity`
  String get social_creat_event {
    return Intl.message(
      'Create activity',
      name: 'social_creat_event',
      desc: '',
      args: [],
    );
  }

  /// `Date/Time`
  String get social_date {
    return Intl.message(
      'Date/Time',
      name: 'social_date',
      desc: '',
      args: [],
    );
  }

  /// `Length`
  String get social_duration {
    return Intl.message(
      'Length',
      name: 'social_duration',
      desc: '',
      args: [],
    );
  }

  /// `Activity description (optional)`
  String get social_event_desc {
    return Intl.message(
      'Activity description (optional)',
      name: 'social_event_desc',
      desc: '',
      args: [],
    );
  }

  /// `New friends`
  String get social_friend_req {
    return Intl.message(
      'New friends',
      name: 'social_friend_req',
      desc: '',
      args: [],
    );
  }

  /// `Friends`
  String get social_friends {
    return Intl.message(
      'Friends',
      name: 'social_friends',
      desc: '',
      args: [],
    );
  }

  /// `Ignore`
  String get social_ignore {
    return Intl.message(
      'Ignore',
      name: 'social_ignore',
      desc: '',
      args: [],
    );
  }

  /// `Enter the activity name (within 14 characters).`
  String get social_input_name {
    return Intl.message(
      'Enter the activity name (within 14 characters).',
      name: 'social_input_name',
      desc: '',
      args: [],
    );
  }

  /// `Interested`
  String get social_join_in {
    return Intl.message(
      'Interested',
      name: 'social_join_in',
      desc: '',
      args: [],
    );
  }

  /// `Add to VR`
  String get social_join_in_vr {
    return Intl.message(
      'Add to VR',
      name: 'social_join_in_vr',
      desc: '',
      args: [],
    );
  }

  /// `Joined`
  String get social_joined {
    return Intl.message(
      'Joined',
      name: 'social_joined',
      desc: '',
      args: [],
    );
  }

  /// `Add friend to learn more.`
  String get social_know_more {
    return Intl.message(
      'Add friend to learn more.',
      name: 'social_know_more',
      desc: '',
      args: [],
    );
  }

  /// `Quit Activity`
  String get social_leave_event {
    return Intl.message(
      'Quit Activity',
      name: 'social_leave_event',
      desc: '',
      args: [],
    );
  }

  /// `If you quit the activity, you will not receive any activity notifications or updates, but you can join the activity again.`
  String get social_leave_event_desc {
    return Intl.message(
      'If you quit the activity, you will not receive any activity notifications or updates, but you can join the activity again.',
      name: 'social_leave_event_desc',
      desc: '',
      args: [],
    );
  }

  /// `Minutes`
  String get social_minute {
    return Intl.message(
      'Minutes',
      name: 'social_minute',
      desc: '',
      args: [],
    );
  }

  /// `My activities`
  String get social_my_activity {
    return Intl.message(
      'My activities',
      name: 'social_my_activity',
      desc: '',
      args: [],
    );
  }

  /// `My friends`
  String get social_my_friends {
    return Intl.message(
      'My friends',
      name: 'social_my_friends',
      desc: '',
      args: [],
    );
  }

  /// `The blacklist is empty.`
  String get social_no_block {
    return Intl.message(
      'The blacklist is empty.',
      name: 'social_no_block',
      desc: '',
      args: [],
    );
  }

  /// `Host`
  String get social_organizers {
    return Intl.message(
      'Host',
      name: 'social_organizers',
      desc: '',
      args: [],
    );
  }

  /// `Pass`
  String get social_pass {
    return Intl.message(
      'Pass',
      name: 'social_pass',
      desc: '',
      args: [],
    );
  }

  /// `{num_0} user(s) joined`
  String social_people_attend(Object num_0) {
    return Intl.message(
      '$num_0 user(s) joined',
      name: 'social_people_attend',
      desc: '',
      args: [num_0],
    );
  }

  /// `Hot activities`
  String get social_popular_activities {
    return Intl.message(
      'Hot activities',
      name: 'social_popular_activities',
      desc: '',
      args: [],
    );
  }

  /// `Recent activities`
  String get social_recent_activity {
    return Intl.message(
      'Recent activities',
      name: 'social_recent_activity',
      desc: '',
      args: [],
    );
  }

  /// `Recommended`
  String get social_recommended {
    return Intl.message(
      'Recommended',
      name: 'social_recommended',
      desc: '',
      args: [],
    );
  }

  /// `Select app`
  String get social_sel_app {
    return Intl.message(
      'Select app',
      name: 'social_sel_app',
      desc: '',
      args: [],
    );
  }

  /// `The request is sent.`
  String get social_sendreq_yet {
    return Intl.message(
      'The request is sent.',
      name: 'social_sendreq_yet',
      desc: '',
      args: [],
    );
  }

  /// `Delete`
  String get social_unfriend {
    return Intl.message(
      'Delete',
      name: 'social_unfriend',
      desc: '',
      args: [],
    );
  }

  /// `Extra {num_0} Y Coins`
  String songNYbi(Object num_0) {
    return Intl.message(
      'Extra $num_0 Y Coins',
      name: 'songNYbi',
      desc: '',
      args: [num_0],
    );
  }

  /// `Search ended.`
  String get sousuojieshu {
    return Intl.message(
      'Search ended.',
      name: 'sousuojieshu',
      desc: '',
      args: [],
    );
  }

  /// `Search for the nickname and ID to add a friend.`
  String get sousuonichenheIDhaot {
    return Intl.message(
      'Search for the nickname and ID to add a friend.',
      name: 'sousuonichenheIDhaot',
      desc: '',
      args: [],
    );
  }

  /// `Search for an online game, friend name, ID, or app.`
  String get sousuozaixianyouhuha {
    return Intl.message(
      'Search for an online game, friend name, ID, or app.',
      name: 'sousuozaixianyouhuha',
      desc: '',
      args: [],
    );
  }

  /// `Nothing Found`
  String get state_empty {
    return Intl.message(
      'Nothing Found',
      name: 'state_empty',
      desc: '',
      args: [],
    );
  }

  /// `Load Failed`
  String get state_error {
    return Intl.message(
      'Load Failed',
      name: 'state_error',
      desc: '',
      args: [],
    );
  }

  /// `Loading failed. Tap to try again.`
  String get state_load_fail {
    return Intl.message(
      'Loading failed. Tap to try again.',
      name: 'state_load_fail',
      desc: '',
      args: [],
    );
  }

  /// `Release to load more.`
  String get state_load_more {
    return Intl.message(
      'Release to load more.',
      name: 'state_load_more',
      desc: '',
      args: [],
    );
  }

  /// `Load Failed, Check network `
  String get state_network {
    return Intl.message(
      'Load Failed, Check network ',
      name: 'state_network',
      desc: '',
      args: [],
    );
  }

  /// `Refresh`
  String get state_refresh {
    return Intl.message(
      'Refresh',
      name: 'state_refresh',
      desc: '',
      args: [],
    );
  }

  /// `Retry`
  String get state_retry {
    return Intl.message(
      'Retry',
      name: 'state_retry',
      desc: '',
      args: [],
    );
  }

  /// `Not sign in yet`
  String get state_unauth {
    return Intl.message(
      'Not sign in yet',
      name: 'state_unauth',
      desc: '',
      args: [],
    );
  }

  /// `Submit`
  String get submit {
    return Intl.message(
      'Submit',
      name: 'submit',
      desc: '',
      args: [],
    );
  }

  /// `Device`
  String get tabbar_device {
    return Intl.message(
      'Device',
      name: 'tabbar_device',
      desc: '',
      args: [],
    );
  }

  /// `Store`
  String get tabbar_home {
    return Intl.message(
      'Store',
      name: 'tabbar_home',
      desc: '',
      args: [],
    );
  }

  /// `Me`
  String get tabbar_profile {
    return Intl.message(
      'Me',
      name: 'tabbar_profile',
      desc: '',
      args: [],
    );
  }

  /// `Community`
  String get tabbar_social {
    return Intl.message(
      'Community',
      name: 'tabbar_social',
      desc: '',
      args: [],
    );
  }

  /// `Photo`
  String get take_picture {
    return Intl.message(
      'Photo',
      name: 'take_picture',
      desc: '',
      args: [],
    );
  }

  /// `Taobao ID`
  String get taobaozhanghuming {
    return Intl.message(
      'Taobao ID',
      name: 'taobaozhanghuming',
      desc: '',
      args: [],
    );
  }

  /// `Add`
  String get tianjia {
    return Intl.message(
      'Add',
      name: 'tianjia',
      desc: '',
      args: [],
    );
  }

  /// `Add VR headset`
  String get tianjiaVRyanjing {
    return Intl.message(
      'Add VR headset',
      name: 'tianjiaVRyanjing',
      desc: '',
      args: [],
    );
  }

  /// `The request has been rejected. Please go back and reselect the desired VR headset.`
  String get tianjiaVRyanjingbeij {
    return Intl.message(
      'The request has been rejected. Please go back and reselect the desired VR headset.',
      name: 'tianjiaVRyanjingbeij',
      desc: '',
      args: [],
    );
  }

  /// `Before adding a VR headset, enable Bluetooth.`
  String get tianjiaVRyanjingqian {
    return Intl.message(
      'Before adding a VR headset, enable Bluetooth.',
      name: 'tianjiaVRyanjingqian',
      desc: '',
      args: [],
    );
  }

  /// `Add Friend`
  String get tianjiahaoyou {
    return Intl.message(
      'Add Friend',
      name: 'tianjiahaoyou',
      desc: '',
      args: [],
    );
  }

  /// `Add friends to learn more.`
  String get tianjiahaoyoukeyilia {
    return Intl.message(
      'Add friends to learn more.',
      name: 'tianjiahaoyoukeyilia',
      desc: '',
      args: [],
    );
  }

  /// `Add`
  String get tianjiashebei {
    return Intl.message(
      'Add',
      name: 'tianjiashebei',
      desc: '',
      args: [],
    );
  }

  /// `Tmall`
  String get tianmao {
    return Intl.message(
      'Tmall',
      name: 'tianmao',
      desc: '',
      args: [],
    );
  }

  /// `1 day ago`
  String get tianqian {
    return Intl.message(
      '1 day ago',
      name: 'tianqian',
      desc: '',
      args: [],
    );
  }

  /// `2 days ago`
  String get tianqian_1 {
    return Intl.message(
      '2 days ago',
      name: 'tianqian_1',
      desc: '',
      args: [],
    );
  }

  /// `Stop Projection`
  String get tingzhitoubingtingzh {
    return Intl.message(
      'Stop Projection',
      name: 'tingzhitoubingtingzh',
      desc: '',
      args: [],
    );
  }

  /// `Note`
  String get tips {
    return Intl.message(
      'Note',
      name: 'tips',
      desc: '',
      args: [],
    );
  }

  /// `You have already reviewed the app.`
  String get toast_already_commented {
    return Intl.message(
      'You have already reviewed the app.',
      name: 'toast_already_commented',
      desc: '',
      args: [],
    );
  }

  /// `You have bought the app already. Do not pay repeatedly.`
  String get toast_app_purchased {
    return Intl.message(
      'You have bought the app already. Do not pay repeatedly.',
      name: 'toast_app_purchased',
      desc: '',
      args: [],
    );
  }

  /// `Failed to upload the avatar.`
  String get toast_avatar_fail {
    return Intl.message(
      'Failed to upload the avatar.',
      name: 'toast_avatar_fail',
      desc: '',
      args: [],
    );
  }

  /// `Failed to upload the avatar.`
  String get toast_avatar_success {
    return Intl.message(
      'Failed to upload the avatar.',
      name: 'toast_avatar_success',
      desc: '',
      args: [],
    );
  }

  /// `Select birth date first.`
  String get toast_birthday {
    return Intl.message(
      'Select birth date first.',
      name: 'toast_birthday',
      desc: '',
      args: [],
    );
  }

  /// `The number of devices logged on has reached the limit.`
  String get toast_dev_limit {
    return Intl.message(
      'The number of devices logged on has reached the limit.',
      name: 'toast_dev_limit',
      desc: '',
      args: [],
    );
  }

  /// `Review content does not support stickers.`
  String get toast_emoticon_icon {
    return Intl.message(
      'Review content does not support stickers.',
      name: 'toast_emoticon_icon',
      desc: '',
      args: [],
    );
  }

  /// `This activity has been deleted by the creator.`
  String get toast_event_delete {
    return Intl.message(
      'This activity has been deleted by the creator.',
      name: 'toast_event_delete',
      desc: '',
      args: [],
    );
  }

  /// `Thanks for your feedback. We will\ncontact you within seven workdays.`
  String get toast_feedback_desc {
    return Intl.message(
      'Thanks for your feedback. We will\ncontact you within seven workdays.',
      name: 'toast_feedback_desc',
      desc: '',
      args: [],
    );
  }

  /// `Failed to submit feedback.`
  String get toast_feedback_fail {
    return Intl.message(
      'Failed to submit feedback.',
      name: 'toast_feedback_fail',
      desc: '',
      args: [],
    );
  }

  /// `Select a gender first.`
  String get toast_gender {
    return Intl.message(
      'Select a gender first.',
      name: 'toast_gender',
      desc: '',
      args: [],
    );
  }

  /// `Insufficient app stock.`
  String get toast_inventory_shortage {
    return Intl.message(
      'Insufficient app stock.',
      name: 'toast_inventory_shortage',
      desc: '',
      args: [],
    );
  }

  /// `An exception occurred.`
  String get toast_login_fail {
    return Intl.message(
      'An exception occurred.',
      name: 'toast_login_fail',
      desc: '',
      args: [],
    );
  }

  /// `Required information is missing.`
  String get toast_msg_not_input {
    return Intl.message(
      'Required information is missing.',
      name: 'toast_msg_not_input',
      desc: '',
      args: [],
    );
  }

  /// `Failed to modify information.`
  String get toast_msg_update_fail {
    return Intl.message(
      'Failed to modify information.',
      name: 'toast_msg_update_fail',
      desc: '',
      args: [],
    );
  }

  /// `Log in first.`
  String get toast_need_login {
    return Intl.message(
      'Log in first.',
      name: 'toast_need_login',
      desc: '',
      args: [],
    );
  }

  /// `The account is not registered yet.`
  String get toast_no_regist {
    return Intl.message(
      'The account is not registered yet.',
      name: 'toast_no_regist',
      desc: '',
      args: [],
    );
  }

  /// `The mobile number does not exist.`
  String get toast_not_exist {
    return Intl.message(
      'The mobile number does not exist.',
      name: 'toast_not_exist',
      desc: '',
      args: [],
    );
  }

  /// `Failed to place the order.`
  String get toast_order_fail {
    return Intl.message(
      'Failed to place the order.',
      name: 'toast_order_fail',
      desc: '',
      args: [],
    );
  }

  /// `Incorrect password`
  String get toast_pwd_error {
    return Intl.message(
      'Incorrect password',
      name: 'toast_pwd_error',
      desc: '',
      args: [],
    );
  }

  /// `Redemption failed.`
  String get toast_redemption_fail {
    return Intl.message(
      'Redemption failed.',
      name: 'toast_redemption_fail',
      desc: '',
      args: [],
    );
  }

  /// `Your refund request is accepted. The refund will be sent to the account used to purchase the app in 3 workdays.`
  String get toast_refund_desc {
    return Intl.message(
      'Your refund request is accepted. The refund will be sent to the account used to purchase the app in 3 workdays.',
      name: 'toast_refund_desc',
      desc: '',
      args: [],
    );
  }

  /// `Refund failed.`
  String get toast_refund_fail {
    return Intl.message(
      'Refund failed.',
      name: 'toast_refund_fail',
      desc: '',
      args: [],
    );
  }

  /// `Refunded.`
  String get toast_refund_success {
    return Intl.message(
      'Refunded.',
      name: 'toast_refund_success',
      desc: '',
      args: [],
    );
  }

  /// `The mobile number is already registered.`
  String get toast_regist_yet {
    return Intl.message(
      'The mobile number is already registered.',
      name: 'toast_regist_yet',
      desc: '',
      args: [],
    );
  }

  /// `Unknown error.`
  String get toast_unknown_error {
    return Intl.message(
      'Unknown error.',
      name: 'toast_unknown_error',
      desc: '',
      args: [],
    );
  }

  /// `The new and original password cannot be the same.`
  String get toast_update_pwd {
    return Intl.message(
      'The new and original password cannot be the same.',
      name: 'toast_update_pwd',
      desc: '',
      args: [],
    );
  }

  /// `User info updated.`
  String get toast_userinfo {
    return Intl.message(
      'User info updated.',
      name: 'toast_userinfo',
      desc: '',
      args: [],
    );
  }

  /// `Failed to request the verification code.`
  String get toast_vfcodeJ_send_fail {
    return Intl.message(
      'Failed to request the verification code.',
      name: 'toast_vfcodeJ_send_fail',
      desc: '',
      args: [],
    );
  }

  /// `Your requests for the verification code have exceeded the daily limit.`
  String get toast_vfcode_day {
    return Intl.message(
      'Your requests for the verification code have exceeded the daily limit.',
      name: 'toast_vfcode_day',
      desc: '',
      args: [],
    );
  }

  /// `Incorrect verification code.`
  String get toast_vfcode_error {
    return Intl.message(
      'Incorrect verification code.',
      name: 'toast_vfcode_error',
      desc: '',
      args: [],
    );
  }

  /// `Too many requests for the verification code.`
  String get toast_vfcode_frequently {
    return Intl.message(
      'Too many requests for the verification code.',
      name: 'toast_vfcode_frequently',
      desc: '',
      args: [],
    );
  }

  /// `Before network synchronization, ensure that the Bluetooth and location permissions are enabled. If the Wi-Fi has no password, directly tap OK.`
  String get tongbuqianqingxianqu {
    return Intl.message(
      'Before network synchronization, ensure that the Bluetooth and location permissions are enabled. If the Wi-Fi has no password, directly tap OK.',
      name: 'tongbuqianqingxianqu',
      desc: '',
      args: [],
    );
  }

  /// `Synchronize`
  String get tongbuwanglaotongbuw {
    return Intl.message(
      'Synchronize',
      name: 'tongbuwanglaotongbuw',
      desc: '',
      args: [],
    );
  }

  /// `Accept`
  String get tongguo {
    return Intl.message(
      'Accept',
      name: 'tongguo',
      desc: '',
      args: [],
    );
  }

  /// `Agree`
  String get tongyi {
    return Intl.message(
      'Agree',
      name: 'tongyi',
      desc: '',
      args: [],
    );
  }

  /// `Download failed. Go to the VR headset to manually download it.`
  String get tongzhiVRxiazaishiba {
    return Intl.message(
      'Download failed. Go to the VR headset to manually download it.',
      name: 'tongzhiVRxiazaishiba',
      desc: '',
      args: [],
    );
  }

  /// `Projection`
  String get toubingtouping {
    return Intl.message(
      'Projection',
      name: 'toubingtouping',
      desc: '',
      args: [],
    );
  }

  /// `Exit`
  String get tuichuzhuxiao {
    return Intl.message(
      'Exit',
      name: 'tuichuzhuxiao',
      desc: '',
      args: [],
    );
  }

  /// `Recommend`
  String get tuijian {
    return Intl.message(
      'Recommend',
      name: 'tuijian',
      desc: '',
      args: [],
    );
  }

  /// `Refund`
  String get tuikuan {
    return Intl.message(
      'Refund',
      name: 'tuikuan',
      desc: '',
      args: [],
    );
  }

  /// `Refund failed.`
  String get tuikuanshibai {
    return Intl.message(
      'Refund failed.',
      name: 'tuikuanshibai',
      desc: '',
      args: [],
    );
  }

  /// `Refund failed. The refund period has expired.`
  String get tuikuanshibaiyichaog {
    return Intl.message(
      'Refund failed. The refund period has expired.',
      name: 'tuikuanshibaiyichaog',
      desc: '',
      args: [],
    );
  }

  /// `Refunding...`
  String get tuikuanzhong {
    return Intl.message(
      'Refunding...',
      name: 'tuikuanzhong',
      desc: '',
      args: [],
    );
  }

  /// `Failed to upload the image. Please try again.`
  String get tupianshangchuanshib {
    return Intl.message(
      'Failed to upload the image. Please try again.',
      name: 'tupianshangchuanshib',
      desc: '',
      args: [],
    );
  }

  /// `Image compression failed.`
  String get tupianyasushibaitupi {
    return Intl.message(
      'Image compression failed.',
      name: 'tupianyasushibaitupi',
      desc: '',
      args: [],
    );
  }

  /// `OK`
  String get understood {
    return Intl.message(
      'OK',
      name: 'understood',
      desc: '',
      args: [],
    );
  }

  /// `Unknown`
  String get unknown {
    return Intl.message(
      'Unknown',
      name: 'unknown',
      desc: '',
      args: [],
    );
  }

  /// `Personal info`
  String get user_info {
    return Intl.message(
      'Personal info',
      name: 'user_info',
      desc: '',
      args: [],
    );
  }

  /// `Log on by verification code`
  String get verify_code_login {
    return Intl.message(
      'Log on by verification code',
      name: 'verify_code_login',
      desc: '',
      args: [],
    );
  }

  /// `Verification code sent. Please check.`
  String get vfcode_send {
    return Intl.message(
      'Verification code sent. Please check.',
      name: 'vfcode_send',
      desc: '',
      args: [],
    );
  }

  /// `Complete`
  String get wancheng {
    return Intl.message(
      'Complete',
      name: 'wancheng',
      desc: '',
      args: [],
    );
  }

  /// `Forgot?`
  String get wangjiliao {
    return Intl.message(
      'Forgot?',
      name: 'wangjiliao',
      desc: '',
      args: [],
    );
  }

  /// `The network is disconnected.`
  String get wanglaolianjieyiduan {
    return Intl.message(
      'The network is disconnected.',
      name: 'wanglaolianjieyiduan',
      desc: '',
      args: [],
    );
  }

  /// `Network error. Please try again later.`
  String get wanglaoyichangqingsh {
    return Intl.message(
      'Network error. Please try again later.',
      name: 'wanglaoyichangqingsh',
      desc: '',
      args: [],
    );
  }

  /// `Network error. Please try again later.`
  String get wanglaoyichangqingsh_1 {
    return Intl.message(
      'Network error. Please try again later.',
      name: 'wanglaoyichangqingsh_1',
      desc: '',
      args: [],
    );
  }

  /// `The splicing video is not saved.`
  String get weibaocunpinjieshipi {
    return Intl.message(
      'The splicing video is not saved.',
      name: 'weibaocunpinjieshipi',
      desc: '',
      args: [],
    );
  }

  /// `To ensure the security of your account, the following conditions must be met before the cancelation takes effect:`
  String get weibaozhengnindezhan {
    return Intl.message(
      'To ensure the security of your account, the following conditions must be met before the cancelation takes effect:',
      name: 'weibaozhengnindezhan',
      desc: '',
      args: [],
    );
  }

  /// `To ensure the recording effect, please hold your phone vertically during recording.`
  String get weibaozhengxiaoguoqi {
    return Intl.message(
      'To ensure the recording effect, please hold your phone vertically during recording.',
      name: 'weibaozhengxiaoguoqi',
      desc: '',
      args: [],
    );
  }

  /// `You are not friends yet.`
  String get weichengweihaoyou {
    return Intl.message(
      'You are not friends yet.',
      name: 'weichengweihaoyou',
      desc: '',
      args: [],
    );
  }

  /// `No webpage link is found. Please check the jump settings.`
  String get weichuanruwangyelian {
    return Intl.message(
      'No webpage link is found. Please check the jump settings.',
      name: 'weichuanruwangyelian',
      desc: '',
      args: [],
    );
  }

  /// `Illegal content`
  String get weifaweigui {
    return Intl.message(
      'Illegal content',
      name: 'weifaweigui',
      desc: '',
      args: [],
    );
  }

  /// `If the desired VR headset is not found, tap Search Again.`
  String get weifaxianxiangyaodeV {
    return Intl.message(
      'If the desired VR headset is not found, tap Search Again.',
      name: 'weifaxianxiangyaodeV',
      desc: '',
      args: [],
    );
  }

  /// `You have not followed this activity and cannot unfollow it.`
  String get weiguanzhugaihuodong {
    return Intl.message(
      'You have not followed this activity and cannot unfollow it.',
      name: 'weiguanzhugaihuodong',
      desc: '',
      args: [],
    );
  }

  /// `Invalid nickname. Please create a new one and re-submit.`
  String get weiguinichenqingchon {
    return Intl.message(
      'Invalid nickname. Please create a new one and re-submit.',
      name: 'weiguinichenqingchon',
      desc: '',
      args: [],
    );
  }

  /// `The location service is not enabled. Some functions are unavailable.`
  String get weikaiqidingweifuwub {
    return Intl.message(
      'The location service is not enabled. Some functions are unavailable.',
      name: 'weikaiqidingweifuwub',
      desc: '',
      args: [],
    );
  }

  /// `Not connected.`
  String get weilianjie {
    return Intl.message(
      'Not connected.',
      name: 'weilianjie',
      desc: '',
      args: [],
    );
  }

  /// `WeChat`
  String get weixin {
    return Intl.message(
      'WeChat',
      name: 'weixin',
      desc: '',
      args: [],
    );
  }

  /// `By WeChat`
  String get weixinzhifu {
    return Intl.message(
      'By WeChat',
      name: 'weixinzhifu',
      desc: '',
      args: [],
    );
  }

  /// `No related activity is found.`
  String get weizhaodaoxiangguanh {
    return Intl.message(
      'No related activity is found.',
      name: 'weizhaodaoxiangguanh',
      desc: '',
      args: [],
    );
  }

  /// `Unknown`
  String get weizhi {
    return Intl.message(
      'Unknown',
      name: 'weizhi',
      desc: '',
      args: [],
    );
  }

  /// `Unknown error.`
  String get weizhicuowu {
    return Intl.message(
      'Unknown error.',
      name: 'weizhicuowu',
      desc: '',
      args: [],
    );
  }

  /// `Welcome to Play for Dream`
  String get welcome {
    return Intl.message(
      'Welcome to Play for Dream',
      name: 'welcome',
      desc: '',
      args: [],
    );
  }

  /// `The file does not exist.`
  String get wenjianbucunzai {
    return Intl.message(
      'The file does not exist.',
      name: 'wenjianbucunzai',
      desc: '',
      args: [],
    );
  }

  /// `Kindly reminder`
  String get wenxindishiwenxintis {
    return Intl.message(
      'Kindly reminder',
      name: 'wenxindishiwenxintis',
      desc: '',
      args: [],
    );
  }

  /// `My headset`
  String get wodeVRyanjingwodiVRy {
    return Intl.message(
      'My headset',
      name: 'wodeVRyanjingwodiVRy',
      desc: '',
      args: [],
    );
  }

  /// `My friend`
  String get wodehaoyouwodihaoyou {
    return Intl.message(
      'My friend',
      name: 'wodehaoyouwodihaoyou',
      desc: '',
      args: [],
    );
  }

  /// `Blacklist`
  String get wodeheimingdan {
    return Intl.message(
      'Blacklist',
      name: 'wodeheimingdan',
      desc: '',
      args: [],
    );
  }

  /// `My Activity`
  String get wodehuodongwodihuodo {
    return Intl.message(
      'My Activity',
      name: 'wodehuodongwodihuodo',
      desc: '',
      args: [],
    );
  }

  /// `My Wallet`
  String get wodeqianbaowodiqianb {
    return Intl.message(
      'My Wallet',
      name: 'wodeqianbaowodiqianb',
      desc: '',
      args: [],
    );
  }

  /// `I have read and agreed to the terms and conditions of the {str_0}`
  String woyiyuedubingtongyiS(Object str_0) {
    return Intl.message(
      'I have read and agreed to the terms and conditions of the $str_0',
      name: 'woyiyuedubingtongyiS',
      desc: '',
      args: [str_0],
    );
  }

  /// `I have read and agree to YVR{str_0} and {str_1}.`
  String woyiyuedubingtongyiY(Object str_0, Object str_1) {
    return Intl.message(
      'I have read and agree to YVR$str_0 and $str_1.',
      name: 'woyiyuedubingtongyiY',
      desc: '',
      args: [str_0, str_1],
    );
  }

  /// `OK`
  String get wozhidaoliao {
    return Intl.message(
      'OK',
      name: 'wozhidaoliao',
      desc: '',
      args: [],
    );
  }

  /// `I'm interested`
  String get xiangcanjiaxiangcenj {
    return Intl.message(
      'I\'m interested',
      name: 'xiangcanjiaxiangcenj',
      desc: '',
      args: [],
    );
  }

  /// `Album`
  String get xiangce {
    return Intl.message(
      'Album',
      name: 'xiangce',
      desc: '',
      args: [],
    );
  }

  /// `according to the relevant terms of the User Privacy Policy.`
  String get xiangguantiaokuanchu {
    return Intl.message(
      'according to the relevant terms of the User Privacy Policy.',
      name: 'xiangguantiaokuanchu',
      desc: '',
      args: [],
    );
  }

  /// `Related apps`
  String get xiangguanyingyong {
    return Intl.message(
      'Related apps',
      name: 'xiangguanyingyong',
      desc: '',
      args: [],
    );
  }

  /// `Camera`
  String get xiangji {
    return Intl.message(
      'Camera',
      name: 'xiangji',
      desc: '',
      args: [],
    );
  }

  /// `camera error`
  String get xiangjicuowu {
    return Intl.message(
      'camera error',
      name: 'xiangjicuowu',
      desc: '',
      args: [],
    );
  }

  /// `Introduce yourself to people interested in you.`
  String get xiangxiangrenshinide {
    return Intl.message(
      'Introduce yourself to people interested in you.',
      name: 'xiangxiangrenshinide',
      desc: '',
      args: [],
    );
  }

  /// `Transaction time:`
  String get xiaofeishijian {
    return Intl.message(
      'Transaction time:',
      name: 'xiaofeishijian',
      desc: '',
      args: [],
    );
  }

  /// `Transaction Details`
  String get xiaofeixiangqing {
    return Intl.message(
      'Transaction Details',
      name: 'xiaofeixiangqing',
      desc: '',
      args: [],
    );
  }

  /// `1 h`
  String get xiaoshi {
    return Intl.message(
      '1 h',
      name: 'xiaoshi',
      desc: '',
      args: [],
    );
  }

  /// `3 h`
  String get xiaoshi_1 {
    return Intl.message(
      '3 h',
      name: 'xiaoshi_1',
      desc: '',
      args: [],
    );
  }

  /// `4 h`
  String get xiaoshi_2 {
    return Intl.message(
      '4 h',
      name: 'xiaoshi_2',
      desc: '',
      args: [],
    );
  }

  /// `2 h`
  String get xiaoshi_3 {
    return Intl.message(
      '2 h',
      name: 'xiaoshi_3',
      desc: '',
      args: [],
    );
  }

  /// `5 h`
  String get xiaoshi_4 {
    return Intl.message(
      '5 h',
      name: 'xiaoshi_4',
      desc: '',
      args: [],
    );
  }

  /// `p.m.`
  String get xiawu {
    return Intl.message(
      'p.m.',
      name: 'xiawu',
      desc: '',
      args: [],
    );
  }

  /// `Next`
  String get xiayibu {
    return Intl.message(
      'Next',
      name: 'xiayibu',
      desc: '',
      args: [],
    );
  }

  /// `Download App`
  String get xiazaiyingyong {
    return Intl.message(
      'Download App',
      name: 'xiazaiyingyong',
      desc: '',
      args: [],
    );
  }

  /// `Add period`
  String get xinzengshijianduan {
    return Intl.message(
      'Add period',
      name: 'xinzengshijianduan',
      desc: '',
      args: [],
    );
  }

  /// `Disable teen mode before modification.`
  String get xiugaiqianqingxiangu {
    return Intl.message(
      'Disable teen mode before modification.',
      name: 'xiugaiqianqingxiangu',
      desc: '',
      args: [],
    );
  }

  /// `The new mobile number cannot be the same as the old one.`
  String get xiugaishoujihaobunen {
    return Intl.message(
      'The new mobile number cannot be the same as the old one.',
      name: 'xiugaishoujihaobunen',
      desc: '',
      args: [],
    );
  }

  /// `Select a device and try the app on the device.`
  String get xuanzeshebeiqushebei {
    return Intl.message(
      'Select a device and try the app on the device.',
      name: 'xuanzeshebeiqushebei',
      desc: '',
      args: [],
    );
  }

  /// `Select Device`
  String get xuanzeshebeixuanzhai {
    return Intl.message(
      'Select Device',
      name: 'xuanzeshebeixuanzhai',
      desc: '',
      args: [],
    );
  }

  /// `Select App`
  String get xuanzeyingyongxuanzh {
    return Intl.message(
      'Select App',
      name: 'xuanzeyingyongxuanzh',
      desc: '',
      args: [],
    );
  }

  /// `You can view downloaded apps only after you log in.`
  String get xuyaodenglucainengch {
    return Intl.message(
      'You can view downloaded apps only after you log in.',
      name: 'xuyaodenglucainengch',
      desc: '',
      args: [],
    );
  }

  /// `You can view your app only after you log in.`
  String get xuyaodenglucainengch_1 {
    return Intl.message(
      'You can view your app only after you log in.',
      name: 'xuyaodenglucainengch_1',
      desc: '',
      args: [],
    );
  }

  /// `You can enter the notification center only after you log in.`
  String get xuyaodenglucainengch_2 {
    return Intl.message(
      'You can enter the notification center only after you log in.',
      name: 'xuyaodenglucainengch_2',
      desc: '',
      args: [],
    );
  }

  /// `You can purchase it only after you log in.`
  String get xuyaodenglucainenggo {
    return Intl.message(
      'You can purchase it only after you log in.',
      name: 'xuyaodenglucainenggo',
      desc: '',
      args: [],
    );
  }

  /// `You can report it only after you log in.`
  String get xuyaodenglucainengju {
    return Intl.message(
      'You can report it only after you log in.',
      name: 'xuyaodenglucainengju',
      desc: '',
      args: [],
    );
  }

  /// `Authenticate parent identity`
  String get yanzhengguchangshenf {
    return Intl.message(
      'Authenticate parent identity',
      name: 'yanzhengguchangshenf',
      desc: '',
      args: [],
    );
  }

  /// `Incorrect verification code.`
  String get yanzhengmabuzhengque {
    return Intl.message(
      'Incorrect verification code.',
      name: 'yanzhengmabuzhengque',
      desc: '',
      args: [],
    );
  }

  /// `Incorrect verification code.`
  String get yanzhengmacuowu {
    return Intl.message(
      'Incorrect verification code.',
      name: 'yanzhengmacuowu',
      desc: '',
      args: [],
    );
  }

  /// `Verify Identity`
  String get yanzhengshenfen {
    return Intl.message(
      'Verify Identity',
      name: 'yanzhengshenfen',
      desc: '',
      args: [],
    );
  }

  /// `Authentication failed.`
  String get yanzhengshibai {
    return Intl.message(
      'Authentication failed.',
      name: 'yanzhengshibai',
      desc: '',
      args: [],
    );
  }

  /// `To join this activity, you need to download {str_0}.`
  String yaocanjiahuodongnixu(Object str_0) {
    return Intl.message(
      'To join this activity, you need to download $str_0.',
      name: 'yaocanjiahuodongnixu',
      desc: '',
      args: [str_0],
    );
  }

  /// `Invite Friend`
  String get yaoqinghaoyou {
    return Intl.message(
      'Invite Friend',
      name: 'yaoqinghaoyou',
      desc: '',
      args: [],
    );
  }

  /// `Saved.`
  String get yibaocun {
    return Intl.message(
      'Saved.',
      name: 'yibaocun',
      desc: '',
      args: [],
    );
  }

  /// `Saved`
  String get yibaocun_1 {
    return Intl.message(
      'Saved',
      name: 'yibaocun_1',
      desc: '',
      args: [],
    );
  }

  /// `Saved to`
  String get yibaocunzhi {
    return Intl.message(
      'Saved to',
      name: 'yibaocunzhi',
      desc: '',
      args: [],
    );
  }

  /// `Saved to the album.`
  String get yibaocunzhixiangce {
    return Intl.message(
      'Saved to the album.',
      name: 'yibaocunzhixiangce',
      desc: '',
      args: [],
    );
  }

  /// `Already signed up`
  String get yibaoming {
    return Intl.message(
      'Already signed up',
      name: 'yibaoming',
      desc: '',
      args: [],
    );
  }

  /// `Joined in this activity`
  String get yicanjiagaihuodongyi {
    return Intl.message(
      'Joined in this activity',
      name: 'yicanjiagaihuodongyi',
      desc: '',
      args: [],
    );
  }

  /// `Already joined in`
  String get yicanjiayicenjiayisa {
    return Intl.message(
      'Already joined in',
      name: 'yicanjiayicenjiayisa',
      desc: '',
      args: [],
    );
  }

  /// `Invitation sent.`
  String get yichenggongfachuyaoq {
    return Intl.message(
      'Invitation sent.',
      name: 'yichenggongfachuyaoq',
      desc: '',
      args: [],
    );
  }

  /// `You have joined in this activity.`
  String get yichenggongjiaruhuod {
    return Intl.message(
      'You have joined in this activity.',
      name: 'yichenggongjiaruhuod',
      desc: '',
      args: [],
    );
  }

  /// `Delete`
  String get yichu {
    return Intl.message(
      'Delete',
      name: 'yichu',
      desc: '',
      args: [],
    );
  }

  /// ` Delete VR Headset`
  String get yichuciVRyanjing {
    return Intl.message(
      ' Delete VR Headset',
      name: 'yichuciVRyanjing',
      desc: '',
      args: [],
    );
  }

  /// `Unblocked.`
  String get yichuheimingchanchen {
    return Intl.message(
      'Unblocked.',
      name: 'yichuheimingchanchen',
      desc: '',
      args: [],
    );
  }

  /// `Remove from Blacklist`
  String get yichuheimingchanyich {
    return Intl.message(
      'Remove from Blacklist',
      name: 'yichuheimingchanyich',
      desc: '',
      args: [],
    );
  }

  /// `Remove from Blacklist`
  String get yichuheimingdan {
    return Intl.message(
      'Remove from Blacklist',
      name: 'yichuheimingdan',
      desc: '',
      args: [],
    );
  }

  /// `After this VR headset is deleted, it will automatically log out of \nthe original login account when connected to network. Are you sure you want to proceed?`
  String get yichuhouVRjiangzaili {
    return Intl.message(
      'After this VR headset is deleted, it will automatically log out of \nthe original login account when connected to network. Are you sure you want to proceed?',
      name: 'yichuhouVRjiangzaili',
      desc: '',
      args: [],
    );
  }

  /// `Request sent.`
  String get yifasongqingqiuyifei {
    return Intl.message(
      'Request sent.',
      name: 'yifasongqingqiuyifei',
      desc: '',
      args: [],
    );
  }

  /// `I. We regret that we will no longer provide services for you. If you still choose to cancel your account, you are deemed to automatically give up your virtual assets (including but not limited to the following rights and interests) under the account. You understand and agree that we cannot assist you in restoring the preceding services.`
  String get yifeichangweihanwome {
    return Intl.message(
      'I. We regret that we will no longer provide services for you. If you still choose to cancel your account, you are deemed to automatically give up your virtual assets (including but not limited to the following rights and interests) under the account. You understand and agree that we cannot assist you in restoring the preceding services.',
      name: 'yifeichangweihanwome',
      desc: '',
      args: [],
    );
  }

  /// `Purchased`
  String get yigoumai {
    return Intl.message(
      'Purchased',
      name: 'yigoumai',
      desc: '',
      args: [],
    );
  }

  /// `Personalized recommendation has been disabled.`
  String get yiguanbigexinghuanar {
    return Intl.message(
      'Personalized recommendation has been disabled.',
      name: 'yiguanbigexinghuanar',
      desc: '',
      args: [],
    );
  }

  /// `You have joined in this activity.`
  String get yijiarugaihuodong {
    return Intl.message(
      'You have joined in this activity.',
      name: 'yijiarugaihuodong',
      desc: '',
      args: [],
    );
  }

  /// `Blocked.`
  String get yijiaruheimingchanyi {
    return Intl.message(
      'Blocked.',
      name: 'yijiaruheimingchanyi',
      desc: '',
      args: [],
    );
  }

  /// `Already ended`
  String get yijieshu {
    return Intl.message(
      'Already ended',
      name: 'yijieshu',
      desc: '',
      args: [],
    );
  }

  /// `You have already followed this activity and cannot follow it again.`
  String get yijingguanzhugaihuod {
    return Intl.message(
      'You have already followed this activity and cannot follow it again.',
      name: 'yijingguanzhugaihuod',
      desc: '',
      args: [],
    );
  }

  /// `You have received the reward.`
  String get yilingjiang {
    return Intl.message(
      'You have received the reward.',
      name: 'yilingjiang',
      desc: '',
      args: [],
    );
  }

  /// `No more files.`
  String get yimogengduowenjianyi {
    return Intl.message(
      'No more files.',
      name: 'yimogengduowenjianyi',
      desc: '',
      args: [],
    );
  }

  /// `No more apps.`
  String get yimogengduoyingyongy {
    return Intl.message(
      'No more apps.',
      name: 'yimogengduoyingyongy',
      desc: '',
      args: [],
    );
  }

  /// `Purchased.`
  String get yingyonggoumaichengg {
    return Intl.message(
      'Purchased.',
      name: 'yingyonggoumaichengg',
      desc: '',
      args: [],
    );
  }

  /// `Notification of App Promotion`
  String get yingyongtuiandetongz {
    return Intl.message(
      'Notification of App Promotion',
      name: 'yingyongtuiandetongz',
      desc: '',
      args: [],
    );
  }

  /// `The app has been removed or does not exist.`
  String get yingyongyixiajiahuob {
    return Intl.message(
      'The app has been removed or does not exist.',
      name: 'yingyongyixiajiahuob',
      desc: '',
      args: [],
    );
  }

  /// `Privacy management`
  String get yinsiguanli {
    return Intl.message(
      'Privacy management',
      name: 'yinsiguanli',
      desc: '',
      args: [],
    );
  }

  /// `Synthesis canceled. Please record again.`
  String get yiquxiaogechengqingc {
    return Intl.message(
      'Synthesis canceled. Please record again.',
      name: 'yiquxiaogechengqingc',
      desc: '',
      args: [],
    );
  }

  /// `You have exited this activity.`
  String get yituichugaihuodong {
    return Intl.message(
      'You have exited this activity.',
      name: 'yituichugaihuodong',
      desc: '',
      args: [],
    );
  }

  /// `You have exited this activity.`
  String get yituichugaihuodong_1 {
    return Intl.message(
      'You have exited this activity.',
      name: 'yituichugaihuodong_1',
      desc: '',
      args: [],
    );
  }

  /// `Played`
  String get yiwan {
    return Intl.message(
      'Played',
      name: 'yiwan',
      desc: '',
      args: [],
    );
  }

  /// `Time played: {num_0} minutes.`
  String yiwanNfenzhong(Object num_0) {
    return Intl.message(
      'Time played: $num_0 minutes.',
      name: 'yiwanNfenzhong',
      desc: '',
      args: [num_0],
    );
  }

  /// ` Time played: {num_0} hours.`
  String yiwanNxiaoshi(Object num_0) {
    return Intl.message(
      ' Time played: $num_0 hours.',
      name: 'yiwanNxiaoshi',
      desc: '',
      args: [num_0],
    );
  }

  /// `You have checked in.`
  String get yiwanchengdakayiwanc {
    return Intl.message(
      'You have checked in.',
      name: 'yiwanchengdakayiwanc',
      desc: '',
      args: [],
    );
  }

  /// `A request for adding the VR headset has been sent. Go to the VR headset and click Agree.`
  String get yixiangVRyanjingfaso {
    return Intl.message(
      'A request for adding the VR headset has been sent. Go to the VR headset and click Agree.',
      name: 'yixiangVRyanjingfaso',
      desc: '',
      args: [],
    );
  }

  /// `User Privacy Policy`
  String get yonghuyinsizhengce {
    return Intl.message(
      'User Privacy Policy',
      name: 'yonghuyinsizhengce',
      desc: '',
      args: [],
    );
  }

  /// `3. Coupons: {num_0}`
  String youhuquanNzhang3youh(Object num_0) {
    return Intl.message(
      '3. Coupons: $num_0',
      name: 'youhuquanNzhang3youh',
      desc: '',
      args: [num_0],
    );
  }

  /// `Notification of Coupons Distribution`
  String get youhuquanfafangtongz {
    return Intl.message(
      'Notification of Coupons Distribution',
      name: 'youhuquanfafangtongz',
      desc: '',
      args: [],
    );
  }

  /// `coupons`
  String get youhuquanyouhuiquany {
    return Intl.message(
      'coupons',
      name: 'youhuquanyouhuiquany',
      desc: '',
      args: [],
    );
  }

  /// `Coupons`
  String get youhuquanyouhuiquany_1 {
    return Intl.message(
      'Coupons',
      name: 'youhuquanyouhuiquany_1',
      desc: '',
      args: [],
    );
  }

  /// `Games Owned`
  String get youhushuyouhuishuyou {
    return Intl.message(
      'Games Owned',
      name: 'youhushuyouhuishuyou',
      desc: '',
      args: [],
    );
  }

  /// `Valid until:`
  String get youxiaoqizhi {
    return Intl.message(
      'Valid until:',
      name: 'youxiaoqizhi',
      desc: '',
      args: [],
    );
  }

  /// `CNY 1 = 10 Y Coins`
  String get yuan10Ybi {
    return Intl.message(
      'CNY 1 = 10 Y Coins',
      name: 'yuan10Ybi',
      desc: '',
      args: [],
    );
  }

  /// `Passwords do not match.`
  String get yudiyicishurudemimab {
    return Intl.message(
      'Passwords do not match.',
      name: 'yudiyicishurudemimab',
      desc: '',
      args: [],
    );
  }

  /// `{num_0} seconds left`
  String yujihaixuNmiao(Object num_0) {
    return Intl.message(
      '$num_0 seconds left',
      name: 'yujihaixuNmiao',
      desc: '',
      args: [num_0],
    );
  }

  /// `Preview Image`
  String get yulantupian {
    return Intl.message(
      'Preview Image',
      name: 'yulantupian',
      desc: '',
      args: [],
    );
  }

  /// `Allow joined users to invite friends`
  String get yunxuyijiaruyonghuya {
    return Intl.message(
      'Allow joined users to invite friends',
      name: 'yunxuyijiaruyonghuya',
      desc: '',
      args: [],
    );
  }

  /// `Registration by mobile number means that you have agreed to YVR`
  String get yvr_protocol {
    return Intl.message(
      'Registration by mobile number means that you have agreed to YVR',
      name: 'yvr_protocol',
      desc: '',
      args: [],
    );
  }

  /// `Registration by email means that you have agreed to YVR{str_0} and {str_1}`
  String yvr_protocol2(Object str_0, Object str_1) {
    return Intl.message(
      'Registration by email means that you have agreed to YVR$str_0 and $str_1',
      name: 'yvr_protocol2',
      desc: '',
      args: [str_0, str_1],
    );
  }

  /// `Join in from VR`
  String get zaiVRzhongjiaru {
    return Intl.message(
      'Join in from VR',
      name: 'zaiVRzhongjiaru',
      desc: '',
      args: [],
    );
  }

  /// `Before you request account cancelation, please carefully read and agree to this Account Cancellation Agreement.`
  String get zaininshenqingzhuxia {
    return Intl.message(
      'Before you request account cancelation, please carefully read and agree to this Account Cancellation Agreement.',
      name: 'zaininshenqingzhuxia',
      desc: '',
      args: [],
    );
  }

  /// `No`
  String get zanbu {
    return Intl.message(
      'No',
      name: 'zanbu',
      desc: '',
      args: [],
    );
  }

  /// `Maybe Later`
  String get zanbugengxin {
    return Intl.message(
      'Maybe Later',
      name: 'zanbugengxin',
      desc: '',
      args: [],
    );
  }

  /// `Later`
  String get zanbushiyong {
    return Intl.message(
      'Later',
      name: 'zanbushiyong',
      desc: '',
      args: [],
    );
  }

  /// `No more posts.`
  String get zanmogengduodongtaiz {
    return Intl.message(
      'No more posts.',
      name: 'zanmogengduodongtaiz',
      desc: '',
      args: [],
    );
  }

  /// `No friend found.`
  String get zanmohaoyouzanwuhaoy {
    return Intl.message(
      'No friend found.',
      name: 'zanmohaoyouzanwuhaoy',
      desc: '',
      args: [],
    );
  }

  /// `No screenshot or recording file available.`
  String get zanmojiebinghelubing {
    return Intl.message(
      'No screenshot or recording file available.',
      name: 'zanmojiebinghelubing',
      desc: '',
      args: [],
    );
  }

  /// `No record available.`
  String get zanmojiluzanwujilu {
    return Intl.message(
      'No record available.',
      name: 'zanmojiluzanwujilu',
      desc: '',
      args: [],
    );
  }

  /// `No voucher available.`
  String get zanmokaquanzanwukaqu {
    return Intl.message(
      'No voucher available.',
      name: 'zanmokaquanzanwukaqu',
      desc: '',
      args: [],
    );
  }

  /// `No voucher available.`
  String get zanmokeyongzanwukeyo {
    return Intl.message(
      'No voucher available.',
      name: 'zanmokeyongzanwukeyo',
      desc: '',
      args: [],
    );
  }

  /// `No usage instruction is available.`
  String get zanmoshiyongshuiming {
    return Intl.message(
      'No usage instruction is available.',
      name: 'zanmoshiyongshuiming',
      desc: '',
      args: [],
    );
  }

  /// `Unable to obtain the device binding information.`
  String get zanshimofahuoqushebe {
    return Intl.message(
      'Unable to obtain the device binding information.',
      name: 'zanshimofahuoqushebe',
      desc: '',
      args: [],
    );
  }

  /// `No app has been purchased yet.`
  String get zanweigoumaiyingyong {
    return Intl.message(
      'No app has been purchased yet.',
      name: 'zanweigoumaiyingyong',
      desc: '',
      args: [],
    );
  }

  /// `Bill`
  String get zhangchanzhangdanzha {
    return Intl.message(
      'Bill',
      name: 'zhangchanzhangdanzha',
      desc: '',
      args: [],
    );
  }

  /// `The account does not exist.`
  String get zhanghaobucunzai {
    return Intl.message(
      'The account does not exist.',
      name: 'zhanghaobucunzai',
      desc: '',
      args: [],
    );
  }

  /// `Account login`
  String get zhanghaodenglu {
    return Intl.message(
      'Account login',
      name: 'zhanghaodenglu',
      desc: '',
      args: [],
    );
  }

  /// `The account has been canceled.`
  String get zhanghaoyishenqingzh {
    return Intl.message(
      'The account has been canceled.',
      name: 'zhanghaoyishenqingzh',
      desc: '',
      args: [],
    );
  }

  /// `The account is invalid.`
  String get zhanghaoyishixiao {
    return Intl.message(
      'The account is invalid.',
      name: 'zhanghaoyishixiao',
      desc: '',
      args: [],
    );
  }

  /// `The account has been canceled.`
  String get zhanghaoyizhuxiao {
    return Intl.message(
      'The account has been canceled.',
      name: 'zhanghaoyizhuxiao',
      desc: '',
      args: [],
    );
  }

  /// `Account & Security`
  String get zhanghaoyuanquan {
    return Intl.message(
      'Account & Security',
      name: 'zhanghaoyuanquan',
      desc: '',
      args: [],
    );
  }

  /// `Account cancelation failed.`
  String get zhanghaozhuxiaoshiba {
    return Intl.message(
      'Account cancelation failed.',
      name: 'zhanghaozhuxiaoshiba',
      desc: '',
      args: [],
    );
  }

  /// `Account & Security`
  String get zhanghuyuanquan {
    return Intl.message(
      'Account & Security',
      name: 'zhanghuyuanquan',
      desc: '',
      args: [],
    );
  }

  /// `Expand`
  String get zhankai {
    return Intl.message(
      'Expand',
      name: 'zhankai',
      desc: '',
      args: [],
    );
  }

  /// `It is the video title.`
  String get zhegeshishipindebiao {
    return Intl.message(
      'It is the video title.',
      name: 'zhegeshishipindebiao',
      desc: '',
      args: [],
    );
  }

  /// `is in the YVR hall.`
  String get zhengzaiYVRdatingzhe {
    return Intl.message(
      'is in the YVR hall.',
      name: 'zhengzaiYVRdatingzhe',
      desc: '',
      args: [],
    );
  }

  /// `Saving...`
  String get zhengzaibaocun {
    return Intl.message(
      'Saving...',
      name: 'zhengzaibaocun',
      desc: '',
      args: [],
    );
  }

  /// `Trying to reconnect...`
  String get zhengzaichangshichon {
    return Intl.message(
      'Trying to reconnect...',
      name: 'zhengzaichangshichon',
      desc: '',
      args: [],
    );
  }

  /// `Trying to connect another device...`
  String get zhengzaichangshilian {
    return Intl.message(
      'Trying to connect another device...',
      name: 'zhengzaichangshilian',
      desc: '',
      args: [],
    );
  }

  /// `The app is being opened. Do not repeat this operation.`
  String get zhengzaidakaiyingyon {
    return Intl.message(
      'The app is being opened. Do not repeat this operation.',
      name: 'zhengzaidakaiyingyon',
      desc: '',
      args: [],
    );
  }

  /// `Ongoing`
  String get zhengzaijinhangzhong {
    return Intl.message(
      'Ongoing',
      name: 'zhengzaijinhangzhong',
      desc: '',
      args: [],
    );
  }

  /// `An ongoing activity cannot be deleted.`
  String get zhengzaijinhangzhong_1 {
    return Intl.message(
      'An ongoing activity cannot be deleted.',
      name: 'zhengzaijinhangzhong_1',
      desc: '',
      args: [],
    );
  }

  /// `Connecting VR headset...`
  String get zhengzailianjieVRyan {
    return Intl.message(
      'Connecting VR headset...',
      name: 'zhengzailianjieVRyan',
      desc: '',
      args: [],
    );
  }

  /// `Connecting...`
  String get zhengzailianjiezhong {
    return Intl.message(
      'Connecting...',
      name: 'zhengzailianjiezhong',
      desc: '',
      args: [],
    );
  }

  /// `Switching device...`
  String get zhengzaiqiehuanshebe {
    return Intl.message(
      'Switching device...',
      name: 'zhengzaiqiehuanshebe',
      desc: '',
      args: [],
    );
  }

  /// `Searching for the VR headset...`
  String get zhengzaiquanlisousuo {
    return Intl.message(
      'Searching for the VR headset...',
      name: 'zhengzaiquanlisousuo',
      desc: '',
      args: [],
    );
  }

  /// `is using {str_0}.`
  String zhengzaishiyongS(Object str_0) {
    return Intl.message(
      'is using $str_0.',
      name: 'zhengzaishiyongS',
      desc: '',
      args: [str_0],
    );
  }

  /// `is using an unknown app.`
  String get zhengzaishiyongweizh {
    return Intl.message(
      'is using an unknown app.',
      name: 'zhengzaishiyongweizh',
      desc: '',
      args: [],
    );
  }

  /// `is using {str_0}.`
  String zhengzaishiyongyingy(Object str_0) {
    return Intl.message(
      'is using $str_0.',
      name: 'zhengzaishiyongyingy',
      desc: '',
      args: [str_0],
    );
  }

  /// `Searching...`
  String get zhengzaisousuo {
    return Intl.message(
      'Searching...',
      name: 'zhengzaisousuo',
      desc: '',
      args: [],
    );
  }

  /// `Connecting the VR headset to Wi-Fi... Please wait...`
  String get zhengzaiweiVRyanjing {
    return Intl.message(
      'Connecting the VR headset to Wi-Fi... Please wait...',
      name: 'zhengzaiweiVRyanjing',
      desc: '',
      args: [],
    );
  }

  /// `OK`
  String get zhidaoliao {
    return Intl.message(
      'OK',
      name: 'zhidaoliao',
      desc: '',
      args: [],
    );
  }

  /// `Authenticated real name for the Bank account`
  String get zhifubaozhanghaodesh {
    return Intl.message(
      'Authenticated real name for the Bank account',
      name: 'zhifubaozhanghaodesh',
      desc: '',
      args: [],
    );
  }

  /// `Bank account (for receiving cashback)`
  String get zhifubaozhanghaoyong {
    return Intl.message(
      'Bank account (for receiving cashback)',
      name: 'zhifubaozhanghaoyong',
      desc: '',
      args: [],
    );
  }

  /// `By Alipay`
  String get zhifubaozhifu {
    return Intl.message(
      'By Alipay',
      name: 'zhifubaozhifu',
      desc: '',
      args: [],
    );
  }

  /// `Paid.`
  String get zhifuchenggong {
    return Intl.message(
      'Paid.',
      name: 'zhifuchenggong',
      desc: '',
      args: [],
    );
  }

  /// `Payment method:`
  String get zhifufangshi {
    return Intl.message(
      'Payment method:',
      name: 'zhifufangshi',
      desc: '',
      args: [],
    );
  }

  /// `Payment method`
  String get zhifufangshi_1 {
    return Intl.message(
      'Payment method',
      name: 'zhifufangshi_1',
      desc: '',
      args: [],
    );
  }

  /// `Payment result`
  String get zhifujieguo {
    return Intl.message(
      'Payment result',
      name: 'zhifujieguo',
      desc: '',
      args: [],
    );
  }

  /// `Payment amount:`
  String get zhifujine {
    return Intl.message(
      'Payment amount:',
      name: 'zhifujine',
      desc: '',
      args: [],
    );
  }

  /// `Payment failed.`
  String get zhifushibai {
    return Intl.message(
      'Payment failed.',
      name: 'zhifushibai',
      desc: '',
      args: [],
    );
  }

  /// `Payment time:`
  String get zhifushijian {
    return Intl.message(
      'Payment time:',
      name: 'zhifushijian',
      desc: '',
      args: [],
    );
  }

  /// `Only the activity creator has this permission.`
  String get zhiyouhuodongchuangj {
    return Intl.message(
      'Only the activity creator has this permission.',
      name: 'zhiyouhuodongchuangj',
      desc: '',
      args: [],
    );
  }

  /// `Saturday to Sunday`
  String get zhouliuzhizhourizhou {
    return Intl.message(
      'Saturday to Sunday',
      name: 'zhouliuzhizhourizhou',
      desc: '',
      args: [],
    );
  }

  /// `Monday to Friday`
  String get zhouyizhizhouwu {
    return Intl.message(
      'Monday to Friday',
      name: 'zhouyizhizhouwu',
      desc: '',
      args: [],
    );
  }

  /// `Topic`
  String get zhuanti {
    return Intl.message(
      'Topic',
      name: 'zhuanti',
      desc: '',
      args: [],
    );
  }

  /// `The topic has expired or does not exist.`
  String get zhuantiyiguoqihuobuc {
    return Intl.message(
      'The topic has expired or does not exist.',
      name: 'zhuantiyiguoqihuobuc',
      desc: '',
      args: [],
    );
  }

  /// `The subject has expired or does not exist.`
  String get zhutiyiguoqihuobucun {
    return Intl.message(
      'The subject has expired or does not exist.',
      name: 'zhutiyiguoqihuobucun',
      desc: '',
      args: [],
    );
  }

  /// `Canceled. Your mobile phone has been unbound with the third party.`
  String get zhuxiaochenggongshou {
    return Intl.message(
      'Canceled. Your mobile phone has been unbound with the third party.',
      name: 'zhuxiaochenggongshou',
      desc: '',
      args: [],
    );
  }

  /// `Account Cancelation Agreement`
  String get zhuxiaoxieyi {
    return Intl.message(
      'Account Cancelation Agreement',
      name: 'zhuxiaoxieyi',
      desc: '',
      args: [],
    );
  }

  /// `Account Cancelation Agreement`
  String get zhuxiaoxieyi_1 {
    return Intl.message(
      'Account Cancelation Agreement',
      name: 'zhuxiaoxieyi_1',
      desc: '',
      args: [],
    );
  }

  /// `Cancel account`
  String get zhuxiaozhanghao {
    return Intl.message(
      'Cancel account',
      name: 'zhuxiaozhanghao',
      desc: '',
      args: [],
    );
  }

  /// `1.请再次确认信息填写是否有误，审核中无法修改信息。\n2.提交后，将生成返现流程码，请联系原销售渠道员工/客服，进行最终核对。\n3.您所提交的所有信息我们将信息保密处理，不与第三方共享，继续提交将视为您已同意向信息授权给YVR进行合法校验。`
  String get zhuyireshenhetongguo {
    return Intl.message(
      '1.请再次确认信息填写是否有误，审核中无法修改信息。\n2.提交后，将生成返现流程码，请联系原销售渠道员工/客服，进行最终核对。\n3.您所提交的所有信息我们将信息保密处理，不与第三方共享，继续提交将视为您已同意向信息授权给YVR进行合法校验。',
      name: 'zhuyireshenhetongguo',
      desc: '',
      args: [],
    );
  }

  /// `You cannot exit an activity created by yourself.`
  String get zijichuangjiandehuod {
    return Intl.message(
      'You cannot exit an activity created by yourself.',
      name: 'zijichuangjiandehuod',
      desc: '',
      args: [],
    );
  }

  /// `Total balance`
  String get zongyue {
    return Intl.message(
      'Total balance',
      name: 'zongyue',
      desc: '',
      args: [],
    );
  }

  /// `A maximum of {num_0} images are allowed.`
  String zuiduoyunxushangchua(Object num_0) {
    return Intl.message(
      'A maximum of $num_0 images are allowed.',
      name: 'zuiduoyunxushangchua',
      desc: '',
      args: [num_0],
    );
  }

  /// `Recent week`
  String get zuijinyizhou {
    return Intl.message(
      'Recent week',
      name: 'zuijinyizhou',
      desc: '',
      args: [],
    );
  }

  /// `Hot`
  String get zuirehuodong {
    return Intl.message(
      'Hot',
      name: 'zuirehuodong',
      desc: '',
      args: [],
    );
  }

  /// `New`
  String get zuixinhuodong {
    return Intl.message(
      'New',
      name: 'zuixinhuodong',
      desc: '',
      args: [],
    );
  }

  /// ` `
  String get cardsForCN {
    return Intl.message(
      ' ',
      name: 'cardsForCN',
      desc: '',
      args: [],
    );
  }

  /// `Y币记录`
  String get yCoinRecord {
    return Intl.message(
      'Y币记录',
      name: 'yCoinRecord',
      desc: '',
      args: [],
    );
  }

  /// `支付方式管理`
  String get payWayManage {
    return Intl.message(
      '支付方式管理',
      name: 'payWayManage',
      desc: '',
      args: [],
    );
  }

  /// `优惠券`
  String get coupon {
    return Intl.message(
      '优惠券',
      name: 'coupon',
      desc: '',
      args: [],
    );
  }

  /// `去绑定`
  String get toBind {
    return Intl.message(
      '去绑定',
      name: 'toBind',
      desc: '',
      args: [],
    );
  }

  /// `解除绑定`
  String get unbind {
    return Intl.message(
      '解除绑定',
      name: 'unbind',
      desc: '',
      args: [],
    );
  }

  /// `绑定失败`
  String get bindFail {
    return Intl.message(
      '绑定失败',
      name: 'bindFail',
      desc: '',
      args: [],
    );
  }

  /// `解绑失败`
  String get unbindFail {
    return Intl.message(
      '解绑失败',
      name: 'unbindFail',
      desc: '',
      args: [],
    );
  }

  /// `绑定成功`
  String get bindSuccess {
    return Intl.message(
      '绑定成功',
      name: 'bindSuccess',
      desc: '',
      args: [],
    );
  }

  /// `解绑成功`
  String get unbindSuccess {
    return Intl.message(
      '解绑成功',
      name: 'unbindSuccess',
      desc: '',
      args: [],
    );
  }

  /// `确定要解绑支付宝免密支付吗？`
  String get unbindAlipay {
    return Intl.message(
      '确定要解绑支付宝免密支付吗？',
      name: 'unbindAlipay',
      desc: '',
      args: [],
    );
  }

  /// `确认签约`
  String get confirmTheContract {
    return Intl.message(
      '确认签约',
      name: 'confirmTheContract',
      desc: '',
      args: [],
    );
  }

  /// `确定要开通支付宝免密支付吗？`
  String get bindAlipay {
    return Intl.message(
      '确定要开通支付宝免密支付吗？',
      name: 'bindAlipay',
      desc: '',
      args: [],
    );
  }

  /// `支付宝免密支付`
  String get alipayPwdFree {
    return Intl.message(
      '支付宝免密支付',
      name: 'alipayPwdFree',
      desc: '',
      args: [],
    );
  }

  /// `舒适度评级`
  String get ComfortRating {
    return Intl.message(
      '舒适度评级',
      name: 'ComfortRating',
      desc: '',
      args: [],
    );
  }

  /// `舒适度：`
  String get Comfort {
    return Intl.message(
      '舒适度：',
      name: 'Comfort',
      desc: '',
      args: [],
    );
  }

  /// `年龄建议：`
  String get AgeAdvice {
    return Intl.message(
      '年龄建议：',
      name: 'AgeAdvice',
      desc: '',
      args: [],
    );
  }

  /// `支持平台：`
  String get SupportedPlatforms {
    return Intl.message(
      '支持平台：',
      name: 'SupportedPlatforms',
      desc: '',
      args: [],
    );
  }

  /// `控制方式：`
  String get ControlMode {
    return Intl.message(
      '控制方式：',
      name: 'ControlMode',
      desc: '',
      args: [],
    );
  }

  /// `游戏模式：`
  String get GameMode {
    return Intl.message(
      '游戏模式：',
      name: 'GameMode',
      desc: '',
      args: [],
    );
  }

  /// `VR手柄`
  String get VRController {
    return Intl.message(
      'VR手柄',
      name: 'VRController',
      desc: '',
      args: [],
    );
  }

  /// `手势追踪`
  String get HandTracking {
    return Intl.message(
      '手势追踪',
      name: 'HandTracking',
      desc: '',
      args: [],
    );
  }

  /// `游戏手柄`
  String get Gamepad {
    return Intl.message(
      '游戏手柄',
      name: 'Gamepad',
      desc: '',
      args: [],
    );
  }

  /// `键盘`
  String get Keyboard {
    return Intl.message(
      '键盘',
      name: 'Keyboard',
      desc: '',
      args: [],
    );
  }

  /// `舒适`
  String get Comfortable {
    return Intl.message(
      '舒适',
      name: 'Comfortable',
      desc: '',
      args: [],
    );
  }

  /// `适中`
  String get Moderate {
    return Intl.message(
      '适中',
      name: 'Moderate',
      desc: '',
      args: [],
    );
  }

  /// `紧张`
  String get Nervous {
    return Intl.message(
      '紧张',
      name: 'Nervous',
      desc: '',
      args: [],
    );
  }

  /// `单人`
  String get Individual {
    return Intl.message(
      '单人',
      name: 'Individual',
      desc: '',
      args: [],
    );
  }

  /// `多人`
  String get ManyPeople {
    return Intl.message(
      '多人',
      name: 'ManyPeople',
      desc: '',
      args: [],
    );
  }

  /// `合作`
  String get Cooperate {
    return Intl.message(
      '合作',
      name: 'Cooperate',
      desc: '',
      args: [],
    );
  }

  /// `对抗`
  String get Confrontation {
    return Intl.message(
      '对抗',
      name: 'Confrontation',
      desc: '',
      args: [],
    );
  }

  /// `Comfort in VR depends on factors such as game optimization, gameplay mode, and player adaptability. Please refer to the comfort rating to choose a suitable game.`
  String get TheComfortOfVRPlay {
    return Intl.message(
      'Comfort in VR depends on factors such as game optimization, gameplay mode, and player adaptability. Please refer to the comfort rating to choose a suitable game.',
      name: 'TheComfortOfVRPlay',
      desc: '',
      args: [],
    );
  }

  /// `Appropriate for most players.`
  String get SuitableForAllPlayers {
    return Intl.message(
      'Appropriate for most players.',
      name: 'SuitableForAllPlayers',
      desc: '',
      args: [],
    );
  }

  /// `Not appropriate for a few players, and may become comfortable after adaptation.`
  String get FitsMostPlayers {
    return Intl.message(
      'Not appropriate for a few players, and may become comfortable after adaptation.',
      name: 'FitsMostPlayers',
      desc: '',
      args: [],
    );
  }

  /// `Some players may experience discomfort such as dizziness, so players who are new to VR should exercise caution when trying it out.`
  String get SomePlayersAreNotSuitable {
    return Intl.message(
      'Some players may experience discomfort such as dizziness, so players who are new to VR should exercise caution when trying it out.',
      name: 'SomePlayersAreNotSuitable',
      desc: '',
      args: [],
    );
  }

  /// `常见问题`
  String get CommonQuestion {
    return Intl.message(
      '常见问题',
      name: 'CommonQuestion',
      desc: '',
      args: [],
    );
  }

  /// `推荐`
  String get Recommend {
    return Intl.message(
      '推荐',
      name: 'Recommend',
      desc: '',
      args: [],
    );
  }

  /// `游戏`
  String get Games {
    return Intl.message(
      '游戏',
      name: 'Games',
      desc: '',
      args: [],
    );
  }

  /// `应用`
  String get Application {
    return Intl.message(
      '应用',
      name: 'Application',
      desc: '',
      args: [],
    );
  }

  /// `探索`
  String get Explore {
    return Intl.message(
      '探索',
      name: 'Explore',
      desc: '',
      args: [],
    );
  }

  /// `探索专区的应用可能在开发或测试中，可参考兼容性评级和游玩建议进行体验`
  String get ExploreArea {
    return Intl.message(
      '探索专区的应用可能在开发或测试中，可参考兼容性评级和游玩建议进行体验',
      name: 'ExploreArea',
      desc: '',
      args: [],
    );
  }

  /// `未拥有`
  String get NotPossess {
    return Intl.message(
      '未拥有',
      name: 'NotPossess',
      desc: '',
      args: [],
    );
  }

  /// `线下门店`
  String get Stores {
    return Intl.message(
      '线下门店',
      name: 'Stores',
      desc: '',
      args: [],
    );
  }

  /// `注意`
  String get Attention {
    return Intl.message(
      '注意',
      name: 'Attention',
      desc: '',
      args: [],
    );
  }

  /// `请填写银行卡号实名认证人的身份证号码（根据相关财税规定，请务必正确填写身份证号码，且年满18周岁，信息仅用于校验，我们将对您的信息保密）`
  String get PleaseFillInTheIDNumber {
    return Intl.message(
      '请填写银行卡号实名认证人的身份证号码（根据相关财税规定，请务必正确填写身份证号码，且年满18周岁，信息仅用于校验，我们将对您的信息保密）',
      name: 'PleaseFillInTheIDNumber',
      desc: '',
      args: [],
    );
  }

  /// `请正确填写，信息缺漏、错误，则视为申领无效`
  String get PleaseFillInCorrectly {
    return Intl.message(
      '请正确填写，信息缺漏、错误，则视为申领无效',
      name: 'PleaseFillInCorrectly',
      desc: '',
      args: [],
    );
  }

  /// `我确认以上信息无误。信息一旦提交将不能修改，如信息错误导致未能取得返现由本人承担责任。`
  String get IConfirmThatTheAbove {
    return Intl.message(
      '我确认以上信息无误。信息一旦提交将不能修改，如信息错误导致未能取得返现由本人承担责任。',
      name: 'IConfirmThatTheAbove',
      desc: '',
      args: [],
    );
  }

  /// `我知悉并同意为返现活动提供的信息，且知悉以上信息包含个人信息。`
  String get IUnderstandAndAgree {
    return Intl.message(
      '我知悉并同意为返现活动提供的信息，且知悉以上信息包含个人信息。',
      name: 'IUnderstandAndAgree',
      desc: '',
      args: [],
    );
  }

  /// `我同意YVR使用所提交信息进行合法校验。YVR承诺对提供的信息严格保密。`
  String get IAgreeThatYVRWill {
    return Intl.message(
      '我同意YVR使用所提交信息进行合法校验。YVR承诺对提供的信息严格保密。',
      name: 'IAgreeThatYVRWill',
      desc: '',
      args: [],
    );
  }

  /// `我知悉本次参与的VR产品打卡试用活动将会获得不同激励金额，我已阅读并同意{str_0}，根据国家法律规定，YVR将为我申报个人所得税。`
  String IKnownThisVRAcitivity(Object str_0) {
    return Intl.message(
      '我知悉本次参与的VR产品打卡试用活动将会获得不同激励金额，我已阅读并同意$str_0，根据国家法律规定，YVR将为我申报个人所得税。',
      name: 'IKnownThisVRAcitivity',
      desc: '',
      args: [str_0],
    );
  }

  /// `《平台服务协议》`
  String get PlatformServiceAgreement {
    return Intl.message(
      '《平台服务协议》',
      name: 'PlatformServiceAgreement',
      desc: '',
      args: [],
    );
  }

  /// `请先勾选以上协议`
  String get PleaseTickTheAboveAgreement {
    return Intl.message(
      '请先勾选以上协议',
      name: 'PleaseTickTheAboveAgreement',
      desc: '',
      args: [],
    );
  }

  /// `返回检查`
  String get BackToCheck {
    return Intl.message(
      '返回检查',
      name: 'BackToCheck',
      desc: '',
      args: [],
    );
  }

  /// `身份证号码位数验证失败`
  String get FailedToVerifyTheDigits {
    return Intl.message(
      '身份证号码位数验证失败',
      name: 'FailedToVerifyTheDigits',
      desc: '',
      args: [],
    );
  }

  /// `请填写您的购买渠道`
  String get PleaseFillInYourPurchase {
    return Intl.message(
      '请填写您的购买渠道',
      name: 'PleaseFillInYourPurchase',
      desc: '',
      args: [],
    );
  }

  /// `请上传付款截图（支付宝/微信截图）`
  String get PleaseUploadAPaymentScreenshot {
    return Intl.message(
      '请上传付款截图（支付宝/微信截图）',
      name: 'PleaseUploadAPaymentScreenshot',
      desc: '',
      args: [],
    );
  }

  /// `扫描蓝牙需要您在玩出梦想应用设置中先开启连接附近的设备权限`
  String get ConnectNearbyDevicesPermission {
    return Intl.message(
      '扫描蓝牙需要您在玩出梦想应用设置中先开启连接附近的设备权限',
      name: 'ConnectNearbyDevicesPermission',
      desc: '',
      args: [],
    );
  }

  /// `打卡签到活动`
  String get CheckInActivity {
    return Intl.message(
      '打卡签到活动',
      name: 'CheckInActivity',
      desc: '',
      args: [],
    );
  }

  /// `附加内容`
  String get AdditionalContent {
    return Intl.message(
      '附加内容',
      name: 'AdditionalContent',
      desc: '',
      args: [],
    );
  }

  /// `我的积分`
  String get MyPoints {
    return Intl.message(
      '我的积分',
      name: 'MyPoints',
      desc: '',
      args: [],
    );
  }

  /// `积分商城`
  String get PointsMall {
    return Intl.message(
      '积分商城',
      name: 'PointsMall',
      desc: '',
      args: [],
    );
  }

  /// `当前可用积分`
  String get CurrentlyAvailablePoints {
    return Intl.message(
      '当前可用积分',
      name: 'CurrentlyAvailablePoints',
      desc: '',
      args: [],
    );
  }

  /// `积分规则`
  String get PointsRules {
    return Intl.message(
      '积分规则',
      name: 'PointsRules',
      desc: '',
      args: [],
    );
  }

  /// `积分商城预计7月上线，敬请期待`
  String get PointsMallIsUnderConstruction {
    return Intl.message(
      '积分商城预计7月上线，敬请期待',
      name: 'PointsMallIsUnderConstruction',
      desc: '',
      args: [],
    );
  }

  /// `暂无积分明细哦`
  String get NoPointDetailsYet {
    return Intl.message(
      '暂无积分明细哦',
      name: 'NoPointDetailsYet',
      desc: '',
      args: [],
    );
  }

  /// `暂未参加任何打卡活动`
  String get NotJoinPunch {
    return Intl.message(
      '暂未参加任何打卡活动',
      name: 'NotJoinPunch',
      desc: '',
      args: [],
    );
  }

  /// `pointRuleen`
  String get point_rule_html_page_name {
    return Intl.message(
      'pointRuleen',
      name: 'point_rule_html_page_name',
      desc: '',
      args: [],
    );
  }

  /// `商品详情`
  String get ProductDetails {
    return Intl.message(
      '商品详情',
      name: 'ProductDetails',
      desc: '',
      args: [],
    );
  }

  /// `确认订单`
  String get ConfirmOrder {
    return Intl.message(
      '确认订单',
      name: 'ConfirmOrder',
      desc: '',
      args: [],
    );
  }

  /// `收货地址`
  String get ShippingAddress {
    return Intl.message(
      '收货地址',
      name: 'ShippingAddress',
      desc: '',
      args: [],
    );
  }

  /// `编辑收货地址`
  String get EditAddress {
    return Intl.message(
      '编辑收货地址',
      name: 'EditAddress',
      desc: '',
      args: [],
    );
  }

  /// `新建收货地址`
  String get AddNewAddress {
    return Intl.message(
      '新建收货地址',
      name: 'AddNewAddress',
      desc: '',
      args: [],
    );
  }

  /// `兑换结果`
  String get ExchangeResult {
    return Intl.message(
      '兑换结果',
      name: 'ExchangeResult',
      desc: '',
      args: [],
    );
  }

  /// `兑换记录`
  String get ExchangeRecord {
    return Intl.message(
      '兑换记录',
      name: 'ExchangeRecord',
      desc: '',
      args: [],
    );
  }

  /// `订单详情页`
  String get OrderDetails {
    return Intl.message(
      '订单详情页',
      name: 'OrderDetails',
      desc: '',
      args: [],
    );
  }

  /// `相关活动`
  String get RelatedActivity {
    return Intl.message(
      '相关活动',
      name: 'RelatedActivity',
      desc: '',
      args: [],
    );
  }

  /// `礼品卡`
  String get GiftCard {
    return Intl.message(
      '礼品卡',
      name: 'GiftCard',
      desc: '',
      args: [],
    );
  }

  /// `身份证号码校验未通过`
  String get IDCardError {
    return Intl.message(
      '身份证号码校验未通过',
      name: 'IDCardError',
      desc: '',
      args: [],
    );
  }

  /// `YVR GO运动`
  String get YVRGoSport {
    return Intl.message(
      'YVR GO运动',
      name: 'YVRGoSport',
      desc: '',
      args: [],
    );
  }

  /// `隐私设置`
  String get PrivacySetting {
    return Intl.message(
      '隐私设置',
      name: 'PrivacySetting',
      desc: '',
      args: [],
    );
  }

  /// `公开范围`
  String get PublicScope {
    return Intl.message(
      '公开范围',
      name: 'PublicScope',
      desc: '',
      args: [],
    );
  }

  /// `选择游戏`
  String get SelectGames {
    return Intl.message(
      '选择游戏',
      name: 'SelectGames',
      desc: '',
      args: [],
    );
  }

  /// `暂无评分`
  String get NoRatingYet {
    return Intl.message(
      '暂无评分',
      name: 'NoRatingYet',
      desc: '',
      args: [],
    );
  }

  /// `1 star`
  String get Star_1 {
    return Intl.message(
      '1 star',
      name: 'Star_1',
      desc: '',
      args: [],
    );
  }

  /// `3 stars`
  String get Star_2 {
    return Intl.message(
      '3 stars',
      name: 'Star_2',
      desc: '',
      args: [],
    );
  }

  /// `2 stars`
  String get Star_3 {
    return Intl.message(
      '2 stars',
      name: 'Star_3',
      desc: '',
      args: [],
    );
  }

  /// `5 stars`
  String get Star_4 {
    return Intl.message(
      '5 stars',
      name: 'Star_4',
      desc: '',
      args: [],
    );
  }

  /// `4 stars`
  String get Star_5 {
    return Intl.message(
      '4 stars',
      name: 'Star_5',
      desc: '',
      args: [],
    );
  }

  /// `All`
  String get StarAll {
    return Intl.message(
      'All',
      name: 'StarAll',
      desc: '',
      args: [],
    );
  }

  /// `暂无评分，数据累积中`
  String get DataAccumulation {
    return Intl.message(
      '暂无评分，数据累积中',
      name: 'DataAccumulation',
      desc: '',
      args: [],
    );
  }

  /// `你如何评价{str_0}？`
  String HowDoYouEvaluate(Object str_0) {
    return Intl.message(
      '你如何评价$str_0？',
      name: 'HowDoYouEvaluate',
      desc: '',
      args: [str_0],
    );
  }

  /// `写评价留下更多反馈吧！`
  String get WriteReviewAndMoreFeedback {
    return Intl.message(
      '写评价留下更多反馈吧！',
      name: 'WriteReviewAndMoreFeedback',
      desc: '',
      args: [],
    );
  }

  /// `评价将在审核通过后展示`
  String get TheEvaluationIsUnderReview {
    return Intl.message(
      '评价将在审核通过后展示',
      name: 'TheEvaluationIsUnderReview',
      desc: '',
      args: [],
    );
  }

  /// `请先安装应用并体验5分钟再来评价吧！`
  String get PleaseInstallAppAndExperience {
    return Intl.message(
      '请先安装应用并体验5分钟再来评价吧！',
      name: 'PleaseInstallAppAndExperience',
      desc: '',
      args: [],
    );
  }

  /// `Popular`
  String get Popular {
    return Intl.message(
      'Popular',
      name: 'Popular',
      desc: '',
      args: [],
    );
  }

  /// `Latest`
  String get Latest {
    return Intl.message(
      'Latest',
      name: 'Latest',
      desc: '',
      args: [],
    );
  }

  /// `Major Update`
  String get MajorUpdate {
    return Intl.message(
      'Major Update',
      name: 'MajorUpdate',
      desc: '',
      args: [],
    );
  }

  /// `Today`
  String get Today {
    return Intl.message(
      'Today',
      name: 'Today',
      desc: '',
      args: [],
    );
  }

  /// `Tomorrow`
  String get Tomorrow {
    return Intl.message(
      'Tomorrow',
      name: 'Tomorrow',
      desc: '',
      args: [],
    );
  }

  /// `Login failure`
  String get LoginFailure {
    return Intl.message(
      'Login failure',
      name: 'LoginFailure',
      desc: '',
      args: [],
    );
  }

  /// `8-12 digits, with at least 3 digits in numbers, letters, and symbols`
  String get LoginPasswordRules {
    return Intl.message(
      '8-12 digits, with at least 3 digits in numbers, letters, and symbols',
      name: 'LoginPasswordRules',
      desc: '',
      args: [],
    );
  }

  /// `The account has been locked. Please login again after 5 minutes.`
  String get TheAccountHasBeenLocked {
    return Intl.message(
      'The account has been locked. Please login again after 5 minutes.',
      name: 'TheAccountHasBeenLocked',
      desc: '',
      args: [],
    );
  }

  /// `Passwords error，You have {num_0} chances left`
  String PasswordsError(Object num_0) {
    return Intl.message(
      'Passwords error，You have $num_0 chances left',
      name: 'PasswordsError',
      desc: '',
      args: [num_0],
    );
  }

  /// `运动海报`
  String get SportsPoster {
    return Intl.message(
      '运动海报',
      name: 'SportsPoster',
      desc: '',
      args: [],
    );
  }

  /// `相关内容已下架`
  String get RelatedContentHasBeenRemoved {
    return Intl.message(
      '相关内容已下架',
      name: 'RelatedContentHasBeenRemoved',
      desc: '',
      args: [],
    );
  }

  /// `分享`
  String get Share {
    return Intl.message(
      '分享',
      name: 'Share',
      desc: '',
      args: [],
    );
  }

  /// `PLAY FOR DREAM`
  String get PlayForDream {
    return Intl.message(
      'PLAY FOR DREAM',
      name: 'PlayForDream',
      desc: '',
      args: [],
    );
  }

  /// `您提交的评价会经过平台审核后展示`
  String get TheEvaluationYouSubmit {
    return Intl.message(
      '您提交的评价会经过平台审核后展示',
      name: 'TheEvaluationYouSubmit',
      desc: '',
      args: [],
    );
  }

  /// `应用评价至少为5个字`
  String get PleaseEnterAtLeastFive {
    return Intl.message(
      '应用评价至少为5个字',
      name: 'PleaseEnterAtLeastFive',
      desc: '',
      args: [],
    );
  }

  /// `兼容性评级`
  String get CompatibilityRating {
    return Intl.message(
      '兼容性评级',
      name: 'CompatibilityRating',
      desc: '',
      args: [],
    );
  }

  /// `全部游戏`
  String get AllGames {
    return Intl.message(
      '全部游戏',
      name: 'AllGames',
      desc: '',
      args: [],
    );
  }

  /// `全部应用`
  String get AllApps {
    return Intl.message(
      '全部应用',
      name: 'AllApps',
      desc: '',
      args: [],
    );
  }

  /// `预约`
  String get Appointment {
    return Intl.message(
      '预约',
      name: 'Appointment',
      desc: '',
      args: [],
    );
  }

  /// `支付安全`
  String get SafePay {
    return Intl.message(
      '支付安全',
      name: 'SafePay',
      desc: '',
      args: [],
    );
  }

  /// `请求页面不存在`
  String get PageNotFound {
    return Intl.message(
      '请求页面不存在',
      name: 'PageNotFound',
      desc: '',
      args: [],
    );
  }

  /// `Incorrect email address`
  String get IncorrectEmailAddress {
    return Intl.message(
      'Incorrect email address',
      name: 'IncorrectEmailAddress',
      desc: '',
      args: [],
    );
  }

  /// `Email address registered`
  String get EmailAddressHasBeenRegistered {
    return Intl.message(
      'Email address registered',
      name: 'EmailAddressHasBeenRegistered',
      desc: '',
      args: [],
    );
  }

  /// `遥控拍摄`
  String get RemoteShooting {
    return Intl.message(
      '遥控拍摄',
      name: 'RemoteShooting',
      desc: '',
      args: [],
    );
  }

  /// `Please keep the phone and device time synchronized`
  String get KeepTimeSynchronized {
    return Intl.message(
      'Please keep the phone and device time synchronized',
      name: 'KeepTimeSynchronized',
      desc: '',
      args: [],
    );
  }

  /// `4 stars`
  String get xing {
    return Intl.message(
      '4 stars',
      name: 'xing',
      desc: '',
      args: [],
    );
  }

  /// `1 star`
  String get xing_1 {
    return Intl.message(
      '1 star',
      name: 'xing_1',
      desc: '',
      args: [],
    );
  }

  /// `3 stars`
  String get xing_2 {
    return Intl.message(
      '3 stars',
      name: 'xing_2',
      desc: '',
      args: [],
    );
  }

  /// `2 stars`
  String get xing_3 {
    return Intl.message(
      '2 stars',
      name: 'xing_3',
      desc: '',
      args: [],
    );
  }

  /// `5 stars`
  String get xing_4 {
    return Intl.message(
      '5 stars',
      name: 'xing_4',
      desc: '',
      args: [],
    );
  }

  /// `Free`
  String get mianfeiwenfei {
    return Intl.message(
      'Free',
      name: 'mianfeiwenfei',
      desc: '',
      args: [],
    );
  }

  /// `Comment`
  String get home_comment {
    return Intl.message(
      'Comment',
      name: 'home_comment',
      desc: '',
      args: [],
    );
  }

  /// `Platform Policy`
  String get pingtaizhengce {
    return Intl.message(
      'Platform Policy',
      name: 'pingtaizhengce',
      desc: '',
      args: [],
    );
  }

  /// `Illegal content`
  String get pinglunnarongweiguip {
    return Intl.message(
      'Illegal content',
      name: 'pinglunnarongweiguip',
      desc: '',
      args: [],
    );
  }

  /// `Earliest release`
  String get home_earliest_released {
    return Intl.message(
      'Earliest release',
      name: 'home_earliest_released',
      desc: '',
      args: [],
    );
  }

  /// `Select payment method`
  String get xuanzezhifufangshixu {
    return Intl.message(
      'Select payment method',
      name: 'xuanzezhifufangshixu',
      desc: '',
      args: [],
    );
  }

  /// `6-12 characters (letters & digits)`
  String get pwd_rule {
    return Intl.message(
      '6-12 characters (letters & digits)',
      name: 'pwd_rule',
      desc: '',
      args: [],
    );
  }

  /// `Synthesis failed. Please record again.`
  String get gechengshibaiqingcho {
    return Intl.message(
      'Synthesis failed. Please record again.',
      name: 'gechengshibaiqingcho',
      desc: '',
      args: [],
    );
  }

  /// `Enter a string of 6 to 12 letters, digits, or special characters.`
  String get qingshuru612weidaxia {
    return Intl.message(
      'Enter a string of 6 to 12 letters, digits, or special characters.',
      name: 'qingshuru612weidaxia',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<YLocal> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'zh'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<YLocal> load(Locale locale) => YLocal.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    if (locale != null) {
      for (var supportedLocale in supportedLocales) {
        if (supportedLocale.languageCode == locale.languageCode) {
          return true;
        }
      }
    }
    return false;
  }
}