import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/app_navigator.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/pages/profile/views/profile_view.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';

import '../../../styles/app_color.dart';

class PrivacyPage extends StatefulWidget {
  PrivacyPage({Key key}) : super(key: key);

  @override
  State<PrivacyPage> createState() => _PrivacyPageState();
}

class _PrivacyPageState extends State<PrivacyPage> {
  ValueNotifier<bool> _isAllowNotifier = ValueNotifier<bool>(
      StorageManager.foreverData.getItem("isAllowPersonalizedRecommendation") ??
          true);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TopNavbar(
        title: YLocal.of(context).yinsiguanli,
      ),
      backgroundColor: Color(0xffF5F7FA),
      body: ListView(
        children: [
          SizedBox(
            height: 10,
          ),
          Container(
            decoration: BoxDecoration(
              color: Color(0xffFFFFFF),
              border: Border(
                top: BorderSide(color: Color(0xffEEEEEE), width: 0.5),
                bottom: BorderSide(color: Color(0xffEEEEEE), width: 0.5),
              ),
            ),
            child: profileCell(
                title: YLocal.of(context).privacy_policy,
                cellClick: () {
                  String title = YLocal.of(context).privacy_policy;
                  navigateToAgreementPage(
                      context: context,
                      pageName:
                          YLocal.of(context).privacy_policy_html_page_name,
                      title: title);
                },
                padding: 16),
          ),
          Container(
              decoration: BoxDecoration(
                color: Color(0xffFFFFFF),
                border: Border(
                  top: BorderSide(color: Color(0xffEEEEEE), width: 0.5),
                  bottom: BorderSide(color: Color(0xffEEEEEE), width: 0.5),
                ),
              ),
              padding: EdgeInsets.symmetric(vertical: 15, horizontal: 16),
              margin: EdgeInsets.only(top: 15),
              child: Flex(
                direction: Axis.horizontal,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    flex: 1,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          YLocal.of(context).gexinghuanarongtuiji,
                          style: TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 16,
                            height: 1,
                            letterSpacing: 0,
                            color: AppColors.textTitle,
                          ),
                        ),
                        SizedBox(
                          height: 8,
                        ),
                        Text(
                          YLocal.of(context).guanbihoujiangmofash,
                          style: TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 12,
                            height: 1.5,
                            color: AppColors.textWeak,
                          ),
                        ),
                      ],
                    ),
                  ),
                  ValueListenableBuilder<bool>(
                      valueListenable: _isAllowNotifier,
                      builder: (context, value, child) {
                        return Container(
                          height: 20,
                          width: 40,
                          margin: EdgeInsets.only(left: 50),
                          alignment: Alignment.centerRight,
                          child: CupertinoSwitch(
                            value: value,
                            onChanged: (isAllow) {
                              if (!isAllow) {
                                YvrToast.showToast(
                                    YLocal.of(context).yiguanbigexinghuanar);
                              }
                              _isAllowNotifier.value = isAllow;
                              StorageManager.foreverData.setItem(
                                  "isAllowPersonalizedRecommendation", isAllow);
                            },
                            activeColor: Color(0xff4F7FFE),
                            trackColor: Color(0xffAFB6CC),
                          ),
                        );
                      })
                ],
              ))
        ],
      ),
    );
  }
}
