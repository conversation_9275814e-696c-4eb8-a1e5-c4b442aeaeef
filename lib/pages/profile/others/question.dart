import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/model/qa_model.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/provider/provider_widget.dart';
import 'package:yvr_assistant/provider/view_state_widget.dart';
import 'package:yvr_assistant/view_model/feedback_vmodel.dart';

import '../../../styles/app_color.dart';
import '../../../styles/app_divider.dart';

class QuestionPage extends StatefulWidget {
  QuestionPage({Key key}) : super(key: key);

  @override
  _QuestionPageState createState() => _QuestionPageState();
}

class _QuestionPageState extends State<QuestionPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TopNavbar(
        title: YLocal.of(context).profile_question_type,
      ),
      backgroundColor: AppColors.secondaryBg,
      body: ProviderWidget<FeedbackVModel>(
        model: FeedbackVModel(),
        onModelReady: (model) => model.initData(),
        builder: (context, model, child) {
          if (model.busy) {
            return ViewStateBusyWidget();
          } else if (model.error) {
            return ViewStateErrorWidget(
                error: model.viewStateError, onPressed: model.initData);
          }

          List<QaModel> qaList = model.qaList;

          return ListView.builder(
              itemCount: qaList.length,
              itemBuilder: (context, index) {
                return Column(
                  children: [
                    index == 0 ? AppDivider.backgroundDivider : SizedBox(),
                    Container(
                      decoration: BoxDecoration(
                        color: Color(0xffFFFFFF),
                      ),
                      child: ListTile(
                        title: Text(
                          qaList[index].descp,
                          style: TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 16,
                            height: 1,
                            letterSpacing: 0,
                            color: AppColors.textTitle,
                          ),
                        ),
                        onTap: () {
                          Map param = {
                            "kQuestion": qaList[index].descp,
                            "kDescId": qaList[index].id.toString()
                          };
                          Navigator.pop(context, param);
                        },
                      ),
                    ),
                    AppDivider.backgroundDivider,
                  ],
                );
              });
        },
      ),
    );
  }
}
