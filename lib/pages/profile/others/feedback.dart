import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import '../../../public_ui/widget/feedback_input2.dart';
import '../../../styles/app_color.dart';

class FeedbackPage extends StatefulWidget {
  FeedbackPage({Key key}) : super(key: key);

  @override
  _FeedbackPageState createState() => _FeedbackPageState();
}

class _FeedbackPageState extends State<FeedbackPage> {
  String _qaTitle = "";
  String _descId = "";
  String _content = "";
  String _contact = "";
  bool _isCanBack = false;
  FocusNode _focusNode = FocusNode();
  ValueNotifier<bool> _isChangeTitle = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          if (isShowNavBackRemind()) {
            return true;
          }
          Navigator.of(context).pop();
          return false;
        },
        child: Scaffold(
            // 自定义APPBar 导航返回无法弹框二次确认
            appBar: AppBar(
              title: Text(YLocal.of(context).profile_feedback),
              actions: [submitButtonWidget()],
              leading: CustomBackButton(
                onPressed: () {
                  if (isShowNavBackRemind()) {
                    return true;
                  }
                  Navigator.of(context).pop();
                  return false;
                },
              ),
            ),
            backgroundColor: AppColors.secondaryBg,
            body: SingleChildScrollView(
                child: Column(
              children: [
                SizedBox(
                  height: 12,
                ),
                ValueListenableBuilder<bool>(
                    valueListenable: _isChangeTitle,
                    builder: (context, value, child) {
                      return Container(
                        decoration: BoxDecoration(
                            color: Color(0xffFFFFFF),
                            border: Border(
                              top: BorderSide(
                                  width: 0.5, color: Color(0xffEEEEEE)),
                              bottom: BorderSide(
                                  width: 0.5, color: Color(0xffEEEEEE)),
                            )),
                        child: ListTile(
                          title: Text(
                            (_qaTitle.length > 0)
                                ? _qaTitle
                                : YLocal.of(context).profile_question_type,
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: 16,
                              height: 1,
                              letterSpacing: 0,
                              color: AppColors.textTitle,
                            ),
                          ),
                          trailing: Icon(
                            Icons.chevron_right,
                            color: Color(0xffAFB6CC),
                          ),
                          onTap: () {
                            Navigator.pushNamed(context, "/question")
                                .then((value) {
                              setState(() {
                                Map data = value;
                                _isChangeTitle.value = !_isChangeTitle.value;
                                if (data == null) {
                                  _qaTitle = "";
                                  _descId = "";
                                } else {
                                  _qaTitle = data["kQuestion"];
                                  _descId = data["kDescId"];
                                }
                              });
                            });
                          },
                        ),
                      );
                    }),
                SizedBox(
                  height: 10,
                ),
                Container(
                  decoration: BoxDecoration(
                      border: Border(
                    top: BorderSide(width: 0.5, color: Color(0xffEEEEEE)),
                    bottom: BorderSide(width: 0.5, color: Color(0xffEEEEEE)),
                  )),
                  child: FeedbackInput2(
                      focusNode: _focusNode,
                      hint: YLocal.of(context).profile_desc_problem,
                      height: 300,
                      maxLength: 400,
                      fillColor: Colors.white,
                      onChanged: (text) {
                        setState(() {
                          _content = text;
                        });
                      }),
                ),
                SizedBox(
                  height: 10,
                ),
                Container(
                  padding: EdgeInsets.fromLTRB(20, 3, 20, 3),
                  margin: EdgeInsets.only(bottom: 20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      top: BorderSide(width: 0.5, color: Color(0xffEEEEEE)),
                      bottom: BorderSide(width: 0.5, color: Color(0xffEEEEEE)),
                    ),
                  ),
                  child: TextField(
                    onChanged: (text) {
                      setState(() {
                        _contact = text;
                      });
                    },
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 16,
                      height: 1,
                      letterSpacing: 0,
                      color: AppColors.textTitle,
                    ),
                    decoration: InputDecoration(
                      hintText: YLocal.of(context).profile_connect_way,
                      hintStyle: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 16,
                        height: 1,
                        letterSpacing: 0,
                        color: AppColors.textWeak,
                      ),
                      focusedBorder: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      border: InputBorder.none,
                      disabledBorder: InputBorder.none,
                      errorBorder: InputBorder.none,
                      focusedErrorBorder: InputBorder.none,
                    ),
                  ),
                ),
                SizedBox(
                  height: 100,
                )
              ],
            ))));
  }

  bool isShowNavBackRemind() {
    if ((_descId.length > 0 || _contact.length > 0 || _content.length > 0) &&
        (!_isCanBack)) {
      _focusNode?.unfocus();
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (_) {
            return CustomDialog(
              title: YLocal.of(context).profile_cancel_feedback,
              content: YLocal.of(context).profile_is_feedback,
              confirmText: YLocal.of(context).confirm,
              height: 200,
              confirmColor: Color(0xff52555C),
              confirmCallback: () {
                Navigator.of(context).pop();
                return true;
              },
            );
          });
      return true;
    } else {
      return false;
    }
  }

  Widget submitButtonWidget() {
    return Container(
      margin: EdgeInsets.only(right: 16),
      alignment: AlignmentDirectional.center,
      child: YvrTextButton(
          height: 24,
          padding: EdgeInsets.symmetric(horizontal: 12),
          child: Text(
            YLocal.of(context).submit,
            style: TextStyle(
                color: (_descId.length > 0 &&
                        _content.length > 0 &&
                        _contact.length > 0)
                    ? null
                    : Color(0xFF767880),
                fontSize: 12,
                fontWeight: FontWeight.w600),
            textAlign: TextAlign.center,
          ),
          color:
              (_descId.length > 0 && _content.length > 0 && _contact.length > 0)
                  ? null
                  : AppColors.buttonDisable,
          onPressed: (_descId.length == 0 ||
                  _content.length < 1 ||
                  _contact.length == 0)
              ? null
              : () {
                  http.post<Map>('vrmcsys/appstore/commitFeedback', data: {
                    'descId': _descId,
                    'content': _content.trim(),
                    'contact': _contact
                  }).then((response) {
                    _isCanBack = true;
                    if (response.data["errCode"] == 0) {
                      YvrToast.showSuccess(
                          YLocal.of(context).toast_feedback_desc);
                      Navigator.pop(context);
                    }
                  }).catchError((error) {
                    _isCanBack = true;
                    YvrToast.showToast(YLocal.of(context).toast_feedback_fail);
                  });
                }),
    );
  }
}
