import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:package_info/package_info.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/model/user_model.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_divider.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';
import 'package:yvr_assistant/utils/version_update.dart';
import 'package:yvr_assistant/manager/app_navigator.dart';
import 'package:yvr_assistant/public_ui/widget/image_view.dart';
import 'package:yvr_assistant/manager/resource_mananger.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/pages/profile/account/account_safe.dart';
import 'package:yvr_assistant/pages/profile/pay/tool/wallet_tool.dart';
import 'package:list_tile_more_customizable/list_tile_more_customizable.dart';

const Color _cellBgColor = Colors.white;

// ignore: must_be_immutable
class ProfileViewWidget extends StatefulWidget {
  ScrollController controller;
  final UserModel userModel;
  final logout;

  ProfileViewWidget({Key key, this.controller, this.userModel, this.logout})
      : super(key: key);

  @override
  _ProfileViewWidgetState createState() => _ProfileViewWidgetState();
}

class _ProfileViewWidgetState extends State<ProfileViewWidget> {
  Future<String> getProjectVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    return packageInfo.version ?? "";
  }

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      controller: widget.controller,
      slivers: [
        SliverToBoxAdapter(
          child: Container(
            height: 168,
            child: Stack(
              alignment: AlignmentDirectional.center,
              children: [
                Positioned.fill(
                  child: Image.asset(
                    'assets/images/profile_bg.png',
                    fit: BoxFit.cover,
                  ),
                ),
                Positioned(
                  bottom: 24,
                  left: 16,
                  right: 16,
                  child: profileHeaderWidget(widget.userModel),
                )
              ],
            ),
          ),
        ),
        SliverToBoxAdapter(
          child: Column(
            children: [
              // if (!VersionUpdate.isInAppleReview)
              Container(
                color: _cellBgColor,
                child: Column(
                  children: [
                    profileCell(
                        imagePath: "profile_wallet",
                        title: YLocal.of(context).youhuquanyouhuiquany_1,
                        cellClick: () {
                          if (widget.userModel == null) {
                            Navigator.pushNamed(context, "/login");
                          } else {
                            WalletTool()
                                .showProgressAndJumpToWalletPage(context);
                          }
                        }),
                    // MyDivider(),
                    AppDivider.commonDividerWithIndent(24),
                    profileCell(
                        icon: IconFonts.iconPurchase,
                        title: YLocal.of(context).profile_buy_history,
                        cellClick: () {
                          Navigator.pushNamed(
                              context,
                              (widget.userModel == null)
                                  ? "/login"
                                  : '/pay_history');
                        }),
                  ],
                ),
              ),
              Container(
                color: _cellBgColor,
                margin: EdgeInsets.only(top: 16),
                child: Column(
                  children: [
                    profileCell(
                        icon: IconFonts.iconLaw,
                        title: YLocal.of(context).profile_legal_info,
                        cellClick: () {
                          String title = YLocal.of(context).profile_legal_info;
                          navigateToAgreementPage(
                              context: context,
                              pageName: YLocal.of(context)
                                  .service_agreement_html_page_name,
                              title: title);
                        }),
                    AppDivider.commonDividerWithIndent(24),
                    profileCell(
                        imagePath: "my_privacy",
                        title: YLocal.of(context).yinsiguanli,
                        cellClick: () {
                          Navigator.pushNamed(context, '/privacy');
                        }),

                    AppDivider.commonDividerWithIndent(24),
                    profileCell(
                        icon: IconFonts.iconFeedback,
                        title: YLocal.of(context).profile_feedback,
                        cellClick: () {
                          Navigator.pushNamed(context, '/feedback');
                        }),
                    AppDivider.commonDividerWithIndent(24),
                    // 异步返回数据加载视图：FutureBuilder
                    FutureBuilder(
                      future: getProjectVersion(),
                      builder: (BuildContext context,
                          AsyncSnapshot<String> snapshot) {
                        return !snapshot.hasData
                            ? SizedBox()
                            : profileCell(
                                icon: IconFonts.iconVersion,
                                title: Platform.isAndroid
                                    ? YLocal.of(context).profile_check_version
                                    : YLocal.of(context)
                                        .profile_current_version,
                                rightText: "V" + snapshot.data,
                                isShowTrailing: Platform.isAndroid,
                                cellClick: () {
                                  if (Platform.isAndroid) {
                                    VersionUpdate.getAppVerInfo(
                                        snapshot.data, context,
                                        isShowToast: true);
                                  }
                                });
                      },
                    )
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: 16),
                color: _cellBgColor,
                child: Column(
                  children: [
                    profileCell(
                        imagePath: "my_safe",
                        title: YLocal.of(context).zhanghaoyuanquan,
                        cellClick: () {
                          if ((widget.userModel == null)) {
                            Navigator.pushNamed(context, "/login");
                          } else {
                            //跳转到要可能会被指定返回的页面的时候
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => AccountSafePage(),
                                settings: RouteSettings(name: '/account_safe'),
                              ),
                            );
                          }
                        }),
                  ],
                ),
              ),
              (widget.userModel == null)
                  ? SizedBox(height: 10)
                  : Container(
                      width: double.infinity,
                      margin: EdgeInsets.fromLTRB(10, 16, 10, 50),
                      child: CupertinoButton(
                        padding: EdgeInsets.symmetric(vertical: 16),
                        onPressed: () {
                          showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (_) {
                                return CustomDialog(
                                  title: YLocal.of(context).confirm_leave,
                                  confirmText: YLocal.of(context).leave,
                                  confirmColor: Color(0xffF0F2F5),
                                  confirmTextColor: Color(0xff6E7380),
                                  confirmCallback: () {
                                    widget.logout();
                                  },
                                );
                              });
                        },
                        child: Text(
                          YLocal.of(context).sign_out,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 18,
                            letterSpacing: 1,
                            color: AppColors.warning,
                          ),
                        ),
                        color: Color(0xffFFFFFF),
                      ),
                    ),
            ],
          ),
        )
      ],
    );
  }

  Widget profileHeaderWidget(UserModel userModel) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        InkWell(
          child: Row(
            children: [
              AvatarImageWidget(
                userModel?.avatar == null ? "" : userModel.avatar,
                width: 60,
                height: 60,
                sex: userModel?.sex,
              ),
              SizedBox(
                width: 16,
              ),
              userModel == null
                  ? Text(
                      YLocal.current.dianjidengluzhuce,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 20,
                        height: 1,
                        letterSpacing: 1,
                        color: AppColors.textTitle,
                      ),
                    )
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ConstrainedBox(
                          constraints: BoxConstraints(maxWidth: 180),
                          child: Text(
                            userModel.nick != null
                                ? userModel.nick
                                : YLocal.of(context).nickname,
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 20,
                              height: 1,
                              letterSpacing: 1,
                              color: AppColors.textTitle,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(
                          height: 12,
                        ),
                        Row(
                          children: [
                            Text(
                              YLocal.of(context).profile_homepage,
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 14,
                                height: 1.1,
                                color: AppColors.textSub,
                              ),
                            ),
                            SizedBox(
                              width: 6,
                            ),
                            SvgPicture.asset(
                              "assets/svg/arrow_right.svg",
                              color: AppColors.textSub,
                              width: 10,
                              height: 10,
                            ),
                          ],
                        ),
                      ],
                    )
            ],
          ),
          onTap: () {
            if (userModel == null) {
              // 1.只有push 页面的返回值才会生效 2.对手势返回无效！
              Global.navigatorKey.currentState
                  .pushNamedAndRemoveUntil("/login", (route) => route.isFirst);
            } else {
              Navigator.pushNamed(context, "/person_home", arguments: {
                "kUser": userModel,
                "relation": 5,
                "actId": userModel.actId
              });
            }
          },
        )
      ],
    );
  }
}

Widget profileCell({
  String rightText = "",
  String imagePath = "",
  bool isShowTrailing = true,
  IconData icon,
  @required String title,
  @required VoidCallback cellClick,
  double padding = 24,
}) {
  Widget leading;
  if (imagePath.length > 0) {
    leading = SvgPicture.asset(
      "assets/svg/$imagePath.svg",
      width: 22,
      color: Color(0xff2C2E33),
    );
  } else if (icon != null) {
    leading = Icon(
      icon,
      size: 22,
      color: Color(0xff2C2E33),
    );
  }

  return Container(
      child: ListTileMoreCustomizable(
    contentPadding: EdgeInsets.symmetric(horizontal: padding),
    leading: leading,
    onTap: (details) {
      cellClick();
    },
    horizontalTitleGap: 5,
    title: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w400,
            fontSize: 16,
            height: 1,
            color: AppColors.textTitle,
          ),
        ),
        Text(
          rightText,
          style: TextStyle(
            fontWeight: FontWeight.w400,
            fontSize: 14,
            height: 1,
            color: AppColors.textWeak,
          ),
        ),
      ],
    ),
    trailing: isShowTrailing
        ? SvgPicture.asset(
            "assets/svg/ic_arrow_right.svg",
            width: 16,
            height: 16,
            color: Color(0xffAFB6CC),
          )
        : null,
  ));
}
