import 'dart:math';
import 'package:flutter/material.dart';
import 'package:common_utils/common_utils.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/model/coupon_model.dart';
import 'package:yvr_assistant/public_ui/widget/rich_text.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';

class CouponCell extends StatefulWidget {
  final CouponModel detlModel;

  CouponCell({Key key, this.detlModel}) : super(key: key);

  @override
  State<CouponCell> createState() => _CouponCellState();
}

class _CouponCellState extends State<CouponCell> {
  @override
  Widget build(BuildContext context) {
    CouponModel detlModel = widget.detlModel;
    double imgW = (Global.screenWidth - 20 * 2);
    double imgH = (imgW * 131 / 374);
    String endDate = DateUtil.formatDateMs(detlModel.endTime.toInt(),
        format: "yyyy.MM.dd HH:mm");
    return Column(
      children: [
        Container(
            width: imgW,
            height: imgH,
            margin: EdgeInsets.only(top: 20),
            alignment: Alignment.topLeft,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(6),
                  topRight: Radius.circular(6),
                ),
                image: DecorationImage(
                  image: AssetImage('assets/images/game_coupon.png'),
                  fit: BoxFit.contain,
                )),
            child: Container(
              alignment: Alignment.topLeft,
              padding: EdgeInsets.fromLTRB(15, 5, 15, 0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Spacer(),
                  Text(
                    detlModel.couponInfo.name,
                    style: TextStyle(fontSize: 16, color: Color(0xFFE8E8E8)),
                  ),
                  Spacer(),
                  IntlRichText1(
                    intlTextBuilder: YLocal.current.Nzhang,
                    defaultStyle: TextStyle(
                      fontSize: 12,
                      color: Color(0xFFB0B2B8),
                    ),
                    param: detlModel.surplus,
                    paramStyle: TextStyle(
                        fontSize: 40,
                        height: 1,
                        fontWeight: FontWeight.w700,
                        color: Color(0xffE8E8E8)),
                  ),
                  Container(
                    padding: EdgeInsets.zero,
                    width: imgW * 4 / 7,
                    child: Text(
                      detlModel.couponInfo.shortDescription,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                      style: TextStyle(fontSize: 12, color: Color(0xFFB0B2B8)),
                    ),
                  ),
                  Spacer(),
                ],
              ),
            )),
        GestureDetector(
          child: Container(
            height: 34,
            width: imgW,
            padding: EdgeInsets.only(left: 15, right: 15),
            decoration: BoxDecoration(
              color: Color(0xFF32373F),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(6),
                bottomRight: Radius.circular(6),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(YLocal.current.youxiaoqizhi + endDate,
                    style: TextStyle(fontSize: 12, color: Color(0xFFB0B2B8))),
                Text("${YLocal.current.shiyongshuimingshiyo}  >",
                    style: TextStyle(fontSize: 12, color: Color(0xFFB0B2B8))),
              ],
            ),
          ),
          onTap: () {
            if (detlModel.couponInfo.detailDescription == null) {
              YvrToast.showToast(YLocal.current.zanmoshiyongshuiming);
              return;
            }

            double lines = detlModel.couponInfo.detailDescription.length / 20;

            lines = max(3, lines);
            lines = min(10, lines);
            showDialog(
                context: context,
                barrierDismissible: false,
                builder: (_) {
                  return CustomDialog(
                    height: (150 + lines * 15),
                    title: YLocal.current.shiyongshuimingshiyo,
                    dialogType: DialogType.DialogDIY,
                    contentWidget: Container(
                      height: (lines * 15),
                      margin: EdgeInsets.only(bottom: 20),
                      child: SingleChildScrollView(
                        child: Text(
                          detlModel.couponInfo.detailDescription,
                          style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: 14,
                              height: 1.57143,
                              letterSpacing: 1,
                              color: Color(0xff6e7380)),
                        ),
                      ),
                    ),
                    isCancel: false,
                    confirmText: YLocal.current.zhidaoliao,
                    confirmCallback: () {
                      Navigator.pop(context);
                    },
                  );
                });
          },
        ),
      ],
    );
  }
}
