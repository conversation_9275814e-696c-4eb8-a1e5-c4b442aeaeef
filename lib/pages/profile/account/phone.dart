import 'package:flutter/material.dart';
import 'package:keyboard_avoider/keyboard_avoider.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/pages/login/views/code_button.dart';
import 'package:yvr_assistant/pages/login/views/input_field.dart';
import 'package:yvr_assistant/pages/login/views/login_btn.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/utils/yvr_utils.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';

import '../../../styles/app_divider.dart';

class PhonePage extends StatefulWidget {
  PhonePage({Key key}) : super(key: key);

  @override
  _PhonePageState createState() => _PhonePageState();
}

class _PhonePageState extends State<PhonePage> {
  String _phoneNumStr = DBUtil.instance.userBox.get(kMobile) ?? "";
  var _verifyCode = new TextEditingController();

  // ignore: non_constant_identifier_names
  var _new_phoneNum = new TextEditingController();

  // ignore: non_constant_identifier_names
  var _re_verifyCode = new TextEditingController();
  final GlobalKey<AuthCodeButtonState> authCodeKey = GlobalKey();
  final GlobalKey<AuthCodeButtonState> newAuthCodeKey = GlobalKey();
  String forCodeBtnPhoneNum = "";
  String forLoginBtnVfCode = "";
  String forLoginBtnNewVfCode = "";

  @override
  Widget build(BuildContext context) {
    String _safeNum =
        _phoneNumStr.length > 0 ? _phoneNumStr.replaceRange(3, 7, "****") : "";
    return Scaffold(
        appBar: TopNavbar(
          title: YLocal.of(context).genghuanshoujihao,
        ),
        body: KeyboardAvoider(
            autoScroll: true,
            child: Container(
              margin: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    YLocal.of(context).profile_verify_phone_num +
                        " " +
                        _safeNum,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 18,
                      height: 1,
                      color: Color(0xff2c2e33),
                    ),
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  Container(
                    height: 58,
                    child: Flex(
                      direction: Axis.horizontal,
                      children: [
                        Expanded(
                          child: ImputFieldWidget(
                              maxLength: 6,
                              controller: _verifyCode,
                              updateText: (value) {
                                if (value.length >= 3) {
                                  setState(() {
                                    forLoginBtnVfCode = value;
                                  });
                                }
                              },
                              hintText: YLocal.of(context).qingshuruyanzhengma),
                          flex: 3,
                        ),
                        SizedBox(
                          width: 8,
                        ),
                        SizedBox(
                          height: 49,
                          child: codeButtonWidget(mobileNum: _phoneNumStr),
                        ),
                      ],
                    ),
                  ),
                  AppDivider.backgroundDivider,
                  SizedBox(
                    height: 60,
                  ),
                  Text(
                    "${YLocal.of(context).qingshuruxinshoujiha}",
                    style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 18,
                        height: 1,
                        color: Color(0xff2c2e33)),
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 20, bottom: 1),
                    height: 50,
                    child: ImputFieldWidget(
                        controller: _new_phoneNum,
                        maxLength: 11,
                        updateText: (value) {
                          setState(() {
                            forCodeBtnPhoneNum = value;
                          });
                        },
                        hintText: YLocal.of(context).qingshuruxinshoujiha),
                  ),
                  AppDivider.backgroundDivider,
                  SizedBox(
                    height: 12,
                  ),
                  Container(
                      height: 58,
                      child: Flex(
                        direction: Axis.horizontal,
                        children: [
                          Expanded(
                            child: ImputFieldWidget(
                                maxLength: 6,
                                controller: _re_verifyCode,
                                updateText: (value) {
                                  if (value.length >= 3) {
                                    setState(() {
                                      forLoginBtnNewVfCode = value;
                                    });
                                  }
                                },
                                hintText:
                                    YLocal.of(context).qingshuruyanzhengma),
                            flex: 3,
                          ),
                          SizedBox(
                            width: 8,
                          ),
                          SizedBox(
                            height: 49,
                            child: newPhoneCodeButtonWidget(),
                          ),
                        ],
                      )),
                  AppDivider.backgroundDivider,
                  SizedBox(
                    height: 72,
                  ),
                  loginButtonWidget()
                ],
              ),
            )));
  }

  Widget codeButtonWidget({@required mobileNum}) {
    return AuthCodeButton(
        key: authCodeKey,
        timeCount: 60,
        isActive: true,
        onPressed: () {
          if (!YvrUtils.isPhoneNum(mobileNum)) {
            YvrToast.showToast(YLocal.of(context).qingshuruyouxiaoshou);
            return;
          }
          authCodeKey.currentState.startAction();
          http.post<Map>('vrmcsys/account/reqSmscode', data: {
            'mobile': mobileNum,
          }).then((response) {
            if (response.data["errCode"] == 0) {
              YvrToast.showToast(YLocal.of(context).vfcode_send);
            }
          }).catchError((error) {
            YvrToast.showError(YLocal.of(context).get_vfcode_failed);
          });
        });
  }

  Widget newPhoneCodeButtonWidget() {
    return AuthCodeButton(
        key: newAuthCodeKey,
        timeCount: 60,
        isActive: YvrUtils.isPhoneNum(forCodeBtnPhoneNum),
        onPressed: () {
          if (!YvrUtils.isPhoneNum(forCodeBtnPhoneNum)) {
            YvrToast.showToast(YLocal.of(context).qingshuruyouxiaoshou);
            return;
          }
          newAuthCodeKey.currentState.startAction();
          http.post<Map>('vrmcsys/account/reqSmscode', data: {
            'mobile': forCodeBtnPhoneNum,
          }).then((response) {
            if (response.data["errCode"] == 0) {
              YvrToast.showToast(YLocal.of(context).vfcode_send);
            }
          }).catchError((error) {
            YvrToast.showError(YLocal.of(context).get_vfcode_failed);
          });
        });
  }

  Widget loginButtonWidget() {
    return LoginButtonWidget(
      buttonText: YLocal.of(context).lijigenghuan,
      isActive: YvrUtils.isPhoneNum(forCodeBtnPhoneNum) &&
          (forLoginBtnVfCode.length >= 4) &&
          (forLoginBtnNewVfCode.length >= 4),
      isCustomBtn: true,
      onPressed: () {
        if (_phoneNumStr == _new_phoneNum.text) {
          YvrToast.showToast(YLocal.of(context).xiugaishoujihaobunen);
          return;
        }
        if (!YvrUtils.isPhoneNum(_new_phoneNum.text)) {
          YvrToast.showToast(YLocal.of(context).qingshuruyouxiaoshou);
          return;
        }
        if (_verifyCode.text.length == 0 || _re_verifyCode.text.length == 0) {
          YvrToast.showToast(YLocal.of(context).input_verify_code);
          return;
        }

        http.post<Map>('vrmcsys/account/modifyMobile', data: {
          'oMobile': _phoneNumStr,
          'oCode': _verifyCode.text,
          'nMobile': _new_phoneNum.text,
          'nCode': _re_verifyCode.text
        }).then((response) async {
          if (response.data["errCode"] == 0) {
            await DBUtil.instance.userBox.put(kMobile, _new_phoneNum.text);
            Global.navigatorKey.currentState
                .pushNamedAndRemoveUntil("/login", (route) => route.isFirst);
          }
        }).catchError((error) {});
      },
    );
  }
}
