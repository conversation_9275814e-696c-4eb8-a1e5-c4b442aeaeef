import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:flutter_picker/flutter_picker.dart';
import 'package:yvr_assistant/styles/app_divider.dart';
import 'package:yvr_assistant/utils/yvr_utils.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/utils/formatter.dart';
import 'package:yvr_assistant/view_model/vmodel_mixin.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';
import 'package:yvr_assistant/base/lifecycle_state_mixin.dart';
import 'package:yvr_assistant/public_ui/widget/image_view.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/public_ui/widget/picker_tool.dart';
import 'package:yvr_assistant/public_ui/widget/input_dialog.dart';
import 'package:yvr_assistant/public_ui/widget/refresh_layout.dart';
import 'package:yvr_assistant/view_model/personal_home_vmodel.dart';
import 'package:yvr_assistant/public_ui/widget/image_browser.dart';

class UserInfoPage extends StatefulWidget {
  UserInfoPage({Key key}) : super(key: key);

  @override
  UserInfoPageState createState() => UserInfoPageState();
}

class UserInfoPageState extends LifecycleState<UserInfoPage> {
  UserInfoVModel _viewModel;

  @override
  void initState() {
    super.initState();
    _viewModel = UserInfoVModel();
  }

  void pickUserImage() {
    doSelectAndCropImage(context, upload, aspectRatio: 1.0).then((value) {
      if (value == 1) {
        _viewModel.refreshQuietly();
      }
    });
  }

  @override
  void onRefresh() {
    _viewModel.refreshQuietly();
  }

  @override
  Widget build(BuildContext context) {
    List<String> gender = [
      YLocal.of(context).nan,
      YLocal.of(context).nv,
      YLocal.of(context).baomi
    ];

    return Scaffold(
      appBar: TopNavbar(
        title: YLocal.of(context).user_info,
      ),
      body: SimpleRefreshList<UserInfoVModel>(
          model: _viewModel,
          builder: (context, viewModel) {
            final String modelNick = viewModel.model.nick;
            final int modelSex = viewModel.model.sex;

            Color nameColor;
            String userName;
            if (modelNick != null && modelNick.isNotEmpty) {
              userName = modelNick;
              nameColor = Color(0xff2C2E33);
            } else {
              userName = YLocal.of(context).profile_input_nick;
              nameColor = Color(0xffAFB6CC);
            }

            Color sexColor;
            String userSex;
            if (modelSex != null) {
              userSex = YvrUtils.getSexText(modelSex);
              sexColor = Color(0xff2C2E33);
            } else {
              userSex = YLocal.of(context).weizhi;
              sexColor = Color(0xffAFB6CC);
            }
            Color signColor;
            String userSign;
            if (viewModel.model.motto != null &&
                viewModel.model.motto.isNotEmpty) {
              userSign = viewModel.model.motto;
              signColor = Color(0xff2C2E33);
            } else {
              userSign = YLocal.of(context).jieshaoyixianiziji;
              signColor = Color(0xffAFB6CC);
            }

            Color birthColor;
            String userBirth;
            if (viewModel.userBirthdayText != null &&
                viewModel.userBirthdayText.isNotEmpty) {
              userBirth = viewModel.userBirthdayText;
              birthColor = Color(0xff2C2E33);
            } else {
              userBirth = YLocal.of(context).weizhi;
              birthColor = Color(0xffAFB6CC);
            }

            return ListView(
              children: [
                buildAvatar(viewModel),
                const SizedBox(height: 30),
                ListTile(
                  title: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        YLocal.of(context).nickname,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          letterSpacing: 0,
                          color: AppColors.textSub,
                        ),
                      ),
                      Text(
                        userName,
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: 16,
                          color: nameColor,
                        ),
                      )
                    ],
                  ),
                  trailing: SvgPicture.asset(
                    "assets/svg/arrow_right.svg",
                    color: AppColors.textSub,
                    width: 10,
                    height: 10,
                  ),
                  onTap: () {
                    showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (_) {
                          String nameForbidStr = '';
                          return StatefulBuilder(builder: (context, setState) {
                            return InputDialog(
                              title: YLocal.of(context).modify_nickname,
                              isCancel: true,
                              keyboardType: TextInputType.text,
                              hintText: userName,
                              maxLength: 12,
                              errNote: nameForbidStr,
                              inputCommitCallback: (text) {
                                String nick = text?.trim() ?? '';
                                if (nick.isNotEmpty) {
                                  viewModel.updateUserInfo(
                                      nick: text,
                                      onSuccess: () {
                                        Navigator.of(context).pop();
                                      });
                                } else {
                                  setState(() {
                                    nameForbidStr =
                                        YLocal.of(context).nichenbukeweikonghuo;
                                  });
                                }
                              },
                            );
                          });
                        });
                  },
                ),
                Container(
                  child: AppDivider.backgroundDividerWithIndent(16),
                  margin: const EdgeInsets.only(top: 3, bottom: 11),
                ),
                ListTile(
                  title: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        YLocal.of(context).gexingqianming,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          letterSpacing: 0,
                          color: AppColors.textSub,
                        ),
                      ),
                      const SizedBox(width: 60),
                      Expanded(
                        child: Text(
                          userSign,
                          style: TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 16,
                            color: signColor,
                          ),
                          maxLines: 1,
                          textAlign: TextAlign.end,
                          overflow: TextOverflow.ellipsis,
                        ),
                      )
                    ],
                  ),
                  trailing: SvgPicture.asset(
                    "assets/svg/arrow_right.svg",
                    color: AppColors.textSub,
                    width: 10,
                    height: 10,
                  ),
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) {
                          return _UserSignPage(
                            viewModel: viewModel,
                          );
                        },
                      ),
                    );
                  },
                ),
                Container(
                  child: AppDivider.backgroundDividerWithIndent(16),
                  margin: const EdgeInsets.only(top: 3, bottom: 11),
                ),
                ListTile(
                  title: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        YLocal.of(context).gender,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          letterSpacing: 0,
                          color: AppColors.textSub,
                        ),
                      ),
                      Text(
                        userSex,
                        style: TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 16,
                            color: sexColor),
                      )
                    ],
                  ),
                  trailing: SvgPicture.asset(
                    "assets/svg/arrow_right.svg",
                    color: AppColors.textSub,
                    width: 10,
                    height: 10,
                  ),
                  onTap: () {
                    JhPickerTool.showStringPicker(
                      context,
                      title: '',
                      data: gender,
                      normalIndex: gender.indexOf(userSex) ?? 0,
                      clickCallBack: (int index, var item) {
                        viewModel.updateUserInfo(sex: (index + 1));
                      },
                    );
                  },
                ),
                Container(
                  child: AppDivider.backgroundDividerWithIndent(16),
                  margin: const EdgeInsets.only(top: 3, bottom: 11),
                ),
                ListTile(
                  title: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        YLocal.of(context).birthday,
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                          letterSpacing: 0,
                          color: AppColors.textSub,
                        ),
                      ),
                      Text(
                        userBirth,
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: 16,
                          color: birthColor,
                        ),
                      )
                    ],
                  ),
                  trailing: SvgPicture.asset(
                    "assets/svg/arrow_right.svg",
                    color: AppColors.textSub,
                    width: 10,
                    height: 10,
                  ),
                  onTap: () {
                    // Locale myLocale = Localizations.localeOf(context);
                    // bool isZh = myLocale.toString() == "zh";

                    JhPickerTool.showDatePicker(context,
                        adapter: DateTimePickerAdapter(
                          minValue: DateTime(1900, 1, 1),
                          maxValue: DateTime.now(),
                          value: viewModel.userBirthday,
                        ), clickCallBack: (var str, var time) {
                      setState(() {
                        String tempDate = time
                            .replaceRange(4, 5, "-")
                            .replaceRange(7, 8, "-");
                        viewModel.updateUserInfo(
                          birth: tempDate.substring(0, 11),
                        );
                      });
                    });
                  },
                ),
                Container(
                  child: AppDivider.backgroundDividerWithIndent(16),
                  margin: const EdgeInsets.only(top: 3, bottom: 11),
                ),
              ],
            );
          }),
    );
  }

  Widget buildAvatar(UserInfoVModel viewModel) {
    return Container(
      alignment: AlignmentDirectional.center,
      child: GestureDetector(
        onTap: pickUserImage,
        child: Container(
          margin: const EdgeInsets.only(top: 35),
          width: 100,
          height: 100,
          child: Stack(
            children: [
              Positioned(
                left: 2,
                top: 2,
                right: 2,
                bottom: 2,
                child: OnlineAvatarImageWidget(
                  viewModel.model.avatar,
                  sex: viewModel.model.sex,
                ),
              ),
              Positioned(
                right: -2,
                bottom: -2,
                child: SvgPicture.asset(
                  "assets/svg/upate_avatar.svg",
                  width: 40,
                  height: 40,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  static Future<String> upload(Uint8List data, String fileName) async {
    var result = await YvrRequests.uploadPersonalAvatar(data, fileName);
    switch (result.errCode) {
      case 0:
        return result.data;
      case 10540:
        throw UploadImageException(YLocal.current.nindetouxiangtupians);
      default:
        throw UploadImageException(YLocal.current.shangchuanshibaishan);
    }
  }
}

class _UserSignPage extends StatefulWidget {
  final UserInfoVModel viewModel;

  const _UserSignPage({Key key, @required this.viewModel}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _UserSignPageState();
  }
}

const TextStyle kUserSignTextStyle = TextStyle(
  fontWeight: FontWeight.w400,
  fontSize: 18,
  height: 1.61111,
  color: Color(0xffb0b2b8),
);

class _UserSignPageState extends State<_UserSignPage> {
  TextEditingController _controller;
  static const int kMaxLines = 10;
  static const double kHPadding = 20;
  static const int kSignMaxLines = 6;

  @override
  void initState() {
    super.initState();
    _controller =
        TextEditingController(text: widget.viewModel.model.motto ?? '');
  }

  static const kBorder = UnderlineInputBorder(
    borderSide: BorderSide(
      color: Color(0xffEEEEEE),
      width: 0.5,
    ),
  );

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<UserInfoVModel>.value(
      value: widget.viewModel,
      child: Scaffold(
        appBar: TopNavbar(
          title: YLocal.of(context).gexingqianming,
          actions: [
            Consumer<UserInfoVModel>(builder: (context, value, child) {
              return Container(
                margin: const EdgeInsets.only(right: 20),
                alignment: AlignmentDirectional.center,
                child: ElevatedProgressButton(
                  padding: EdgeInsets.zero,
                  radius: 16,
                  width: 52,
                  height: 30,
                  state: value.getProgressState(),
                  onPressed: save,
                  idleChild: Text(
                    YLocal.of(context).baocun,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      height: 1.1,
                      color: Color(0xffffffff),
                    ),
                  ),
                ),
              );
            }),
          ],
        ),
        body: Column(
          children: [
            SingleChildScrollView(
              padding: const EdgeInsets.only(top: 26),
              child: Column(
                children: [
                  TextField(
                    controller: _controller,
                    maxLines: kMaxLines,
                    maxLength: 70,
                    inputFormatters: [
                      SignTextInputFormatter(),
                    ],
                    buildCounter: (
                      BuildContext context, {
                      @required int currentLength,
                      @required int maxLength,
                      @required bool isFocused,
                    }) {
                      return Container(
                        margin: const EdgeInsets.only(top: 8),
                        child: Text(
                          '${maxLength - currentLength}',
                          style: TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 13,
                            height: 1,
                            color: AppColors.textWeak,
                          ),
                        ),
                      );
                    },
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 18,
                      height: 1.61111,
                      color: AppColors.textSub,
                    ),
                    decoration: InputDecoration(
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: kHPadding),
                      border: kBorder,
                      disabledBorder: kBorder,
                      enabledBorder: kBorder,
                      errorBorder: kBorder,
                      focusedBorder: kBorder,
                      focusedErrorBorder: kBorder,
                      isDense: true,
                      isCollapsed: true,
                      hintText: YLocal.of(context).xiangxiangrenshinide,
                      helperText: YLocal.of(context).qianmingbunenghanyou,
                      helperStyle: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 13,
                        height: 1,
                        color: AppColors.textWeak,
                      ),
                      hintStyle: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 18,
                        height: 1.61111,
                        color: AppColors.textWeak,
                      ),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  void save() {
    final String text = _controller.text.trim();
    if (text.isNotEmpty) {
      TextPainter textPainter = TextPainter(
          text: TextSpan(text: text, style: kUserSignTextStyle),
          maxLines: kMaxLines,
          textDirection: TextDirection.ltr);
      textPainter.layout(maxWidth: Global.screenWidth - 2 * kHPadding);
      if (textPainter.computeLineMetrics().length > kSignMaxLines) {
        YvrToast.showToast(YLocal.of(context).qianmingbunengchaogu);
        return;
      }
    }
    widget.viewModel.updateUserInfo(
        motto: text,
        onSuccess: () {
          Navigator.of(context).pop();
        });
  }

  @override
  void dispose() {
    super.dispose();
  }
}
