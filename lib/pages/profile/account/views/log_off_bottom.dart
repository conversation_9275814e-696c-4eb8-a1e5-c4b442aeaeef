// ignore_for_file: deprecated_member_use
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';
import 'package:yvr_assistant/public_ui/widget/rich_text.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';

import '../../../../generated/l10n.dart';
import '../../../../manager/environment.dart';
import '../../../../styles/app_color.dart';

Widget logOffBottomButton(
    {bool isShowAgree = true,
    ValueNotifier<bool> isAgreeProtrol,
    Function nextStep,
    @required String leftBtnText}) {
  return ValueListenableBuilder<bool>(
      valueListenable: isAgreeProtrol,
      builder: (context, isAgree, child) {
        return Container(
            height: isShowAgree ? 120 : 100,
            alignment: Alignment(0, -1),
            color: Colors.white,
            width: Global.screenWidth,
            padding: EdgeInsets.fromLTRB(20, 10, 20, 20),
            child: Column(
              children: [
                if (isShowAgree)
                  Row(
                    children: [
                      GestureDetector(
                        onTap: () {
                          isAgreeProtrol.value = !isAgreeProtrol.value;
                        },
                        child: SvgPicture.asset(
                          isAgree
                              ? "assets/svg/option_done.svg"
                              : "assets/svg/option_define.svg",
                          width: 20,
                          height: 20,
                        ),
                      ),
                      SizedBox(
                        width: 10,
                      ),
                      Expanded(
                        child: IntlRichText1(
                          intlTextBuilder:
                              YLocal.of(context).woyiyuedubingtongyiS,
                          defaultStyle: TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 12,
                            color: Color(0xff767880),
                          ),
                          param: YLocal.of(context).zhuxiaoxieyi_1,
                          paramStyle: TextStyle(color: Color(0xff4F7FFE)),
                          paramRecognizer: TapGestureRecognizer()
                            ..onTap = () {
                              String url = Environment()
                                  .config
                                  .getAccountCancellationAgreementUrl(context);
                              Navigator.pushNamed(
                                context,
                                '/yvr_web',
                                arguments: {
                                  "title": YLocal.of(context).zhuxiaoxieyi,
                                  "url": url,
                                  "isNeedLogin": 1,
                                },
                              );
                            },
                        ),
                      ),
                    ],
                  ),
                Container(
                    height: 48,
                    margin: EdgeInsets.only(top: 12),
                    width: Global.screenWidth - 40,
                    child: Flex(
                      direction: Axis.horizontal,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                            flex: 1,
                            child: Container(
                              height: 48,
                              child: YvrTextButton(
                                color: Color(0xffF0F2F5),
                                onPressed: () {
                                  if (!isAgree) {
                                    YvrToast.showToast(
                                      YLocal.of(context).qingxianyuedubington,
                                    );
                                  } else {
                                    nextStep();
                                  }
                                },
                                child: Text(
                                  leftBtnText,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 18,
                                    letterSpacing: 1,
                                    color: AppColors.textSub,
                                  ),
                                ),
                              ),
                            )),
                        SizedBox(
                          width: 15,
                        ),
                        Expanded(
                          flex: 1,
                          child: Container(
                            height: 48,
                            child: YvrTextButton(
                              onPressed: () {
                                Navigator.popUntil(context,
                                    ModalRoute.withName('/account_safe'));
                              },
                              child: Text(
                                YLocal.of(context).tuichuzhuxiao,
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 18,
                                  letterSpacing: 1,
                                  color: AppColors.background,
                                ),
                              ),
                            ),
                          ),
                        )
                      ],
                    ))
              ],
            ));
      });
}
