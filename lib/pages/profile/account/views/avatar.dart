import 'package:adaptive_dialog/adaptive_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yvr_assistant/pages/device/tool/file_handle.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';

import '../../../../generated/l10n.dart';

class AvatarWidget extends StatefulWidget {
  final bool isHasAvatar;
  final String avatarUrl;
  final bool isGirl;
  final Function onPressed; // 注意：带参数的 方法 不允许添加 VoidCallback ？？
  AvatarWidget(
      {Key key,
      @required this.isHasAvatar,
      this.avatarUrl,
      this.isGirl,
      this.onPressed})
      : super(key: key);

  @override
  _AvatarWidgetState createState() => _AvatarWidgetState();
}

class _AvatarWidgetState extends State<AvatarWidget> {
  @override
  Widget build(BuildContext context) {
    Widget avatar = CircleAvatar(
      backgroundImage: AssetImage('assets/images/avatar_boy.jpg'),
    );

    if (widget.isHasAvatar) {
      avatar = Container(
        width: 90,
        height: 90,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(45),
          child: FadeInImage.assetNetwork(
            placeholder: 'assets/images/s_plhd.png',
            image: widget.avatarUrl ?? "",
            fit: BoxFit.cover,
            imageErrorBuilder: (context, error, stackTrace) {
              return Image.asset(
                'assets/images/avatar_boy.jpg',
                fit: BoxFit.cover,
              );
            },
          ),
        ), // 通过 container 实现圆角
      );
    } else if (widget.isGirl) {
      avatar = CircleAvatar(
        backgroundImage: AssetImage('assets/images/avatar_girl.jpg'),
      );
    }

    List<SheetAction<String>> actions = [
      SheetAction(
        icon: Icons.camera,
        label: YLocal.of(context).paizhao,
        key: 'carema',
      ),
      SheetAction(
        icon: Icons.photo,
        label: YLocal.of(context).congxiangcezhongxuan,
        key: 'photo',
        isDefaultAction: true,
      )
    ];
    if (Platform.isAndroid) {
      actions.add(const SheetAction(
        label: "",
        key: 'cancel',
        isDefaultAction: true,
      ));
    }

    return Container(
      height: 160,
      child: Stack(
        alignment: Alignment.center, //指定未定位或部分定位widget的对齐方式
        children: <Widget>[
          InkWell(
            onTap: () async {
              final result = await showModalActionSheet<String>(
                  context: context, actions: actions);
              if (result == 'carema' || result == 'photo') {
                requestPhotoPermission(result == 'carema').then((isAllow) {
                  if (isAllow) {
                    widget.onPressed(result);
                  }
                });
              }
            },
            child: SizedBox(
              height: 90,
              width: 90,
              child: avatar,
            ),
          ),
          Positioned(
            right: (MediaQuery.of(context).size.width / 2 - 50),
            top: 100,
            child: SvgPicture.asset(
              "assets/svg/upate_avatar.svg",
              width: 36,
              height: 36,
            ),
          ),
        ],
      ),
    );
  }
}
