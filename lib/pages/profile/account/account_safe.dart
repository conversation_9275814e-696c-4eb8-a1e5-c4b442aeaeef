import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/model/user_model.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/styles/app_divider.dart';
import 'package:yvr_assistant/model/dev_info_model.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/pages/profile/views/profile_view.dart';

class AccountSafePage extends StatefulWidget {
  AccountSafePage({Key key}) : super(key: key);

  @override
  State<AccountSafePage> createState() => _AccountSafePageState();
}

class _AccountSafePageState extends State<AccountSafePage> {
  bool _isTestUser =
      StorageManager.localStorage.getItem(Global.kIsTestUser) ?? false;
  bool _isDeveloper =
      StorageManager.localStorage.getItem(Global.kIsDeveloper) ?? false;

  @override
  Widget build(BuildContext context) {
    UserModel _userModel = UserVModel().user;
    // Log.d('手机号 ===================>${DBUtil.instance.userBox.get(kMobile)}');
    String mobile = DBUtil.instance.userBox.get(kMobile);
    return Scaffold(
      backgroundColor: AppColors.secondaryBg,
      appBar: TopNavbar(
        title: YLocal.of(context).zhanghuyuanquan,
      ),
      body: ListView(
        children: [
          SizedBox(
            height: 15,
          ),
          AppDivider.backgroundDivider,
          Container(
            color: Colors.white,
            child: profileCell(
              title: YLocal.of(context).profile_change_pwd,
              cellClick: () {
                checkTeenMode(
                  () {
                    if (mobile.contains("@")) {
                      YvrToast.showToast(
                          'Please visit https://pfdm.cn/en to retrieve your password',
                          duration: Duration(seconds: 3));
                    } else {
                      Navigator.pushNamed(context, '/reset_pwd',
                          arguments: {"isForget": false});
                    }
                  },
                );
              },
            ),
          ),
          AppDivider.backgroundDivider,
          Container(
            color: Colors.white,
            child: profileCell(
              title: YLocal.of(context).email_address,
              isShowTrailing: mobile != null && !mobile.contains('@'),
              rightText:
                  (mobile == null || (mobile != null && mobile.contains('@')))
                      ? mobile ?? ''
                      : mobile.replaceRange(3, 7, "****"),
              cellClick: () {
                checkTeenMode(() {
                  if (_userModel == null) {
                    Navigator.pushNamed(context, "/login");
                  } else {
                    if (mobile.contains("@")) {
                      // YvrToast.showToast('unknow error');
                    } else {
                      Navigator.pushNamed(context, "/phone");
                    }
                  }
                });
              },
            ),
          ),
          AppDivider.backgroundDivider,
          SizedBox(
            height: 10,
          ),
          if (!_isTestUser && !_isDeveloper)
            Column(
              children: [
                AppDivider.backgroundDivider,
                Container(
                  color: Colors.white,
                  child: profileCell(
                      title: YLocal.of(context).zhuxiaozhanghao,
                      cellClick: () {
                        Navigator.pushNamed(context, '/log_off');
                      }),
                ),
                AppDivider.backgroundDivider,
              ],
            ),
        ],
      ),
    );
  }

  bool checkingTeenMode = false;

  void checkTeenMode(VoidCallback onComplete) {
    if (checkingTeenMode) {
      return;
    }
    checkingTeenMode = true;
    hasTeenModeDevice().then((value) {
      if (value) {
        YvrToast.showToast(YLocal.of(context).xiugaiqianqingxiangu);
      } else {
        onComplete.call();
      }
    }).catchError((e, s) {
      YvrToast.showExceptionMessage(e);
    }).whenComplete(() {
      checkingTeenMode = false;
    });
  }

  Future<bool> hasTeenModeDevice() async {
    ///如果青少年模式已经打开，防止VR端关闭青少年模式导致App设备列表没有及时更新isOpen状态，需要再次检测到底有没有打开青少年模式
    List<DevInfoModel> devModels = await YvrRequests.getLoginDevInfos();
    int teenIndex = devModels.indexWhere((element) => element.teen == 1);
    if (teenIndex >= 0) {
      return true;
    }
    return false;
  }
}
