import 'package:flutter/material.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';

import '../../../../generated/l10n.dart';
import '../../../../styles/app_color.dart';

class LogOffProtocolPage extends StatefulWidget {
  LogOffProtocolPage({Key key}) : super(key: key);

  @override
  State<LogOffProtocolPage> createState() => _LogOffProtocolPageState();
}

class _LogOffProtocolPageState extends State<LogOffProtocolPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TopNavbar(
        title: YLocal.of(context).zhuxiaoxieyi,
      ),
      body: Container(
        height: Global.screenHeight,
        child: Stack(children: [
          SingleChildScrollView(
            padding: EdgeInsets.fromLTRB(15, 10, 15, 20),
            child: <PERSON>umn(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  """
${YLocal.of(context).zaininshenqingzhuxia}

${YLocal.of(context).YVRzaicishanyidixing}
${YLocal.of(context).jianyininzaizhuxiaoq}

${YLocal.of(context).yifeichangweihanwome}
${YLocal.of(context).ninjiangfangqiweidao}
${YLocal.of(context).ninjiangjiechugaizha}
${YLocal.of(context).gaizhanghaodequanbug}
${YLocal.of(context).gaizhanghaodehaoyouj}
${YLocal.of(context).gaizhanghaoleijidede}

${YLocal.of(context).erweiliaobangchuninw}
${YLocal.of(context).gaizhanghaojitongguo}
${YLocal.of(context).gaizhanghaonamoweich}
${YLocal.of(context).nindezhanghaochuyuzh_1}

${YLocal.of(context).sanninzhuxiaobenYVRz}
""",
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 16,
                    height: 1.5,
                    letterSpacing: 1,
                    color: AppColors.textTitle,
                  ),
                ),
              ],
            ),
          ),
        ]),
      ),
    );
  }
}
