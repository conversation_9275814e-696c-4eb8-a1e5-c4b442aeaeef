import 'package:flutter/material.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/model/user_assets.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/view_model/assets_vmodel.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/provider/provider_widget.dart';
import 'package:yvr_assistant/provider/view_state_widget.dart';
import 'package:yvr_assistant/pages/profile/account/views/log_off_bottom.dart';
import 'package:yvr_assistant/pages/profile/account/withdraw/log_off.dart';

import '../../../../generated/l10n.dart';
import '../../../../styles/app_color.dart';
import '../../../../styles/app_divider.dart';

class ApplyWithdrawPage extends StatefulWidget {
  ApplyWithdrawPage({Key key}) : super(key: key);

  @override
  State<ApplyWithdrawPage> createState() => _ApplyWithdrawPageState();
}

class _ApplyWithdrawPageState extends State<ApplyWithdrawPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppColors.secondaryBg,
        appBar: TopNavbar(
          title: YLocal.of(context).shenqingzhanghaozhux,
        ),
        body: ProviderWidget<AssetsVModel>(
            model: AssetsVModel(),
            onModelReady: (model) => model.initData(),
            builder: (context, model, child) {
              if (model.busy) {
                return ViewStateBusyWidget();
              } else if (model.error) {
                return ViewStateErrorWidget(
                    error: model.viewStateError, onPressed: model.initData);
              }

              UserAssetsModel assetsModel = model.assetsModel;
              return Container(
                height: Global.screenHeight,
                child: Stack(children: [
                  SingleChildScrollView(
                    padding: EdgeInsets.only(
                        left: 16, top: 16, right: 16, bottom: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          YLocal.of(context).shifouquedingfangqid,
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 18,
                            height: 1.5,
                            letterSpacing: 0.5,
                            color: AppColors.textTitle,
                          ),
                        ),
                        SizedBox(
                          height: 24,
                        ),
                        Text(
                          YLocal.of(context).dangqianzhanghaoyong,
                          style: TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 16,
                            height: 1.5,
                            letterSpacing: 1,
                            color: AppColors.textTitle,
                          ),
                        ),
                        textForRow(YLocal.of(context)
                            .VRyingyongyouhuS1VRy(assetsModel.app ?? 0)),
                        AppDivider.backgroundDivider,
                        textForRow(
                            YLocal.of(context).YbiNge(assetsModel.ycoin ?? 0)),
                        AppDivider.backgroundDivider,
                        textForRow(YLocal.of(context)
                            .youhuquanNzhang3youh(assetsModel.coupon ?? 0)),
                      ],
                    ),
                  ),
                  Positioned(
                    left: 0,
                    bottom: 0,
                    child: logOffBottomButton(
                        isShowAgree: false,
                        leftBtnText: YLocal.of(context).henxinfangqi,
                        isAgreeProtrol: ValueNotifier<bool>(true),
                        nextStep: () {
                          showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (_) {
                                return CustomDialog(
                                  title:
                                      YLocal.of(context).quedingfangqicaichan,
                                  content:
                                      YLocal.of(context).ninjiangjixuwancheng,
                                  height: 220,
                                  confirmText: YLocal.of(context).queding,
                                  confirmCallback: () {
                                    Navigator.pushNamed(
                                        context, "/confirm_withdraw");
                                  },
                                );
                              });
                        }),
                  )
                ]),
              );
            }));
  }
}
