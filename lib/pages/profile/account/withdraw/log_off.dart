import 'package:flutter/material.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/pages/profile/account/views/log_off_bottom.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/styles/app_divider.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';

import '../../../../generated/l10n.dart';
import '../../../../styles/app_color.dart';
import '../../../../view_model/user_vmodel.dart';

class LogOffAccountPage extends StatefulWidget {
  LogOffAccountPage({Key key}) : super(key: key);

  @override
  State<LogOffAccountPage> createState() => _LogOffAccountPageState();
}

class _LogOffAccountPageState extends State<LogOffAccountPage> {
  ValueNotifier<bool> _isAgreeProtrol = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: TopNavbar(
          title: YLocal.of(context).zhuxiaozhanghao,
        ),
        backgroundColor: AppColors.secondaryBg,
        body: Container(
          height: Global.screenHeight,
          child: Stack(children: [
            SingleChildScrollView(
              padding:
                  EdgeInsets.only(left: 16, top: 16, right: 16, bottom: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    YLocal.of(context).weibaozhengnindezhan,
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 18,
                      height: 1.5,
                      letterSpacing: 0.5,
                      color: AppColors.textTitle,
                    ),
                  ),
                  textForRow(YLocal.of(context).gaizhanghaojitongton),
                  AppDivider.backgroundDivider,
                  textForRow(YLocal.of(context).gaizhanghaonamofachu),
                  AppDivider.backgroundDivider,
                  textForRow(YLocal.of(context).nindezhanghaochuyuzh),
                ],
              ),
            ),
            Positioned(
              left: 0,
              bottom: 0,
              child: logOffBottomButton(
                  isAgreeProtrol: _isAgreeProtrol,
                  nextStep: () {
                    String mobile = DBUtil.instance.userBox.get(kMobile);
                    if (mobile.contains("@")) {
                      YvrToast.showToast(
                          'Email account cancellation is not currently supported');
                    } else {
                      Navigator.pushNamed(context, "/verify_identity");
                    }
                  },
                  leftBtnText: YLocal.of(context).xiayibu),
            )
          ]),
        ));
  }
}

Widget textForRow(String text, {double top = 24, double fontSize = 16}) {
  return Container(
    padding: EdgeInsets.only(top: top, bottom: top),
    child: Text(
      text,
      style: TextStyle(
        fontWeight: FontWeight.w400,
        fontSize: 16,
        height: 1.5,
        letterSpacing: 1,
        color: AppColors.textTitle,
      ),
    ),
  );
}
