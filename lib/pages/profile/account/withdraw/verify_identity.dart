import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/pages/login/views/code_button.dart';
import 'package:yvr_assistant/pages/login/views/input_field.dart';
import 'package:yvr_assistant/pages/profile/account/views/log_off_bottom.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/styles/app_divider.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';

import '../../../../styles/app_color.dart';

class VerifyIdentityPage extends StatefulWidget {
  VerifyIdentityPage({Key key}) : super(key: key);

  @override
  State<VerifyIdentityPage> createState() => _VerifyIdentityPageState();
}

class _VerifyIdentityPageState extends State<VerifyIdentityPage> {
  final GlobalKey<AuthCodeButtonState> authCodeKey = GlobalKey();
  var _verifyCode = new TextEditingController();
  String _vfCode = "";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: TopNavbar(
          title: YLocal.of(context).yanzhengshenfen,
        ),
        body: Container(
          height: Global.screenHeight,
          child: Stack(children: [
            SingleChildScrollView(
              padding:
                  EdgeInsets.only(left: 16, top: 16, right: 16, bottom: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    YLocal.of(context).shuruyanzhengma,
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 40,
                      height: 1.4,
                      letterSpacing: 0.5,
                      color: AppColors.textTitle,
                    ),
                  ),
                  SizedBox(
                    height: 12,
                  ),
                  Text(
                    YLocal.of(context).qingshurufashenggeig,
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 16,
                      height: 1.5,
                      letterSpacing: 1,
                      color: AppColors.textWeak,
                    ),
                  ),
                  SizedBox(
                    height: 60,
                  ),
                  Container(
                      height: 58,
                      child: Flex(
                        direction: Axis.horizontal,
                        children: [
                          Expanded(
                            child: ImputFieldWidget(
                              controller: _verifyCode,
                              maxLength: 6,
                              hintText: YLocal.of(context).qingshuruyanzhengma,
                              updateText: (value) {
                                _vfCode = value;
                              },
                            ),
                            flex: 3,
                          ),
                          SizedBox(
                            width: 8,
                          ),
                          SizedBox(
                            height: 49,
                            child: codeButtonWidget(),
                          ),
                        ],
                      )),
                  AppDivider.backgroundDivider,
                ],
              ),
            ),
            Positioned(
              left: 0,
              bottom: 0,
              child: logOffBottomButton(
                  isShowAgree: false,
                  isAgreeProtrol: ValueNotifier<bool>(true),
                  nextStep: () {
                    verifyUserRequest();
                  },
                  leftBtnText: YLocal.of(context).xiayibu),
            )
          ]),
        ));
  }

  Widget codeButtonWidget() {
    return AuthCodeButton(
        key: authCodeKey,
        timeCount: 60,
        isActive: true,
        onPressed: () {
          authCodeKey.currentState.startAction();
          YvrToast.showLoading();
          http.post<Map>('vrmcsys/account/reqSmscode', data: {
            'mobile': UserVModel().user.mobile,
          }).then((response) {
            YvrToast.showToast(YLocal.of(context).vfcode_send);
          }).catchError((error) {
            YvrToast.showError(YLocal.of(context).get_vfcode_failed);
          });
        });
  }

  void verifyUserRequest() {
    if (_vfCode.length < 4) {
      if (_vfCode.length == 0) {
        YvrToast.showToast(YLocal.of(context).qingshuruyanzhengma);
      } else {
        YvrToast.showToast(YLocal.of(context).yanzhengmacuowu);
      }
      return;
    }
    http.post<Map>('vrmcsys/account/validateSmscode', data: {
      'mobile': UserVModel().user.mobile,
      'code': _vfCode
    }).then((response) async {
      if (response.data["errCode"] == 0) {
        if (response.data["status"] == 1) {
          await StorageManager.localStorage.setItem("kLogOffVFCode", _vfCode);
          Navigator.pushNamed(context, "/apply_withdraw");
        } else {
          YvrToast.showToast(YLocal.of(context).yanzhengmacuowu);
        }
      }
    });
  }
}
