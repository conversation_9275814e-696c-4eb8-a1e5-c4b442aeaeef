import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/app_navigator.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/pages/profile/account/views/log_off_bottom.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/public_ui/widget/rich_text.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';

import '../../../../styles/app_color.dart';

class ConfirmWithdrawPage extends StatefulWidget {
  ConfirmWithdrawPage({Key key}) : super(key: key);

  @override
  State<ConfirmWithdrawPage> createState() => _ConfirmWithdrawPageState();
}

class _ConfirmWithdrawPageState extends State<ConfirmWithdrawPage> {
  ValueNotifier<bool> _isAgreeProtrol = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TopNavbar(
        title: YLocal.of(context).querenzhuxiaochongya,
      ),
      body: Container(
        height: Global.screenHeight,
        child: Stack(children: [
          SingleChildScrollView(
            padding: EdgeInsets.fromLTRB(16, 20, 16, 150),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  YLocal.of(context).querenzhuxiaojiangwa,
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 16,
                    height: 1.5,
                    letterSpacing: 1,
                    color: AppColors.textTitle,
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 24, bottom: 8),
                  padding: EdgeInsets.fromLTRB(8, 5, 8, 5),
                  decoration: BoxDecoration(
                      color: Color(0xffE04343),
                      borderRadius: BorderRadius.all(Radius.circular(4))),
                  child: Text(
                    YLocal.of(context).chongyaozhongyao,
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 16,
                      letterSpacing: 1,
                      color: AppColors.textTitle,
                    ),
                  ),
                ),
                SizedBox(
                  height: 29,
                ),
                IntlRichText1(
                  intlTextBuilder: YLocal.of(context).YVRzaicishanyidixing_1,
                  defaultStyle: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 18,
                    height: 1.5,
                    letterSpacing: 0.5,
                    color: AppColors.textTitle,
                  ),
                  param: YLocal.of(context).yonghuyinsizhengce,
                  paramStyle: TextStyle(
                    color: Color(0xFF4F7FFE),
                  ),
                  paramRecognizer: TapGestureRecognizer()
                    ..onTap = () {
                      String title = YLocal.of(context).privacy_policy;
                      navigateToAgreementPage(
                          context: context,
                          pageName:
                              YLocal.of(context).privacy_policy_html_page_name,
                          title: title);
                    },
                ),
                SizedBox(
                  height: 24,
                ),
                Text(
                  "${YLocal.of(context).runinrengxuanzejixuz}\n${YLocal.of(context).ninjiangfangqiweidao}\n${YLocal.of(context).ninjiangjiechugaizha}\n${YLocal.of(context).gaizhanghaodequanbug}\n${YLocal.of(context).gaizhanghaodehaoyouj}\n${YLocal.of(context).gaizhanghaoleijidede}",
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 16,
                    height: 1.5,
                    letterSpacing: 1,
                    color: AppColors.textTitle,
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            left: 0,
            bottom: 0,
            child: logOffBottomButton(
                leftBtnText: YLocal.of(context).queren,
                isAgreeProtrol: _isAgreeProtrol,
                nextStep: () {
                  showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (_) {
                        return CustomDialog(
                          title: YLocal.of(context).quedingzhuxiaozhangh,
                          content: YLocal.of(context).querenzhuxiaozhangha,
                          height: 220,
                          confirmText: YLocal.of(context).quedingzhuxiao,
                          confirmCallback: () {
                            reqCloseAccount();
                          },
                        );
                      });
                }),
          )
        ]),
      ),
    );
  }

  void reqCloseAccount() {
    http.post<Map>('vrmcsys/account/reqCloseAccount', data: {
      'code': StorageManager.localStorage.getItem("kLogOffVFCode")
    }).then((response) {
      if (response.data["errCode"] == 0) {
        YvrToast.showToast(YLocal.of(context).zhuxiaochenggongshou);
        eventBus.fire(EventFn({Global.kLogout: true}));
        eventBus.fire(EventFn({'selectedTabbarIndex': 3}));
      }
    }).catchError((error) {
      YvrToast.showToast(YLocal.of(context).zhanghaozhuxiaoshiba);
    });
  }
}
