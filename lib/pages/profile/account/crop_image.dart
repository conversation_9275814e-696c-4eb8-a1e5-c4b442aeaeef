import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:image_crop/image_crop.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/log.dart';

import '../../../generated/l10n.dart';

class CropImageRoute extends StatefulWidget {
  CropImageRoute(this.image);

  final File image; //原始图片路径

  @override
  _CropImageRouteState createState() => new _CropImageRouteState();
}

class _CropImageRouteState extends State<CropImageRoute> {
  double baseLeft; //图片左上角的x坐标
  double baseTop; //图片左上角的y坐标
  double imageWidth; //图片宽度，缩放后会变化
  double imageScale = 1; //图片缩放比例
  Image imageView;
  final cropKey = GlobalKey<CropState>();

  @override
  Widget build(BuildContext context) {
    var kScreenWidth = MediaQuery.of(context).size.width;
    var kScreenHeight = MediaQuery.of(context).size.height;
    return Scaffold(
        backgroundColor: Color(0xff17191B),
        body: Container(
          height: kScreenHeight,
          width: kScreenWidth,
          color: Color(0xff333333),
          child: Column(
            children: <Widget>[
              Container(
                height: kScreenHeight - 100,
                child: Crop.file(
                  widget.image,
                  key: cropKey,
                  aspectRatio: 1.0,
                  alwaysShowGrid: true,
                ),
              ),
              SizedBox(
                height: 20,
              ),
              // ignore: deprecated_member_use
              RaisedButton(
                onPressed: () {
                  _crop(widget.image);
                },
                child: Text(YLocal.of(context).queding),
              ),
            ],
          ),
        ));
  }

  Future<void> _crop(File originalFile) async {
    final crop = cropKey.currentState;
    final area = crop.area;
    if (area == null) {
      Log.d('裁剪不成功');
    }
    await ImageCrop.requestPermissions().then((isAllow) {
      if (isAllow) {
        ImageCrop.cropImage(
          file: originalFile,
          area: crop.area,
        ).then((value) {
          fetchUploadUserImage(value);
          // ignore: argument_type_not_assignable_to_error_handler
        });
      } else {
        fetchUploadUserImage(originalFile);
      }
    });
  }

  fetchUploadUserImage(File file) async {
    var image = await MultipartFile.fromFile(
      file.path,
      filename: file.path.split("/").last,
    );
    FormData formData = FormData.fromMap({"avatarFile": image});
    http.post('vrmcsys/account/uploadAvatar', data: formData).then((response) {
      if (mounted) {
        if (response.data["errCode"] == 0) {
          String imgUrl = response.data["url"];
          Log.d("上传成功！$imgUrl");
          Navigator.pop(context, imgUrl); //这里的url在上一页调用的result可以拿到
        } else if (response.data["errCode"] == 10540) {
          Navigator.pop(context, "10540"); //这里的url在上一页调用的result可以拿到
        } else {
          Navigator.pop(context);
          Log.d("上传失败！");
        }
      }
      YvrToast.dismiss();
    }).catchError((error) {
      Log.d("上传失败,错误：$error");
      Navigator.pop(context);
      YvrToast.dismiss();
    });
  }
}
