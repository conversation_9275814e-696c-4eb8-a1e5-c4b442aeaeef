import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';

class CardCodePage extends StatefulWidget {
  CardCodePage({Key key}) : super(key: key);

  @override
  _CardCodePageState createState() => _CardCodePageState();
}

class _CardCodePageState extends State<CardCodePage> {
  FocusNode _focusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Global.kBgColor,
        appBar: TopNavbar(
          title: YLocal.of(context).profile_exchange_code,
        ),
        body: Padding(
          padding: EdgeInsets.fromLTRB(10, 20, 10, 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                YLocal.of(context).profile_please_redemption,
                style: TextStyle(fontSize: 16, color: Color(0xffE8E8E8)),
              ),
              SizedBox(
                height: 8,
              ),
              TextField(
                autofocus: true,
                focusNode: _focusNode,
                decoration: InputDecoration(
                  hintText: YLocal.of(context).profile_please_redemption,
                  fillColor: Color(0xff242527),
                  filled: true,
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Color(0xff242527)),
                  ),
                  enabledBorder: new UnderlineInputBorder(
                      borderSide: new BorderSide(color: Color(0xff242527))),
                ),
              ),
              SizedBox(
                height: 20,
              ),
              Container(
                height: 49,
                width: double.infinity,
                margin: EdgeInsets.fromLTRB(0, 15.5, 0, 14.5),
                child: ElevatedButton(
                    style: ButtonStyle(
                      elevation: MaterialStateProperty.all(0),
                      backgroundColor:
                          MaterialStateProperty.all(Color(0xff393A3C)),
                      overlayColor:
                          MaterialStateProperty.all(Color(0xff4F7FFE)),
                      shape: MaterialStateProperty.all(RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8))),
                    ),
                    child: Text(
                      YLocal.of(context).submit,
                      style: TextStyle(color: Color(0xffE8E8E8), fontSize: 20),
                    ),
                    onPressed: () {
                      setState(() {
                        YvrToast.showToast(
                            YLocal.of(context).toast_redemption_fail);
                      });
                    }),
              )
            ],
          ),
        ));
  }
}
