import 'package:flutter/material.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';

import '../../../../generated/l10n.dart';
import '../../../../public_ui/widget/dialogs.dart';

class WalletTool {
  jumpToWalletPage(context, {int selIdx = 0}) async {
    await UserVModel().getUserInfoFromMobile();
    Navigator.pushNamed(context, '/wallet', arguments: {"idx": selIdx});
  }

  void showProgressAndJumpToWalletPage(context, {int selIdx = 0}) {
    Dialogs.showProgressFutureDialog<bool>(context, Future<bool>.sync(() async {
      try {
        await UserVModel().getUserInfoFromMobile();
        return true;
      } catch (e, s) {
        print(s);
        return false;
      }
    })).then((value) {
      if (value == null) {
        return;
      }
      if (value) {
        Navigator.pushNamed(context, '/wallet', arguments: {"idx": selIdx});
      } else {
        YvrToast.showToast(YLocal.of(context).jiazaishibaiqingchon);
      }
    });
  }
}
