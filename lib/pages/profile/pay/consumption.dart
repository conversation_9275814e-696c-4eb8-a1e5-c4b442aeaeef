import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:common_utils/common_utils.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/styles/app_divider.dart';
import 'package:yvr_assistant/model/pay_history_model.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';

class ConsumptionPage extends StatefulWidget {
  final arguments;

  ConsumptionPage({Key key, this.arguments}) : super(key: key);

  @override
  State<ConsumptionPage> createState() => _ConsumptionPageState();
}

class _ConsumptionPageState extends State<ConsumptionPage> {
  @override
  Widget build(BuildContext context) {
    PayHistoryModel model = widget.arguments["model"];
    String symbol = (model.currency == 1) ? "\$" : "￥";
    String priceText = "";
    String payText = "";
    switch (model.payType) {
      case 0:
        priceText = YLocal.of(context).mianfeihuodewenfeihu;
        payText = "-";
        break;
      case 1:
        priceText = "-$symbol${MoneyUtil.changeF2Y(model.sprice)}";
        payText = YLocal.of(context).weixinzhifu;
        break;
      case 2:
        priceText = "-$symbol${MoneyUtil.changeF2Y(model.sprice)}";
        payText = YLocal.of(context).zhifubaozhifu;
        break;
      case 3:
        if (model.couponType == 1) {
          priceText = "-${model.couponName} ${YLocal.of(context).Nzhang(1)}";
          payText = model.couponName;
        } else {
          priceText = "-${NumUtil.getNumByValueDouble(model.sprice / 10, 0)}";
          payText = YLocal.of(context).Ybizhifu;
        }
        break;
      case 4:
        priceText = "-$symbol${MoneyUtil.changeF2Y(model.sprice)}";
        payText = "PayPal";
        break;
      default:
    }

    return Scaffold(
        appBar: TopNavbar(
          title: YLocal.of(context).xiaofeixiangqing,
        ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              const SizedBox(height: 28),
              Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: model.scover,
                      width: 68,
                      height: 68,
                      fit: BoxFit.fill,
                      placeholder: (context, url) =>
                          const CircularProgressIndicator(), // 占位图
                      errorWidget: (context, url, error) =>
                          const Icon(Icons.error), // 错误图标
                      memCacheWidth: 136, // 控制缓存的宽度，降低内存占用 (2x 倍率)
                      memCacheHeight: 136,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.fromLTRB(0, 11, 0, 26),
                    child: Text(
                      model.appName,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 18,
                        height: 1,
                        color: Color(0xff242527),
                      ),
                    ),
                  ),
                  Text(
                    priceText,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 20,
                      height: 1.4,
                      color: Color(0xff17191b),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 50),
              AppDivider.backgroundDividerWithIndent(20),
              const SizedBox(height: 30),
              payInfoRow(
                  title: YLocal.of(context).consume_time,
                  msg: DateUtil.formatDateMs(model.buyTime,
                      format: DateFormats.y_mo_d_h_m)),
              payInfoRow(
                  title: YLocal.of(context).dingchanhaodingdanha_1,
                  msg: model.requestId),
              payInfoRow(title: YLocal.of(context).zhifufangshi_1, msg: payText)
            ],
          ),
        ));
  }

  Widget payInfoRow({String title = "", String msg = ""}) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch, // 确保子组件高度一致
        children: [
          const SizedBox(width: 30),
          Container(
            width: 94,
            alignment: Alignment.centerLeft,
            child: Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 16,
                height: 1,
                color: Color(0xff808080),
              ),
            ),
          ),
          Flexible(
            child: Container(
              height: 40,
              alignment: Alignment.centerLeft,
              child: Text(
                msg,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                  height: 1,
                  color: Color(0xff242527),
                ),
                maxLines: 2, // 可设置最大行数
                overflow: TextOverflow.ellipsis, // 超出部分显示省略号
              ),
            ),
          ),
          const SizedBox(width: 30),
        ],
      ),
    );
  }
}
