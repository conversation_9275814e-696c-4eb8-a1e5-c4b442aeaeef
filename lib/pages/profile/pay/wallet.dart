import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/model/user_model.dart';
import 'package:yvr_assistant/model/coupon_model.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/provider/provider_widget.dart';
import 'package:yvr_assistant/view_model/coupon_vmodel.dart';
import 'package:yvr_assistant/provider/view_state_widget.dart';
import 'package:yvr_assistant/pages/social/views/plhd_view.dart';
import 'package:yvr_assistant/pages/profile/views/coupon_cell.dart';

class CouponPage extends StatefulWidget {
  final arguments;
  CouponPage({Key key, this.arguments}) : super(key: key);

  @override
  State<CouponPage> createState() => _CouponPageState();
}

class _CouponPageState extends State<CouponPage>
    with SingleTickerProviderStateMixin {
  ValueNotifier<UserModel> _userModel = ValueNotifier<UserModel>(UserModel());

  @override
  void initState() {
    super.initState();
    _userModel.value = UserVModel().user;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: true,
        backgroundColor: Color(0xffF5F7FA),
        appBar: TopNavbar(title: YLocal.of(context).youhuquanyouhuiquany_1),
        body: ProviderWidget<CouponVModel>(
            model: CouponVModel(),
            onModelReady: (model) => model.initData(),
            builder: (context, model, child) {
              if (model.busy) {
                return ViewStateBusyWidget();
              } else if (model.error) {
                return ViewStateErrorWidget(
                    error: model.viewStateError, onPressed: model.initData);
              }

              List<CouponModel> modelList = model.models;
              modelList.sort((a, b) => a.endTime.compareTo((b.endTime)));

              return modelList.length == 0
                  ? Center(
                      child: PagePlhdWidget(
                      message: YLocal.of(context).zanmokaquanzanwukaqu,
                      imgWidth: 56.0,
                      vSpaceHeight: 0,
                      imagePath: "assets/svg/no_bill.svg",
                    ))
                  : ListView.builder(
                      itemCount: modelList.length,
                      itemBuilder: (context, index) {
                        return CouponCell(
                          detlModel: modelList[index],
                        );
                      });
            }));
  }
}
