import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/model/pay_history_model.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/pages/social/views/plhd_view.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/provider/provider_widget.dart';
import 'package:yvr_assistant/provider/view_state_widget.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/public_ui/widget/image_view.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/data_record.dart';
import 'package:yvr_assistant/view_model/pay_history_vmodel.dart';

import '../../../styles/app_color.dart';

class PayHistoryPage extends StatefulWidget {
  PayHistoryPage({Key key}) : super(key: key);

  @override
  _PayHistoryPageState createState() => _PayHistoryPageState();
}

class _PayHistoryPageState extends State<PayHistoryPage> {
  bool _isRefund = false;

  @override
  void dispose() {
    if (_isRefund) {
      eventBus.fire(EventFn({Global.kRefreshHome: true}));
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: TopNavbar(
          title: YLocal.of(context).profile_buy_history,
        ),
        body: ProviderWidget<PayHistoryVModel>(
            model: PayHistoryVModel(),
            onModelReady: (model) => model.initData(),
            builder: (context, model, child) {
              if (model.busy) {
                return ViewStateBusyWidget();
              } else if (model.error) {
                return ViewStateErrorWidget(
                    error: model.viewStateError, onPressed: model.initData);
              }

              List<PayHistoryModel> models = model.historyModels;
              // 提前处理支付方式字段

              List<String> payTypeTexts = models.map((model) {
                String symbol = (model.currency == 1) ? "\$" : "￥";
                String text = "$symbol${MoneyUtil.changeF2Y(model.sprice)}";
                if (model.payType == 0) {
                  text = YLocal.of(context).mianfeihuodewenfeihu;
                } else if (model.payType == 3) {
                  if (model.couponType == 1) {
                    text = "${model.couponName} ${YLocal.of(context).duihuan}";
                  } else {
                    text = YLocal.of(context).NYbi(
                        NumUtil.getNumByValueDouble(model.sprice / 10, 0));
                  }
                }
                return text;
              }).toList();

              return models.length == 0
                  ? Center(
                      child: PagePlhdWidget(
                      message: YLocal.of(context).zanweigoumaiyingyong,
                      imgWidth: 56.0,
                      imagePath: "assets/images/person_home_plhd.svg",
                    ))
                  : ListView.builder(
                      itemCount: models.length,
                      itemBuilder: (context, index) {
                        PayHistoryModel payModel = models[index];

                        return GestureDetector(
                            onTap: () {
                              DataRecord().saveData(
                                  eventId:
                                      "assistant_me_purchaseHistory_appList_app_pit_click",
                                  extraData: {"appId": payModel.appId});
                              Navigator.pushNamed(context, '/consumption',
                                  arguments: {"model": payModel});
                            },
                            child: Container(
                                margin: EdgeInsets.only(top: 10),
                                padding: EdgeInsets.all(16),
                                color: Color(0xffF5F7FA),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      width: 80,
                                      height: 80,
                                      margin: EdgeInsets.only(right: 10),
                                      child: ClipRRect(
                                        borderRadius: new BorderRadius.all(
                                          new Radius.circular(4),
                                        ),
                                        child: Opacity(
                                          opacity:
                                              payModel.status == 0 ? 0.5 : 1,
                                          child: NetworkImageWidget(
                                            payModel.scover,
                                            width: double.infinity,
                                            height: double.infinity,
                                          ),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                        flex: 1,
                                        child: Container(
                                          child: Flex(
                                            direction: Axis.vertical,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                payModel.appName ?? '',
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                                style: payModel.status == 0
                                                    ? TextStyle(
                                                        fontWeight:
                                                            FontWeight.w600,
                                                        fontSize: 16,
                                                        color:
                                                            AppColors.textWeak,
                                                      )
                                                    : TextStyle(
                                                        fontWeight:
                                                            FontWeight.w600,
                                                        fontSize: 16,
                                                        color:
                                                            AppColors.textTitle,
                                                      ),
                                              ),
                                              SizedBox(
                                                height: 23,
                                              ),
                                              Row(
                                                children: [
                                                  Expanded(
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Container(
                                                          child: Text(
                                                            payTypeTexts[
                                                                    index] ??
                                                                "",
                                                            maxLines: 1,
                                                            overflow:
                                                                TextOverflow
                                                                    .ellipsis,
                                                            style:
                                                                payModel.status ==
                                                                        0
                                                                    ? TextStyle(
                                                                        fontWeight:
                                                                            FontWeight.w400,
                                                                        fontSize:
                                                                            16,
                                                                        height:
                                                                            1,
                                                                        color: AppColors
                                                                            .textWeak,
                                                                      )
                                                                    : TextStyle(
                                                                        fontWeight:
                                                                            FontWeight.w500,
                                                                        fontSize:
                                                                            16,
                                                                        height:
                                                                            1,
                                                                        color: AppColors
                                                                            .textSub,
                                                                      ),
                                                          ),
                                                        ),
                                                        SizedBox(
                                                          height: 8,
                                                        ),
                                                        Text(
                                                          YLocal.of(context)
                                                              .Dgoumai(
                                                            DateUtil.formatDateMs(
                                                                payModel
                                                                    .buyTime,
                                                                format:
                                                                    DateFormats
                                                                        .mo_d),
                                                          ),
                                                          style: TextStyle(
                                                            fontWeight:
                                                                FontWeight.w400,
                                                            fontSize: 12,
                                                            height: 1,
                                                            color: Color(
                                                                0xff767880),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  (payModel.canRefund == 1 &&
                                                          payModel.status == 1)
                                                      ? YvrTextButton(
                                                          color: Color(
                                                                  0xff4F7FFE)
                                                              .withOpacity(0.1),
                                                          radius: 4,
                                                          padding: EdgeInsets
                                                              .symmetric(
                                                                  horizontal:
                                                                      12,
                                                                  vertical: 7),
                                                          onPressed: () {
                                                            showDialog(
                                                                context: Global
                                                                    .context,
                                                                barrierDismissible:
                                                                    false,
                                                                builder: (_) {
                                                                  return CustomDialog(
                                                                      title: YLocal.of(
                                                                              context)
                                                                          .tuikuan,
                                                                      content: YLocal.of(
                                                                              context)
                                                                          .quedingyaotuikuanmat,
                                                                      cancelText:
                                                                          YLocal.of(context)
                                                                              .quxiao,
                                                                      confirmText:
                                                                          YLocal.of(context)
                                                                              .queding,
                                                                      height:
                                                                          200,
                                                                      confirmColor:
                                                                          Color(
                                                                              0xFF4F7FFE),
                                                                      confirmCallback:
                                                                          () {
                                                                        refundMoney(
                                                                            payModel
                                                                                .requestId,
                                                                            payModel.appId,
                                                                            () {
                                                                          model
                                                                              .initData();
                                                                        });
                                                                      });
                                                                });
                                                          },
                                                          child: Text(
                                                            YLocal.of(context)
                                                                .profile_request_refund,
                                                            style: TextStyle(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              fontSize: 12,
                                                              height: 1,
                                                              color: AppColors
                                                                  .standard,
                                                            ),
                                                          ),
                                                        )
                                                      : textWidget(
                                                          status:
                                                              payModel.status)
                                                ],
                                              ),
                                            ],
                                          ),
                                        ))
                                  ],
                                )));
                      });
            }));
  }

  Widget textWidget({int status}) {
    Widget button = SizedBox();
    switch (status) {
      case 0:
        button = Text(
          YLocal.of(context).profile_refunded,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 12,
            height: 1,
            color: AppColors.textWeak,
          ),
        );
        break;
      case 2:
        button = Text(
          YLocal.of(context).profile_refunding,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 12,
            height: 1,
            color: AppColors.standard,
          ),
        );
        break;
      default:
    }
    return button;
  }

  refundMoney(String requestId, int appId, Function relodCallBack) {
    // 微信支付接口
    YvrToast.showLoading();
    http
        .get<Map>('vrmcsys/appstore/apply/refund?requestId=' +
            requestId +
            '&appId=' +
            appId.toString())
        .then((response) {
      YvrToast.dismiss();
      if (response.data["errCode"] == 0) {
        _isRefund = true;
        showDialog(
            context: context,
            barrierDismissible: false,
            builder: (_) {
              return CustomDialog(
                title: YLocal.of(context).toast_refund_success,
                content: YLocal.of(context).toast_refund_desc,
                confirmText: YLocal.of(context).understood,
                isCancel: false,
                confirmCallback: () {
                  relodCallBack();
                },
              );
            });
      } else {
        YvrToast.dismiss();
        YvrToast.showToast(YLocal.of(context).toast_refund_fail);
      }
    });
  }
}
