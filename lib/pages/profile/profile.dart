import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:yvr_assistant/main.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/model/user_model.dart';
import 'package:yvr_assistant/network/socket.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/utils/data_record.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/utils/yvr_utils.dart';
import 'package:yvr_assistant/view_model/friends_vmodel.dart';
import 'package:yvr_assistant/view_model/unread_vmodel.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/pages/profile/views/profile_view.dart';

class ProfilePage extends StatefulWidget {
  ProfilePage({Key key}) : super(key: key);

  @override
  _ProfilePageState createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage>
    with RouteAware, WidgetsBindingObserver {
  ScrollController _scrollController = ScrollController();

  int _isOddNumber = 0; // 自动登录时,防止重复获取用户数据，忽略奇数次刷新
  UserModel _userModel = (StorageManager.localStorage.getItem(kUser) == null ||
          DBUtil.instance.userBox.get(kToken) != null)
      ? null
      : UserModel.fromJson(StorageManager.localStorage.getItem(kUser));

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver?.subscribe(this, ModalRoute.of(context));
  }

  @override
  void dispose() {
    super.dispose();
    eventBusFn?.cancel();
    routeObserver?.unsubscribe(this);
  }

  @override
  void didPush() {
    super.didPush();
    Log.d('进入我的页面');
    // 监测网络访问失败在此处提示！
    if (DBUtil.instance.userBox.get(kToken) != null) {
      if (StorageManager.localStorage.getItem(kUser) != null) {
        setState(() {
          if (_userModel == null) {
            _userModel =
                UserModel.fromJson(StorageManager.localStorage.getItem(kUser));
          }
        });
      } else {
        getUserInfoFromMobile();
      }
    }
  }

  @override
  void didPopNext() {
    super.didPopNext();
    if (DBUtil.instance.userBox.get(kToken) != null) {
      getUserInfoFromMobile();
    }
  }

  var eventBusFn;

  @override
  void initState() {
    super.initState();

    eventBusFn = eventBus.on<EventFn>().listen((event) {
      bool isLogout = event.obj[Global.kLogout] ?? false;
      if (isLogout) {
        logoutFunc();
      }
    });
    if (mounted) {
      DataRecord().saveData(
        eventId: "assistant_me_me_0_0_page_view",
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppColors.secondaryBg,
        body: Stack(
          children: [
            ProfileViewWidget(
              controller: _scrollController,
              userModel: _userModel,
              logout: () {
                logoutFunc();
              },
            ),
          ],
        ));
  }

  void reset() async {
    await DBUtil.instance.userBox.delete(kRefreshToken);
    await DBUtil.instance.userBox.delete(kToken);
    await DBUtil.instance.userBox.delete(kActId);
    await StorageManager.localStorage.deleteItem(kUser);
    setState(() {
      YvrUtils.resetUserInfo();

      _userModel = null;
      SocketManager().closeSocket(); // 主动断开Socket连接
      Provider.of<UnreadVModel>(context, listen: false).resetUnreadNum();
      Provider.of<FriendVModel>(context, listen: false).logout();
    });
  }

  logoutFunc() {
    http.post<Map>('vrmcsys/account/logoutSd', data: {
      "refresh_token": DBUtil.instance.userBox.get(kRefreshToken) ?? null
    });
    reset();
  }

  getUserInfoFromMobile() {
    http.post<Map>('vrmcsys/account/getUserInfoFromMobile', data: {}).then(
        (response) async {
      if (response.data["errCode"] == 0) {
        if (mounted) {
          setState(() {
            // 改变页面全局变量则重新布局页面:
            // 若两个全局变量，则拿到第一个后就去更新ProvideWidget，不满足需求
            _userModel = UserModel.fromJson(response.data);
          });
        }
        await StorageManager.localStorage.setItem(kUser, response.data);
      } else {
        if (DBUtil.instance.userBox.get(kToken) != null &&
            _isOddNumber % 2 != 0) {
          _isOddNumber++;
          Future.delayed(Duration(milliseconds: 100), () {
            getUserInfoFromMobile();
          });
        }
      }
    });
  }
}
