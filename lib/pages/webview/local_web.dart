import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:common_utils/common_utils.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';

class LocalWebPage extends StatefulWidget {
  final arguments;
  LocalWebPage({Key key, @required this.arguments}) : super(key: key);
  @override
  _LocalWebPageState createState() => _LocalWebPageState();
}

class _LocalWebPageState extends State<LocalWebPage> {
  WebViewController _webViewController;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Color(0xff17191B),
        appBar: TopNavbar(
          title: widget.arguments["title"] ?? "YVR",
        ),
        body: widget.arguments["path"] == null
            ? Center(
                child: Text(
                  YLocal.of(context).weich<PERSON><PERSON>wangyelian,
                  style: TextStyle(fontSize: 16, color: Color(0xffCCCCCC)),
                ),
              )
            : WebView(
                initialUrl: '',
                javascriptMode: JavascriptMode.unrestricted,
                backgroundColor: Colors.white,
                onWebViewCreated: (WebViewController webViewController) {
                  _webViewController = webViewController;
                  _loadHtmlFromAssets();
                },
                onPageFinished: (url) {
                  String path = widget.arguments["path"];
                  if (path.contains("clock.html")) {
                    String format = "yyyy年MM月dd日";
                    String endDate = DateUtil.formatDateStr(
                        widget.arguments["endDate"],
                        format: format);
                    String expireDate = DateUtil.formatDateStr(
                        widget.arguments["expireDate"],
                        format: format);
                    String callJSFun =
                        'getNetworkData("$endDate", "$expireDate")';
                    _webViewController.runJavascript(callJSFun);
                  }
                  YvrToast.dismiss();
                },
              ));
  }

  _loadHtmlFromAssets() async {
    YvrToast.showLoading();
    Future.delayed(Duration(milliseconds: 500), () {
      YvrToast.dismiss();
    });

    String fileHtmlContents =
        await rootBundle.loadString(widget.arguments["path"]);
    _webViewController.loadUrl(Uri.dataFromString(fileHtmlContents,
            mimeType: 'text/html', encoding: Encoding.getByName('utf-8'))
        .toString());
  }
}

// 文档转html
// http://www.docpe.com/word/word-to-html.aspx