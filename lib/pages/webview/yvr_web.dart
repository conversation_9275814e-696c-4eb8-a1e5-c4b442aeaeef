import 'dart:io';

import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';

import '../../generated/l10n.dart';

/// 必须传入url
class YVRWebPage extends StatefulWidget {
  final arguments;

  YVRWebPage({Key key, @required this.arguments}) : super(key: key);

  @override
  _YVRWebPageState createState() => _YVRWebPageState();
}

class _YVRWebPageState extends State<YVRWebPage> {
  String _webUrl;

  @override
  void initState() {
    super.initState();
    configWebUrl();
    YvrToast.showLoading();
    Future.delayed(Duration(seconds: 2), () {
      YvrToast.dismiss();
    });
  }

  void configWebUrl() {
    _webUrl = widget.arguments["url"];

    if (Platform.isAndroid) WebView.platform = SurfaceAndroidWebView();
    int isNeedLogin = widget.arguments["isNeedLogin"] ?? 0;
    if (DBUtil.instance.userBox.get(kToken) != null && isNeedLogin == 0) {
      Uri uri = Uri.parse(widget.arguments["url"]);
      Map<String, dynamic> param = Map.of(uri.queryParameters);
      param["token"] = DBUtil.instance.userBox.get(kToken) ?? "";
      var httpsUri = Uri(
        scheme: uri.scheme,
        host: uri.host,
        path: uri.path,
        queryParameters: param,
      );
      _webUrl = httpsUri.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TopNavbar(
        title: widget.arguments["title"] ?? "YVR",
      ),
      body: widget.arguments["url"] == null
          ? Center(
              child: Text(
                YLocal.of(context).weichuanruwangyelian,
                style: TextStyle(fontSize: 16, color: Color(0xffCCCCCC)),
              ),
            )
          : WebView(
              javascriptMode: JavascriptMode.unrestricted,
              initialUrl: _webUrl ?? "",
              backgroundColor: Colors.white,
              initialMediaPlaybackPolicy: AutoMediaPlaybackPolicy.always_allow,
              javascriptChannels: {
                ///YVR世界游戏前流程引导
                JavascriptChannel(
                  name: 'YvrFlutterMessage',
                  onMessageReceived: (JavascriptMessage message) {
                    final String url = message.message;
                    if (url != null && url.isNotEmpty) {
                      _launchWebView(url).then((value) {
                        if (!value) {
                          YvrToast.showToast(
                              YLocal.of(context).dakailianjieshibai);
                        }
                      });
                    }
                  },
                ),
              },
              onPageFinished: (url) {
                YvrToast.dismiss();
              },
            ),
    );
  }

  Future<bool> _launchWebView(String urlString) async {
    Uri url = Uri.tryParse(urlString);
    if (url == null) {
      return false;
    }
    if (await (canLaunchUrl(url))) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
      return true;
    }
    return false;
  }
}
