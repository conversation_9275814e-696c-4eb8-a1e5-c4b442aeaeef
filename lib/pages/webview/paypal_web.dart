import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/manager/environment.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';

/// 必须传入url
class PayPalWebPage extends StatefulWidget {
  final arguments;

  PayPalWebPage({Key key, @required this.arguments}) : super(key: key);

  @override
  _PayPalWebPageState createState() => _PayPalWebPageState();
}

class _PayPalWebPageState extends State<PayPalWebPage> {
  /// 支付结果 0=支付取消，1=支付成功，-1=支付失败
  int _payStatus = 0;

  /// 购买方式 1-Y币支付 2-附加内容支付 3-应用支付
  int _tag = 1;
  String _webUrl;
  WebViewController _controller;

  @override
  void initState() {
    super.initState();
    configWebUrl();
    YvrToast.showLoading();
    Future.delayed(Duration(seconds: 2), () {
      YvrToast.dismiss();
    });
    eventBus.fire(EventFn({'CloseVideo': true}));
  }

  void configWebUrl() {
    _tag = widget.arguments["type"] ?? '3';
    _webUrl = widget.arguments["url"] ?? '1';
    if (Platform.isAndroid) WebView.platform = SurfaceAndroidWebView();
  }

  bool goBack() {
    if (isShowNavBackRemind()) {
      return true;
    }
    // 支付成功
    eventBus.fire(EventFn({'HomeRefresh': true}));
    eventBus.fire(EventFn({'BundledRefresh': true}));
    eventBus.fire(EventFn({'ProdRefresh': true}));
    Navigator.of(context).pop(_payStatus);
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          return goBack();
        },
        child: Scaffold(
          appBar: AppBar(
            title: Text(
              widget.arguments["title"] ?? "YVR",
            ),
            leading: CustomBackButton(
              onPressed: () {
                return goBack();
              },
            ),
          ),
          body: widget.arguments["url"] == null
              ? Center(
                  child: Text(
                    YLocal.of(context).weichuanruwangyelian,
                    style: TextStyle(fontSize: 16, color: Color(0xffCCCCCC)),
                  ),
                )
              : WebView(
                  javascriptMode: JavascriptMode.unrestricted,
                  initialUrl: _webUrl ?? "",
                  backgroundColor: Colors.white,
                  initialMediaPlaybackPolicy:
                      AutoMediaPlaybackPolicy.always_allow,
                  onWebViewCreated: (WebViewController webViewController) {
                    _controller = webViewController;
                  },
                  navigationDelegate: (request) {
                    /// 方案1：PayPal 支付完成，回调后端设置的 webview 带参数结果页，目前平台不支持该方式
                    /* 
                    try {
                      Uri uri = Uri.parse(request.url);
                      Map<String, dynamic> param = Map.of(uri.queryParameters);
                      _payStatus = int.parse(param["status"]) ?? 0;
                      Log.d('PayPal请求监听 $param _payStatus:$_payStatus');
                    } catch (e) {
                      Log.d('解析错误：$e');
                    }
                    */

                    /// 方案2：监听特定 URL 请求，询问后端服务器是否支付成功
                    /// 支付监听到支付前一次URL请求时
                    ///  https://apitest.yvrdream.com/vrmcsys/appstore/pay/success?price=1&token=26V3376436737750D&PayerID=ZRGFS4CU6RN8W
                    /// 延迟一段时间发起请求获取支付结果
                    /// 根据结果加载 webview 带参数结果页：https://apitest.yvrdream.com/paypal?status=1&price=150

                    Uri uri = Uri.parse(request.url);
                    if (uri.path == '/vrmcsys/appstore/pay/success') {
                      YvrToast.showLoading();
                      Future.delayed(Duration(seconds: 1), () {
                        requestPaypalResult(Map.of(uri.queryParameters));
                      });
                      return NavigationDecision.navigate;
                    }
                    return NavigationDecision.navigate;
                  },
                  onPageFinished: (url) {
                    YvrToast.dismiss();
                  },
                ),
        ));
  }

  void requestPaypalResult(Map<String, String> param) {
    const int maxRetries = 30; // 最大重试次数
    const Duration retryInterval = Duration(seconds: 1); // 每次请求间隔时间
    int attempt = 0; // 当前重试次数

    Timer.periodic(retryInterval, (timer) {
      attempt++;
      Log.d("尝试第 $attempt 次请求 PayPal 结果...");

      // 发起请求
      http
          .get<Map>(
              'vrmcsys/appstore/payPalCheck?payNo=${param['token']}&tag=$_tag&price=${param['price']}')
          .then((response) {
        if (response.data["errCode"] == 0) {
          final data = response.data;
          _payStatus = data["status"] ?? 0;
          Log.d('PayPal加载结果页: _payStatus:$_payStatus  price:${data["price"]}');

          if (_payStatus == 1) {
            loadingPayResultURL(status: _payStatus, price: data["price"] ?? 0);
          } else if (attempt >= maxRetries) {
            loadingPayResultURL(status: _payStatus, price: data["price"] ?? 0);
          }
        }
      }).catchError((error) {
        // 请求失败，打印日志
        Log.d('PayPal加载结果页: $error');
        if (attempt >= maxRetries) {
          // 达到最大重试次数，显示提示信息并停止定时器
          timer.cancel();
          YvrToast.dismiss();
          YvrToast.showToast(YLocal.of(context).qingqiushibai);
        }
      });

      // 如果重试次数达到最大值，停止定时器
      if (attempt >= maxRetries) {
        timer.cancel();
      }
    });
  }

  void loadingPayResultURL({int status, int price}) {
    String url =
        '${Environment().config.apiHost}paypal?status=$status&price=$price';
    _controller.loadUrl(url);
  }

  bool isShowNavBackRemind() {
    if (_payStatus == 0) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (_) {
            return CustomDialog(
              title: YLocal.current.dishitishi,
              content: "Do you want to continue to complete the payment",
              height: 180,
              confirmCallback: () {
                Navigator.of(context).pop(_payStatus);
                return true;
              },
            );
          });
      return true;
    } else {
      return false;
    }
  }
}
