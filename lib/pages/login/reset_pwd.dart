import 'package:flutter/material.dart';
import 'package:keyboard_avoider/keyboard_avoider.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/pages/login/views/code_button.dart';
import 'package:yvr_assistant/pages/login/views/input_field.dart';
import 'package:yvr_assistant/pages/login/views/login_btn.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/styles/app_divider.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/utils/string_utils.dart';
import 'package:yvr_assistant/utils/textInput_formatter.dart';
import 'package:yvr_assistant/utils/yvr_utils.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';

import '../../styles/app_color.dart';

class ResetPwdPage extends StatefulWidget {
  final arguments;

  ResetPwdPage({Key key, this.arguments}) : super(key: key);

  @override
  _ResetPwdPageState createState() => _ResetPwdPageState();
}

class _ResetPwdPageState extends State<ResetPwdPage> {
  var _phoneNum = new TextEditingController(
      text: DBUtil.instance.userBox.get(kMobile) ?? "");
  var _verifyCode = new TextEditingController();
  var _password = new TextEditingController();
  var _rePassword = new TextEditingController();
  final GlobalKey<AuthCodeButtonState> authCodeKey = GlobalKey();
  String forCodeBtnPhoneNum = DBUtil.instance.userBox.get(kMobile) ?? "";
  String forLoginBtnVFCode = "";
  String forLoginBtnPwd = "";
  String forLoginBtnNewPwd = "";

  bool _pwdErrorIsShow = false;
  bool _rePwdErrorIsShow = false;

  @override
  Widget build(BuildContext context) {
    bool isForget = widget.arguments["isForget"];

    String _safeNum = (_phoneNum.text.length > 0) && !isForget
        ? _phoneNum.text.replaceRange(3, 7, "****")
        : "";

    return Scaffold(
        appBar: TopNavbar(
          title: isForget
              ? YLocal.of(context).chongzhimimazhongzhi
              : YLocal.of(context).profile_change_pwd,
        ),
        body: KeyboardAvoider(
            autoScroll: true,
            child: Container(
              margin: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  isForget
                      ? ImputFieldWidget(
                          controller: _phoneNum,
                          maxLength: 11,
                          updateText: (value) {
                            if (value.length >= 10) {
                              setState(() {
                                forCodeBtnPhoneNum = value;
                              });
                            }
                          },
                        )
                      : Text(
                          YLocal.of(context).profile_verify_phone_num +
                              " " +
                              _safeNum,
                          style: TextStyle(
                            color: Color(0xff2c2e33),
                            fontWeight: FontWeight.w600,
                            fontSize: 18,
                          ),
                        ),
                  SizedBox(
                    height: 10,
                  ),
                  Container(
                      height: 58,
                      child: Flex(
                        direction: Axis.horizontal,
                        children: [
                          Expanded(
                            child: ImputFieldWidget(
                              controller: _verifyCode,
                              maxLength: 6,
                              hintText: YLocal.of(context).qingshuruyanzhengma,
                              updateText: (value) {
                                if (value.length >= 3) {
                                  setState(() {
                                    forLoginBtnVFCode = value;
                                  });
                                }
                              },
                            ),
                          ),
                          SizedBox(
                            width: 8,
                          ),
                          SizedBox(
                            height: 49,
                            child: codeButtonWidget(),
                          ),
                        ],
                      )),
                  AppDivider.backgroundDivider,
                  SizedBox(
                    height: 60,
                  ),
                  Text(
                    YLocal.of(context).profile_set_password,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 18,
                      letterSpacing: 0,
                      color: AppColors.textTitle,
                    ),
                  ),
                  SizedBox(
                    height: 22,
                  ),
                  ImputFieldWidget(
                    controller: _password,
                    hintText: YLocal.of(context).profile_new_password,
                    keyboardType: TextInputType.text,
                    inputFormatters: [PwdFormatter()],
                    maxLength: 12,
                    hasLock: true,
                    updateText: (value) {
                      if (value.length >= 5) {
                        setState(() {
                          forLoginBtnPwd = value;
                        });
                      }
                    },
                  ),
                  AppDivider.backgroundDivider,
                  Container(
                    padding: EdgeInsets.only(left: 0, top: 9),
                    alignment: Alignment(-1, -1),
                    child: (_pwdErrorIsShow)
                        ? Container(
                            margin: EdgeInsets.only(top: 5),
                            child: Text(
                              YLocal.of(context).qingshuru612weidaxia,
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 12,
                                height: 1,
                                color: AppColors.warning,
                              ),
                            ),
                          )
                        : SizedBox(),
                  ),
                  SizedBox(
                    height: 8,
                  ),
                  ImputFieldWidget(
                    controller: _rePassword,
                    hintText: YLocal.of(context).profile_confirm_password,
                    keyboardType: TextInputType.text,
                    inputFormatters: [PwdFormatter()],
                    maxLength: 12,
                    hasLock: true,
                    updateText: (value) {
                      if (value.length >= 5) {
                        setState(() {
                          forLoginBtnNewPwd = value;
                        });
                      }
                    },
                  ),
                  AppDivider.backgroundDivider,
                  Container(
                    padding: EdgeInsets.only(left: 0, top: 9),
                    alignment: Alignment(-1, -1),
                    height: 25,
                    child: (_rePwdErrorIsShow)
                        ? Text(
                            YLocal.of(context).mimabuyizhiqingheshi,
                            style: TextStyle(
                                color: Color(0xffE04343), fontSize: 12),
                          )
                        : SizedBox(
                            height: 25,
                          ),
                  ),
                  SizedBox(
                    height: 44,
                  ),
                  loginButtonWidget()
                ],
              ),
            )));
  }

  Widget codeButtonWidget() {
    return AuthCodeButton(
        key: authCodeKey,
        timeCount: 60,
        isActive: YvrUtils.isPhoneNum(forCodeBtnPhoneNum),
        onPressed: () {
          if (!YvrUtils.isPhoneNum(_phoneNum.text)) {
            YvrToast.showToast(YLocal.of(context).qingshuruyouxiaoshou);
            return;
          }
          authCodeKey.currentState.startAction();
          YvrToast.showLoading();
          http.post<Map>('vrmcsys/account/reqSmscode', data: {
            'mobile': _phoneNum.text,
          }).then((response) {
            YvrToast.showToast(YLocal.of(context).vfcode_send);
          }).catchError((error) {
            YvrToast.showError(YLocal.of(context).get_vfcode_failed);
          });
        });
  }

  Widget loginButtonWidget() {
    return LoginButtonWidget(
      buttonText: YLocal.of(context).confirm,
      isCustomBtn: true,
      isActive: YvrUtils.isPhoneNum(forCodeBtnPhoneNum) &&
          (forLoginBtnVFCode.length >= 4) &&
          (forLoginBtnPwd.length >= 6),
      onPressed: () {
        if (!YvrUtils.isPhoneNum(_phoneNum.text)) {
          YvrToast.showToast(YLocal.of(context).qingshuruyouxiaoshou);
          return;
        }
        if (_verifyCode.text.length == 0) {
          YvrToast.showToast(YLocal.of(context).input_verify_code);
          return;
        }
        if (_password.text.length < 6) {
          setState(() {
            _pwdErrorIsShow = true;
          });
          return;
        } else {
          setState(() {
            _pwdErrorIsShow = false;
          });
        }
        if (_password.text != _rePassword.text) {
          setState(() {
            _rePwdErrorIsShow = true;
          });
          return;
        } else {
          setState(() {
            _rePwdErrorIsShow = false;
          });
        }

        String pwd = StringUtils.toMD5(
                StringUtils.toMD5(_password.text).toUpperCase() + "WCMX_YVR")
            .toUpperCase();
        http.post<Map>('vrmcsys/account/modifyPwd', data: {
          'mobile': _phoneNum.text,
          'code': _verifyCode.text,
          'pwd': pwd
        }).then((response) async {
          if (response.data["errCode"] == 0) {
            YvrToast.showToast(YLocal.current.denglumimaxiugaichen);
            Navigator.pop(context);
          }
        }).catchError((error) {
          YvrToast.showError(YLocal.current.denglumimaxiugaishib);
        });
      },
    );
  }
}
