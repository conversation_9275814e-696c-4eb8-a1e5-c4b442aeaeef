import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/pages/login/login.dart';
import 'package:yvr_assistant/pages/login/views/input_field.dart';
import 'package:yvr_assistant/pages/login/views/login_btn.dart';
import 'package:yvr_assistant/pages/login/views/login_header.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/utils/string_utils.dart';
import 'package:yvr_assistant/utils/textInput_formatter.dart';
import 'package:yvr_assistant/utils/yvr_utils.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';

import '../../manager/event_bus.dart';
import '../../manager/global.dart';
import '../../styles/app_divider.dart';
import '../../utils/platform_utils.dart';

class PwdLoginPage extends StatefulWidget {
  PwdLoginPage({Key key}) : super(key: key);

  @override
  _PwdLoginPageState createState() => _PwdLoginPageState();
}

class _PwdLoginPageState extends State<PwdLoginPage> {
  ScrollController _scrollController = ScrollController();
  StreamSubscription<bool> keyboardSubscription;
  bool isJump = true;

  TextEditingController _phoneNum =
      TextEditingController(text: DBUtil.instance.userBox.get(kMobile) ?? "");
  TextEditingController _password = new TextEditingController();

  @override
  void initState() {
    super.initState();

    var keyboardVisibilityController = KeyboardVisibilityController();
    keyboardSubscription =
        keyboardVisibilityController.onChange.listen((bool visible) {
      if (visible) {
        _scrollController.animateTo(100,
            curve: Curves.easeOut, duration: const Duration(milliseconds: 200));
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    keyboardSubscription?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          Navigator.pop(context, "Refresh Previous Page");
          return false;
        },
        child: Scaffold(
            floatingActionButtonLocation: FloatingActionButtonLocation.startTop,
            floatingActionButton: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(0),
              ),
              child: FloatingActionButton(
                  elevation: 0,
                  highlightElevation: 0,
                  backgroundColor: Colors.transparent,
                  onPressed: () {
                    Navigator.pop(context, "Refresh");
                  },
                  child: Icon(
                    Icons.arrow_back_ios,
                    color: Color(0xff6E7380),
                  )),
            ),
            body: CustomScrollView(controller: _scrollController, slivers: [
              SliverToBoxAdapter(
                child: Stack(
                  children: [
                    LoginHeaderWidget(),
                    Container(
                        margin: EdgeInsets.only(top: 250),
                        child: Card(
                            elevation: 0,
                            color: Color(0xffFFFFFF),
                            clipBehavior: Clip.antiAlias,
                            margin: EdgeInsets.only(top: 0),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(32.0),
                                topRight: Radius.circular(32.0),
                              ),
                            ),
                            child: Container(
                                padding: EdgeInsets.only(
                                    left: 16, right: 16, top: 37),
                                child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      ImputFieldWidget(
                                        controller: _phoneNum,
                                        keyboardType:
                                            TextInputType.emailAddress,
                                        isLocation: Platform.isAndroid,
                                        updateText: (value) {
                                          setState(() {
                                            _phoneNum.text = value;
                                          });
                                        },
                                      ),
                                      AppDivider.backgroundDivider,
                                      SizedBox(height: 25),
                                      ImputFieldWidget(
                                        controller: _password,
                                        hintText: YLocal.of(context).input_pwd,
                                        keyboardType: TextInputType.text,
                                        inputFormatters: [PwdFormatter()],
                                        hasLock: true,
                                        maxLength: 12,
                                        updateText: (value) {
                                          setState(() {
                                            _password.text = value;
                                          });
                                        },
                                      ),
                                      AppDivider.backgroundDivider,
                                      SizedBox(height: 40),
                                      InkWell(
                                        onTap: () {
                                          YvrToast.showToast(
                                              'Please visit https://pfdm.cn/en to retrieve your password');
                                        },
                                        child: Text(
                                          YLocal.of(context).forget_pwd,
                                          style: AppStyle
                                              .style_textSub_w400_14pt(),
                                        ),
                                      ),
                                      SizedBox(
                                        height: 60,
                                      ),
                                      loginButtonWidget(),
                                      SizedBox(
                                        height: 28,
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            YLocal.of(context).no_account,
                                            style: AppStyle
                                                .style_textWeak_w400_14pt(),
                                          ),
                                          InkWell(
                                            onTap: () {
                                              if (isJump) {
                                                isJump = false;
                                                Navigator.of(context)
                                                    .pushNamed('/register');
                                                Future.delayed(
                                                    Duration(seconds: 2), () {
                                                  isJump = true;
                                                });
                                              }
                                            },
                                            child: Text(
                                              YLocal.of(context)
                                                  .register_account,
                                              style: AppStyle
                                                  .style_textSub_w400_14pt(),
                                            ),
                                          )
                                        ],
                                      ),
                                    ]))))
                  ],
                ),
              ),
            ])));
  }

  Widget loginButtonWidget() {
    return LoginButtonWidget(
      buttonText: YLocal.of(context).sign_in,
      hasArrow: true,
      isActive: _phoneNum.text.length > 0 && _password.text.length > 0,
      onPressed: () {
        if (!YvrUtils.isPhoneAndEmain(_phoneNum.text)) {
          YvrToast.showToast('Illegal account');
          return;
        }
        YvrToast.showLoading();
        String pwd = StringUtils.toMD5(
                StringUtils.toMD5(_password.text).toUpperCase() + "WCMX_YVR")
            .toUpperCase();
        //area 1国内版本 2国外版本
        Map<String, dynamic> map = {
          'pwd': pwd,
          'area': 2,
        };
        String url = 'vrmcsys/account/loginSd';
        if (_phoneNum.text.contains('@')) {
          url = 'vrmcsys/account/loginSdByMail';
          map['email'] = _phoneNum.text;
        } else {
          map['mobile'] = _phoneNum.text;
        }
        http.post<Map>(url, data: map).then((response) async {
          if (response.data["errCode"] == 0) {
            await configUserInfo(
                data: response.data,
                phoneNum: _phoneNum.text,
                vrPwd: _password.text);
            eventBus.fire(EventFn({'HomeRefresh': true}));
            eventBus.fire(EventFn({'BundledRefresh': true}));
            eventBus.fire(EventFn({Global.deviceListRefreshKey: true}));
            Navigator.pop(context);
            YvrToast.showToast(YLocal.of(context).login_successful);
          } else if (response.data["errCode"] == 10010) {
            YvrToast.dismiss();
            String dateText = response.data["exDate"] ?? YLocal.current.jinqi;
            showWithdrawDialog(
              dateText: dateText,
              mobile: _phoneNum.text,
              pwd: pwd,
            );
          }
        }).catchError((e, s) {
          if (e is DioError && (e.type == DioErrorType.connectTimeout)) {
            YvrToast.showToast(YLocal.of(context).login_network_error);
            return;
          }
          YvrToast.showExceptionMessage(e);
        });
      },
    );
  }
}
