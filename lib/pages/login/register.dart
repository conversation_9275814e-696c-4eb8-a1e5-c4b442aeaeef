import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/app_navigator.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/pages/login/views/code_button.dart';
import 'package:yvr_assistant/pages/login/views/input_field.dart';
import 'package:yvr_assistant/pages/login/views/login_btn.dart';
import 'package:yvr_assistant/pages/login/views/login_header.dart';
import 'package:yvr_assistant/public_ui/widget/rich_text.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/string_utils.dart';
import 'package:yvr_assistant/utils/textInput_formatter.dart';
import 'package:yvr_assistant/utils/yvr_utils.dart';

import '../../styles/app_divider.dart';
import '../../utils/platform_utils.dart';

class RegisterPage extends StatefulWidget {
  RegisterPage({Key key}) : super(key: key);

  @override
  _RegisterPageState createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  var _email = new TextEditingController();
  var _password = new TextEditingController();
  var _rePassword = new TextEditingController();
  final GlobalKey<AuthCodeButtonState> authCodeKey = GlobalKey();
  String email = "";
  String password = "";
  String password2 = "";
  bool isShowErrorTips = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        floatingActionButtonLocation: FloatingActionButtonLocation.startTop,
        floatingActionButton: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(0),
          ),
          child: FloatingActionButton(
              elevation: 0,
              highlightElevation: 0,
              backgroundColor: Colors.transparent,
              onPressed: () {
                Navigator.pop(context, "Refresh");
              },
              child: Icon(
                Icons.arrow_back_ios,
                color: Color(0xff6E7380),
              )),
        ),
        body: CustomScrollView(slivers: [
          SliverToBoxAdapter(
            child: Stack(
              children: [
                LoginHeaderWidget(),
                Container(
                    margin: const EdgeInsets.only(top: 250),
                    child: Card(
                        elevation: 0,
                        clipBehavior: Clip.antiAlias,
                        margin: EdgeInsets.only(top: 0),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(32.0),
                            topRight: Radius.circular(32.0),
                          ),
                        ),
                        child: Container(
                            padding: const EdgeInsets.fromLTRB(16, 40, 16, 0),
                            child: Column(children: [
                              ImputFieldWidget(
                                controller: _email,
                                keyboardType: TextInputType.emailAddress,
                                isLocation: Platform.isAndroid,
                                updateText: (value) {
                                  if (value.length >= 0) {
                                    setState(() {
                                      email = value;
                                    });
                                  }
                                },
                              ),
                              AppDivider.backgroundDivider,
                              const SizedBox(height: 24),
                              ImputFieldWidget(
                                controller: _password,
                                keyboardType: TextInputType.text,
                                hintText: YLocal.of(context).pwd_rule,
                                hasLock: true,
                                maxLength: 12,
                                inputFormatters: [PwdFormatter()],
                                updateText: (value) {
                                  if (value.length >= 0) {
                                    setState(() {
                                      password = value;
                                    });
                                  }
                                },
                              ),
                              AppDivider.backgroundDivider,
                              const SizedBox(height: 24),
                              ImputFieldWidget(
                                controller: _rePassword,
                                hintText: YLocal.of(context).pwd_input_again,
                                hasLock: true,
                                maxLength: 12,
                                keyboardType: TextInputType.text,
                                inputFormatters: [PwdFormatter()],
                                updateText: (value) {
                                  if (value.length >= 0) {
                                    setState(() {
                                      password2 = value;
                                    });
                                  }
                                },
                              ),
                              AppDivider.backgroundDivider,
                              Visibility(
                                  visible: false,
                                  child: Padding(
                                      padding: const EdgeInsets.only(top: 10),
                                      child: Text(
                                        YLocal.of(context).mimabuyizhiqingheshi,
                                        style: TextStyle(
                                            color: Color(0xffE04343),
                                            fontSize: 12),
                                      ))),
                              const SizedBox(height: 60),
                              loginButtonWidget(),
                              const SizedBox(height: 16),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    YLocal.of(context).account_yet,
                                    style: AppStyle.style_textWeak_w400_14pt(),
                                  ),
                                  InkWell(
                                    onTap: () {
                                      setState(() {
                                        // Navigator.of(context).pushReplacementNamed('/login');
                                        Navigator.of(context).pop();
                                      });
                                    },
                                    child: Text(
                                      YLocal.of(context).direct_login,
                                      style: AppStyle.style_textSub_w400_14pt(),
                                    ),
                                  )
                                ],
                              ),
                              // Expanded(child: SizedBox()),
                              const SizedBox(height: 90),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Expanded(
                                    child: IntlRichText2(
                                      // intlTextBuilder: YLocal.of(context).woyiyuedubingtongyiY,
                                      intlTextBuilder:
                                          YLocal.of(context).yvr_protocol2,
                                      defaultStyle:
                                          AppStyle.style_textWeak_w400_10pt(),
                                      param0:
                                          '《${YLocal.of(context).service_agreement}》',
                                      paramStyle0:
                                          AppStyle.style_standard_10pt(),
                                      paramRecognizer0: TapGestureRecognizer()
                                        ..onTap = () {
                                          String title = YLocal.of(context)
                                              .service_agreement;
                                          navigateToAgreementPage(
                                              context: context,
                                              pageName: YLocal.of(context)
                                                  .service_agreement_html_page_name,
                                              title: title);
                                        },
                                      param1:
                                          '《${YLocal.of(context).privacy_policy}》',
                                      paramStyle1:
                                          AppStyle.style_standard_10pt(),
                                      paramRecognizer1: TapGestureRecognizer()
                                        ..onTap = () {
                                          String title =
                                              YLocal.of(context).privacy_policy;
                                          navigateToAgreementPage(
                                              context: context,
                                              pageName: YLocal.of(context)
                                                  .privacy_policy_html_page_name,
                                              title: title);
                                        },
                                    ),
                                  )
                                ],
                              )
                            ]))))
              ],
            ),
          ),
        ]));
  }

  Widget loginButtonWidget() {
    return LoginButtonWidget(
      buttonText: YLocal.of(context).sign_up,
      isActive: email.length > 0 && password.length > 0 && password2.length > 0,
      hasArrow: true,
      onPressed: () {
        if (!YvrUtils.isEmail(email)) {
          YvrToast.showToast('Incorrect email address AA');
          return;
        }
        if (password != password2) {
          YvrToast.showToast('Password is inconsistent');
          return;
        }
        if (!YvrUtils.checkPwd(password)) {
          YvrToast.showToast(
              'The password must be 6 to 12 characters in length and can contain uppercase and lowercase letters');
          return;
        }
        YvrToast.showLoading();
        String pwd = StringUtils.toMD5(
                StringUtils.toMD5(password).toUpperCase() + "WCMX_YVR")
            .toUpperCase();
        http.post<Map>('vrmcsys/account/registerMail', data: {
          'email': email,
          'pwd': pwd,
          'link': 1,
          'area': 2,
        }).then((response) {
          YvrToast.dismiss();
          if (response.data["errCode"] == 0) {
            Navigator.of(context)
                .pushReplacementNamed('/email_validation', arguments: {
              'email': email,
              'pwd': pwd,
              'link': 1,
              'area': 2,
            });
          }
        }).onError((error, stackTrace) {
          YvrToast.dismiss();
        });
      },
    );
  }
}
