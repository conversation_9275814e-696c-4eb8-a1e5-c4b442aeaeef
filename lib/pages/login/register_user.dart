import 'dart:io';

import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_picker/flutter_picker.dart';
import 'package:flutter_svg/svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/pages/login/views/login_btn.dart';
import 'package:yvr_assistant/pages/profile/account/crop_image.dart';
import 'package:yvr_assistant/pages/profile/account/views/avatar.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/public_ui/widget/picker_tool.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/styles/app_divider.dart';
import 'package:yvr_assistant/utils/deep_copy.dart';
import 'package:yvr_assistant/utils/log.dart';

import '../../styles/app_color.dart';
import '../../utils/yvr_utils.dart';

class RegisterUserPage extends StatefulWidget {
  RegisterUserPage({Key key}) : super(key: key);

  @override
  _RegisterUserPageState createState() => _RegisterUserPageState();
}

class _RegisterUserPageState extends State<RegisterUserPage> {
  String _avatarUrl;
  String _userName;
  String _sex;
  String _birthday;
  String _param0 = "";
  String _param1 = "";
  String _param2 = "";

  DateTime _selDate = DateTime(1990, 6, 15);
  final picker = ImagePicker();
  ValueNotifier<bool> _isHasAvatar = ValueNotifier<bool>(false);
  ValueNotifier<int> _isAllInput = ValueNotifier<int>(0);

  @override
  void initState() {
    super.initState();
  }

  Future pickUserImage({String pickType = 'photo'}) async {
    await ImagePicker()
        // ignore: deprecated_member_use
        .getImage(
            source:
                pickType == "carema" ? ImageSource.camera : ImageSource.gallery)
        .then((image) => cropImage(File(image.path)));
  }

  @override
  Widget build(BuildContext context) {
    List _gender = [
      YLocal.of(context).nan,
      YLocal.of(context).nv,
      YLocal.of(context).baomi
    ];

    return WillPopScope(
      onWillPop: () async {
        Navigator.pop(context, "Refresh Previous Page");
        return false;
      },
      child: Scaffold(
          appBar: AppBar(
            automaticallyImplyLeading: false,
            title: Text(YLocal.of(context).user_info),
          ),
          body: ValueListenableBuilder<int>(
              valueListenable: _isAllInput,
              builder: (context, value, child) {
                Color nameColor;
                String userName;
                if (_userName != null && ObjectUtil.isNotEmpty(_userName)) {
                  userName = _userName;
                  nameColor = Color(0xff2C2E33);
                } else {
                  userName = YLocal.of(context).profile_input_nick;
                  nameColor = Color(0xffAFB6CC);
                }

                Color sexColor;
                String userSex;
                if (_sex != null) {
                  userSex = YvrUtils.getSexText(int.tryParse(_sex));
                  sexColor = Color(0xff2C2E33);
                } else {
                  userSex = YLocal.of(context).weizhi;
                  sexColor = Color(0xffAFB6CC);
                }

                Color birthColor;
                String userBirth;
                if (_birthday != null && ObjectUtil.isNotEmpty(_birthday)) {
                  userBirth = _birthday;
                  birthColor = Color(0xff2C2E33);
                } else {
                  userBirth = YLocal.of(context).weizhi;
                  birthColor = Color(0xffAFB6CC);
                }
                return ListView(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  children: [
                    ValueListenableBuilder<bool>(
                        valueListenable: _isHasAvatar,
                        builder: (context, value, child) {
                          return AvatarWidget(
                            isHasAvatar: value,
                            avatarUrl: _avatarUrl,
                            isGirl: _sex == YLocal.of(context).nv,
                            onPressed: (value) {
                              pickUserImage(pickType: value);
                            },
                          );
                        }),
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      title: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            YLocal.of(context).nickname,
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                              height: 1,
                              letterSpacing: 0,
                              color: AppColors.textSub,
                            ),
                          ),
                          Text(
                            userName,
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: 16,
                              height: 1,
                              color: nameColor,
                            ),
                          )
                        ],
                      ),
                      trailing: SvgPicture.asset(
                        "assets/svg/arrow_right.svg",
                        color: AppColors.textSub,
                        width: 10,
                        height: 10,
                      ),
                      onTap: () {
                        showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (_) {
                              return CustomDialog(
                                title: YLocal.of(context).modify_nickname,
                                isCancel: true,
                                dialogType: DialogType.DialogInput,
                                keyboardType: TextInputType.text,
                                hintText: _userName,
                                maxLength: 12,
                                inputCommitCallback: (text) {
                                  if (text == null ||
                                      text == "" ||
                                      text.contains(" ")) {
                                    YvrToast.showToast(YLocal.of(context)
                                        .nichenbukeweikongzif);
                                  } else {
                                    _userName = text;
                                    _param0 = text;
                                    if (text.length > 0) {
                                      _isAllInput.value++;
                                    }
                                  }
                                },
                              );
                            });
                      },
                    ),
                    AppDivider.backgroundDivider,
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      title: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            YLocal.of(context).gender,
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                              height: 1,
                              letterSpacing: 0,
                              color: AppColors.textSub,
                            ),
                          ),
                          Text(
                            userSex,
                            style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 16,
                                height: 1,
                                color: sexColor),
                          )
                        ],
                      ),
                      trailing: Icon(Icons.chevron_right),
                      onTap: () {
                        JhPickerTool.showStringPicker(context,
                            title: '', data: _gender,
                            clickCallBack: (int index, var item) {
                          _sex = (index + 1).toString();
                          _param1 = (index + 1).toString();
                          _isAllInput.value++;
                        });
                      },
                    ),
                    AppDivider.backgroundDivider,
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      title: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            YLocal.of(context).birthday,
                            style: TextStyle(fontSize: 16),
                          ),
                          Text(
                            userBirth,
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: 16,
                              height: 1,
                              color: birthColor,
                            ),
                          )
                        ],
                      ),
                      trailing: Icon(Icons.chevron_right),
                      onTap: () {
                        // Locale myLocale = Localizations.localeOf(context);
                        // bool isZh = myLocale.toString() == "zh";

                        JhPickerTool.showDatePicker(context,
                            adapter: DateTimePickerAdapter(
                              minValue: DateTime(1900, 1, 1),
                              maxValue: DateTime.now(),
                              value: _selDate,
                            ), clickCallBack: (var str, var time) {
                          // setState(() {
                          String tempDate =
                              time.toString().replaceRange(4, 5, "-");
                          tempDate = time.toString().replaceRange(7, 8, "-");
                          Log.d(tempDate);

                          DeepCopy value = DeepCopy(strValue: tempDate);
                          _selDate = DateTime.parse(tempDate);

                          _birthday = value.strValue.substring(0, 11);
                          _param2 = value.strValue.substring(0, 11);
                          _isAllInput.value++;
                        });
                      },
                    ),
                    AppDivider.backgroundDivider,
                    saveButton()
                  ],
                );
              })),
    );
  }

  Widget saveButton() {
    return ValueListenableBuilder<int>(
        valueListenable: _isAllInput,
        builder: (context, value, child) {
          return Column(
            children: [
              SizedBox(
                height: 50,
              ),
              Container(
                margin: EdgeInsets.only(left: 20, right: 20),
                child: LoginButtonWidget(
                  buttonText: YLocal.of(context).submit,
                  onPressed: () {
                    reqUpdateUserInfo(
                        nick: _userName, birth: _birthday, sex: _sex);
                  },
                  isActive: (_param0.length > 0) &&
                      (_param1.length > 0) &&
                      (_param2.length > 0),
                  isCustomBtn: true,
                ),
              ),
              // ignore: deprecated_member_use
              FlatButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(YLocal.of(context).do_it_later,
                    style: TextStyle(color: Color(0xff6E7380), fontSize: 14)),
              )
            ],
          );
        });
  }

  void cropImage(File originalImage) async {
    if (originalImage == null) {
      // 图片未选择
      Log.d("图片未选择");
      return;
    }
    Log.d("图片已选择");

    String imageUrl = await Navigator.push(context,
        MaterialPageRoute(builder: (context) => CropImageRoute(originalImage)));
    if (imageUrl == "10540") {
      YvrToast.showToast(YLocal.of(context).ninshangchuandetouxi_1);
    } else if (imageUrl == null) {
      YvrToast.showToast(YLocal.of(context).toast_avatar_fail);
    } else {
      YvrToast.showToast(YLocal.of(context).toast_avatar_success);
      _isHasAvatar.value = true;
      _avatarUrl = imageUrl;
    }
  }

  reqUpdateUserInfo({String nick = "", String birth = "", String sex = ""}) {
    Map<String, dynamic> params = {};

    if (nick.length == 0) {
      YvrToast.showToast(YLocal.of(context).toast_userinfo);
      return;
    } else if (sex == YLocal.of(context).unknown) {
      YvrToast.showToast(YLocal.of(context).toast_gender);
      return;
    } else if (birth == YLocal.of(context).unknown) {
      YvrToast.showToast(YLocal.of(context).toast_birthday);
      return;
    }

    params["nick"] = nick;
    params["birth"] = birth;
    params["sex"] = sex;

    http
        .post<Map>('vrmcsys/account/setUserInfo', data: params)
        .then((response) {
      if (response.data["errCode"] == 0) {
        setState(() {
          Navigator.pop(context);
          YvrToast.showToast(YLocal.of(context).toast_userinfo);
        });
      } else if (response.data["errCode"] == 10540) {
        YvrToast.showToast(YLocal.of(context).weiguinichenqingchon);
      }
    }).catchError((error) {
      YvrToast.showToast(YLocal.of(context).toast_msg_update_fail);
    });
  }
}
