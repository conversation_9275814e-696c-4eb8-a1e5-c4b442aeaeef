import 'dart:async';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:yvr_assistant/styles/app_divider.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/network/socket.dart';
import 'package:yvr_assistant/utils/yvr_utils.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/pages/home/<USER>/message.dart';
import 'package:yvr_assistant/pages/login/views/code_button.dart';
import 'package:yvr_assistant/pages/login/views/input_field.dart';
import 'package:yvr_assistant/pages/login/views/login_btn.dart';
import 'package:yvr_assistant/pages/login/views/login_header.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import '../../manager/events_bus2.dart';
import '../../styles/app_color.dart';

class LoginPage extends StatefulWidget {
  LoginPage({Key key}) : super(key: key);

  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  ScrollController _scrollController = ScrollController();
  StreamSubscription<bool> keyboardSubscription;

  @override
  void initState() {
    super.initState();
    StorageManager.foreverData.setItem("isLoginPage", true);
    var keyboardVisibilityController = KeyboardVisibilityController();
    keyboardSubscription =
        keyboardVisibilityController.onChange.listen((bool visible) {
      if (visible) {
        _scrollController.animateTo(120,
            curve: Curves.easeOut, duration: const Duration(milliseconds: 200));
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    StorageManager.foreverData.setItem("isLoginPage", false);
    keyboardSubscription?.cancel();
  }

  var _phoneNum = new TextEditingController(
      text: DBUtil.instance.userBox.get(kMobile) ?? "");
  var _verifyCode = new TextEditingController();
  final GlobalKey<AuthCodeButtonState> authCodeKey = GlobalKey();
  String forLoginBtnVFCode = "";
  String forCodeBtnPhoneNum = DBUtil.instance.userBox.get(kMobile) ?? "";

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          Navigator.pop(context, "Refresh Previous Page");
          return false;
        },
        child: Scaffold(
            floatingActionButtonLocation: FloatingActionButtonLocation.startTop,
            floatingActionButton: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(0),
              ),
              child: FloatingActionButton(
                  elevation: 0,
                  highlightElevation: 0,
                  backgroundColor: Colors.transparent,
                  onPressed: () {
                    Navigator.pop(context, "Refresh");
                  },
                  child: Icon(
                    Icons.arrow_back_ios,
                    color: Color(0xff6E7380),
                  )),
            ),
            body: CustomScrollView(controller: _scrollController, slivers: [
              SliverToBoxAdapter(
                child: Stack(
                  children: [
                    LoginHeaderWidget(),
                    Container(
                        margin: EdgeInsets.only(top: 250),
                        child: Card(
                            elevation: 0,
                            color: Color(0xffFFFFFF),
                            clipBehavior: Clip.antiAlias,
                            margin: EdgeInsets.only(top: 0),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(32.0),
                                topRight: Radius.circular(32.0),
                              ),
                            ),
                            child: Container(
                              padding: EdgeInsets.fromLTRB(16, 40, 16, 0),
                              child: Column(
                                children: [
                                  ImputFieldWidget(
                                    controller: _phoneNum,
                                    maxLength: 11,
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                          RegExp(r'[0-9]')),
                                      FilteringTextInputFormatter.digitsOnly
                                    ],
                                    updateText: (value) {
                                      if (value.length >= 10) {
                                        setState(() {
                                          forCodeBtnPhoneNum = value;
                                        });
                                      }
                                    },
                                  ),
                                  AppDivider.backgroundDivider,
                                  Container(
                                      margin: EdgeInsets.only(
                                        top: 12.5,
                                      ),
                                      height: 58,
                                      child: Flex(
                                        direction: Axis.horizontal,
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Expanded(
                                            child: ImputFieldWidget(
                                                controller: _verifyCode,
                                                maxLength: 6,
                                                updateText: (String value) {
                                                  if (value.length >= 3) {
                                                    setState(() {
                                                      forLoginBtnVFCode = value;
                                                    });
                                                  }
                                                },
                                                hintText: YLocal
                                                    .current.input_verify_code),
                                            flex: 3,
                                          ),
                                          SizedBox(
                                            width: 8,
                                          ),
                                          Expanded(
                                            flex: 2,
                                            child: SizedBox(
                                              height: 49,
                                              child: codeButtonWidget(),
                                            ),
                                          ),
                                        ],
                                      )),
                                  AppDivider.backgroundDivider,
                                  SizedBox(
                                    height: 25,
                                  ),
                                  Row(
                                    children: [
                                      CupertinoButton(
                                          padding:
                                              EdgeInsets.fromLTRB(10, 0, 10, 0),
                                          child: Text(
                                            YLocal.current.verify_code_login,
                                            style: TextStyle(
                                              fontWeight: FontWeight.w400,
                                              fontSize: 14,
                                              height: 1,
                                              color: AppColors.textTitle,
                                            ),
                                          ),
                                          onPressed: () {}),
                                      SizedBox(
                                        width: 1,
                                        height: 16,
                                        child: DecoratedBox(
                                          decoration: BoxDecoration(
                                              color: Color(0xffAFB6CC)),
                                        ),
                                      ),
                                      CupertinoButton(
                                          padding:
                                              EdgeInsets.fromLTRB(10, 0, 0, 0),
                                          child: Text(
                                            YLocal.current.pwd_login,
                                            style: TextStyle(
                                              fontWeight: FontWeight.w400,
                                              fontSize: 14,
                                              height: 1,
                                              color: AppColors.textWeak,
                                            ),
                                          ),
                                          onPressed: () {
                                            Navigator.of(context)
                                                .pushReplacementNamed(
                                              '/pwd_login',
                                            );
                                          }),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 30,
                                  ),
                                  loginButtonWidget(),
                                  SizedBox(
                                    height: 28,
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        YLocal.current.no_account,
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontSize: 14,
                                          height: 1,
                                          color: AppColors.textWeak,
                                        ),
                                      ),
                                      InkWell(
                                        onTap: () {
                                          setState(() {
                                            Navigator.of(context)
                                                .pushReplacementNamed(
                                              '/register',
                                            );
                                          });
                                        },
                                        child: Text(
                                          YLocal.current.register_account,
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 14,
                                            height: 1,
                                            letterSpacing: 1,
                                            color: AppColors.textSub,
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ],
                              ),
                            )))
                  ],
                ),
              ),
            ])));
  }

  Widget codeButtonWidget() {
    return AuthCodeButton(
        key: authCodeKey,
        timeCount: 60,
        isActive: YvrUtils.isPhoneNum(forCodeBtnPhoneNum),
        onPressed: () {
          authCodeKey.currentState.startAction();
          YvrToast.showLoading();
          http.post<Map>('vrmcsys/account/reqSmscode', data: {
            'mobile': _phoneNum.text,
          }).then((response) {
            YvrToast.showToast(YLocal.current.vfcode_send);
          }).catchError((error) {
            YvrToast.showError(YLocal.current.get_vfcode_failed);
          });
        });
  }

  Widget loginButtonWidget() {
    return LoginButtonWidget(
      isActive: YvrUtils.isPhoneNum(forCodeBtnPhoneNum) &&
          forLoginBtnVFCode.length >= 4,
      buttonText: YLocal.current.sign_in,
      hasArrow: true,
      onPressed: () {
        YvrToast.showLoading();
        http.post<Map>('vrmcsys/account/loginSd', data: {
          'mobile': _phoneNum.text,
          'code': _verifyCode.text,
          'area': 1
        }).then((response) async {
          if (response.data["errCode"] == 0) {
            await configUserInfo(data: response.data, phoneNum: _phoneNum.text);
            eventBus.fire(EventFn({'HomeRefresh': true}));
            eventBus.fire(EventFn({'BundledRefresh': true}));
            eventBus.fire(EventFn({Global.deviceListRefreshKey: true}));
            Navigator.pop(context);
            YvrToast.showToast(YLocal.current.login_successful);
          } else if (response.data["errCode"] == 10010) {
            YvrToast.dismiss();
            String dateText = response.data["exDate"] ?? YLocal.current.jinqi;
            showWithdrawDialog(
                dateText: dateText,
                mobile: _phoneNum.text,
                code: _verifyCode.text);
          }
        }).catchError((e, s) {
          if (e is DioError && (e.type == DioErrorType.connectTimeout)) {
            YvrToast.showToast(YLocal.current.login_network_error);
            return;
          }
          YvrToast.showExceptionMessage(e);
        });
      },
    );
  }
}

Future configUserInfo({
  String vrPwd,
  @required Map<dynamic, dynamic> data,
  @required String phoneNum,
}) async {
  // 清除上个用户登录信息 防止个人页面加载老数据
  await StorageManager.localStorage.deleteItem(kUser);
  await DBUtil.instance.userBox.put(kToken, data["token"]);
  await DBUtil.instance.userBox.put(kActId, data["actId"]);
  await DBUtil.instance.userBox.put(kRefreshToken, data["refresh_token"]);
  await DBUtil.instance.userBox.put(kMobile, phoneNum);

  SocketManager().initWebSocket(
      onOpen: () {},
      onMessage: (data) {},
      onError: (e) {
        Log.e("📢 initWebSocket fail!");
      });
  AppEvent.fireLoginSuccessEvent(data["actId"]);

  List<String> devIds;
  devIds = await YvrRequests.getLoginDevIds();
  StorageManager.localStorage
      .setItem(Global.kUserHaveDevice, devIds.length > 0);
  HandelPayMessage.handelMsg();

  Provider.of<UserVModel>(Global.context, listen: false)
      .getUserInfoFromMobile();
  // 0普通用户 1开发者
  bool isDeveloper = (data["auth"] ?? 0) == 1;
  StorageManager.localStorage.setItem(Global.kIsDeveloper, isDeveloper);
  // 0普通用户 1显示待发布人员
  int isTestUser = await YvrRequests.quireAppPrePublishAuth();
  StorageManager.localStorage.setItem(Global.kIsTestUser, isTestUser == 1);
}

void showWithdrawDialog(
    {@required String dateText,
    @required String mobile,
    String pwd = "",
    String code = ""}) {
  Map param = {'mobile': mobile};
  if (pwd.length > 0) {
    param.addEntries({'pwd': pwd}.entries);
  } else {
    param.addEntries({'code': code}.entries);
  }
  showDialog(
      context: Global.context,
      barrierDismissible: false,
      builder: (_) {
        return CustomDialog(
          title: YLocal.current.zhanghaodenglu,
          content: YLocal.current.gaizhanghaoyishenqin(dateText),
          height: 200,
          confirmText: YLocal.current.denglu,
          confirmCallback: () {
            http
                .post<Map>('vrmcsys/account/loginSdEnhance', data: param)
                .then((response) {
              if (response.data["errCode"] == 0) {
                configUserInfo(data: response.data, phoneNum: mobile)
                    .then((value) {
                  Navigator.of(Global.context)
                      .popUntil((route) => route.isFirst);
                  YvrToast.showToast(YLocal.current.login_successful);
                }).catchError((e, s) {
                  if (e is DioError &&
                      (e.type == DioErrorType.connectTimeout)) {
                    YvrToast.showToast(YLocal.current.login_network_error);
                    return;
                  }
                  YvrToast.showExceptionMessage(e);
                });
              } else {
                YvrToast.showToast(YLocal.current.denglushibai);
              }
            });
          },
        );
      });
}
