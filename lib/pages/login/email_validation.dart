// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/public_ui/widget/common_button.dart';
import '../../provider/provider_widget.dart';
import '../../view_model/email_validation_vmodel.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import '../tabbar/top_navbar.dart';

///邮箱验证页
class EmailValidationPage extends StatefulWidget {
  dynamic arguments;
  EmailValidationPage(this.arguments);

  @override
  State<StatefulWidget> createState() {
    return _EmailValidationState();
  }
}

class _EmailValidationState extends State<EmailValidationPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.colorFFFFFF,
      appBar: TopNavbar(
        title: '',
      ),
      body: ProviderWidget<EmailValidationVModel>(
        model: EmailValidationVModel(widget.arguments),
        builder: (context, model, child) {
          return Container(
            width: double.infinity,
            height: double.infinity,
            child: Column(children: [
              SizedBox(height: 63),
              Image.asset('assets/images/ic_email_validation.png',
                  width: 96, height: 96),
              SizedBox(height: 44),
              Text('We have sent an email to',
                  style: AppStyle.style_textSub_w400_18pt()),
              SizedBox(height: 12),
              Text(model.email ?? '',
                  style: AppStyle.style_textTitle_w600_20pt()),
              SizedBox(height: 12),
              Text('for verification.',
                  style: AppStyle.style_textSub_w400_18pt()),
              SizedBox(height: 32),
              Text('Please check your email in order to',
                  style: AppStyle.style_textSub_w400_16pt()),
              SizedBox(height: 4),
              Text('finish creating your YVR account.',
                  style: AppStyle.style_textSub_w400_16pt()),
              SizedBox(height: 68),
              Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20),
                  child: CommonButton(
                      text: 'Back to log on',
                      onTap: () {
                        Navigator.pop(context);
                      })),
              SizedBox(height: 12),
              GestureDetector(
                onTap: () {
                  model.registerEmail();
                },
                child: Text.rich(TextSpan(
                    text: 'Did not receive?',
                    style: AppStyle.style_textWeak_w400_14pt(),
                    children: [
                      TextSpan(
                        text: 'Resend',
                        style: AppStyle.style_standard_w400_14pt(),
                      )
                    ])),
              )
            ]),
          );
        },
      ),
    );
  }
}
