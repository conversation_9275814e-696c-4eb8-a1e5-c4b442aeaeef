import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';

class LoginHeaderWidget extends StatelessWidget {
  const LoginHeaderWidget({Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 280,
      padding: EdgeInsets.only(left: 16, top: 50),
      decoration: BoxDecoration(
        image: new DecorationImage(
          fit: BoxFit.cover,
          image: AssetImage('assets/images/login_bg.png'),
        ),
      ),
      child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            const SizedBox(height: 66),
            Image.asset(
              "assets/images/login_header.png",
              fit: BoxFit.fitWidth,
              width: Global.screenWidth * 0.6,
            ),
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(top: 15),
              child: Text(
                YLocal.of(context).welcome,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 13.5,
                  height: 1,
                  letterSpacing: 1.5,
                  color: Color(0xffe8e8e8),
                ),
              ),
            ),
          ]),
    );
  }
}
