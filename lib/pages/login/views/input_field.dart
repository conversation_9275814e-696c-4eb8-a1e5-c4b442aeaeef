import 'dart:math';

import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/resource_mananger.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/utils/textInput_formatter.dart';

import '../../../styles/app_color.dart';

// ignore: must_be_immutable
class ImputFieldWidget extends StatefulWidget {
  TextEditingController controller;
  FocusNode focusNode;
  final keyboardType;
  final hintText;
  final maxLength;
  final text;
  final hasLock;
  final updateText;
  final inputFormatters;
  bool isLocation;

  ImputFieldWidget(
      {Key key,
      @required this.controller,
      this.focusNode,
      this.keyboardType = TextInputType.number,
      this.maxLength,
      this.hintText = "",
      this.text = "",
      this.hasLock = false,
      this.updateText,
      this.inputFormatters,
      this.isLocation = true})
      : super(key: key);

  @override
  ImputFieldWidgetState createState() => ImputFieldWidgetState();
}

class ImputFieldWidgetState extends State<ImputFieldWidget> {
  bool isPwdHide = true;
  String oldValue;
  TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController.fromValue(TextEditingValue(
        text: widget.controller.text,
        // 保持光标在最后
        selection: TextSelection.fromPosition(
            TextPosition(affinity: TextAffinity.downstream, offset: 0))));
    if (widget.controller != null) {
      oldValue = widget.controller.text;
      Log.d('oldValue =======获取初始值=======>$oldValue');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        child: TextField(
      inputFormatters: widget.inputFormatters ?? [NumberFormatter()],
      keyboardType: widget.keyboardType,
      controller: _controller,
      maxLength: widget.maxLength,
      focusNode: widget.focusNode,
      obscureText: isPwdHide && widget.hasLock,
      onChanged: (value) {
        /*if(widget.isLocation){
          int index = getCursorIndex(value);
          oldValue = value;
          widget.controller.text = value;
          widget.controller.selection =
              TextSelection.fromPosition(TextPosition(offset: index));
          widget.updateText(value);
        }else{
          widget.controller.text = value;
          widget.updateText(value);
        }*/
        widget.updateText(value);
      },
      style: TextStyle(
        fontWeight: FontWeight.w600,
        fontSize: 16,
        letterSpacing: 0,
        color: AppColors.textTitle,
      ),
      decoration: InputDecoration(
        counterText: "",
        hintText: widget.hintText == ""
            ? YLocal.of(context).input_email
            : widget.hintText,
        hintStyle: TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 14,
          height: 1,
          color: AppColors.textWeak,
        ),
        border: InputBorder.none,
        focusedBorder: InputBorder.none,
        enabledBorder: InputBorder.none,
        errorBorder: InputBorder.none,
        disabledBorder: InputBorder.none,
        suffixIcon: widget.hasLock
            ? InkWell(
                onTap: () {
                  setState(() {
                    isPwdHide = !isPwdHide;
                  });
                },
                child: Container(
                  child: Icon(
                    isPwdHide ? IconFonts.iconHide : IconFonts.iconShow,
                    size: 20,
                    color: Color(0xff6E7380),
                  ),
                ),
              )
            : SizedBox(),
      ),
    ));
  }

  //光标定位
  int getCursorIndex(String newValue) {
    Log.d('oldValue ============>$oldValue');
    Log.d('newValue ============>$newValue');
    if (oldValue != null && oldValue.isNotEmpty) {
      int length = min(newValue.length, oldValue.length);
      for (int i = 0; i < length; i++) {
        String text = oldValue.substring(i, i + 1);
        String newText = newValue.substring(i, i + 1);
        if (text != newText) {
          Log.d('index ============>$i');
          return i;
        }
      }
    }
    return newValue.length;
  }
}
