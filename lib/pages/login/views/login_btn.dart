import 'package:flutter/material.dart';
import 'package:yvr_assistant/manager/resource_mananger.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';

class LoginButtonWidget extends StatelessWidget {
  final bool isActive;
  final bool hasArrow;

  // 默认为Colourful 按钮
  final bool isCustomBtn;
  final String buttonText;
  final VoidCallback onPressed;

  LoginButtonWidget(
      {@required this.isActive,
      this.isCustomBtn,
      this.hasArrow,
      this.buttonText,
      this.onPressed});

  @override
  Widget build(BuildContext context) {
    bool isArrow = hasArrow ?? false;

    return Container(
        child: YvrTextButton(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 12),
            onPressed: isActive ? onPressed : null,
            color: isActive ? null : Color(0xffF0F2F5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  buttonText,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 18,
                    letterSpacing: 1,
                    color: isActive ? Colors.white : Color(0xff2C2E33),
                  ),
                ),
                isArrow ? SizedBox(width: 6) : SizedBox(width: 0),
                isArrow
                    ? Icon(
                        IconFonts.iconArrow,
                        color: isActive ? Colors.white : Color(0xff2C2E33),
                        size: 20,
                      )
                    : SizedBox(width: 0),
              ],
            )));
  }
}
