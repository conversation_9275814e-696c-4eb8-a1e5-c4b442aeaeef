import 'dart:async';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';

import '../../../styles/app_color.dart';

class AuthCodeButton extends StatefulWidget {
  final int timeCount;
  final bool isActive;
  final VoidCallback onPressed;
  final VoidCallback endActoon;

  AuthCodeButton({
    Key key,
    @required VoidCallback onPressed,
    this.timeCount,
    this.isActive,
    VoidCallback endActoon,
  })  : onPressed = onPressed,
        endActoon = endActoon,
        super(key: key);

  @override
  AuthCodeButtonState createState() => AuthCodeButtonState();
}

class AuthCodeButtonState extends State<AuthCodeButton> {
  bool isDisable = false;
  ValueNotifier<String> _title = ValueNotifier<String>('');
  Timer _timer;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      child: ValueListenableBuilder<String>(
          valueListenable: _title,
          builder: (context, value, child) {
            return Container(
              alignment: AlignmentDirectional.center,
              child: Text(
                value == null || value.isEmpty
                    ? YLocal.of(context).get_verify_code
                    : value,
                style: (!isDisable && widget.isActive)
                    ? TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 16,
                        height: 1,
                        letterSpacing: 0.5,
                        color: AppColors.textSub,
                      )
                    : TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 16,
                        height: 1,
                        letterSpacing: 0.5,
                        color: AppColors.textWeak,
                      ),
                textAlign: TextAlign.right,
              ),
            );
          }),
      onTap: (!isDisable && widget.isActive) ? widget.onPressed : () {},
    );
  }

  void startAction() {
    int counter = widget.timeCount;
    _title.value = YLocal.of(context).resend + '(${counter}s)';
    setState(() {
      isDisable = true;
    });

    if (_timer != null) {
      return;
    }
    // 开始倒计时
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      counter--;
      if (counter <= 0) {
        if (widget.endActoon != null) {
          widget.endActoon();
        }
        _title.value = YLocal.of(context).resend;
        setState(() {
          isDisable = false;
        });
        _timer.cancel();
        _timer = null;
      } else {
        _title.value = YLocal.of(context).resend + '(${counter}s)';
      }
    });
  }

  @override
  void dispose() {
    super.dispose();

    if (_timer != null) {
      _timer.cancel();
      _timer = null;
    }
  }
}
