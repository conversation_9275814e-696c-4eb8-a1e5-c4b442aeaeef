import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/network/socket.dart';
import 'package:yvr_assistant/pages/home/<USER>';
import 'package:yvr_assistant/pages/social/social.dart';
import 'package:yvr_assistant/pages/profile/profile.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/utils/data_record.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/view_model/unread_vmodel.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';
import '../../manager/events_bus2.dart';
import '../device/device_list.dart';

List<Widget> pages = <Widget>[
  HomePage(),
  SocialPage(),
  // DevicePage(),
  DeviceListPage(),
  ProfilePage()
];

class MainTab extends StatefulWidget {
  MainTab({Key key}) : super(key: key);

  @override
  _MainTabState createState() => _MainTabState();
}

class _MainTabState extends State<MainTab> {
  var _pageController = PageController();
  int _selectedIndex = 0;

  DateTime _lastPressed;
  var eventBusFn;

  @override
  void initState() {
    super.initState();
    eventBusFn = eventBus.on<EventFn>().listen((event) {
      int selIdx = event.obj["selectedTabbarIndex"] ?? -1;
      if (selIdx > 0) {
        setState(() {
          _selectedIndex = selIdx;
        });
        Navigator.of(context).popUntil((route) => route.isFirst);
        _pageController.jumpToPage(selIdx);
      }
      int tabIndex = event.obj[Global.switchTabEventKey] ?? -1;
      if (tabIndex >= 0) {
        setState(() {
          _selectedIndex = tabIndex;
        });
        _pageController.jumpToPage(tabIndex);
      }
    });

    SocketManager.getInstance().addNotifyCallback(onSocketNotify);
  }

  void onSocketNotify(Map data, String tag) {
    Provider.of<UnreadVModel>(context, listen: false).getUnreadNtyNum();
  }

  @override
  void dispose() {
    SocketManager.getInstance().removeNotifyCallback(onSocketNotify);
    super.dispose();
    eventBusFn.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: WillPopScope(
        onWillPop: () async {
          if (_lastPressed == null ||
              DateTime.now().difference(_lastPressed) > Duration(seconds: 1)) {
            //两次点击间隔超过1秒则重新计时
            _lastPressed = DateTime.now();
            return false;
          }
          return true;
        },
        child: PageView.builder(
          itemBuilder: (ctx, index) => pages[index],
          itemCount: pages.length,
          controller: _pageController,
          physics: NeverScrollableScrollPhysics(),
          onPageChanged: (index) async {
            if (index == 0 || index == 1) {
              Provider.of<UnreadVModel>(context, listen: false)
                  .getUnreadNtyNum();
              if (index == 1) {
                AppEvent.fireEventRefreshEvent();
              } else {
                DataRecord().saveData(
                    eventId: "assistant_appstore_store_0_0_page_view");
              }
            }
            setState(() {
              _selectedIndex = index;
            });
            switch (index) {
              case 1:
                if (DBUtil.instance.userBox.get(kToken) == null) {
                  _pageController.jumpToPage(0);
                  Global.navigatorKey.currentState.pushNamedAndRemoveUntil(
                      "/login", (route) => route.isFirst);
                }
                break;
              case 2:
                if (DBUtil.instance.userBox.get(kToken) == null) {
                  await Navigator.pushNamed(context, '/login');
                  _pageController.jumpToPage(0);
                } else {
                  eventBus.fire(EventFn({Global.deviceTabEventKey: true}));
                  // 每次重新验证是否添加新设备
                  /*List<String> devIds;
                  devIds = await YvrRequests.getLoginDevIds();
                  StorageManager.localStorage.setItem(Global.kUserHaveDevice, devIds.length > 0);
                  if (devIds.length == 0) {
                    _pageController.jumpToPage(0);
                    Navigator.pushNamed(context, '/dev_search',arguments: {"deviceObj": null});
                  }*/
                }
                break;
              default:
            }
          },
        ),
      ),
      bottomNavigationBar: SizedBox(
        child: BottomNavigationBar(
          showSelectedLabels: true,
          showUnselectedLabels: true,
          selectedFontSize: 0,
          unselectedFontSize: 0,
          items: <BottomNavigationBarItem>[
            tabbarItem(
                icon: 'assets/svg/nav_shop.svg',
                activeIcon: 'assets/images/nav_shop_selected.png',
                title: YLocal.of(context).tabbar_home),
            tabbarItem(
                icon: 'assets/svg/nav_social.svg',
                activeIcon: 'assets/images/nav_social_selected.png',
                title: YLocal.of(context).tabbar_social),
            tabbarItem(
                icon: 'assets/svg/nav_device.svg',
                activeIcon: 'assets/images/nav_device_selected.png',
                title: YLocal.of(context).tabbar_device),
            tabbarItem(
                icon: 'assets/svg/nav_mine.svg',
                activeIcon: 'assets/images/nav_mine_selected.png',
                title: YLocal.of(context).tabbar_profile),
          ],
          currentIndex: _selectedIndex,
          onTap: (index) {
            _pageController.jumpToPage(index);
          },
        ),
      ),
    );
  }

  static const LinearGradient kBottomNavText = LinearGradient(colors: [
    AppColors.buttonStart,
    AppColors.buttonEnd,
  ]);

  BottomNavigationBarItem tabbarItem(
      {String icon, String activeIcon, String title}) {
    return BottomNavigationBarItem(
      tooltip: '',
      icon: Column(
        children: [
          SvgPicture.asset(
            icon,
            width: 24,
            height: 24,
          ),
          SizedBox(
            height: 4,
          ),
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 10,
              height: 1,
              color: Color(0xff808080),
            ),
          ),
        ],
      ),
      activeIcon: Column(
        children: [
          Image.asset(
            activeIcon,
            width: 24,
            height: 24,
          ),
          SizedBox(
            height: 4,
          ),
          ShaderMask(
            blendMode: BlendMode.srcIn,
            shaderCallback: (bounds) => kBottomNavText.createShader(
              Rect.fromLTWH(0, 0, bounds.width, bounds.height),
            ),
            child: Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 10,
                height: 1,
                color: Color(0xff808080),
              ),
            ),
          )
        ],
      ),
      label: title,
      // ignore: deprecated_member_use
      // title: Container(
      //     margin: EdgeInsets.fromLTRB(0, 5, 0, 0),
      //     child: Text(title, style: TextStyle(fontSize: 10))),
    );
  }
}
