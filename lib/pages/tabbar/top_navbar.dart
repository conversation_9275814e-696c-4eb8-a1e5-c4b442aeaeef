import 'package:flutter/material.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/styles/app_color.dart';

// ignore: non_constant_identifier_names
AppBar TopNavbar(
    {Color backgroundColor,
    String title,
    Color titleColor,
    List<Widget> actions,
    Color backColor,
    Function backCallBack,
    PreferredSizeWidget bottom}) {
  return AppBar(
    backgroundColor: backgroundColor ?? AppColors.colorFFFFFF,
    centerTitle: true,
    leading: CustomBackButton(
      color: backColor ?? AppColors.textSub,
      onPressed: backCallBack,
    ),
    /*title: TextScroll(
      title ?? "",
      delayBefore: Duration(milliseconds: 1000),
      numberOfReps: 2,
      style: TextStyle(fontSize: 18, color: titleColor ?? AppColors.textTitle),
    ),*/
    title: Text(
      title ?? "",
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(fontSize: 18, color: titleColor ?? AppColors.textTitle),
    ),
    actions: actions,
    bottom: bottom,
  );
}

class CustomBackButton extends StatelessWidget {
  final VoidCallback onPressed;
  final Color color;

  CustomBackButton({this.onPressed, this.color = AppColors.textSub});

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: onPressed ??
          () {
            Global.navigatorKey.currentState.pop();
          },
      icon: Icon(
        Icons.arrow_back_ios,
        color: color,
      ),
    );
  }
}
