import 'package:flutter/material.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/pages/home/<USER>/popup_menu.dart';

class HomeDevOptionView extends StatelessWidget {
  final Function onTap;
  const HomeDevOptionView({Key key, this.onTap}) : super(key: key);

  String currentDev() {
    return DBUtil.instance.userBox.get(Global.kDevModel) ??
        Global.kDevModelOptions.first;
  }

  @override
  Widget build(BuildContext context) {
    String devName = DBUtil.instance.userBox.get(Global.kDevModel) ??
        Global.kDevModelOptions.first;
    int defIndex = Global.kDevModelOptions.indexOf(devName);

    return PopupView(
      defIndex: defIndex,
      offsetDy: 45,
      titles: Global.kDevModelOptions,
      padding: const EdgeInsets.only(left: 15),
      itemsecondaryBg: AppColors.colorFFFFFF,
      decoration: BoxDecoration(color: AppColors.colorFFFFFF),
      titleStyle: AppStyle.style_textTitle_w400_14pt(),
      iconWidget: Icon(
        Icons.arrow_drop_down_rounded,
        size: 24,
        color: AppColors.textTitle,
      ),
      onTap: (index) async {
        if (onTap != null) {
          String devModelStr = Global.kDevModelOptions[index];
          await DBUtil.instance.userBox.put(Global.kDevModel, devModelStr);
          onTap();
        }
      },
    );
  }
}
