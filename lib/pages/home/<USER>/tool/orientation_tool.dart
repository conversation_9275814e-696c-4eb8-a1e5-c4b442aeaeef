import 'package:flutter/services.dart';
import 'package:yvr_assistant/utils/channel.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';

class OrientationTool {
  /// 切换至竖屏
  void portraitScreen() {
    if (Platform.isIOS) {
      FlutterChannel().iOSChangeOrientation(orientation: 0);
    } else {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
    }
  }

  /// 切换至横屏
  void landscapeScreen() {
    if (Platform.isIOS) {
      FlutterChannel().iOSChangeOrientation(orientation: 1);
    } else {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }
  }

  /// 自由屏
  void freeScreen() {
    if (Platform.isIOS) {
      FlutterChannel().iOSChangeOrientation(orientation: 2);
    } else {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }
  }
}
