import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/model/app_type_model.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/utils/log.dart';

/// 获取应用标签 存储至本地
Future<void> initAppTypes() async {
  List<dynamic> appTypeModels =
      StorageManager.localStorage.getItem("kAppTagModels");
  if (appTypeModels != null && appTypeModels.isNotEmpty) {
    if (appTypeModels[0] is Map) {
      List<dynamic> globalTags =
          appTypeModels.map((e) => AppTypeModel.fromJson(e)).toList();
      try {
        await StorageManager.localStorage.setItem("kAppTagModels", globalTags);
      } catch (e, s) {
        print(s);
      }
    }
  }
  await configAppTypes();
  Future cfgFuture = Future.value(YvrRequests.getAppCfgInfo());
  Future tagFuture = Future.value(YvrRequests.getAppTagInfo());
  Future priceFuture = Future.value(YvrRequests.getAppPriceRanges());

  await Future.wait([cfgFuture, tagFuture, priceFuture]).then((datas) async {
    try {
      final datas0 = datas[0];
      final datas1 = datas[1];
      final datas2 = datas[2];

      final cfgItem = {
        "selIndex": 0,
        "msgs": datas0.map((e) => e.english).toList(),
        "values": datas0.map((e) => e.value).toList(),
      };

      final tagItem = {
        "selIndex": 0,
        "msgs": datas1.map((e) => e.english).toList(),
        "values": datas1.map((e) => e.tag).toList(),
      };

      final priceItem = {
        "selIndex": 0,
        "msgs": datas2.map((e) => e.descp.enUs).toList(),
        "lprice": datas2.map((e) => e.lprice).toList(),
        "hprice": datas2.map((e) => e.hprice).toList(),
      };

      Log.d('价格数据为：$priceItem');

      await Future.wait([
        StorageManager.localStorage.setItem("kAppTagModels", datas1),
        StorageManager.localStorage.setItem("kAppCfgItem", cfgItem),
        StorageManager.localStorage.setItem("kAppTagItem", tagItem),
        StorageManager.localStorage.setItem("kPriceTagItem", priceItem),
      ]);

      await configAppTypes(
        cfgItem: cfgItem,
        tagItem: tagItem,
        priceItem: priceItem,
      );
    } catch (e, s) {
      Log.e('请求配置数据失败：$s');
    }

    /**
       * 价格接口失败则采用原始默认数据 
       * 接口价格数据结构
       *  {
                "lprice": -1,
                "hprice": -1,
                "descp": {
                    "en_US": "All prices",
                    "zh_CN": "全部价格"
                }
            },

        * 组装后结构
        * {
            "selIndex": 0,
            "msgs": en_US,
            "lprice": lprice,
            "hprice": hprice,
          };   
       */
  });
}

Future<void> configAppTypes({Map cfgItem, Map tagItem, Map priceItem}) async {
  bool _isTestUser =
      StorageManager.localStorage.getItem(Global.kIsTestUser) ?? false;
  BuildContext context = Global.navigatorKey.currentState.overlay.context;
  // 筛选条件依次为：类型、标签、价格、快捷
  List<Map> categorys = [
    {
      // "type": "sort",
      "selIndex": 0,
      "msgs": [
        YLocal.of(context).home_recently_released,
        YLocal.of(context).home_earliest_released,
        YLocal.of(context).home_lowest_price,
        YLocal.of(context).home_highest_price
      ],
      "values": [0, 1, 2, 3],
    },
  ];

  // 添加接口价格数据
  if (priceItem != null) {
    categorys.insert(0, priceItem);
  } else if (StorageManager.localStorage.getItem("kPriceTagItem") != null) {
    priceItem = StorageManager.localStorage.getItem("kPriceTagItem");
    categorys.insert(0, priceItem);
  }

  if (cfgItem != null) {
    // 测试用户特殊处理cfgItem 添加后即第一项数据 价格顺位为第二个
    if (_isTestUser) {
      cfgItem["msgs"].add(YLocal.of(context).daifabudaifeibu);
      cfgItem["values"].add(3);
    }
    categorys.insert(0, cfgItem);
  } else if (StorageManager.localStorage.getItem("kAppCfgItem") != null) {
    cfgItem = StorageManager.localStorage.getItem("kAppCfgItem");
    if (_isTestUser) {
      cfgItem["msgs"].add(YLocal.of(context).daifabudaifeibu);
      cfgItem["values"].add(3);
    }
    categorys.insert(0, cfgItem);
  }

  if (tagItem != null) {
    categorys.insert(1, tagItem);
  } else if (StorageManager.localStorage.getItem("kAppTagItem") != null) {
    tagItem = StorageManager.localStorage.getItem("kAppTagItem");
    categorys.insert(1, tagItem);
  }

  // 根据categorys长度判断是否价格添加成功
  // Log.d("categorys的长度：${categorys.length} ${categorys.last}");

  await StorageManager.localStorage.setItem("kCategorys", categorys);
}
