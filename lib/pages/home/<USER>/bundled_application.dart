import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/application_list.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/bundled_bottom.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/sliver_appbar.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/utils/log.dart';
import '../../../generated/l10n.dart';
import '../../../manager/event_bus.dart';
import '../../../manager/global.dart';
import '../../../manager/storage_manager.dart';
import '../../../model/bundled_info_model.dart';
import '../../../network/request.dart';
import '../../../provider/provider_widget.dart';
import '../../../provider/view_state_widget.dart';
import '../../../public_ui/widget/common_button.dart';
import '../../../public_ui/widget/toast_show.dart';
import '../../../utils/data_record.dart';
import '../../../view_model/bundled_vmodel.dart';
import '../prod/views/pay_cell.dart';
import '../views/pay_noti_view.dart';
import 'package:fluwx/fluwx.dart' as fluwx;

///捆绑应用详情页
class BundledApplicationPage extends StatefulWidget {
  final arguments;
  BundledApplicationPage({Key key, @required this.arguments}) : super(key: key);

  @override
  _BundledApplicationState createState() => _BundledApplicationState();
}

class _BundledApplicationState extends State<BundledApplicationPage> {
  int mpId;
  var eventBusFn;
  var _paySubscription;
  BundledApplicationVModel _bundledVModel = BundledApplicationVModel();

  @override
  void initState() {
    super.initState();
    var object = widget.arguments as Map<String, dynamic>;
    mpId = object['mpId'];
    Log.d('mpId=================>$mpId');
    StorageManager.foreverData.setItem(Global.kCurrentSubId, mpId);
    DataRecord().saveData(eventId: "assistant_appstore_bundle_0_0_page_view");
    initEventBus();
  }

  @override
  void dispose() {
    eventBusFn?.cancel();
    _paySubscription?.cancel();
    StorageManager.foreverData.setItem(Global.kCurrentSubId, 0);
    if (!_bundledVModel.isPaySuccess &&
        _bundledVModel.bundledInfo != null &&
        _bundledVModel.bundledInfo.subSaleSummaryInfo != null) {
      int subId = _bundledVModel.bundledInfo.subSaleSummaryInfo.subId;
      String ntyId =
          StorageManager.localStorage.getItem("NotiIdForApp_$subId") ?? null;
      //如果是未购买，则通知VR端
      if (ntyId != null &&
          !_bundledVModel.bundledInfo.subSaleSummaryInfo.hasAllPur) {
        notiPayResultForVR(ntyId: ntyId, isPay: false, appId: subId);
      }
    }
    super.dispose();
  }

  initEventBus() {
    eventBusFn = eventBus.on<EventFn>().listen((event) {
      if (event.obj != null &&
          event.obj['BundledRefresh'] != null &&
          event.obj['BundledRefresh']) {
        Log.d('------------------捆绑详情页刷新------------------');
        _bundledVModel.getAppsOnSubject();
      }
      if (event.obj != null &&
          event.obj['BundledEnd'] != null &&
          event.obj['BundledEnd']) {
        Log.d('------------------捆绑活动结束通知------------------');
        Navigator.pushReplacementNamed(context, '/apps',
            arguments: {"isSingleSubject": true, "title": "", "id": mpId});
      }
    });
  }

  //移除支付监听
  cleanPayListener() {
    _paySubscription?.cancel();
    _paySubscription = null;
  }

  //支付监听
  void payListener() {
    if (_paySubscription == null) {
      _paySubscription = fluwx.weChatResponseEventHandler.listen((event) {
        Log.d("微信支付返回信息 ====================>${event.toString()}");
        if (event is fluwx.WeChatPaymentResponse) {
          if (!event.isSuccessful) {
            // YvrToast.showToast("支付失败");
            YvrToast.showToast(YLocal.of(context).zhifushibai);
          } else {
            // YvrToast.showToast("支付成功");
            YvrToast.showToast(YLocal.of(context).zhifuchenggong);
            handlePaySuccess();
            SubSaleSummaryInfo info =
                _bundledVModel.bundledInfo.subSaleSummaryInfo;
            Navigator.pushNamed(context, '/pay_result', arguments: {
              "appPayType": PayWayType.payForWechat,
              "price": MoneyUtil.changeF2Y(info.sprice),
              "appId": info.subId,
              'isBundled': true
            });
          }
        }
      });
    }
  }

  handlePaySuccess() {
    int subId = _bundledVModel.bundledInfo.subSaleSummaryInfo.subId;
    String ntyId =
        StorageManager.localStorage.getItem("NotiIdForApp_$subId") ?? null;
    if (ntyId != null) {
      notiPayResultForVR(ntyId: ntyId, isPay: true, appId: subId);
      YvrRequests.disposeNotify(
          ntyId: int.parse(ntyId), tag: "recvAppPayOnMobile", mark: 1);
    }
    YvrToast.dismiss();
    eventBus.fire(EventFn({Global.kRefreshHome: true}));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: AppColors.colorFFFFFF,
      body: ProviderWidget<BundledApplicationVModel>(
        model: _bundledVModel,
        builder: customScrollView,
        onModelReady: (BundledApplicationVModel model) {
          model.init(mpId, PrimaryScrollController.of(context), kToolbarHeight);
        },
      ),
    );
  }

  Widget customScrollView(
      BuildContext context, BundledApplicationVModel model, Widget child) {
    if (model.idle) {
      if (model.bundledInfo != null) {
        return Column(
          children: [
            Expanded(
                child: CustomScrollView(
              slivers: <Widget>[
                BundledSliverAppBar(model, model.appbarVModel),
                sliverContent(context, model)
              ],
            )),
            Visibility(
              visible: model.bundledInfo.subSaleSummaryInfo != null &&
                  !model.bundledInfo.subSaleSummaryInfo.hasAllPur,
              child: BundledBottomWidget(model, () {
                payListener();
              }, () {
                cleanPayListener();
              }),
            )
          ],
        );
      }
    } else if (model.error) {
      return ViewStateErrorWidget(
          error: model.viewStateError,
          onPressed: () {
            model.loadAllData();
          });
    }
    return const SizedBox();
  }

  Widget sliverContent(BuildContext context, BundledApplicationVModel model) {
    return SliverToBoxAdapter(
        child: Padding(
      padding: EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 20),
          titleTextWidget(context, model),
          SizedBox(height: 20),
          ApplicationListWidget(model, () {
            cleanPayListener();
          }),
          SizedBox(height: 20),
        ],
      ),
    ));
  }

  Widget titleTextWidget(BuildContext context, BundledApplicationVModel model) {
    SubSaleSummaryInfo info = model.bundledInfo.subSaleSummaryInfo;
    return Container(
        width: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(children: [
              Expanded(
                  child: Text(
                "${model.bundledInfo.name}",
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: AppStyle.style_textTitle_w500_27pt(),
              )),
              SizedBox(width: 8),
              // CommonButton('捆绑包',width: 47,height: 21,fontSize:9,radius: 2,colors: <Color>[
              CommonButton(
                text: YLocal.of(context).kunbangbao,
                width: 47,
                height: 21,
                fontSize: 9,
                radius: 2,
                colors: <Color>[Color(0xFF1656FF), Color(0xFF4F7FFE)],
              ),
            ]),
            Visibility(
                visible: info.promInfo != null || info.subIntroduce != null,
                child: SizedBox(height: 16)),
            Visibility(
              visible: info.promInfo != null || info.subIntroduce != null,
              child: Text(
                "${info.promInfo ?? info.subIntroduce}",
                maxLines: 5,
                overflow: TextOverflow.ellipsis,
                style: AppStyle.style_textSub_w400_16pt_15height(),
              ),
            ),
            SizedBox(height: 20),
            Container(
              width: double.infinity,
              height: 0.5,
              color: AppColors.divider,
            ),
          ],
        ));
  }
}
