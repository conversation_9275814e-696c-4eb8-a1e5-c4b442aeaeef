import 'dart:math';
import 'package:flutter/material.dart';
import 'package:common_utils/common_utils.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/model/apps_model.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:yvr_assistant/pages/home/<USER>/home_tool.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:yvr_assistant/pages/home/<USER>/banner_swiper.dart';
import 'package:yvr_assistant/public_ui/widget/smart_row.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/rating_star.dart';

class AppsCell extends StatelessWidget {
  final AppsModel appsModel;
  final Function clickFunc;

  const AppsCell({Key key, @required this.appsModel, @required this.clickFunc})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          clickFunc();
          Navigator.pushNamed(context, '/prod', arguments: {
            "id": appsModel.id,
          });
        },
        behavior: HitTestBehavior.opaque,
        child: Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildImageSection(),
              const SizedBox(width: 12),
              Expanded(child: _buildDetailsSection()),
            ],
          ),
        ));
  }

  Widget _buildImageSection() {
    return Expanded(
      child: ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(8)),
        child: AspectRatio(
          aspectRatio: 16 / 9,
          child: CachedNetworkImage(
            imageUrl: appsModel.rcover + "?scale=small",
            cacheKey: appsModel.rcover + "?scale=small",
            fit: BoxFit.cover,
            memCacheWidth: (Global.appCellImageW * Global.winPxRatio).toInt(),
            placeholder: (context, url) => rPlhdImage,
            errorWidget: (context, url, error) => rPlhdImage,
          ),
        ),
      ),
    );
  }

  Widget _buildDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (appsModel.name != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 7),
            child: Text(
              appsModel.name ?? '',
              maxLines: 2,
              style: const TextStyle(
                color: Color(0xFF2C2E33),
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
        SmartRow(
          spacing: 4,
          children: symbolLabels(tags: appsModel.tag),
        ),
        const SizedBox(height: 5),
        if (appsModel.state != -1) RatingSection(appsModel: appsModel),
        const SizedBox(height: 5),
        _priceWidget(appsModel),
      ],
    );
  }

  Widget _priceWidget(AppsModel model) {
    return priceWidget(
      model.state,
      model.sprice,
      model.id,
      model.bprice,
      currency: model.currency,
    );
  }
}

Widget priceWidget(int state, int sprice, int id, int bprice,
    {Color freeTextColor = const Color(0xffB0B2B8), int currency = 0}) {
  String symbol = (currency == 1) ? "\$" : "￥";
  if (state == -1) {
    return Text(YLocal.current.jijiangshangxianjiqi,
        style: AppStyle.style_textSub_w400_14pt());
  }
  if (HomeTool().appIsPay(id)) {
    return Text(YLocal.current.yigoumai,
        style: AppStyle.style_textSub_w400_14pt());
  }
  String price = (sprice == 0
      ? YLocal.current.home_free
      : "$symbol${MoneyUtil.changeF2Y(sprice)}");
  if (bprice > sprice) {
    return RichText(
      text: TextSpan(
          text: price + "  ",
          style: AppStyle.style_standard_bold_14pt(),
          children: [
            TextSpan(
                text: "$symbol${MoneyUtil.changeF2Y(bprice)} ",
                style: AppStyle.style_textWeak_w400_10pt_line())
          ]),
    );
  } else {
    return Text(price, style: AppStyle.style_standard_bold_14pt());
  }
}

List<Widget> symbolLabels({@required List<int> tags}) {
  List<dynamic> appTypeModels =
      StorageManager.localStorage.getItem("kAppTagModels");

  if (appTypeModels == null) {
    return [const SizedBox()];
  }

  List<Widget> labels = [];
  for (var i = 0; i < min(tags.length, 2); i++) {
    String tagName;
    appTypeModels.forEach((e) {
      if (e.tag == tags[i]) {
        tagName = e.english;
      }
    });
    labels.add((tagName != null)
        ? Container(
            padding: const EdgeInsets.only(left: 5, right: 5, top: 1),
            //边框设置
            decoration: new BoxDecoration(
              //设置四周圆角 角度
              borderRadius: const BorderRadius.all(Radius.circular(20)),
              //设置四周边框
              border: new Border.all(width: 0.6, color: Color(0xffEEEEEE)),
            ),
            child: Text(
              /**防止英文自动换行*/
              Characters(tagName).join('\u{200B}'),
              style: AppStyle.style_textSub_w400_10pt(),
              softWrap: false,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          )
        : const SizedBox());
  }

  return labels.toList();
}

class RatingSection extends StatelessWidget {
  final AppsModel appsModel;
  const RatingSection({@required this.appsModel, Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          YLocal.of(context).home_person_com(appsModel.count.toString()),
          style: AppStyle.style_textWeak_w400_10pt(),
        ),
        const SizedBox(width: 10),
        RatingBar(
          initialRating: appsModel.count == 0
              ? 0
              : ((appsModel.aver.floor() == appsModel.aver.round())
                  ? appsModel.aver
                  : (appsModel.aver.floor() + 0.5)),
          allowHalfRating: true,
          itemCount: 5,
          itemSize: 10,
          ignoreGestures: true,
          ratingWidget: ratingWidget,
          itemPadding: const EdgeInsets.symmetric(horizontal: 2.0),
          onRatingUpdate: (double value) {},
        ),
      ],
    );
  }
}
