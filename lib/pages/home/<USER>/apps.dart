import 'package:flutter/material.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/model/subject_model.dart';
import 'package:yvr_assistant/model/theme_data_model.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/pages/home/<USER>/apps_cell.dart';
import 'package:yvr_assistant/pages/home/<USER>/subj_cell.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/provider/view_state_widget.dart';
import 'package:yvr_assistant/public_ui/skeleton/app_list_skeleton.dart';
import 'package:yvr_assistant/public_ui/skeleton/skeleton.dart';
import 'package:yvr_assistant/public_ui/skeleton/subj_list_skeleton.dart';
import 'package:yvr_assistant/utils/data_record.dart';
import 'package:yvr_assistant/view_model/single_subj_vmodel.dart';
import 'package:yvr_assistant/provider/provider_widget.dart';
import 'package:yvr_assistant/view_model/single_theme_vmodel.dart';

import '../../../model/summary_info_model.dart';
import '../../../utils/log.dart';

class AppsPage extends StatefulWidget {
  final arguments;
  AppsPage({Key key, @required this.arguments}) : super(key: key);

  @override
  _AppsPageState createState() => _AppsPageState();
}

class _AppsPageState extends State<AppsPage> {
  var eventBusFn;
  SingleSubjVModel singleSubjVModel = SingleSubjVModel();

  @override
  void dispose() {
    super.dispose();
    eventBusFn.cancel();
  }

  @override
  void initState() {
    eventBusFn = eventBus.on<EventFn>().listen((event) {
      bool isRefresh = event.obj[Global.kRefreshAppPayStatus] ?? false;
      if (isRefresh) {
        setState(() {});
      }
      if (event.obj != null &&
          event.obj['BundledEnd'] != null &&
          event.obj['BundledEnd']) {
        Log.d('------------------捆绑活动结束通知------------------');
        singleSubjVModel.getAppsOnSubject(widget.arguments["id"]);
      }
    });
    super.initState();
    if (mounted) {
      bool isSingleSubject = widget.arguments["isSingleSubject"] ?? false;
      if (isSingleSubject) {
        DataRecord().saveData(
            eventId: "assistant_appstore_subject_0_0_page_view",
            extraData: {"subjId": widget.arguments["id"]});
      } else {
        DataRecord().saveData(
            eventId: "assistant_appstore_theme_0_0_page_view",
            extraData: {"themeId": widget.arguments["id"]});
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    bool isSingleSubject = widget.arguments["isSingleSubject"] ?? false;
    String _navTitle = widget.arguments["title"].toString().length > 0
        ? widget.arguments["title"]
        : "";

    return isSingleSubject
        // 专题对应的应用列表
        ? ProviderWidget<SingleSubjVModel>(
            model: singleSubjVModel,
            onModelReady: (model) {
              model.getAppsOnSubject(widget.arguments["id"]);
            },
            builder: (context, model, child) {
              if (model.busy) {
                return myScaffold(
                    title: _navTitle,
                    body: SkeletonList(
                      builder: (context, index) => AppSkeletonItem(),
                    ));
              } else if (model.error) {
                return myScaffold(
                    title: _navTitle,
                    body: ViewStateErrorWidget(
                        error: model.viewStateError,
                        onPressed: () {
                          model.getAppsOnSubject(widget.arguments["id"]);
                        }));
              }

              SubjectModel subjectModel = model.subjectModel;
              List<Widget> subjListViews = [];
              for (var i = 0; i < subjectModel.apps.length; i++) {
                subjListViews.add(
                  AppsCell(
                    appsModel: subjectModel.apps[i],
                    clickFunc: () {
                      DataRecord().saveData(
                          eventId:
                              "assistant_appstore_theme_list_app_block_click",
                          extraData: {
                            "subjId": subjectModel.id,
                            "appId": subjectModel.apps[i].id
                          });
                    },
                  ),
                );
              }

              return Scaffold(
                  backgroundColor: AppColors.colorFFFFFF,
                  appBar: TopNavbar(
                    title: subjectModel.name,
                  ),
                  body: Container(
                      margin: EdgeInsets.symmetric(horizontal: 16),
                      child: ListView(
                        padding: EdgeInsets.zero,
                        children: subjListViews.toList(),
                      )));
            },
          )
        // 主题对应的应用列表
        : ProviderWidget<SingleThemeVModel>(
            model: SingleThemeVModel(),
            onModelReady: (model) {
              model.getThemeInfo(widget.arguments["id"]);
            },
            builder: (context, model, child) {
              if (model.busy) {
                if (widget.arguments["type"] == null) {
                  return myScaffold(
                      title: _navTitle,
                      body: SkeletonList(
                        builder: (context, index) => AppSkeletonItem(),
                      ));
                } else if (widget.arguments["type"] == 3) {
                  return myScaffold(
                      title: _navTitle,
                      body: SkeletonList(
                        builder: (context, index) => SubjSkeletonItem(),
                      ));
                } else {
                  return myScaffold(
                      title: _navTitle,
                      body: SkeletonList(
                        builder: (context, index) => AppSkeletonItem(),
                      ));
                }
              } else if (model.error) {
                return myScaffold(
                    title: _navTitle,
                    body: ViewStateErrorWidget(
                        error: model.viewStateError,
                        onPressed: () {
                          model.getThemeInfo(widget.arguments["id"]);
                        }));
              }

              ThemeDataModel themeDataModel = model.themeDataModel;
              bool includeBundle = isIncludeBundle(themeDataModel.subjects);
              double childAspectRatio = includeBundle ? (1 / 1) : (4 / 3);

              // ignore: deprecated_member_use
              List<Widget> subjListViews = [];
              if (themeDataModel.type != 3) {
                for (var i = 0; i < themeDataModel.apps.length; i++) {
                  subjListViews.add(AppsCell(
                    appsModel: themeDataModel.apps[i],
                    clickFunc: () {
                      DataRecord().saveData(
                          eventId:
                              "assistant_appstore_theme_list_app_block_click",
                          extraData: {
                            "themeId": themeDataModel.id,
                            "appId": themeDataModel.apps[i].id
                          });
                    },
                  ));
                }
              }
              return Scaffold(
                  backgroundColor: AppColors.colorFFFFFF,
                  appBar: TopNavbar(
                    title: themeDataModel.name,
                  ),
                  body: (themeDataModel.type != 3)
                      ? Container(
                          margin: EdgeInsets.all(10),
                          child: ListView(
                            children: subjListViews.toList(),
                          ))
                      : Container(
                          padding: EdgeInsets.all(10),
                          child: GridView.custom(
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 2,
                                mainAxisSpacing: 10.0,
                                crossAxisSpacing: 10.0,
                                childAspectRatio: childAspectRatio,
                              ),
                              childrenDelegate:
                                  SliverChildBuilderDelegate((context, index) {
                                return SubjCell(
                                    clickFunc: () {
                                      DataRecord().saveData(
                                          eventId:
                                              "assistant_appstore_theme_list_subject_block_click",
                                          extraData: {
                                            "themeId": themeDataModel.id,
                                            "subjId": themeDataModel
                                                .subjects[index].id
                                          });
                                    },
                                    subjectModel:
                                        themeDataModel.subjects[index]);
                              }, childCount: themeDataModel.subjects.length)),
                        ));
            },
          );
  }

  Widget myScaffold({String title = "", Widget body}) {
    return Scaffold(
        backgroundColor: Color(0xff17191B),
        appBar: TopNavbar(title: title),
        body: body);
  }

  //查询是否包含捆绑包
  bool isIncludeBundle(List<SubjectModel> subjects) {
    if (subjects != null && subjects.length > 0) {
      for (int i = 0; i < subjects.length; i++) {
        SummaryInfoModel summaryInfo = subjects[i].summaryInfo;
        if (summaryInfo != null && summaryInfo.bundle) {
          return true;
        }
      }
    }
    return false;
  }
}
