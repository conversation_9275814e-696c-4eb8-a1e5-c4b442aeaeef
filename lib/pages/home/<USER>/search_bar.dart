import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/utils/data_record.dart';

class HomeSearchBar extends StatelessWidget {
  const HomeSearchBar({Key key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
        onTap: () async {
          DataRecord().saveData(
              eventId: "assistant_appstore_store_topBar_searchBar_pit_click");
          Navigator.pushNamed(context, "/search");

          // Share.share('YVR官网 https://www.pfdm.cn/',
          //     subject: 'Look what I made!');

          // launchWebUrl();
        },
        child: Container(
          height: 32,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
              color: AppColors.secondaryBg,
              borderRadius: BorderRadius.circular(16.0)),
          child: <PERSON>(
            children: [
              SvgPicture.asset(
                'assets/svg/ic_search.svg',
                width: 18,
                height: 18,
                color: AppColors.textWeak,
              ),
              const SizedBox(width: 8),
              Text(
                YLocal.current.home_search_plhd,
                style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textWeak,
                    fontWeight: FontWeight.normal),
              )
            ],
          ),
        ));
  }
}
