import 'dart:async';
import 'dart:math';
import 'package:get/get.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fijkplayer/fijkplayer.dart';
import 'package:text_scroll/text_scroll.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:common_utils/common_utils.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_page_view_indicator/flutter_page_view_indicator.dart';

import 'package:yvr_assistant/main.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/model/comment_model.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/utils/data_record.dart';
import 'package:yvr_assistant/model/pay_model.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:flutter_swiper/flutter_swiper.dart';
import 'package:yvr_assistant/model/prod_model.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';
import 'package:yvr_assistant/utils/date_time_utils.dart';
import 'package:yvr_assistant/view_model/prod_vmodel.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/public_ui/widget/image.dart';
import 'package:yvr_assistant/provider/provider_widget.dart';
import 'package:yvr_assistant/manager/resource_mananger.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/provider/view_state_widget.dart';
import 'package:yvr_assistant/view_model/comments_vmodel.dart';
import 'package:yvr_assistant/public_ui/widget/star_judge.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/public_ui/widget/image_browser.dart';
import 'package:yvr_assistant/public_ui/widget/image_view.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/public_ui/widget/common_button.dart';
import 'package:yvr_assistant/pages/social/views/plhd_view.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/prod_info.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/pay_cell.dart';
import 'package:yvr_assistant/pages/home/<USER>/pay_noti_view.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/prod_data.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/rating_star.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/video_player.dart';
import 'package:yvr_assistant/public_ui/widget/countdown_widget.dart';
import 'package:yvr_assistant/public_ui/widget/pay_bottom_sheet.dart';
import 'package:yvr_assistant/pages/home/<USER>/tool/orientation_tool.dart';
import 'package:yvr_assistant/pages/home/<USER>/comment/views/readmore.dart';
import 'package:yvr_assistant/pages/home/<USER>/comment/views/comment_cell.dart';

class ProdPage extends StatefulWidget {
  final arguments;
  ProdPage({Key key, @required this.arguments}) : super(key: key);

  @override
  _ProdPageState createState() => _ProdPageState();
}

class _ProdPageState extends State<ProdPage> with RouteAware {
  bool _canReloadForTokenExpire = true;
  List<Widget> _commentList = [];
  // 防止递归 持续刷新
  int _isOddNumber = 0;
  ProdVModel _viewModel = ProdVModel();
  ProdModel get _prodModel => _viewModel.prodModel;
  // 修改评论总数、评论进度条、无评论到有评论查看全部的状态：
  // 存储本地某应用信息，评论后全局通知（参考做法：用户评论的状态）
  double picHeight = Global.screenWidth * 9 / 16;
  ScrollController _scrollController = ScrollController();
  ValueNotifier<bool> _isShowTopNav = ValueNotifier<bool>(false);
  ValueNotifier<PayWayType> _payWayType =
      ValueNotifier<PayWayType>(PayWayType.payForPayPal);
  ValueNotifier<bool> _isPaySuccess = ValueNotifier<bool>(false);
  List<Map> _prodInfoMap = ProdData().prodInfoMap;
  String _selectedCouponName;
  int _selCouponId = 0;
  var eventBusFn;
  Timer _exitTimer;

  FijkPlayer _player;
  int _bannerIndex = 0;
  OrientationTool orienTool = OrientationTool();

  @override
  void didPush() {
    // push入当前页面时走这里
    super.didPush();
    delayedExecution(() {
      if (_viewModel.prodModel != null &&
          _bannerIndex < _viewModel.prodModel?.vedios?.length) {
        orienTool.freeScreen();
      }
    });
  }

  @override
  void didPushNext() {
    // push出当前页面时走这里
    super.didPushNext();
    leaveOrBackPage(leave: true);
  }

  @override
  void didPopNext() {
    // pop回当前页面走这里
    super.didPopNext();
    leaveOrBackPage(leave: false);
  }

  leaveOrBackPage({bool leave = true}) {
    if (MediaQuery.of(context).size.width >
            MediaQuery.of(context).size.height ||
        _player?.value?.fullScreen == true) {
      // 已全屏不做任何处理
      return;
    }

    if (leave) {
      // 离开当前页面： 取消自由屏任务 并保持竖屏
      playVideo(false);
      _exitTimer?.cancel();
      orienTool.portraitScreen();
    } else {
      //  返回当前页面：区分是否为视频页面 决定横屏或竖屏
      if (_bannerIndex < _prodModel?.vedios?.length) {
        delayedExecution(() {
          orienTool.freeScreen();
        }, seconds: 5);
      } else {
        orienTool.portraitScreen();
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context));
  }

  @override
  void dispose() {
    super.dispose();
    orienTool.portraitScreen();
    routeObserver.unsubscribe(this);
    prodNotiPurchasedRightNow();
    eventBusFn.cancel();
    _exitTimer?.cancel();
    _viewModel.dispose();
    StorageManager.foreverData.setItem(Global.kCurrentProdId, 0);
    YvrToast.dismiss();
  }

  void prodNotiPurchasedRightNow() {
    if (_prodModel != null &&
        _prodModel?.state == 0 &&
        !_isPaySuccess.value &&
        _prodModel?.purchased == 0) {
      String ntyId = StorageManager.localStorage
              .getItem("NotiIdForApp_${_prodModel.id}") ??
          null;
      if (ntyId != null) {
        notiPayResultForVR(
          ntyId: ntyId,
          isPay: false,
          appId: _prodModel.id,
        );
      }
    }
  }

  @override
  void initState() {
    super.initState();
    orienTool.portraitScreen();
    StorageManager.foreverData
        .setItem(Global.kCurrentProdId, widget.arguments["id"]);
    _scrollController.addListener(() {
      _isShowTopNav.value = _scrollController.position.pixels > picHeight;
    });
    eventBusFn = eventBus.on<EventFn>().listen((event) {
      bool closeVideo = event.obj["CloseVideo"] ?? false;
      int isCommentProdId = event.obj["kCommentProdId"] ?? 0;
      bool kChangeCouponInfo = event.obj["kChangeCouponInfo"] ?? false;

      if (event.obj != null &&
          event.obj['ProdRefresh'] != null &&
          event.obj['ProdRefresh']) {
        Log.d('------------------应用详情页刷新------------------');
        _viewModel.reqProdData(widget.arguments["id"]);
      }

      if (kChangeCouponInfo) {
        _selCouponId = event.obj["kSelectedCouponId"] ?? 0;
        _selectedCouponName = event.obj["kSelectedCouponName"] ?? "";

        _payWayType.value = (_selCouponId == 0)
            ? PayWayType.payForYCoin
            : PayWayType.payForCoupon;
      }

      if (isCommentProdId == widget.arguments["id"]) {
        Widget widget = commentButton(viewModel: _viewModel);
        if (_commentList.contains(widget)) {
          setState(() {
            _commentList.remove(widget);
          });
        }
      }
      Future.delayed(Duration(milliseconds: 100), () {
        if (closeVideo) {
          playVideo(false);
        }
      });
    });

    if (mounted) {
      DataRecord().saveData(
          eventId: "assistant_appstore_appDetail_0_0_page_view",
          extraData: {"appId": widget.arguments["id"]});
    }
  }

  Future<void> playVideo(bool play) async {
    if (_prodModel.vedios.length == 0) {
      return;
    }
    Log.d('当前的播放器状态：${_player?.state}');
    if (_player?.state == FijkState.initialized ||
        _player?.state == FijkState.started) {
      play ? await _player?.start() : await _player?.pause();
    }
  }

  void delayedExecution(Function callback, {int seconds = 2}) {
    if (_exitTimer != null) _exitTimer?.cancel();
    _exitTimer = Timer(Duration(seconds: seconds), () {
      if (callback != null) {
        callback();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
        builder: (BuildContext context, Orientation orientation) {
      if (_player != null &&
          _player?.value?.fullScreen == false &&
          orientation == Orientation.landscape) {
        _player?.enterFullScreen();
      }
      return contentWidget(context);
    });
  }

  Widget contentWidget(BuildContext context) {
    return Scaffold(
        backgroundColor: AppColors.colorFFFFFF,
        appBar: AppBar(toolbarHeight: 0),
        body: ProviderWidget<ProdVModel>(
          model: _viewModel,
          autoDispose: false,
          onModelReady: (model) => model.reqProdData(widget.arguments["id"]),
          builder: (context, model, child) {
            if (model.busy) {
              return ViewStateBusyWidget();
            } else if (model.error) {
              //  token 过期会跳两次登录
              if (_canReloadForTokenExpire &&
                  DBUtil.instance.userBox.get(kToken) != null) {
                _canReloadForTokenExpire = false;
                Future.delayed(Duration(milliseconds: 1000), () {
                  model.reqProdData(widget.arguments["id"]);
                });
                return SizedBox();
              }

              return Stack(children: [
                ViewStateErrorWidget(
                  error: model.viewStateError,
                  onPressed: () {
                    model.reqProdData(widget.arguments["id"]);
                  },
                ),
                Positioned(
                    top: 10,
                    left: 15,
                    child: IconButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: Icon(
                          Icons.arrow_back_ios,
                        )))
              ]);
            }
            ProdModel prodModel = model.prodModel;
            //-1即将推出 0:正常 1：下架
            if (prodModel.state == 1) {
              return Stack(children: [
                PagePlhdWidget(
                  message: YLocal.of(context).dangqianyingyongyixi,
                  iconData: IconFonts.iconApps,
                  imgWidth: 56.0,
                ),
                Positioned(
                    top: 10,
                    left: 15,
                    child: IconButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: Icon(
                          Icons.arrow_back_ios,
                        )))
              ]);
            } else if (prodModel.state == -1) {
              return Stack(
                children: [
                  SingleChildScrollView(
                    controller: _scrollController,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        bannerWidget(viewModel: model),
                        Container(
                          height: 28,
                          margin: EdgeInsets.fromLTRB(16, 10, 16, 10),
                          padding: EdgeInsets.zero,
                          alignment: Alignment.centerLeft,
                          width: Global.screenWidth - 120,
                          child: AutoSizeText(prodModel.name ?? "",
                              maxLines: 1,
                              style: AppStyle.style_textTitle_w600_20pt()),
                        ),
                        Container(
                            margin: EdgeInsets.fromLTRB(16, 5, 16, 20),
                            alignment: Alignment(-1, -1),
                            child: Text(
                              prodModel.brief ?? "",
                              style: AppStyle.style_textSub_w400_14pt(),
                            )),
                      ],
                    ),
                  ),
                  topNavWidget(prodModel: prodModel)
                ],
              );
            }
            if (_isOddNumber % 2 == 0) {
              _isOddNumber++;
              reqCommentListData(viewModel: model);
            }
            const String message = "message";
            //支持退款申请
            _prodInfoMap[0]
                .putIfAbsent(message, () => YLocal.of(context).YVRRefundPolicy);
            //所需空间
            _prodInfoMap[6].putIfAbsent(message,
                () => (prodModel.size / 1000 / 1000).toStringAsFixed(1) + "M");
            //网络模式
            _prodInfoMap[2].putIfAbsent(message, () => prodModel.nMode);
            //发布时间
            _prodInfoMap[7].putIfAbsent(
                message,
                () => (prodModel.prepub == 1
                    ? YLocal.of(context).daifabudaifeibu
                    : DateTimeUtils.formatToLocalFromDateTimeText(
                        prodModel.pTime, 'yyyy-MM-dd HH:mm:ss')));
            //开发者
            _prodInfoMap[3].putIfAbsent(message, () => prodModel.developer);
            //发行商
            _prodInfoMap[4].putIfAbsent(message, () => prodModel.publisher);
            //权限要求
            _prodInfoMap[8].putIfAbsent(
                message,
                () => prodModel.authority =
                    prodModel.authority.replaceAll('权限', ''));
            //语言
            _prodInfoMap[1].putIfAbsent(message, () => prodModel.lang);
            //版本
            _prodInfoMap[5].putIfAbsent(message, () => prodModel.ver);
            //平台政策
            // _prodInfoMap[8].putIfAbsent(message, () => YLocal.of(context).prod_detail);
            String updateInfo =
                "${YLocal.of(context).banben}${prodModel.ver}      ${prodModel.prepub == 1 ? YLocal.of(context).daifabudaifeibu : DateTimeUtils.formatToLocalFromDateTimeText(prodModel.time, 'yyyy/MM/dd HH:mm:ss')}";

            // ignore: deprecated_member_use
            List<Widget> prodInfoViews = [];
            for (var i = 0; i < _prodInfoMap.length; i++) {
              prodInfoViews.add(ProdInfoWidget(
                model: _prodInfoMap[i],
                htmlCallBack: () {
                  // model.isShowVideo.value = false;
                },
                restartPlay: () {
                  // model.isShowVideo.value = true;
                },
              ));
            }
            List labelList = symbolLabelsForProd(tags: prodModel.tag);
            return Stack(
              children: [
                SingleChildScrollView(
                  controller: _scrollController,
                  child: Column(
                    children: [
                      bannerWidget(viewModel: model),
                      Container(
                        margin:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 20),
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(children: [
                                Expanded(
                                    child: Text(prodModel.name ?? "",
                                        style: AppStyle
                                            .style_textTitle_w600_20pt())),
                                Text(
                                  prodModel.aver == null
                                      ? '0'
                                      : prodModel.aver.toString(),
                                  style: AppStyle.style_textTitle_w600_20pt(),
                                ),
                              ]),
                              SizedBox(height: 12),
                              Row(children: [
                                Expanded(
                                    child: Text(labelList.join(" | "),
                                        style: AppStyle
                                            .style_textWeak_w400_12pt())),
                                RatingBar(
                                  initialRating: prodModel.aver == null
                                      ? 0
                                      : ((prodModel.aver.floor() ==
                                              prodModel.aver.round())
                                          ? prodModel.aver
                                          : (prodModel.aver.floor() + 0.5)),
                                  allowHalfRating: true,
                                  itemCount: 5,
                                  itemSize: 12,
                                  ignoreGestures: true,
                                  ratingWidget: ratingWidget,
                                  itemPadding:
                                      EdgeInsets.symmetric(horizontal: 2.0),
                                  onRatingUpdate: (double value) {},
                                )
                              ]),
                              SizedBox(height: 20),
                              Container(
                                alignment: Alignment(-1, -1),
                                child: ReadMoreText(
                                  text: prodModel.brief ?? "",
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  expandButtonText: YLocal.of(context).zhankai,
                                  buttonBackgroundColor: AppColors.colorFFFFFF,
                                  style: AppStyle.style_textSub_w400_14pt(),
                                  buttonStyle:
                                      AppStyle.style_standard_w400_14pt(),
                                  closeButtonText: YLocal.of(context).shouqi,
                                ),
                              ),
                              SizedBox(height: 32),
                              Text(YLocal.of(context).home_recently_update,
                                  style: AppStyle.style_textTitle_w600_18pt()),
                              SizedBox(height: 12),
                              Text(updateInfo,
                                  style: AppStyle.style_textWeak_w400_14pt()),
                              SizedBox(height: 12),
                              Visibility(
                                visible: prodModel.vnote != null,
                                child: Container(
                                  alignment: Alignment(-1, -1),
                                  child: ReadMoreText(
                                    text: prodModel.vnote ?? "",
                                    maxLines: 3,
                                    overflow: TextOverflow.ellipsis,
                                    expandButtonText:
                                        YLocal.of(context).zhankai,
                                    buttonBackgroundColor:
                                        AppColors.colorFFFFFF,
                                    style: AppStyle.style_textSub_w400_14pt(),
                                    buttonStyle:
                                        AppStyle.style_standard_w400_14pt(),
                                    closeButtonText: YLocal.of(context).shouqi,
                                  ),
                                ),
                              ),
                              SizedBox(height: 32),
                              Column(
                                children: _commentList,
                              ),
                              ValueListenableBuilder<bool>(
                                  key: ValueKey(prodModel),
                                  valueListenable: _isPaySuccess,
                                  builder: (context, value, child) {
                                    return ((value &&
                                            StorageManager.localStorage
                                                    .getItem(kToken) !=
                                                null &&
                                            !Provider.of<UserIsComment>(context,
                                                    listen: false)
                                                .getIsComment(prodModel.id))
                                        ? commentButton(viewModel: model)
                                        : SizedBox(
                                            height: 0,
                                          ));
                                  }),
                              SizedBox(height: 32),
                              Text(
                                YLocal.of(context).home_app_info,
                                style: AppStyle.style_textTitle_w600_20pt(),
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: prodInfoViews,
                              ),
                              SizedBox(
                                height: 120,
                              )
                            ]),
                      ),
                    ],
                  ),
                ),
                Positioned(
                  left: 0,
                  bottom: 0,
                  child: prodBottomButton(viewModel: model),
                ),
                topNavWidget(prodModel: prodModel)
              ],
            );
          },
        ));
  }

  Widget bannerWidget({ProdVModel viewModel}) {
    ProdModel prodModel = viewModel.prodModel;
    // Swiper 放在非body中 需要Container包裹
    return Container(
        width: double.infinity,
        height: Global.screenWidth * (9 / 16),
        child: Stack(children: [
          // 不可见视图，帮助预加载广告图
          Offstage(
            offstage: true,
            child: Column(
                children: List.generate(
              prodModel?.pics?.length,
              (picIdx) => NetworkImageWidget(
                prodModel.pics[picIdx] ?? '',
                width: Global.screenWidth,
                height: Global.screenWidth * 9 / 16,
              ),
            )),
          ),

          new Swiper(
            loop: true,
            itemCount: prodModel.pics.length,
            onIndexChanged: (index) {
              _bannerIndex = index;
              playVideo(false);
              if (index < prodModel.vedios.length) {
                playVideo(true);
                orienTool.freeScreen();
              } else {
                _exitTimer?.cancel();
                orienTool.portraitScreen();
              }
            },
            onTap: (index) {
              if (index < prodModel.vedios.length) {
                playVideo(true);
              }
            },
            itemBuilder: (BuildContext context, int index) {
              return bannerItem(
                  picIdx: index,
                  prodModel: prodModel,
                  isShowVideo: viewModel.isShowVideo);
            },
            pagination: SwiperCustomPagination(
                builder: (BuildContext context, SwiperPluginConfig config) {
              // Obx中监听条件必须为可监听 obs
              // If you need to update a parent widget and a child widget, wrap each one in an Obx/GetX.
              final RxInt _activeIndex = config.activeIndex.obs;
              return Obx(() => (_activeIndex >= prodModel.vedios.length ||
                      _viewModel.hideStuff.value)
                  ? Container(
                      alignment: Alignment.bottomCenter,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 30, vertical: 5),
                      child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: PageViewIndicator(
                            currentItemColor: AppColors.standard,
                            otherItemColor: Color(0xFF808080),
                            length: config.itemCount,
                            otherItemWidth: 6,
                            otherItemHeight: 6,
                            currentItemWidth: 20,
                            currentItemHeight: 6,
                            currentIndex: config.activeIndex,
                          )))
                  : const SizedBox());
            }),
          )
        ]));
  }

  Widget bannerItem({
    int picIdx,
    ProdModel prodModel,
    ValueNotifier<bool> isShowVideo,
  }) {
    List _picListOnly = prodModel.pics;
    if (prodModel.vedios.length > 0) {
      // 排除视频个数后的图片数组
      _picListOnly =
          _picListOnly.sublist(prodModel.vedios.length, prodModel.pics.length);
    }

    return ValueListenableBuilder<bool>(
        valueListenable: isShowVideo,
        builder: (context, isPlay, child) {
          Widget picView;
          if (picIdx < prodModel.vedios.length) {
            bool localIsMute =
                DBUtil.instance.projectConfigBox.get("ProdVideoIsMute") ?? true;
            picView = Stack(
              children: [
                VideoPlayerWidget(
                  bottomPadding: 100,
                  fit: FijkFit.ar16_9,
                  isMute: localIsMute,
                  title: prodModel.name,
                  //thumPicUrl: prodModel.pics[picIdx],
                  videoUrl: prodModel.vedios[picIdx].vedio,
                  isAllowPortraitFullScreen: false,
                  getHideStuff: (bool hideStuff) {
                    _viewModel.hideStuff.value = hideStuff;
                  },
                  playerCallBack: (FijkPlayer player) {
                    _player = player;
                    Future.delayed(const Duration(milliseconds: 500),
                        () => isShowVideo.value = true);
                  },
                  fullScreenCallBack: (bool isClickFull) {
                    if (isClickFull) {
                      orienTool.landscapeScreen();
                      _exitTimer?.cancel();
                    } else {
                      orienTool.portraitScreen();
                    }
                  },
                ),
                Visibility(
                  visible: !isPlay,
                  child: Positioned(child: VideoWaitWidget()),
                ),
              ],
            );
          } else {
            picView = GestureDetector(
              child: NetworkImageWidget(
                prodModel.pics[picIdx] ?? '',
                width: Global.screenWidth,
                height: Global.screenWidth * 9 / 16,
              ),
              onTap: () {
                Global.navigatorKey.currentState?.push(
                  ImageBrowserPageRoute(builder: (BuildContext context) {
                    return NetworkImageBrowserPage(
                      images: _picListOnly.cast<String>(),
                      index: picIdx - prodModel.vedios.length,
                    );
                  }),
                );
              },
            );
          }
          return picView;
        });
  }

  Widget topNavWidget({ProdModel prodModel}) {
    return ValueListenableBuilder<bool>(
      key: ValueKey(prodModel),
      valueListenable: _isShowTopNav,
      builder: (context, value, child) {
        if (_isShowTopNav.value) {
          return Positioned(
              top: 0,
              left: 0,
              child: Container(
                width: Global.screenWidth,
                height: kToolbarHeight,
                color: AppColors.colorFFFFFF,
                child: Stack(children: [
                  Container(
                    alignment: Alignment.center,
                    padding: EdgeInsets.symmetric(horizontal: 40),
                    child: TextScroll(
                      prodModel.name ?? "",
                      delayBefore: Duration(milliseconds: 1000),
                      numberOfReps: 2,
                      textAlign: TextAlign.center,
                      style:
                          TextStyle(fontSize: 18, color: AppColors.textTitle),
                    ),
                  ),
                  Positioned(
                    left: 15,
                    child: Container(
                      height: kToolbarHeight,
                      alignment: Alignment.center,
                      child: IconButton(
                          onPressed: () {
                            setState(() {
                              Navigator.pop(context);
                            });
                          },
                          icon: Icon(
                            Icons.arrow_back_ios,
                            color: AppColors.textSub,
                          )),
                    ),
                  )
                ]),
              ));
        } else {
          return Positioned(
              top: 10,
              left: 11,
              child: GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SizedBox(
                  width: 30,
                  height: 30,
                  child: SvgPicture.asset(
                    'assets/svg/ic_detail_back.svg',
                  ),
                ),
              ));
        }
      },
    );
  }

  void reqCommentListData({ProdVModel viewModel}) {
    ProdModel prodModel = viewModel.prodModel;
    http.post<Map>('vrmcsys/appstore/getAppComments', data: {
      "appId": prodModel.id,
      "cmtId": 0,
      "star": 0,
      "size": 4
    }).then((response) async {
      if (response.data["errCode"] == 0) {
        List<Widget> tempCommentList = [];
        await StorageManager.localStorage.setItem(
            "UserIsComment_${prodModel.id}",
            response.data["status"] == 1 ?? false);

        List<CommentModel> tempList = response.data["comments"]
            .map<CommentModel>((item) => CommentModel.fromJson(item))
            .toList();

        Widget commentTitleRow = Container(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(YLocal.of(context).prod_ratings_reviews,
                  style: AppStyle.style_textTitle_w600_18pt()),
              if (tempList.length > 0)
                InkWell(
                  onTap: () {
                    //viewModel.isShowVideo.value = false;
                    DataRecord().saveData(
                        eventId:
                            "assistant_appstore_appDetail_ratings_all_pit_click",
                        extraData: {"appId": widget.arguments["id"]});

                    Navigator.pushNamed(context, "/comment", arguments: {
                      "prodModel": prodModel,
                    }).then((value) {
                      setState(() {
                        _isOddNumber = 2;
                      });
                    });
                  },
                  child: Text(
                    YLocal.of(context).home_view_all,
                    style: AppStyle.style_textTitle_w400_20pt(),
                  ),
                )
            ],
          ),
        );
        tempCommentList.add(commentTitleRow);

        if (prodModel.count > 0) {
          tempCommentList.add(SizedBox(height: 20));
          tempCommentList.add(StarJudge(
            aver: prodModel.aver,
            peoples: prodModel.count,
            starList: [
              prodModel.star5,
              prodModel.star4,
              prodModel.star3,
              prodModel.star2,
              prodModel.star1,
            ],
          ));
        }

        tempCommentList.add(SizedBox(height: 24));

        if (tempList.length == 0) {
          tempCommentList.add(Container(
            alignment: Alignment.center,
            margin: EdgeInsets.only(top: 24),
            child: Column(
              children: [
                SvgPicture.asset(
                  "assets/svg/prod_no_comment.svg",
                  width: 77,
                  height: 77,
                ),
                SizedBox(
                  height: 20,
                ),
                Text(YLocal.of(context).prod_sofa,
                    style: AppStyle.style_textSub_w400_14pt())
              ],
            ),
          ));
        } else {
          for (var i = 0; i < min(tempList.length, 3); i++) {
            tempCommentList.add(CommentCell(
              commentModel: tempList[i],
              reportClick: (var reason) {
                DataRecord().saveData(
                    eventId:
                        "assistant_appstore_appDetail_comment_report_pit_click",
                    extraData: {
                      "appId": prodModel.id,
                      "commentId": tempList[i].id,
                      "reason": reason ?? ""
                    });
              },
            ));
          }
          tempCommentList.toList();
          if (tempList.length > 3) {
            tempCommentList.add(InkWell(
                onTap: () {
                  //viewModel.isShowVideo.value = false;
                  DataRecord().saveData(
                      eventId:
                          "assistant_appstore_appDetail_ratings_all_pit_click",
                      extraData: {"appId": widget.arguments["id"]});
                  Navigator.pushNamed(context, '/comment', arguments: {
                    "prodModel": prodModel,
                  }).then((value) {
                    setState(() {
                      _isOddNumber = 2;
                    });
                  });
                },
                child: Container(
                  width: double.infinity,
                  margin: EdgeInsets.only(top: 21),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(YLocal.of(context).home_view_all_comments,
                          style: AppStyle.style_standard_w400_14pt()),
                      SizedBox(width: 10),
                      SvgPicture.asset(
                        "assets/svg/ic_arrow_blue.svg",
                        width: 9,
                        height: 9,
                      ),
                    ],
                  ),
                )));
          }
        }

        if (prodModel.purchased == 1 &&
            DBUtil.instance.userBox.get(kToken) != null &&
            !Provider.of<UserIsComment>(context, listen: false)
                .getIsComment(prodModel.id)) {
          tempCommentList.add(commentButton(viewModel: viewModel));
        }
        setState(() {
          _commentList = tempCommentList;
        });
      }
    });
  }

  Widget commentButton({ProdVModel viewModel}) {
    ProdModel prodModel = viewModel.prodModel;
    return Padding(
      padding: EdgeInsets.only(top: 21),
      child: CommonButton(
          text: YLocal.of(context).home_comment,
          onTap: () {
            //viewModel.isShowVideo.value = false;
            DataRecord().saveData(
                eventId:
                    "assistant_appstore_appDetail_comment_commentMenu_pit_click",
                extraData: {"appId": widget.arguments["id"]});
            Navigator.pushNamed(context, '/judge',
                    arguments: {"appId": prodModel.id, "name": prodModel.name})
                .then((param) {
              Log.d('评论返回 ==============>$param');
              Map judgeParam = param;
              // 更新评分数据模型
              if (judgeParam["star"] != 0) {
                prodModel.count += 1;
                switch (judgeParam["star"]) {
                  case 1:
                    prodModel.star1 += 1;
                    break;
                  case 2:
                    prodModel.star2 += 1;
                    break;
                  case 3:
                    prodModel.star3 += 1;
                    break;
                  case 4:
                    prodModel.star4 += 1;
                    break;
                  case 5:
                    prodModel.star5 += 1;
                    break;
                  default:
                    break;
                }
                // 保留一位小数
                // (NumUtil.getNumByValueDouble(aver, 1));
                // 评分计算规则： 贝叶斯算法公式
                /*if(judgeParam["aver"] != null){
                  prodModel.aver = judgeParam["aver"].toDouble();
                }*/
              }
              reqCommentListData(viewModel: viewModel);
            });
          }),
    );
  }

  ///purchased 0未购买 1已购买 2退款中
  Widget prodBottomButton({ProdVModel viewModel}) {
    ProdModel prodModel = viewModel.prodModel;
    double paddingBottom = Platform.isIOS ? Global.paddingBottom : 0;
    return ValueListenableBuilder<bool>(
        key: ValueKey(prodModel),
        valueListenable: _isPaySuccess,
        builder: (context, value, child) {
          return Container(
            width: Global.screenWidth,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: AppColors.colorFFFFFF,
              boxShadow: [
                BoxShadow(
                  color: AppColors.color000000_5, //底色,阴影颜色
                  offset: Offset(0, -2), //阴影位置,从什么位置开始
                  blurRadius: 8, //阴影模糊层度
                  spreadRadius: 0,
                ) //阴影模糊大小
              ],
            ),
            child: Column(
              children: [
                if (prodModel.hasCoupon && !value && prodModel.purchased <= 0)
                  Container(
                    height: 22,
                    width: Global.screenWidth,
                    alignment: Alignment.bottomCenter,
                    child: Text(
                      YLocal.of(context).keshiyongyouhuquandu,
                      style: TextStyle(fontSize: 12, color: Color(0xFFB0B2B8)),
                    ),
                  ),
                Container(
                    width: Global.screenWidth,
                    alignment: Alignment(0, -1),
                    color: AppColors.colorFFFFFF,
                    // padding: EdgeInsets.symmetric(vertical: 12,horizontal: 16),
                    padding: EdgeInsets.only(
                        left: 16,
                        right: 16,
                        top: 12,
                        bottom: 12 + paddingBottom),
                    child: (prodModel.purchased == 0 &&
                            ObjectUtil.isNotEmpty(prodModel.discount) &&
                            prodModel.discount != '1' &&
                            // !prodModel.hasCoupon &&
                            !value)
                        ? bottomBtnHaveDiscount(context, viewModel: viewModel)
                        : Container(
                            height: 48,
                            width: Global.screenWidth - 30,
                            child: ((prodModel.purchased != 0 || value)
                                ? bottomBtnWithPurchased(prodModel.purchased)
                                : payBottomBtn(
                                    context,
                                    viewModel: viewModel,
                                    eventType: 10,
                                  ))))
              ],
            ),
          );
        });
  }

  Widget bottomBtnWithPurchased(int purchased) {
    return Text(
      purchased == 2
          ? YLocal.of(context).tuikuanzhong
          : YLocal.of(context).qingqianwangVRyanjin,
      textAlign: TextAlign.center,
      style: AppStyle.style_textSub_w400_16pt(),
    );
  }

  Widget bottomBtnHaveDiscount(
    BuildContext context, {
    @required ProdVModel viewModel,
    String payText = "",
    int eventType = 10,
  }) {
    ProdModel prodModel = viewModel.prodModel;
    String discount = "1";
    String symbol = (prodModel.currency == 1) ? "\$" : "￥";
    if (prodModel.discount != null &&
        ObjectUtil.isNotEmpty(prodModel.discount)) {
      discount =
          (Decimal.parse(prodModel.discount) * Decimal.parse("10")).toString();
    }
    return Flex(
        direction: Axis.horizontal,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CommonButton(
                text: YLocal.of(context).NsheNzhe('${discount ?? ""}'),
                width: 50,
                height: 22,
                fontSize: 14,
                radius: 4,
                isGradient: false,
                isShowBorder: true,
                borderWidth: 1,
                textColor: AppColors.color5EA4E6,
                borderColor: AppColors.color5EA4E6_80,
                secondaryBg: AppColors.color5EA4E6_10,
              ),
              SizedBox(height: 6),
              Text(
                "$symbol${MoneyUtil.changeF2Y(prodModel.bprice)} ",
                style: AppStyle.style_textWeak_w400_14pt_line(),
              ),
            ],
          ),
          SizedBox(
            width: 12,
          ),
          Expanded(
              flex: 1,
              child: Container(
                  height: 48,
                  child: payBottomBtn(context,
                      viewModel: viewModel,
                      payText: payText,
                      eventType: eventType)))
        ]);
  }

  ///eventType: 10弹框选择支付方式，11直接微信支付，12支付宝支付，13Y币充足直接支付，14Y币不足跳转充值
  Widget payBottomBtn(
    BuildContext context, {
    ProdVModel viewModel,
    String payText = "",
    @required int eventType,
  }) {
    var prodModel = viewModel.prodModel;
    String symbol = (prodModel.currency == 1) ? "\$ " : "￥ ";
    if (payText.isEmpty) {
      payText = (prodModel.sprice == 0)
          ? YLocal.of(context).home_free
          : "${MoneyUtil.changeF2Y(prodModel.sprice)}";
    }
    return CommonButton(
      textWidget:
          Column(mainAxisAlignment: MainAxisAlignment.center, children: [
        Text.rich(TextSpan(
            text: prodModel.sprice == 0 ? '' : symbol,
            style: AppStyle.style_ffffff_bold_12pt(),
            children: [
              TextSpan(
                text: payText,
                style: AppStyle.style_ffffff_bold_18pt(),
              )
            ])),
        CountdownWidget(
          countdownText: Provider.of<ProdVModel>(context).countdownTimeText,
        ),
      ]),
      onTap: () async {
        DataRecord().saveData(
            eventId: "assistant_appstore_appDetail_bottom_buyButton_pit_click",
            extraData: {
              "appId": _prodModel.id,
            });
        if (DBUtil.instance.userBox.get(kToken) == null) {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (_) {
                return CustomDialog(
                  title: YLocal.of(context).wenxindishiwenxintis,
                  content: YLocal.of(context).xuyaodenglucainenggo,
                  confirmText: YLocal.of(context).qudenglu,
                  height: 180,
                  confirmCallback: () {
                    //viewModel.isShowVideo.value = false;
                    Navigator.pushNamed(context, '/login');
                  },
                );
              });
        } else {
          //viewModel.isShowVideo.value = false;
          if (prodModel.sprice == 0) {
            if (await isDeviceAlreadyBound()) {
              reqPayParams(0);
            }
          } else {
            switch (eventType) {
              case 10:
                YvrToast.showLoading(message: "");
                if (await isDeviceAlreadyBound()) {
                  YvrToast.dismiss();
                  playVideo(false);
                  PayBottomSheet.showPay(
                      context, PayModel.fromJson(viewModel.prodModel.toJson()),
                      payCallback: (isSuccess) {
                    _isPaySuccess.value = isSuccess;
                  });
                }
                YvrToast.dismiss();
                break;
              default:
            }
          }
        }
      },
    );
  }

  reqPayParams(int type, {int couponType = 0, int couponId = 0}) {
    playVideo(false);
    String couponName;
    if (_selCouponId != 0) {
      type = 3;
      couponType = 1;
      couponId = _selCouponId;
      couponName = _selectedCouponName;
    }
    Map reqData = {
      "appInfos": [
        {
          "appId": _prodModel.id,
          "promId": _prodModel.promId,
          "couponType": couponType,
          "couponId": couponId
        }
      ],
      "source": 0,
      "type": type,
      "country": -1,
      "currency": _prodModel.currency,
    };

    YvrToast.showLoading();
    Future.delayed(Duration(seconds: 3), () {
      YvrToast.dismiss();
    });
    http.post<Map>('vrmcsys/appstore/wxPay', data: reqData).then((response) {
      if (response.data["errCode"] == 0) {
        switch (type) {
          case 0:
            handlePaySuccess();
            Navigator.pushNamed(context, '/pay_result', arguments: {
              "appPayType": PayWayType.payForWechat,
              "price": "0",
              "appId": _prodModel.id,
              "currency": _prodModel.currency,
            });
            break;
          case 1:
            Navigator.pop(context);
            weChatPay(response.data);
            break;
          case 2:
            Navigator.pop(context);
            aliPay();
            break;
          case 3:
            Navigator.pop(context);
            handlePaySuccess();
            String priceText =
                (NumUtil.getNumByValueDouble(_prodModel.sprice / 10, 0))
                    .toString();

            Navigator.pushNamed(context, '/pay_result', arguments: {
              "appPayType": (couponId != 0)
                  ? PayWayType.payForCoupon
                  : PayWayType.payForYCoin,
              "appId": _prodModel.id,
              "price": (couponId != 0) ? couponName : priceText,
              "currency": _prodModel.currency,
            });
            break;
          default:
        }
      } else {
        YvrToast.showToast(YLocal.of(context).zhifushibai);
      }
    });
  }

  aliPay() {}

  weChatPay(result) async {}

  handlePaySuccess() {
    playVideo(false);
    _isPaySuccess.value = true;
    String ntyId =
        StorageManager.localStorage.getItem("NotiIdForApp_${_prodModel.id}") ??
            null;
    if (ntyId != null) {
      notiPayResultForVR(ntyId: ntyId, isPay: true, appId: _prodModel.id);
      YvrRequests.disposeNotify(
          ntyId: int.parse(ntyId), tag: "recvAppPayOnMobile", mark: 1);
    }
    YvrToast.dismiss();
    eventBus.fire(EventFn({Global.kRefreshHome: true}));
  }

  Future<bool> isDeviceAlreadyBound() async {
    bool isGotoPay = true;
    try {
      List devs = await YvrRequests.getLoginDevIds();
      if (devs.length == 0) {
        isGotoPay = false;
        showDialog(
            context: context,
            barrierDismissible: false,
            builder: (_) {
              return CustomDialog(
                title: YLocal.of(context).mofagoumaigaiyingyon,
                content: YLocal.of(context).goumaiyingyongqianqi,
                confirmText: YLocal.of(context).tianjiashebei,
                isCancel: true,
                confirmCallback: () {
                  eventBus.fire(EventFn({'selectedTabbarIndex': 2}));
                },
              );
            });
      }
    } catch (e) {
      YvrToast.showToast(YLocal.of(context).zanshimofahuoqushebe);
    }
    return isGotoPay;
  }
}

class PayInfoMap {
  PayInfoMap({this.eventType, this.reqType, this.text});
  int eventType;
  int reqType;
  String text;
}
