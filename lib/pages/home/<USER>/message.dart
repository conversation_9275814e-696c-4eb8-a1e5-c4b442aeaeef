import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:yvr_assistant/base/lifecycle_state_mixin.dart';
import 'package:yvr_assistant/public_ui/widget/refresh_layout.dart';
import 'package:yvr_assistant/styles/app_divider.dart';
import 'package:yvr_assistant/utils/data_record.dart';
import 'package:common_utils/common_utils.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/model/noti_model.dart';
import 'package:yvr_assistant/model/noti_pay_model.dart';
import 'package:yvr_assistant/manager/resource_mananger.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/pages/home/<USER>/pay_noti_view.dart';
import 'package:yvr_assistant/pages/social/views/plhd_view.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/view_model/noti_vmodel.dart';
import 'package:yvr_assistant/pages/profile/pay/tool/wallet_tool.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/message_cell.dart';

class MessagePage extends StatefulWidget {
  MessagePage({Key key}) : super(key: key);

  @override
  _MessagePageState createState() => _MessagePageState();
}

class _MessagePageState extends LifecycleState<MessagePage>
    with SingleTickerProviderStateMixin, RouteAware {
  NotiVModel _viewModel;

  @override
  void initState() {
    super.initState();
    _viewModel = NotiVModel();
  }

  @override
  void onRefresh() {
    _viewModel.refreshQuietly();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TopNavbar(title: YLocal.of(context).home_noti_center),
      body: ChangeNotifierProvider<NotiVModel>.value(
        value: _viewModel,
        child: SimpleRefreshList<NotiVModel>(
            model: _viewModel,
            emptyBuilder: (context, model) {
              return Center(
                child: PagePlhdWidget(
                    message: YLocal.of(context).plhd_no_msg,
                    iconData: IconFonts.iconRemind,
                    imgWidth: 56.0),
              );
            },
            builder: (context, model) {
              return ListView.separated(
                itemBuilder: (context, index) {
                  var data = model.model[index];
                  return MessageCell(
                    key: ValueKey(data),
                    state: model.progressState(data),
                    notiModel: data,
                    handleMsgTap: (mark) {
                      DataRecord().saveData(
                          eventId:
                              "assistant_appstore_notifications_list_notification_pit_click",
                          extraData: {
                            "notiId": model.model[index].id,
                          });
                      disposeNotify(
                          itemModel: model.model[index],
                          mark: (mark <= 0) ? 1 : mark,
                          model: model);
                    },
                    onAgreeFriendInvitation: () {
                      model.agreeFriendInvitation(data);
                    },
                  );
                },
                itemCount: model.model.length,
                separatorBuilder: (BuildContext context, int index) =>
                    AppDivider.backgroundDivider,
              );
            }),
      ),
    );
  }

  disposeNotify({NotiModel itemModel, int mark, NotiVModel model}) async {
    if (await YvrRequests.disposeNotify(
            ntyId: itemModel.id, tag: itemModel.tag, mark: mark) ==
        0) {
      if (mark == 0 || mark == 1) {
        switch (itemModel.tag) {
          case "recvFdInvite":
            //等待我方通过  4--好友邀请待通过
            // recvFdInvite status消息状态 若消息被处理过（不为0） 则再次点击该消息不显示待通过状态
            Navigator.pushNamed(context, "/person_home", arguments: {
              "relation": itemModel.status == 0 ? 4 : 0,
              "actId": itemModel.fromId,
              "ntyId": itemModel.id
            });
            break;
          case "recvFdInviteFeedback":
            //对方已通过 好友跳转 1--好友
            Navigator.pushNamed(context, "/person_home",
                arguments: {"relation": 1, "actId": itemModel.fromId});
            break;
          case "recvEventInvite":
          case "recvEventToCome":
          case "recvEventComing":
            // 活动跳转
            Navigator.pushNamed(context, "/activity_detl",
                arguments: {"id": itemModel.eventId});
            break;
          case "recvEventRemove":
            YvrToast.showToast(YLocal.of(context).toast_event_delete);
            break;
          case "recvUserAuditComment":
            // 评论跳转详情页
            Navigator.pushNamed(context, '/prod',
                arguments: {"id": itemModel.appId});
            break;
          case 'recvAppPayOnMobile':
            switch (itemModel.star) {
              case 0:
                Navigator.pushNamed(context, '/prod',
                    arguments: {"id": itemModel.appId});
                break;
              case 1:
                /* Navigator.pushNamed(context, '/bundledApplication',
                    arguments: {'mpId': itemModel.appId});*/
                model.getAppsOnSubject(context, itemModel.appId);
                break;
            }
            break;
          case 'offerCoupon':
            WalletTool().jumpToWalletPage(context, selIdx: 1);
            break;

          default:
        }
      } else if (mark == 2) {
        if (mounted) {
          model.remove(itemModel);
        }
      }
    }
  }
}

class HandelPayMessage {
  static handelMsg() async {
    try {
      List notiModels = await YvrRequests.getAllNtyList(null);
      if (notiModels.length == 0) {
        return;
      }

      List<NotiModel> notiModelsForPay = notiModels
          .where((element) =>
              (element.tag == "recvAppPayOnMobile" && element.state == 0))
          .toList();
      if (notiModelsForPay.length > 0) {
        notiModelsForPay.sort((a, b) => b.time.compareTo((a.time)));
        NotiModel notiModel = notiModelsForPay[0];
        int appTime = DateUtil.getDateMsByTimeStr(notiModel.time);
        int nowTime = DateUtil.getNowDateMs();
        if ((nowTime - appTime > 10 * 60 * 1000) || (notiModel.state == 1)) {
          return;
        }
        StorageManager.localStorage.setItem(
            "NotiIdForApp_${notiModel.appId}", notiModel.id.toString());
        NotiPayModel notiPayModel = notiPayModelFromJson(jsonEncode(notiModel));
        BuildContext context = Global.navigatorKey.currentState.overlay.context;
        showDialog(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) {
              return Center(
                child: PayNotiView(notiPayModel: notiPayModel),
              );
            });
        YvrRequests.disposeNotify(
            ntyId: notiPayModel.id, tag: notiModel.tag, mark: 1);
      }
    } catch (e, s) {
      print(s);
    }
  }
}
