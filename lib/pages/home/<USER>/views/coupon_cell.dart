import 'dart:math';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yvr_assistant/model/user_model.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/public_ui/widget/common_button.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/public_ui/widget/rich_text.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';

import '../../../../generated/l10n.dart';

// ignore: must_be_immutable
class CouponCell extends StatefulWidget {
  bool isSelected;
  final Function optionCouponCallback;
  final AccountCouponDetailList detlModel;

  CouponCell(
      {Key key,
      @required this.isSelected,
      this.detlModel,
      this.optionCouponCallback})
      : super(key: key);

  @override
  State<CouponCell> createState() => _CouponCellState();
}

class _CouponCellState extends State<CouponCell> {
  @override
  Widget build(BuildContext context) {
    AccountCouponDetailList detlModel = widget.detlModel;
    // double cardW = (Global.screenWidth - 20 * 2);
    String endDate = DateUtil.formatDateMs(detlModel.endTime.toInt(),
        format: "yyyy.MM.dd HH:mm");
    return Opacity(
        opacity: detlModel.canUse ? 1 : 0.4,
        child: GestureDetector(
            onTap: () {
              if (detlModel.canUse) {
                widget.isSelected = !widget.isSelected;
                widget.optionCouponCallback(
                    widget.isSelected,
                    widget.detlModel.couponId,
                    widget.detlModel.couponInfo.name);
              }
            },
            child: Container(
                width: double.infinity,
                margin: EdgeInsets.only(bottom: 20),
                alignment: Alignment.topLeft,
                decoration: BoxDecoration(
                  color: AppColors.colorFFFFFF,
                  borderRadius: BorderRadius.all(
                    Radius.circular(5),
                  ),
                ),
                child: Container(
                  width: double.infinity,
                  alignment: Alignment.topLeft,
                  padding: EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Image.asset("assets/images/ic_item_coupon.png",
                              width: 20, height: 20),
                          SizedBox(
                            width: 10,
                          ),
                          Text(
                            detlModel.couponInfo.name,
                            style: AppStyle.style_textTitle_w500_14pt(),
                          ),
                          Spacer(),
                          IntlRichText1(
                            intlTextBuilder: YLocal.of(context).Nzhang,
                            defaultStyle: AppStyle.style_textSub_w400_14pt(),
                            param: '${detlModel.surplus} ',
                            paramStyle: AppStyle.style_standard_w400_14pt(),
                          ),
                          SizedBox(width: 12),
                          SvgPicture.asset(
                            "assets/svg/coupon_option${widget.isSelected ? '_s' : ''}.svg",
                            width: 15,
                            height: 15,
                          ),
                        ],
                      ),
                      Container(
                        padding: EdgeInsets.fromLTRB(30, 10, 15, 0),
                        width: double.infinity,
                        child: Text(
                          detlModel.couponInfo.shortDescription,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          style: AppStyle.style_textSub_w400_10pt(),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 10, bottom: 6),
                        child: Container(
                          width: double.infinity,
                          height: 0.5,
                          color: AppColors.textWeak,
                        ),
                      ),
                      Container(
                        width: double.infinity,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(YLocal.of(context).youxiaoqizhi + endDate,
                                style: AppStyle.style_textWeak_w400_10pt()),
                            CommonButton(
                              text: YLocal.of(context).guizexiangqing,
                              width: 66,
                              height: 25,
                              fontSize: 10,
                              radius: 4,
                              isGradient: false,
                              textColor: AppColors.standard,
                              secondaryBg: AppColors.standard_10,
                              onTap: () {
                                if (!detlModel.canUse) {
                                  return;
                                }
                                if (detlModel.couponInfo.detailDescription ==
                                    null) {
                                  YvrToast.showToast(
                                      YLocal.of(context).zanmoshiyongshuiming);
                                  return;
                                }
                                double lines = detlModel
                                        .couponInfo.detailDescription.length /
                                    20;
                                lines = max(3, lines);
                                lines = min(10, lines);
                                showDialog(
                                    context: context,
                                    barrierDismissible: false,
                                    builder: (_) {
                                      return CustomDialog(
                                        height: (150 + lines * 15),
                                        title:
                                            YLocal.of(context).guizexiangqing,
                                        dialogType: DialogType.DialogDIY,
                                        contentWidget: Container(
                                          height: (lines * 15),
                                          margin: EdgeInsets.only(bottom: 20),
                                          child: SingleChildScrollView(
                                            child: Text(
                                                detlModel.couponInfo
                                                    .detailDescription,
                                                style: AppStyle
                                                    .style_textTitle_w400_14pt()),
                                          ),
                                        ),
                                        isCancel: false,
                                        confirmText:
                                            YLocal.of(context).zhidaoliao,
                                        confirmCallback: () {
                                          Navigator.pop(context);
                                        },
                                      );
                                    });
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ))));
  }
}
