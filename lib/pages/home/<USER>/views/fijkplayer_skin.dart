import 'dart:ui';
import 'dart:math';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:fijkplayer/fijkplayer.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';

// ignore: must_be_immutable
class CustomFijkPanel extends StatefulWidget {
  final FijkPlayer player;
  final Size viewSize;
  final Rect texturePos;
  final String playerTitle;
  final BuildContext pageContent;
  final BuildContext buildContext;
  // Add Param & Function
  final bool isMute;
  // 用于竖屏全屏时底部控制栏距离
  final double bottomPadding;
  final bool allowFullScreen;
  final Function getHideStuff;
  final Function fullScreenCallBack;
  final bool isAllowPortraitFullScreen;

  CustomFijkPanel({
    @required this.player,
    this.isMute,
    this.viewSize,
    this.texturePos,
    this.pageContent,
    this.playerTitle,
    this.getHideStuff,
    this.buildContext,
    this.allowFullScreen,
    this.fullScreenCallBack,
    this.bottomPadding = 0,
    this.isAllowPortraitFullScreen = true,
  });

  @override
  _CustomFijkPanelState createState() => _CustomFijkPanelState();
}

class _CustomFijkPanelState extends State<CustomFijkPanel> {
  FijkPlayer get player => widget.player;

  Duration _duration = Duration();
  Duration _currentPos = Duration();
  Duration _bufferPos = Duration();
  bool _playing = false;
  bool _prepared = false;
  String _exception;
  // ignore: avoid_init_to_null
  num startPosX = null;
  // ignore: avoid_init_to_null
  num startPosY = null;
  Size playerBoxSize;
  // bool _buffering = false;
  double _seekPos = -1.0;
  StreamSubscription _currentPosSubs;
  StreamSubscription _bufferPosSubs;
  //StreamSubscription _bufferingSubs;

  Timer _hideTimer;
  bool _hideStuff = true;
  bool _lockStuff = true;
  // ignore: unused_field
  double _volume = 1.0;
  final barHeight = 70.0;
  double bottomBarMargin = 20;
  bool _isMute = true;
  double _titleTopMargin = MediaQueryData.fromWindow(window).padding.top;

  @override
  void initState() {
    super.initState();

    _isMute = widget.isMute;
    _duration = player.value.duration;
    _currentPos = player.currentPos;
    _bufferPos = player.bufferPos;
    _prepared = player.state.index >= FijkState.prepared.index;
    _playing = player.state == FijkState.started;
    _exception = player.value.exception.message;
    // _buffering = player.isBuffering;
    changeVolume();

    player.addListener(_playerValueChanged);

    _currentPosSubs = player.onCurrentPosUpdate.listen((v) {
      setState(() {
        _currentPos = v;
      });
    });

    _bufferPosSubs = player.onBufferPosUpdate.listen((v) {
      setState(() {
        _bufferPos = v;
      });
    });

    // 计算全屏状态下，上下控制栏左右距离
    double scale = (Global.screenHeight / Global.screenWidth);
    if (scale > (16 / 9)) {
      bottomBarMargin =
          (Global.screenHeight - Global.screenWidth * 16 / 9) / 2 + 20;
    }
  }

  void _playerValueChanged() {
    FijkValue value = player.value;
    if (value.duration != _duration) {
      setState(() {
        _duration = value.duration;
      });
    }

    bool playing = (value.state == FijkState.started);
    bool prepared = value.prepared;
    String exception = value.exception.message;
    if (playing != _playing ||
        prepared != _prepared ||
        exception != _exception) {
      setState(() {
        _playing = playing;
        _prepared = prepared;
        _exception = exception;
      });
    }
  }

  void _playOrPause() {
    if (_playing == true) {
      player.pause();
    } else {
      player.start();
    }
  }

  @override
  void dispose() {
    super.dispose();
    _hideTimer?.cancel();
    player?.removeListener(_playerValueChanged);
    _currentPosSubs?.cancel();
    _bufferPosSubs?.cancel();
  }

  void _startHideTimer() {
    _hideTimer?.cancel();
    _hideTimer = Timer(const Duration(seconds: 3), () {
      setState(() {
        _hideStuff = true;
        if (widget.getHideStuff != null) {
          widget.getHideStuff(true);
        }
      });
    });
  }

  void _cancelAndRestartTimer() {
    if (_hideStuff == true) {
      _startHideTimer();
    }
    setState(() {
      _hideStuff = !_hideStuff;
      if (widget.getHideStuff != null) {
        widget.getHideStuff(_hideStuff);
      }
    });
  }

  // ignore: unused_element
  void _changePlayerLockState() {
    setState(() {
      _lockStuff = !_lockStuff;
      _cancelAndRestartTimer();
    });
  }

  @override
  Widget build(BuildContext context) {
    Rect rect = player.value.fullScreen
        ? Rect.fromLTWH(
            0,
            0,
            widget.viewSize.width,
            widget.viewSize.height,
          )
        : Rect.fromLTRB(
            max(0.0, widget.texturePos.left),
            max(0.0, widget.texturePos.top),
            min(widget.viewSize.width, widget.texturePos.right),
            min(widget.viewSize.height, widget.texturePos.bottom),
          );
    double iconSize = widget.player.value.fullScreen ? 24 : 20;
    bool isPortraitFullScreen =
        (widget.viewSize.width < widget.viewSize.height);
    if (isPortraitFullScreen && !widget.isAllowPortraitFullScreen) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        player.exitFullScreen();
      });
    }

    return Positioned.fromRect(
      rect: rect,
      child: GestureDetector(
        onTap: _cancelAndRestartTimer,
        behavior: HitTestBehavior.opaque,
        child: AbsorbPointer(
          absorbing: _hideStuff && _lockStuff,
          child: Column(
            children: <Widget>[
              // 播放器顶部控制器
              if (widget.player.value.fullScreen)
                _buildTopBar(
                    iconSize: iconSize,
                    isPortraitFullScreen: isPortraitFullScreen),
              // 中间按钮
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    _cancelAndRestartTimer();
                  },
                  child: _buildCenterPlayBtn(),
                ),
              ),
              // 播放器底部控制器
              _buildBottomBar(context,
                  iconSize: iconSize,
                  isPortraitFullScreen: isPortraitFullScreen),
            ],
          ),
        ),
      ),
    );
  }

  void changeVolume() {
    if (_isMute) {
      if (_volume != 0) {
        widget.player.setVolume(0);
      }
    } else {
      if (_volume != 0.5) {
        widget.player.setVolume(0.5);
      }
    }
  }

  // 底部控制栏 - 播放按钮
  Widget _buildPlayStateBtn() {
    IconData iconData = _playing ? Icons.pause : Icons.play_arrow;

    return IconButton(
      icon: Icon(iconData),
      color: Colors.white,
      padding: const EdgeInsets.only(left: 10.0, right: 10.0),
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onPressed: _playOrPause,
    );
  }

  // 居中播放按钮
  Widget _buildCenterPlayBtn() {
    return Container(
      color: Colors.transparent,
      height: double.infinity,
      width: double.infinity,
      child: Center(
        child: _exception != null
            ? Text(
                _exception,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 25,
                ),
              )
            : (_prepared || player.state == FijkState.initialized)
                ? AnimatedOpacity(
                    opacity: _hideStuff ? 0.0 : 0.7,
                    duration: const Duration(milliseconds: 400),
                    child:
                        // 需求隐藏暂停按钮
                        _playing
                            ? const SizedBox()
                            : IconButton(
                                iconSize: 50,
                                icon: Icon(
                                    _playing
                                        ? Icons.pause_sharp
                                        : Icons.play_circle_outline,
                                    color: Colors.white),
                                padding: const EdgeInsets.only(
                                    left: 10.0, right: 10.0, top: 20),
                                onPressed: _playOrPause),
                  )
                : const SizedBox(),
      ),
    );
  }

  // 播放器顶部 返回 + 标题
  Widget _buildTopBar({double iconSize, bool isPortraitFullScreen = false}) {
    double top = 0;
    double left = 20;
    if (widget.player.value.fullScreen) {
      left = bottomBarMargin;
    }
    if (isPortraitFullScreen) {
      left = 20;
      top = _titleTopMargin + 30;
    }

    return AnimatedOpacity(
        opacity: _hideStuff ? 0.0 : 1,
        duration: const Duration(milliseconds: 400),
        child: Container(
            height: barHeight,
            alignment: Alignment.centerLeft,
            margin: EdgeInsets.only(top: top),
            padding: EdgeInsets.fromLTRB(left, 10, 0, 10),
            decoration: BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: const [
                  const Color(0x33000000),
                  const Color(0x00000000)
                ])),
            child: TextButton.icon(
                onPressed: () {
                  if (widget.player.value.fullScreen) {
                    if (widget.fullScreenCallBack != null) {
                      widget.fullScreenCallBack(false);
                    }
                    player.exitFullScreen();
                  }
                },
                label: Text(
                  widget.playerTitle ?? '',
                  style: TextStyle(
                      fontSize: 20,
                      color: Colors.white,
                      fontWeight: FontWeight.w600),
                ),
                icon: Icon(
                  Icons.arrow_back_ios,
                  color: Colors.white,
                  size: 24,
                ))));
  }

  // 控制器ui 底部
  AnimatedOpacity _buildBottomBar(BuildContext context,
      {double iconSize, bool isPortraitFullScreen = false}) {
    double bottom = 0;
    double hMargin = 10;
    if (widget.player.value.fullScreen) {
      bottom = 30;
      hMargin = Global.screenWidth / 5;
    }
    if (isPortraitFullScreen) {
      hMargin = 20;
      bottom = widget.bottomPadding;
    }
    if (!widget.allowFullScreen) {
      hMargin = 10;
      bottom = widget.bottomPadding;
    }
    double duration = _duration.inMilliseconds.toDouble();
    double currentValue =
        _seekPos > 0 ? _seekPos : _currentPos.inMilliseconds.toDouble();
    currentValue = min(currentValue, duration);
    currentValue = max(currentValue, 0);

    return AnimatedOpacity(
      opacity: _hideStuff ? 0.0 : 1,
      duration: const Duration(milliseconds: 400),
      child: Container(
        height: 50,
        padding: EdgeInsets.symmetric(horizontal: hMargin),
        margin: EdgeInsets.only(bottom: bottom),
        child: Row(
          children: <Widget>[
            // 按钮 - 播放/暂停
            _buildPlayStateBtn(),
            // 已播放时间
            Container(
              constraints: BoxConstraints(
                minWidth: 50,
              ),
              child: Text(
                '${_duration2String(_currentPos)}',
                style: TextStyle(
                  fontSize: 14.0,
                  color: Colors.white,
                ),
              ),
            ),
            // 播放进度 if 没有开始播放 占满，空ui， else fijkSlider widget
            Expanded(
              child: _duration.inMilliseconds == 0
                  ? const SizedBox()
                  : FijkSlider(
                      colors: FijkSliderColors(
                        playedColor: Color(0xFF4F7FFE),
                        cursorColor: Color.fromARGB(255, 255, 255, 255),
                        bufferedColor: Color.fromARGB(60, 255, 255, 255),
                        baselineColor: Color.fromARGB(131, 255, 255, 255),
                      ),
                      value: currentValue,
                      cacheValue: _bufferPos.inMilliseconds.toDouble(),
                      min: 0.0,
                      max: duration,
                      onChanged: (v) {
                        _startHideTimer();
                        setState(() {
                          _seekPos = v;
                        });
                      },
                      onChangeEnd: (v) {
                        setState(() {
                          player.seekTo(v.toInt());
                          _currentPos =
                              Duration(milliseconds: _seekPos.toInt());
                          _seekPos = -1;
                        });
                      },
                    ),
            ),
            const SizedBox(width: 15),
            // 总播放时间
            _duration.inMilliseconds == 0
                ? const SizedBox()
                : Text(
                    '${_duration2String(_duration)}',
                    style: TextStyle(
                      fontSize: 14.0,
                      color: Colors.white,
                    ),
                  ),
            GestureDetector(
              child: Padding(
                  padding: const EdgeInsets.only(left: 12),
                  child: Icon(
                    _isMute ? Icons.volume_off : Icons.volume_up,
                    color: Colors.white,
                    size: 20,
                  )),
              onTap: () async {
                _isMute = !_isMute;
                changeVolume();
                DBUtil.instance.projectConfigBox
                    .put("ProdVideoIsMute", _isMute);
              },
            ),

            if (widget.allowFullScreen)
              // 按钮 - 全屏/退出全屏
              IconButton(
                icon: Icon(widget.player.value.fullScreen
                    ? Icons.fullscreen_exit
                    : Icons.fullscreen),
                color: Colors.white,
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                onPressed: () {
                  if (widget.fullScreenCallBack != null) {
                    widget.fullScreenCallBack(!widget.player.value.fullScreen);
                  }
                  widget.player.value.fullScreen
                      ? player.exitFullScreen()
                      : player.enterFullScreen();
                },
              )
            //
          ],
        ),
      ),
    );
  }
}

String _duration2String(Duration duration) {
  if (duration.inMilliseconds < 0) return "-: negtive";

  String twoDigits(int n) {
    if (n >= 10) return "$n";
    return "0$n";
  }

  String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
  String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
  int inHours = duration.inHours;
  return inHours > 0
      ? "$inHours:$twoDigitMinutes:$twoDigitSeconds"
      : "$twoDigitMinutes:$twoDigitSeconds";
}
