import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/resource_mananger.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';

class ProdData {
  List<Map> prodInfoMap = [
    {
      "icon": IconFonts.iconInfo,
      "title": YLocal.current.refundAvailable,
    },
    {
      "icon": IconFonts.iconLanguage,
      "title": YLocal.current.prod_language,
    },
    {
      "icon": IconFonts.iconWifi,
      "title": YLocal.current.prod_network,
    },
    {
      "icon": IconFonts.iconDeveloper,
      "title": YLocal.current.prod_developer,
    },
    {
      "icon": null,
      "title": YLocal.current.prod_publisher,
    },
    {
      "icon": IconFonts.iconUpdate,
      "title": YLocal.current.prod_version,
    },
    {
      "icon": IconFonts.iconMemory,
      "title": YLocal.current.prod_ram,
    },
    {
      "icon": IconFonts.iconClock,
      "title": YLocal.current.prod_release_date,
    },
    {
      "icon": IconFonts.iconSafe,
      "title": YLocal.current.prod_authority,
    },
    /*{
      "icon": IconFonts.iconMemory,
      "title": YLocal.current.prod_ram,
    },
    {
      "icon": IconFonts.iconWifi,
      "title": YLocal.current.prod_network,
    },
    {
      "icon": IconFonts.iconClock,
      "title": YLocal.current.prod_release_date,
    },
    {
      "icon": IconFonts.iconDeveloper,
      "title": YLocal.current.prod_developer,
    },
    //发行商
    {
      "icon": null,
      "title": YLocal.current.prod_publisher,
    },

    {
      "icon": IconFonts.iconSafe,
      "title": YLocal.current.prod_authority,
    },
    {
      "icon": IconFonts.iconLanguage,
      "title": YLocal.current.prod_language,
    },
    {
      "icon": IconFonts.iconUpdate,
      "title": YLocal.current.prod_version,
    },
    {
      "icon": IconFonts.iconInfo,
      "title": YLocal.current.prod_policy,
    }*/
  ];

  aliPay() {}
}

showAppOffTheShelfDialog(BuildContext context) {
  showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) {
        return CustomDialog(
          title: YLocal.current.dishitishi,
          content: YLocal.current.dangqianyingyongyixi,
          isCancel: false,
          confirmText: YLocal.current.fanhuishangyiye,
          height: 200,
          confirmColor: Color(0xFF4F7FFE),
          confirmCallback: () {
            Navigator.pop(context);
          },
        );
      });
}
