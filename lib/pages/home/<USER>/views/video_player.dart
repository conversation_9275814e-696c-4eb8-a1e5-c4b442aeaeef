import 'package:flutter/material.dart';
import 'package:fijkplayer/fijkplayer.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/fijkplayer_skin.dart';

typedef PlayerCallBack = Function(FijkPlayer player);

// Flutter fijkplayer使用/播放RTSP降低延迟
// 原文链接：https://blog.csdn.net/weixin_51522235/article/details/134325064

class VideoPlayerWidget extends StatefulWidget {
  final bool isMute;
  final FijkFit fit;
  final String title;
  final Color backgroundColor;

  /// 默认自动播放、否则根据是否WiFi播放
  final bool autoPlay;
  final String videoUrl;
  final String thumPicUrl;
  final double bottomPadding;
  final bool allowFullScreen;
  final Function getHideStuff;
  final Function fullScreenCallBack;
  final bool isAllowPortraitFullScreen;
  final PlayerCallBack playerCallBack;

  VideoPlayerWidget({
    this.title,
    this.videoUrl,
    this.thumPicUrl,
    this.getHideStuff,
    this.playerCallBack,
    this.fullScreenCallBack,
    this.fit = FijkFit.fitWidth,
    this.isMute = true,
    this.autoPlay = true,
    this.bottomPadding = 0,
    this.allowFullScreen = true,
    this.isAllowPortraitFullScreen = true,
    this.backgroundColor = Colors.black,
  });

  @override
  _VideoPlayerWidgetState createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  final FijkPlayer _player = FijkPlayer();

  @override
  void initState() {
    super.initState();
    FijkLog.setLevel(FijkLogLevel.Error);
    configPlayer();
  }

  @override
  void dispose() {
    _player.release();
    super.dispose();
  }

  void configPlayer() async {
    FijkOption _fijkOptions = FijkOption();

    if (Platform.isAndroid) {
      String mode = await PlatformUtils.getDeviceModel();
      // HUAWEI AGS5-W00 HarmonyOS 3.0.0 高通骁龙680 Pad 不支持硬解码
      if (mode != 'HUAWEI AGS5-W00' || mode != 'HONOR HRY-AL00a') {
        _fijkOptions.setPlayerOption("mediacodec-all-videos", 1);
      }
    }

    _fijkOptions.setPlayerOption("videotoolbox", 1);
    _fijkOptions.setHostOption("enable-snapshot", 1);
    _fijkOptions.setHostOption("request-screen-on", 1);
    _fijkOptions.setHostOption("request-audio-focus", 1);
    _fijkOptions.setFormatOption("dns_cache_clear", 1);
    await _player.applyOptions(_fijkOptions);

    try {
      await _player.setDataSource(widget.videoUrl, autoPlay: widget.autoPlay);
      if (widget.autoPlay) {
        await _player.start();
      } else {
        final connectivityResult = await (Connectivity().checkConnectivity());
        if (connectivityResult == ConnectivityResult.wifi) {
          await _player.start();
        }
      }
    } catch (e) {
      Log.e('FijkPlayer 初始化异常: $e');
    }

    if (widget.playerCallBack != null) {
      widget.playerCallBack(_player);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: FijkView(
          player: _player,
          fit: widget.fit,
          fsFit: FijkFit.ar16_9,
          color: widget.backgroundColor,
          cover: widget.thumPicUrl == null
              ? null
              : CachedNetworkImageProvider(
                  widget.thumPicUrl,
                  maxWidth: Global.cacheWidth,
                ),
          panelBuilder: (
            FijkPlayer player,
            FijkData data,
            BuildContext context,
            Size viewSize,
            Rect texturePos,
          ) {
            return CustomFijkPanel(
                player: player,
                isMute: widget.isMute,
                viewSize: viewSize,
                texturePos: texturePos,
                pageContent: context,
                playerTitle: widget.title,
                getHideStuff: widget.getHideStuff,
                bottomPadding: widget.bottomPadding,
                allowFullScreen: widget.allowFullScreen,
                isAllowPortraitFullScreen: widget.isAllowPortraitFullScreen,
                fullScreenCallBack: widget.fullScreenCallBack);
          },
        ));
  }
}
