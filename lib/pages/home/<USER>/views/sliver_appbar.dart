import 'package:flutter/material.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import '../../../../generated/l10n.dart';
import '../../../../provider/provider_widget.dart';
import '../../../../public_ui/widget/image.dart';
import '../../../../view_model/appbar_vmodel.dart';
import '../../../../view_model/bundled_vmodel.dart';

///捆绑应用详情页appBar
// ignore: must_be_immutable
class BundledSliverAppBar extends StatefulWidget {
  BundledApplicationVModel parentVModel;
  BundledAppbarVModel bundledAppbarVModel;
  BundledSliverAppBar(this.parentVModel, this.bundledAppbarVModel, {Key key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _BundledSliverAppBarState();
  }
}

class _BundledSliverAppBarState extends State<BundledSliverAppBar> {
  @override
  Widget build(BuildContext context) {
    double statusBarHeight = MediaQuery.of(context).padding.top;
    return ProviderWidget<BundledAppbarVModel>(
        model: widget.bundledAppbarVModel,
        builder: (context, model, _widget) => SliverAppBar(
              backgroundColor: AppColors.colorFFFFFF,
              // ignore: deprecated_member_use
              brightness: Brightness.light,
              titleSpacing: 0.0,
              // automaticallyImplyLeading : false,
              leading: Container(
                // padding: EdgeInsets.only(right:model.showTabBar?16:4),
                child: Center(
                  child: IconButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      icon: Icon(
                        Icons.arrow_back_ios,
                        color: Color(0xFF6E7380),
                      )),
                ),
              ),
              centerTitle: true,
              title: Text(
                model.showTabBar ? YLocal.of(context).huodongxiangqing : "",
                style: AppStyle.style_textTitle_w600_18pt(),
              ),
              flexibleSpace: FlexibleSpaceBar(
                // collapseMode: CollapseMode.pin,
                background: Container(
                  width: double.infinity,
                  height: widget.parentVModel.headHeight +
                      kToolbarHeight -
                      statusBarHeight,
                  child: WrapperImage(
                    url: '${widget.parentVModel.bundledInfo.pic}',
                    width: double.infinity,
                    height: double.infinity,
                  ),
                ),
                centerTitle: true,
              ),
              expandedHeight: widget.parentVModel.headHeight +
                  kToolbarHeight -
                  statusBarHeight,
              pinned: true,
            ));
  }
}
