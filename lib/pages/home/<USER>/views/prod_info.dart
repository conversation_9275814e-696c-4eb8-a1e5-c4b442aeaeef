import 'dart:math';

import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/app_navigator.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';

class ProdInfoWidget extends StatefulWidget {
  final model;
  final Function htmlCallBack;
  final Function restartPlay;

  ProdInfoWidget(
      {Key key, @required this.model, this.htmlCallBack, this.restartPlay})
      : super(key: key);

  @override
  _ProdInfoWidgetState createState() => _ProdInfoWidgetState();
}

class _ProdInfoWidgetState extends State<ProdInfoWidget> {
  @override
  Widget build(BuildContext context) {
    bool isRefund =
        widget.model["message"] == YLocal.of(context).YVRRefundPolicy;
    return Visibility(
        visible: widget.model["message"] != null &&
            ObjectUtil.isNotEmpty(widget.model["message"]),
        child: Container(
          width: double.infinity,
          margin: EdgeInsets.only(top: 21),
          // color: AppColors.colorCCCCCC,
          child: Row(
            children: [
              widget.model["icon"] == null
                  ? SvgPicture.asset(
                      'assets/svg/ic_publisher.svg',
                      width: 20,
                      height: 20,
                    )
                  : Icon(widget.model["icon"],
                      size: 17, color: AppColors.textSub),
              SizedBox(
                width: 8,
              ),
              Text(
                widget.model["title"] ?? "",
                style: AppStyle.style_textSub_w400_14pt(),
              ),
              Expanded(
                  child: InkWell(
                onTap: () {
                  if (isRefund) {
                    widget.htmlCallBack();
                    navigateToAgreementPage(
                        context: context,
                        pageName:
                            YLocal.of(context).refund_policy_html_page_name,
                        title: YLocal.of(context).pingtaizhengce,
                        callback: widget.restartPlay);
                  }
                },
                child: Text(
                  widget.model["message"] ?? '',
                  style: TextStyle(
                    fontSize: 14,
                    color: isRefund ? Color(0xff4F7FFE) : Color(0xff6E7380),
                  ),
                  maxLines: 2,
                  textAlign: TextAlign.start,
                  overflow: TextOverflow.ellipsis,
                ),
              ))
            ],
          ),
        ));
  }
}

List symbolLabelsForProd({@required List<int> tags}) {
  List<dynamic> appTypeModels =
      StorageManager.localStorage.getItem("kAppTagModels");
  List labels = [];
  if (appTypeModels != null) {
    for (var i = 0; i < min(tags.length, 2); i++) {
      appTypeModels.forEach((e) {
        if (e.tag == tags[i]) {
          labels.add(e.english);
        }
      });
    }
  }

  return labels;
}
