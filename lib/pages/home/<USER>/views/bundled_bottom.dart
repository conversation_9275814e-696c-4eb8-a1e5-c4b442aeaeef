import 'dart:io';

import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/model/pay_model.dart';
import 'package:yvr_assistant/public_ui/widget/pay_bottom_sheet.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import '../../../../generated/l10n.dart';
import '../../../../manager/event_bus.dart';
import '../../../../manager/global.dart';
import '../../../../model/bundled_info_model.dart';
import '../../../../public_ui/widget/common_button.dart';
import '../../../../public_ui/widget/count_down_text.dart';
import '../../../../public_ui/widget/dialog_util.dart';
import '../../../../public_ui/widget/toast_show.dart';
import '../../../../utils/data_record.dart';
import '../../../../utils/date_time_utils.dart';
import '../../../../view_model/bundled_vmodel.dart';
import '../../../../view_model/user_vmodel.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import '../../../profile/pay/tool/wallet_tool.dart';

///捆绑应用详情页底部widget
// ignore: must_be_immutable
class BundledBottomWidget extends StatefulWidget {
  Function callBack;
  Function cleanCallBack;
  BundledApplicationVModel model;
  BundledBottomWidget(this.model, this.callBack, this.cleanCallBack, {Key key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _BundledBottomState();
  }
}

class _BundledBottomState extends State<BundledBottomWidget> {
  @override
  Widget build(BuildContext context) {
    SubSaleSummaryInfo info = widget.model.bundledInfo.subSaleSummaryInfo;
    // final screenWidth = MediaQuery.of(context).size.width;
    double paddingBottom = Platform.isIOS ? Global.paddingBottom : 0;
    // info.bundle = false;//纯测试效果
    return Container(
        width: double.infinity,
        height: 72 + paddingBottom,
        child: Container(
          width: double.infinity,
          height: double.infinity,
          alignment: Alignment.center,
          // padding: EdgeInsets.symmetric(horizontal: 16),
          padding: EdgeInsets.only(left: 16, right: 16, bottom: paddingBottom),
          decoration: BoxDecoration(
            color: AppColors.colorFFFFFF,
            boxShadow: [
              BoxShadow(
                color: AppColors.color000000_5, //底色,阴影颜色
                offset: Offset(0, -2), //阴影位置,从什么位置开始
                blurRadius: 8, //阴影模糊层度
                spreadRadius: 0,
              ) //阴影模糊大小
            ],
          ),
          child: info.bundle && !info.hasAllPur && info.sprice > 0
              ? Row(children: [
                  Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CommonButton(
                          text: YLocal.of(context)
                              .NsheNzhe('${info.discount * 10} '),
                          width: 50,
                          height: 22,
                          fontSize: 14,
                          radius: 4,
                          isGradient: false,
                          isShowBorder: true,
                          borderWidth: 1,
                          textColor: AppColors.color5EA4E6,
                          borderColor: AppColors.color5EA4E6_80,
                          secondaryBg: AppColors.color5EA4E6_10,
                        ),
                        SizedBox(height: 6),
                        Text(
                          "¥${MoneyUtil.changeF2Y(info.bprice)}",
                          style: AppStyle.style_textWeak_w400_14pt_line(),
                        ),
                      ]),
                  SizedBox(width: 12),
                  Expanded(child: activityButton(info))
                ])
              : CommonButton(
                  text:
                      '${!info.hasAllPur && info.sprice == 0 ? YLocal.of(context).home_free : '¥${MoneyUtil.changeF2Y(info.sprice)}'}',
                  width: double.infinity,
                  height: 48,
                  onTap: () {
                    showPay();
                  },
                ),
        ));
  }

  Widget activityButton(SubSaleSummaryInfo info) {
    return GestureDetector(
      onTap: () {
        showPay();
      },
      child: Container(
        width: double.infinity,
        height: 48,
        decoration: BoxDecoration(
            color: AppColors.standard,
            borderRadius: BorderRadius.all(Radius.circular(48)),
            gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.topRight,
                colors: [Color(0xFF66A6FF), Color(0xFF6D78F2)])),
        child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Padding(
                padding: EdgeInsets.only(bottom: 1),
                child: Text('¥',
                    style: TextStyle(
                        fontSize: 14,
                        color: AppColors.colorFFFFFF,
                        fontWeight: FontWeight.bold)),
              ),
              Text(
                '${MoneyUtil.changeF2Y(info.sprice)}',
                style: TextStyle(
                    fontSize: 17,
                    color: AppColors.colorFFFFFF,
                    fontWeight: FontWeight.bold),
              )
            ],
          ),
          DateTimeUtils.isShowCountDownText(info.promEndDiffTime)
              ? CountDownText(
                  countSecond: info.promEndDiffTime,
                  textStyle: AppStyle.style_textWeak_w400_12pt())
              : SizedBox()
        ]),
      ),
    );
  }

  void showPay() async {
    DataRecord().saveData(
        eventId: "assistant_appstore_bundle_containedApp_buy_pit_click");
    if (DBUtil.instance.userBox.get(kToken) == null) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (_) {
            return CustomDialog(
              title: YLocal.of(context).wenxindishiwenxintis,
              content: YLocal.of(context).xuyaodenglucainenggo,
              confirmText: YLocal.of(context).qudenglu,
              height: 180,
              confirmCallback: () {
                Navigator.pushNamed(context, '/login');
              },
            );
          });
    } else {
      YvrToast.showLoading(message: "");
      if (await widget.model.isBindingDevice()) {
        //购买之前再刷新以下，防止捆绑包变动
        widget.model.getAppsOnSubject(callback: () {
          SubSaleSummaryInfo info = widget.model.bundledInfo.subSaleSummaryInfo;
          if (info.sprice == 0) {
            //免费则直接支付
            widget.model.freePay(info);
          } else {
            if (widget.callBack != null) {
              widget.callBack();
            }
            info.name = widget.model.bundledInfo.name;
            info.rcover = widget.model.bundledInfo.pic;
            info.isBundle = true;
            info.apps = widget.model.bundledInfo.apps;
            PayBottomSheet.showPay(context, PayModel.fromJson(info.toJson()),
                payCallback: (bool success) {
              widget.model.setPaySuccess(success);
              widget.model.getAppsOnSubject();
            }, addListenerCallback: () {
              widget.cleanCallBack();
              WalletTool().jumpToWalletPage(context);
            });
          }
        });
      } else {
        showDialog(
            context: context,
            barrierDismissible: false,
            builder: (_) {
              return CustomDialog(
                title: YLocal.of(context).mofagoumaigaiyingyon,
                content: YLocal.of(context).goumaiyingyongqianqi,
                confirmText: YLocal.of(context).tianjiashebei,
                isCancel: true,
                confirmCallback: () {
                  eventBus.fire(EventFn({'selectedTabbarIndex': 2}));
                },
              );
            });
      }
      YvrToast.dismiss();
    }
  }
}
