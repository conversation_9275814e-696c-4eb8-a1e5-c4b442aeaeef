import 'package:flutter/material.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';

class CategoryTextView extends StatefulWidget {
  final text;
  final ScrollController scrollController;
  CategoryTextView({
    Key key,
    @required this.text,
    @required this.scrollController,
  }) : super(key: key);

  @override
  _CategoryTextViewState createState() => _CategoryTextViewState();
}

class _CategoryTextViewState extends State<CategoryTextView> {
  bool isShow = false;

  @override
  void initState() {
    super.initState();
    bool _isShow = false;
    // 对 scrollController 进行监听
    widget.scrollController.addListener(() {
      if (widget.scrollController.position.pixels >= 160 && !_isShow) {
        _isShow = true;
        setState(() {
          isShow = _isShow;
        });
      } else if (widget.scrollController.position.pixels < 160 && _isShow) {
        _isShow = false;
        setState(() {
          isShow = _isShow;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
        visible: isShow,
        child: GestureDetector(
          onTap: () {
            widget.scrollController
                .jumpTo(widget.scrollController.position.minScrollExtent);
          },
          child: Container(
            width: MediaQuery.of(context).size.width,
            height: 34,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(0),
              color: AppColors.colorFFFFFF,
            ),
            child: Text(
              widget.text,
              style: AppStyle.style_standard_bold_14pt(),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ));
  }
}
