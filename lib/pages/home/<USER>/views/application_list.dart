import 'dart:math';

import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_svg/svg.dart';
import '../../../../generated/l10n.dart';
import '../../../../model/bundled_info_model.dart';
import '../../../../public_ui/widget/dialog_util.dart';
import '../../../../public_ui/widget/image.dart';
import '../../../../utils/data_record.dart';
import '../../../../view_model/bundled_vmodel.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import '../../prod/views/rating_star.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';

///捆绑应用详情页应用列表
// ignore: must_be_immutable
class ApplicationListWidget extends StatefulWidget {
  Function callBack;
  BundledApplicationVModel model;
  ApplicationListWidget(this.model, this.callBack, {Key key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _ApplicationListState();
  }
}

class _ApplicationListState extends State<ApplicationListWidget> {
  List<Apps> apps;

  @override
  void initState() {
    super.initState();
    apps = widget.model.appList;
  }

  //弹出应用介绍
  void showBundledIntroduce(BuildContext context) {
    DataRecord().saveData(
        eventId:
            "assistant_appstore_bundle_containedApp_information_pit_click");
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) {
          return CustomDialog(
            title: YLocal.of(context).shiyongshuimingshiyo,
            content: "${widget.model.bundledStr}",
            confirmText: YLocal.of(context).zhidaoliao,
            confirmCallback: () {},
            isCancel: false,
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            showBundledIntroduce(context);
          },
          child: Row(children: [
            Text(
              YLocal.of(context).baohanyingyong,
              style: AppStyle.style_textTitle_w500_22pt(),
            ),
            // SizedBox(width: 8),
            Container(
              width: 14,
              height: 14,
              margin: EdgeInsets.only(left: 8),
              child: SvgPicture.asset("assets/svg/ic_help.svg"),
            )
          ]),
        ),
        SizedBox(height: 4),
        ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: apps != null && apps.length > 0 ? apps.length : 0,
            padding: EdgeInsets.zero,
            itemBuilder: (context, index) {
              return itemWidget(context, index);
            })
      ],
    );
  }

  Widget itemWidget(BuildContext context, int index) {
    Apps info = apps[index];
    return GestureDetector(
      onTap: () {
        if (widget.callBack != null) {
          widget.callBack();
        }
        DataRecord().saveData(
            eventId: "assistant_appstore_bundle_containedApp_0_block_click");
        Navigator.pushNamed(context, '/prod', arguments: {"id": info.id});
      },
      child: Column(children: [
        Visibility(
            visible: index > 0 &&
                (info.purchaseStatus == 1 || info.purchaseStatus == 2),
            child: Padding(
              padding: EdgeInsets.only(top: 16),
              child: Container(
                width: double.infinity,
                height: 0.5,
                color: AppColors.divider,
              ),
            )),
        SizedBox(height: 16),
        //purchaseStatus 0 未购买，1：已购买 2：退款中
        Opacity(
          opacity:
              info.purchaseStatus == 1 || info.purchaseStatus == 2 ? 0.5 : 1,
          child: Container(
            width: double.infinity,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                    flex: 1,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(4),
                      child: WrapperImage(
                        url: '${info.rcover}',
                        width: double.infinity,
                        height: 94,
                        fit: BoxFit.fill,
                      ),
                    )),
                SizedBox(width: 12),
                Expanded(
                    flex: 1,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('${info.name}',
                            maxLines: 2,
                            style: AppStyle.style_textTitle_w500_14pt()),
                        SizedBox(
                          height: 7,
                        ),
                        Row(
                          children: symbolLabels(tags: info.tag),
                        ),
                        SizedBox(
                          height: 7,
                        ),
                        Row(
                          children: [
                            Text(
                              YLocal.of(context).Nrenpinglun(info.count),
                              style: AppStyle.style_textWeak_w400_10pt(),
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            RatingBar(
                              initialRating: info.count == 0
                                  ? 0
                                  : ((info.aver.floor() == info.aver.round())
                                      ? info.aver
                                      : (info.aver.floor() + 0.5)),
                              allowHalfRating: true,
                              itemCount: 5,
                              itemSize: 10,
                              ignoreGestures: true,
                              ratingWidget: ratingWidget,
                              itemPadding:
                                  EdgeInsets.symmetric(horizontal: 2.0),
                              onRatingUpdate: (double value) {},
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 6,
                        ),
                        _priceWidget(info)
                      ],
                    ))
              ],
            ),
          ),
        ),
      ]),
    );
  }

  Widget _priceWidget(Apps info) {
    return priceWidget(
        context, info, info.state, info.sprice, info.id, info.bprice);
  }

  Widget priceWidget(BuildContext context, Apps info, int state, int sprice,
      int id, int bprice,
      {Color freeTextColor = const Color(0xffB0B2B8)}) {
    if (state == -1) {
      return Text(YLocal.of(context).jijiangshangxianjiqi,
          style: AppStyle.style_textSub_w400_14pt());
    }
    //purchaseStatus 0 未购买，1：已购买 2：退款中
    if (info.purchaseStatus == 1) {
      return Row(children: [
        Text('${YLocal.of(context).yigoumai} ',
            style: AppStyle.style_standard_bold_14pt()),
        Text('￥${MoneyUtil.changeF2Y(info.bprice)}',
            style: AppStyle.style_textWeak_w400_10pt_line())
      ]);
    } else if (info.purchaseStatus == 2) {
      return Text(YLocal.of(context).tuikuanzhong,
          style: AppStyle.style_textSub_w400_14pt());
    } else if (info.purchaseStatus == 0) {
      return Text(
          info.bprice == 0
              ? YLocal.of(context).home_free
              : '￥${MoneyUtil.changeF2Y(info.bprice)}',
          style: info.bprice == 0
              ? AppStyle.style_textSub_w400_14pt()
              : AppStyle.style_standard_bold_14pt());
    }
    return SizedBox();
  }

  List<Widget> symbolLabels({@required List<int> tags}) {
    List<dynamic> appTypeModels =
        StorageManager.localStorage.getItem("kAppTagModels");

    if (appTypeModels == null) {
      return [SizedBox()];
    }

    List<Widget> labels = [];
    for (var i = 0; i < min(tags.length, 2); i++) {
      String tagName;
      appTypeModels.forEach((e) {
        if (e.tag == tags[i]) {
          tagName = e.chinese;
        }
      });
      labels.add((tagName != null)
          ? Container(
              padding: EdgeInsets.fromLTRB(4, 2, 4, 2),
              margin: EdgeInsets.only(right: 4),
              //边框设置
              decoration: new BoxDecoration(
                //设置四周圆角 角度
                borderRadius: BorderRadius.all(Radius.circular(20)),
                //设置四周边框
                border: new Border.all(width: 1, color: Color(0xffEEEEEE)),
              ),
              child: Text(
                tagName,
                style: AppStyle.style_textSub_w400_10pt(),
              ),
            )
          : SizedBox());
    }
    return labels.toList();
  }
}
