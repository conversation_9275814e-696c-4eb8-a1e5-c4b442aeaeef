import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/model/hot_download_model.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/pages/home/<USER>/apps_cell.dart';
import 'package:yvr_assistant/pages/home/<USER>/home_tool.dart';
import 'package:yvr_assistant/utils/data_record.dart';

class HotDownloadCell extends StatefulWidget {
  final HotDownloadModel appsModel;
  HotDownloadCell({Key key, @required this.appsModel}) : super(key: key);

  @override
  _HotDownloadCellState createState() => _HotDownloadCellState();
}

class _HotDownloadCellState extends State<HotDownloadCell> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        // InkWell 点击范围过大 导航事件无效
        onTap: () {
          DataRecord().saveData(
              eventId: "assistant_appstore_search_hotDownload_app_pit_click",
              extraData: {
                "appId": widget.appsModel.id,
              });
          Navigator.pushNamed(context, '/prod', arguments: {
            "id": widget.appsModel.id,
          });
        },
        child: Container(
          color: AppColors.colorFFFFFF, // 添加背景色 右侧空白处点击事件才生效
          margin: EdgeInsets.only(bottom: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 15,
              ),
              SizedBox(
                  width: 80,
                  height: 80,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: AspectRatio(
                      aspectRatio: 1,
                      child: FadeInImage.assetNetwork(
                        placeholder: 'assets/images/s_plhd.png',
                        image: widget.appsModel.scover ?? "",
                        fit: BoxFit.cover,
                        imageErrorBuilder: (context, error, stackTrace) {
                          return Image.asset(
                            'assets/images/r_plhd.png',
                            fit: BoxFit.cover,
                          );
                        },
                      ),
                    ),
                  )),
              SizedBox(width: 12),
              Expanded(
                  flex: 1,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(widget.appsModel.name,
                          style: AppStyle.style_textTitle_w500_14pt()),
                      SizedBox(
                        height: 10,
                      ),
                      Row(
                        children: symbolLabels(tags: widget.appsModel.tag),
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      priceWidget(widget.appsModel)
                    ],
                  ))
            ],
          ),
        ));
  }

  Widget priceWidget(HotDownloadModel model) {
    if (HomeTool().appIsPay(model.id)) {
      return Text(YLocal.of(context).yigoumai,
          style: TextStyle(fontSize: 13, color: Color(0xffB0B2B8)));
    }
    String symbol = (model.currency == 1) ? "\$" : "￥";

    String price = model.sprice == 0
        ? YLocal.of(context).home_free
        : "$symbol${MoneyUtil.changeF2Y(model.sprice)}";
    Widget priceView = Text(price,
        style: model.sprice == 0
            ? AppStyle.style_standard_bold_14pt()
            : AppStyle.style_textSub_w400_14pt());

    if (model.bprice != null && model.bprice > model.sprice) {
      priceView = RichText(
        text: TextSpan(
            text: price + "  ",
            style: AppStyle.style_standard_bold_14pt(),
            children: [
              TextSpan(
                  text: "$symbol${MoneyUtil.changeF2Y(model.bprice)} ",
                  style: AppStyle.style_textWeak_w400_10pt())
            ]),
      );
    }
    return priceView;
  }
}
