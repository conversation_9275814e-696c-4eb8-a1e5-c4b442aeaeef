import 'package:flutter/material.dart';
import 'package:yvr_assistant/model/apps_model.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';

class SearchResultCell extends StatefulWidget {
  final Function appClick;
  final AppsModel appsModel;
  SearchResultCell({Key key, @required this.appClick, @required this.appsModel})
      : super(key: key);

  @override
  _SearchResultCellState createState() => _SearchResultCellState();
}

class _SearchResultCellState extends State<SearchResultCell> {
  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.only(bottom: 10),
        child: Column(
          children: [
            ListTile(
              onTap: () {
                widget.appClick();
              },
              leading: SizedBox(
                  height: 44,
                  width: 44,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: AspectRatio(
                      aspectRatio: 1,
                      child: FadeInImage.assetNetwork(
                        placeholder: 'assets/images/s_plhd.png',
                        image: widget.appsModel.scover,
                        fit: BoxFit.cover,
                        imageErrorBuilder: (context, error, stackTrace) {
                          return Image.asset(
                            'assets/images/r_plhd.png',
                            fit: BoxFit.cover,
                          );
                        },
                      ),
                    ),
                  )),
              title: Text(
                widget.appsModel.name,
                style: AppStyle.style_textTitle_w400_14pt(),
              ),
            ),
            SizedBox(
              height: 10,
            ),
            Container(
                margin: EdgeInsets.fromLTRB(75, 0, 15, 0),
                child: Divider(
                  height: 1,
                  indent: 0.0,
                  color: AppColors.divider,
                ))
          ],
        ));
  }
}
