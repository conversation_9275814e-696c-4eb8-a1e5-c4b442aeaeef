import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/resource_mananger.dart';
import 'package:yvr_assistant/model/noti_model.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/public_ui/widget/rich_text.dart';
import 'package:yvr_assistant/utils/svg_provider.dart';

import '../../../../styles/app_color.dart';
import '../../../../utils/date_time_utils.dart';

class MessageCell extends StatefulWidget {
  final NotiModel notiModel;
  final Function handleMsgTap;
  final VoidCallback onAgreeFriendInvitation;
  final ProgressState state;

  MessageCell({
    Key key,
    this.notiModel,
    this.handleMsgTap,
    this.onAgreeFriendInvitation,
    this.state,
  }) : super(key: key);

  @override
  _MessageCellState createState() => _MessageCellState();
}

class _MessageCellState extends State<MessageCell> {
  String msgTitle = "";
  String msgInfo = "";
  bool isUseAvatar = false;
  String firstMutualFriendNickName;
  String firstMutualFriendAppName;
  Widget notiImg = Image(
    image: SvgProvider('assets/images/noti_logo.svg'),
    fit: BoxFit.cover,
  );
  bool needProcessInvitation = false;

  @override
  void initState() {
    super.initState();
    processMessage(widget.notiModel);
  }

  @override
  void didUpdateWidget(covariant MessageCell oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.notiModel != oldWidget.notiModel ||
        widget.state != oldWidget.state) {
      processMessage(widget.notiModel);
    }
  }

  static String _processInviteFriendNick(List friends) {
    return friends.take(1).map<String>((e) => e['nick']).first;
  }

  static String _processInviteFriendAppName(List apps) {
    return apps.take(1).map<String>((e) => e['name']).first;
  }

  void processMessage(NotiModel model) {
    String mutualFriendNickName;
    String mutualFriendAppName;
    bool needProcessInviteFriend = false;
    bool isUseAvatar = false;
    String msgTitle = "";
    String msgInfo = "";

    switch (model.tag) {
      case "recvFdInvite":
        isUseAvatar = true;
        msgTitle = YLocal.current.social_friend_req;
        msgInfo = "${model.attach.name ?? ''}" + YLocal.current.msg_req_friend;
        if (model.note != null && model.note.isNotEmpty) {
          try {
            dynamic noteJson = jsonDecode(model.note);
            if (noteJson is Map) {
              final int type = noteJson['type'];
              if (type == 1) {
                final List friends = noteJson['friends'];
                if (friends != null && friends.isNotEmpty) {
                  mutualFriendNickName = _processInviteFriendNick(friends);
                }
              } else if (type == 2) {
                final List apps = noteJson['apps'];
                if (apps != null && apps.isNotEmpty) {
                  mutualFriendAppName = _processInviteFriendAppName(apps);
                }
              }
            }
          } catch (e) {
            print(e);
          }
        }
        if (model.status == 0) {
          needProcessInviteFriend = true;
        }
        break;
      case "recvFdInviteFeedback":
        isUseAvatar = true;
        msgTitle = YLocal.current.msg_friend_pass;
        msgInfo = YLocal.current.msg_accept_friend(model.attach.name ?? '');
        break;
      case "recvEventRemove":
        isUseAvatar = true;
        msgTitle = YLocal.current.msg_evet_cancel;
        msgInfo = YLocal.current.msg_evet_cancel;
        break;
      case "recvEventInvite":
        isUseAvatar = true;
        msgTitle = YLocal.current.msg_invite_you;
        msgInfo = YLocal.current.msg_invite_event(model.attach.name ?? '');
        break;
      case "recvEventToCome":
        isUseAvatar = true;
        msgTitle = YLocal.current.msg_after_half_hour;
        msgInfo = YLocal.current.msg_after_half_hour;
        break;
      case "recvEventComing":
        isUseAvatar = true;
        msgTitle = YLocal.current.msg_evet_start;
        msgInfo = YLocal.current.msg_evet_start;
        break;
      case "recvUserAuditComment":
        String result = "";
        // model.status == 1 直接评分、无内容；
        if (model.status == 2) {
          result =
              YLocal.current.ninzaiyingyongshangd_1(model.star, model.appName);
        } else if (model.status == 3) {
          if (model.note == null || model.note.isEmpty) {
            result = YLocal.current
                .ninzaiyingyongshangd_2(model.star, model.appName);
          } else {
            result = YLocal.current
                .ninzaiyingyongshangd_3(model.star, model.appName, model.note);
          }
        } else {
          result =
              YLocal.current.ninzaiyingyongshangd(model.star, model.appName);
        }
        msgTitle = YLocal.current.jitongxiaoxixitongxi;
        msgInfo = result;
        break;
      case "recvAppPayOnMobile":
        msgTitle = YLocal.current.VRshebeigoumaiqingqi;
        msgInfo = YLocal.current.nindeVRshebeixiangni(model.appName);
        break;
      /*case "appPurchasedSuccess":
        msgTitle = YLocal.current.yingyonggoumaichengg;
        msgInfo = YLocal.current.Sgoumaichenggongkuai(model.appName);
        break;*/
      case "officialOfferY":
        notiImg = Image(
          image: SvgProvider('assets/svg/color_y_coin.svg'),
          fit: BoxFit.cover,
        );
        msgTitle = YLocal.current.YbifafangtongzhiYbif;
        msgInfo = YLocal.current.NYbiyifafangkezaisha(model.star);
        break;
      case "offerCoupon":
        notiImg = Image.asset(
          "assets/images/noti_game_coin.png",
          fit: BoxFit.cover,
        );
        msgTitle = YLocal.current.youhuquanfafangtongz;
        msgInfo =
            YLocal.current.NzhangSyouhuquanyifa(model.star, model.note ?? "");
        break;
      case "recvAppExtension":
        msgTitle = YLocal.current.yingyongtuiandetongz;
        msgInfo = model.note;
        break;
      case "delEventCommentNotify":
        msgTitle = YLocal.current.jitongtongzhixitongt;
        if (model.note == null || model.note.isEmpty) {
          msgInfo = YLocal.current.Szhongdehuifuyinshex(model.appName);
        } else {
          msgInfo =
              YLocal.current.Szhongdehuifuyinshex_1(model.appName, model.note);
        }
        break;
      default:
    }
    this.firstMutualFriendNickName = mutualFriendNickName;
    this.firstMutualFriendAppName = mutualFriendAppName;
    this.needProcessInvitation = needProcessInviteFriend;
    this.isUseAvatar = isUseAvatar;
    this.msgTitle = msgTitle;
    this.msgInfo = msgInfo;
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
        onTap: () {
          widget.handleMsgTap(widget.notiModel.state);
          if (widget.notiModel.state == 0) {
            setState(() {
              widget.notiModel.state = 1;
            });
          }
        },
        child: Container(
          color: widget.notiModel.state == 1 ? Colors.white : Color(0xFFF0F2F5),
          child: Flex(
            direction: Axis.horizontal,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: EdgeInsets.only(left: 16, top: 25),
                width: 60,
                height: 60,
                child: ClipRRect(
                  //剪裁为圆角矩形
                  borderRadius: BorderRadius.circular(60),
                  child: isUseAvatar
                      ? FadeInImage.assetNetwork(
                          placeholder: 'assets/images/s_plhd.png',
                          image: (widget.notiModel.attach.cover ?? ""),
                          fit: BoxFit.cover,
                          imageErrorBuilder: (context, error, stackTrace) {
                            return Image.asset(
                              widget.notiModel.attach.sex == 2
                                  ? 'assets/images/avatar_girl.jpg'
                                  : 'assets/images/avatar_boy.jpg',
                              fit: BoxFit.cover,
                            );
                          },
                        )
                      : notiImg,
                ),
              ),
              Expanded(
                flex: 1,
                child: Row(
                  children: [
                    Expanded(
                        child: Container(
                      padding: EdgeInsets.only(
                          left: 8, top: 18, right: 16, bottom: 18),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            msgTitle,
                            style: widget.notiModel.state == 1
                                ? TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontSize: 14,
                                    height: 1,
                                    letterSpacing: 1,
                                    color: AppColors.textTitle,
                                  )
                                : TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontSize: 14,
                                    height: 1,
                                    letterSpacing: 1,
                                    color: AppColors.textTitle,
                                  ),
                          ),
                          SizedBox(
                            height: 8,
                          ),
                          Text(
                            msgInfo,
                            style: widget.notiModel.state == 1
                                ? TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 12,
                                    height: 1.66667,
                                    letterSpacing: 0.5,
                                    color: AppColors.textSub,
                                  )
                                : TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 12,
                                    height: 1.66667,
                                    letterSpacing: 0.5,
                                    color: AppColors.textSub,
                                  ),
                          ),
                          firstMutualFriendNickName == null
                              ? SizedBox()
                              : Container(
                                  margin: EdgeInsets.only(top: 8),
                                  child: IntlRichText1(
                                    intlTextBuilder:
                                        YLocal.of(context).SyuSshigongtonghaoyo,
                                    defaultStyle: widget.notiModel.state == 1
                                        ? TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 12,
                                            height: 1.66667,
                                            letterSpacing: 0.5,
                                            color: AppColors.textSub,
                                          )
                                        : TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 12,
                                            height: 1.66667,
                                            letterSpacing: 0.5,
                                            color: AppColors.textSub,
                                          ),
                                    param: firstMutualFriendNickName,
                                    paramStyle: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      fontSize: 12,
                                      height: 1.16667,
                                      color: AppColors.textTitle,
                                    ),
                                  ),
                                ),
                          firstMutualFriendAppName == null
                              ? SizedBox()
                              : Container(
                                  margin: EdgeInsets.only(top: 8),
                                  child: IntlRichText1(
                                    intlTextBuilder:
                                        YLocal.of(context).nimenduzaiwanSnimend,
                                    defaultStyle: widget.notiModel.state == 1
                                        ? TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 12,
                                            height: 1.66667,
                                            letterSpacing: 0.5,
                                            color: AppColors.textSub,
                                          )
                                        : TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 12,
                                            height: 1.66667,
                                            letterSpacing: 0.5,
                                            color: AppColors.textSub,
                                          ),
                                    param: firstMutualFriendAppName,
                                    paramStyle: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      fontSize: 12,
                                      height: 1.16667,
                                      color: AppColors.textTitle,
                                    ),
                                  ),
                                ),
                          SizedBox(
                            height: 8,
                          ),
                          Text(
                            DateTimeUtils.formatToLocalFromDateTimeText(
                                widget.notiModel.time, 'yyyy/MM/dd HH:mm:ss'),
                            style: widget.notiModel.state == 1
                                ? TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 12,
                                    height: 1,
                                    color: AppColors.textWeak,
                                  )
                                : TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 12,
                                    height: 1,
                                    color: AppColors.textWeak,
                                  ),
                          )
                        ],
                      ),
                    )),
                    needProcessInvitation
                        ? Container(
                            margin: EdgeInsets.only(top: 10, right: 20),
                            child: ElevatedProgressButton(
                              state: widget.state,
                              color: Color(0xff4F7FFE),
                              onPressed: widget.onAgreeFriendInvitation,
                              padding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 5),
                              idleChild: Text(
                                YLocal.of(context).tongguo,
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontSize: 14,
                                  height: 1.2,
                                  color: AppColors.background,
                                ),
                              ),
                            ),
                          )
                        : PopupMenuButton<String>(
                            padding: EdgeInsets.zero,
                            offset: Offset(-45, 0),
                            iconSize: 18,
                            icon: Icon(
                              IconFonts.iconMore,
                              color: widget.notiModel.state == 1
                                  ? Color.fromARGB(255, 195, 197, 204)
                                  : Color(0xff6E7380),
                            ),
                            onSelected: (value) async {
                              showDialog(
                                  context: context,
                                  barrierDismissible: false,
                                  builder: (_) {
                                    return CustomDialog(
                                      title: YLocal.of(context).msg_delete,
                                      content:
                                          YLocal.of(context).msg_delete_confirm,
                                      height: 180,
                                      confirmCallback: () {
                                        widget.handleMsgTap(2);
                                      },
                                    );
                                  });
                            },
                            itemBuilder: (context) {
                              return [
                                PopupMenuItem<String>(
                                  value: "5", //必须给Key，否则点击事件无效
                                  height: 30,
                                  child: Container(
                                    width: 80,
                                    padding: EdgeInsets.only(left: 10),
                                    // margin: EdgeInsets.only(right: 30),
                                    child: Text(
                                      YLocal.of(context).social_unfriend,
                                      textAlign: TextAlign.left,
                                      style: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontSize: 16,
                                        letterSpacing: 2,
                                        color: AppColors.textTitle,
                                      ),
                                    ),
                                  ),
                                )
                              ];
                            })
                  ],
                ),
              ),
            ],
          ),
        ));
  }
}
