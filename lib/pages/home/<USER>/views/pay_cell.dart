import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/utils/number_utils.dart';

import '../../../../generated/l10n.dart';

enum PayWayType {
  payForYCoin,
  payForAlipay,
  payForWechat,
  payForCoupon,
  payForPayPal,
}

class PayCellWidget extends StatefulWidget {
  final PayWayType type;
  final PayWayType selectedType;
  final Function payCallBack;
  final int sprice;
  PayCellWidget(
      {Key key,
      this.sprice,
      @required this.type,
      @required this.selectedType,
      @required this.payCallBack})
      : super(key: key);

  @override
  _PayCellWidgetState createState() => _PayCellWidgetState();
}

class _PayCellWidgetState extends State<PayCellWidget> {
  final String paypal = "PayPal";
  @override
  Widget build(BuildContext context) {
    Map texts = payInfoMap(widget.type);
    return Container(
        width: double.infinity,
        height: 70,
        child: ListTile(
          horizontalTitleGap: 0,
          minVerticalPadding: 10,
          contentPadding: EdgeInsets.zero,
          leading: (texts["name"] == paypal)
              ? Image.asset(
                  'assets/images/paypal.png',
                  fit: BoxFit.contain,
                  width: 27,
                  height: 27,
                )
              : SvgPicture.asset(
                  texts["imgPath"],
                  width: 26,
                  height: 26,
                ),
          title: RichText(
              text: TextSpan(
                  text: texts["name"],
                  style: AppStyle.style_textSub_w500_18pt(),
                  children: [
                TextSpan(
                    text: texts["msg"],
                    style: AppStyle.style_standard_w400_14pt())
              ])),
          trailing: (widget.selectedType == PayWayType.payForCoupon)
              ? SizedBox()
              : SvgPicture.asset(
                  (widget.type == widget.selectedType)
                      ? "assets/svg/option_s.svg"
                      : "assets/svg/option.svg",
                  width: 20,
                  height: 20,
                ),
          onTap: () {
            if (widget.selectedType != PayWayType.payForCoupon) {
              widget.payCallBack();
            }
          },
        ));
  }

  Map payInfoMap(PayWayType type) {
    Map payInfoMap;
    switch (type) {
      case PayWayType.payForYCoin:
        int yCoin = StorageManager.localStorage.getItem("kUserYcoin") ?? 0;
        String amount = NumberUtils.formatShortNumber(yCoin);
        payInfoMap = {
          "imgPath": "assets/svg/blue_y_coin.svg",
          "name": YLocal.of(context).Ybizhifu,
          "msg": "  ${YLocal.of(context).shengyuN0(amount)}"
        };
        break;
      case PayWayType.payForYCoin:
        payInfoMap = {
          "imgPath": "assets/svg/alipay.svg",
          "name": YLocal.of(context).zhifubaozhifu,
          "msg": " "
        };
        break;
      case PayWayType.payForWechat:
        payInfoMap = {
          "imgPath": "assets/svg/wechat.svg",
          "name": YLocal.of(context).weixinzhifu,
          "msg": " "
        };
        break;
      case PayWayType.payForCoupon:
        // int couponCount = UserVModel().user.accountCouponDetail.surplus ?? 0;
        // String name =
        //     UserVModel().user.accountCouponDetail.couponInfo.name ?? "";
        // payInfoMap = {
        //   "imgPath": "assets/svg/prod_coupon.svg",
        //   "name": name,
        //   "msg": "  (剩余$couponCount张)"
        // };
        break;
      case PayWayType.payForPayPal:
        payInfoMap = {"imgPath": "", "name": paypal, "msg": " "};
        break;
      default:
    }
    return payInfoMap;
  }
}
