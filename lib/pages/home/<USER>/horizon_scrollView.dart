import 'package:cached_network_image/cached_network_image.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/model/apps_model.dart';
import 'package:yvr_assistant/model/theme_data_model.dart';
import 'package:yvr_assistant/pages/home/<USER>/tool/app_type.dart';
import 'package:yvr_assistant/pages/home/<USER>/home_tool.dart';
import 'package:yvr_assistant/pages/home/<USER>/banner_swiper.dart';
import 'package:yvr_assistant/utils/data_record.dart';
import '../../../model/subject_model.dart';
import '../../../model/summary_info_model.dart';
import '../../../public_ui/widget/count_down_text.dart';
import '../../../public_ui/widget/underline_widget.dart';
import '../../../utils/date_time_utils.dart';
import '../../../view_model/home_vmodel.dart';

// ignore: must_be_immutable
class HorizonScrollView extends StatefulWidget {
  HomeVModel homeVModel;
  final ThemeDataModel themeModel;

  HorizonScrollView({
    Key key,
    this.homeVModel,
    @required this.themeModel,
  }) : super(key: key);

  @override
  _HorizonScrollViewState createState() => _HorizonScrollViewState();
}

class _HorizonScrollViewState extends State<HorizonScrollView> {
  @override
  Widget build(BuildContext context) {
    ///title marginTop:24,marginBottom:16
    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(left: 16, top: 20, right: 19, bottom: 12),
          height: 24,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                child: Text(
                  widget.themeModel.name,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 18,
                    height: 1.15,
                    color: Color(0xff2C2E33),
                  ),
                ),
              ),
              Container(
                height: double.infinity,
                alignment: AlignmentDirectional.center,
                child: InkWell(
                  onTap: () async {
                    await initAppTypes();

                    DataRecord().saveData(
                        eventId:
                            "assistant_appstore_store_theme_all_block_click",
                        extraData: {"themeId": widget.themeModel.id});
                    Navigator.pushNamed(context, '/apps', arguments: {
                      "isSingleSubject": false,
                      "title": widget.themeModel.name,
                      "id": widget.themeModel.id,
                      'type': widget.themeModel.type
                    });
                  },
                  child: RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(text: YLocal.of(context).home_view_all),
                        WidgetSpan(
                          child: SizedBox(
                            width: 6,
                          ),
                        ),
                        WidgetSpan(
                          alignment: PlaceholderAlignment.middle,
                          child: SvgPicture.asset(
                            'assets/svg/arrow_right.svg',
                            width: 11.88,
                            height: 11.88,
                            color: Color(0xffAFB6CC),
                          ),
                        )
                      ],
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 12,
                        color: Color(0xff6E7380),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        widget.themeModel.type == 3
            ? ThemeWithSubjView(
                themeModel: widget.themeModel, homeVModel: widget.homeVModel)
            : ThemeWithAppsView(themeModel: widget.themeModel),
      ],
    );
  }
}

class ThemeWithAppsView extends StatefulWidget {
  final ThemeDataModel themeModel;

  ThemeWithAppsView({
    Key key,
    @required this.themeModel,
  }) : super(key: key);

  @override
  _ThemeWithAppsViewState createState() => _ThemeWithAppsViewState();
}

class _ThemeWithAppsViewState extends State<ThemeWithAppsView> {
  List<AppsModel> data;

  @override
  void initState() {
    super.initState();
    _process(widget.themeModel.apps);
  }

  @override
  void didUpdateWidget(covariant ThemeWithAppsView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.themeModel != oldWidget.themeModel) {
      _process(widget.themeModel.apps);
    }
  }

  void _process(List<AppsModel> data) {
    this.data = data.take(5).toList();
  }

  @override
  Widget build(BuildContext context) {
    const double height = _imageHeight +
        _titleMarginTop +
        _titleHeight +
        _priceMarginTop +
        _priceHeight;

    bool isSquare = false;
    return
        //横向滑动区域
        Container(
      // margin: EdgeInsets.fromLTRB(10, 0, 10, 0),
      height: height,
      child: ListView.separated(
        padding: EdgeInsets.symmetric(horizontal: 16),
        scrollDirection: Axis.horizontal,
        itemCount: data.length,
        itemBuilder: (context, index) {
          return InkWell(
            onTap: () {
              DataRecord().saveData(
                  eventId: "assistant_appstore_store_theme_app_block_click",
                  extraData: {
                    "themeId": widget.themeModel.id,
                    "appId": data[index].id
                  });
              Navigator.pushNamed(context, '/prod',
                  arguments: {"id": data[index].id});
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: _imageHeight,
                  child: AspectRatio(
                    aspectRatio: isSquare ? 1 : 368 / 208,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: CachedNetworkImage(
                        fadeInDuration: Duration.zero,
                        fadeInCurve: Curves.easeOut,
                        fit: BoxFit.cover,
                        imageUrl:
                            isSquare ? data[index].scover : data[index].rcover,
                        placeholder: (context, url) => rPlhdImage,
                        errorWidget: (context, url, error) => rPlhdImage,
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: _titleMarginTop,
                ),
                SizedBox(
                  height: _titleHeight,
                  child: Text(
                    data[index].name,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                      height: 1,
                      color: Color(0xff2C2E33),
                    ),
                  ),
                ),
                Builder(builder: (context) {
                  AppsModel model = data[index];
                  Widget child;

                  if (model.state == -1) {
                    child = Text(
                      YLocal.of(context).jijiangshangxianjiqi,
                      style: _kSubTitle,
                    );
                  } else if (HomeTool().appIsPay(model.id)) {
                    child = Text(
                      YLocal.of(context).yigoumai,
                      style: _kWhiteTitle,
                    );
                  } else {
                    String price = model.sprice == 0
                        ? YLocal.of(context).home_free
                        : "￥${MoneyUtil.changeF2Y(model.sprice)}";
                    if (model.bprice > model.sprice) {
                      child = Row(
                        children: [
                          RichText(
                            text: TextSpan(
                                text: price,
                                style: _kBlueTitle,
                                children: [
                                  WidgetSpan(
                                    child: SizedBox(
                                      width: 4,
                                    ),
                                  ),
                                  WidgetSpan(
                                    alignment: PlaceholderAlignment.middle,
                                    child: UnderlineWidget(
                                      child: Text(
                                        "￥${MoneyUtil.changeF2Y(model.bprice)}",
                                        style: _kLineThrough,
                                      ),
                                    ),
                                  ),
                                ]),
                          ),
                          _buildDiscount(model.bprice, model.sprice)
                        ],
                      );
                    } else {
                      child = Text(
                        price,
                        style: _kBlueTitle,
                      );
                    }
                  }

                  return Container(
                    child: child,
                    margin: EdgeInsets.only(top: _priceMarginTop),
                    height: _priceHeight,
                  );
                }),
              ],
            ),
          );
        },
        separatorBuilder: (BuildContext context, int index) {
          return SizedBox(width: 8);
        },
      ),
    );
  }
}

// ignore: must_be_immutable
class ThemeWithSubjView extends StatefulWidget {
  HomeVModel homeVModel;
  final ThemeDataModel themeModel;

  ThemeWithSubjView({
    Key key,
    this.homeVModel,
    @required this.themeModel,
  }) : super(key: key);

  @override
  _ThemeWithSubjViewState createState() => _ThemeWithSubjViewState();
}

class _ThemeWithSubjViewState extends State<ThemeWithSubjView> {
  List<SubjectModel> data;

  void _process(List<SubjectModel> list) {
    final List<SubjectModel> d = list.take(5).toList();
    this.data = d;
  }

  @override
  void didUpdateWidget(covariant ThemeWithSubjView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.themeModel != oldWidget.themeModel) {
      _process(widget.themeModel.subjects);
    }
  }

  @override
  void initState() {
    super.initState();
    _process(widget.themeModel.subjects);
  }

  @override
  Widget build(BuildContext context) {
    double commonHeight = _imageHeight + _titleMarginTop + _titleHeight;
    bool includeBundle = isIncludeBundle(data);
    double bundleHeight = includeBundle
        ? _priceMarginTop + _priceHeight + _subMarginTop + _subHeight
        : 0;

    return //横向滑动区域
        Container(
      height: commonHeight + bundleHeight,
      child: ListView.separated(
        padding: EdgeInsets.symmetric(horizontal: 16),
        scrollDirection: Axis.horizontal,
        itemCount: data.length,
        itemBuilder: (context, index) {
          SummaryInfoModel summaryInfo = data[index].summaryInfo;
          bool includeBundleCurrently =
              includeBundle && summaryInfo != null && summaryInfo.bundle;
          return Container(
            width: 184,
            height: double.infinity,
            child: InkWell(
              onTap: () {
                DataRecord().saveData(
                    eventId:
                        "assistant_appstore_store_theme_subject_block_click",
                    extraData: {
                      "themeId": widget.themeModel.id,
                      "subjId": data[index].id
                    });
                widget.homeVModel.subjectJump(context, data[index].id);
                /*if (summaryInfo != null && summaryInfo.bundle) {
                  Navigator.pushNamed(context, '/bundledApplication',
                      arguments: {'mpId': summaryInfo.subId});
                } else {
                  Navigator.pushNamed(context, '/apps', arguments: {
                    "isSingleSubject": true,
                    "title": data[index].name,
                    "id": data[index].id,
                    "type": data[index].type,
                  });
                }*/
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                      height: _imageHeight,
                      child: Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: FadeInImage.assetNetwork(
                              placeholder: 'assets/images/r_plhd.png',
                              image: data[index].pic,
                            ),
                          ),
                          Visibility(
                            // visible: summaryInfo == null || !summaryInfo.bundle,
                            visible: false,
                            child: Positioned(
                              top: 0,
                              right: 12,
                              child: Container(
                                width: 32,
                                height: 18,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                    boxShadow: [
                                      BoxShadow(
                                        color: Color(0x80000000),
                                        offset: Offset(0, 1), //阴影xy轴偏移量
                                        // blurRadius: 4, //阴影模糊程度
                                        // spreadRadius: 1.0 //阴影扩散程度
                                      )
                                    ],
                                    borderRadius: BorderRadius.only(
                                      bottomLeft: Radius.circular(4),
                                      bottomRight: Radius.circular(4),
                                    ),
                                    gradient: LinearGradient(colors: [
                                      Color(0xff4F7FFE),
                                      Color(0xff1656FF)
                                    ])),
                                child: Text(YLocal.of(context).zhuanti,
                                    style: TextStyle(
                                        fontSize: 12,
                                        color: Color(0xffE8E8E8))),
                              ),
                            ),
                          ),
                          if (includeBundleCurrently &&
                              DateTimeUtils.isShowCountDownText(
                                  summaryInfo.promEndDiffTime))
                            Positioned(
                              bottom: 0,
                              left: 0,
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  boxShadow: [
                                    BoxShadow(color: Color(0xff7691F7)),
                                    BoxShadow(color: Color(0xff5F7CF6)),
                                  ],
                                  borderRadius: BorderRadius.only(
                                      topLeft: Radius.zero,
                                      topRight: Radius.circular(2),
                                      bottomLeft: Radius.circular(8),
                                      bottomRight: Radius.circular(2)),
                                ),
                                child: CountDownText(
                                  key: ValueKey(summaryInfo.promEndDiffTime),
                                  countSecond: summaryInfo.promEndDiffTime,
                                  textStyle: TextStyle(
                                    fontSize: 10,
                                    height: 1,
                                    color: Color(0xffffffff),
                                  ),
                                ),
                              ),
                            )
                        ],
                      )),
                  SizedBox(
                    height: _titleMarginTop,
                  ),
                  SizedBox(
                    height: _titleHeight,
                    child: Text(
                      data[index].name,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                        height: 1,
                        color: Color(0xff2C2E33),
                      ),
                    ),
                  ),

                  ///捆绑包主题
                  Visibility(
                    visible: includeBundle,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: _priceMarginTop,
                        ),
                        SizedBox(
                          height: _priceHeight,
                          child: Builder(builder: (context) {
                            String priceText;
                            String bpriceText;
                            TextStyle priceTextStyle;
                            Widget discountWidget;
                            if (summaryInfo.hasAllPur) {
                              priceText = YLocal.of(context).yigoumai;
                              priceTextStyle = _kWhiteTitle;
                              bpriceText = '';
                              discountWidget = null;
                            } else if (summaryInfo.sprice <= 0) {
                              priceText = YLocal.of(context).mianfeiwenfei;
                              priceTextStyle = _kBlueTitle;
                              bpriceText = '';
                              discountWidget = null;
                            } else if (summaryInfo.sprice <
                                summaryInfo.bprice) {
                              priceText =
                                  '￥${MoneyUtil.changeF2Y(summaryInfo.sprice)}';
                              priceTextStyle = _kBlueTitle;
                              bpriceText =
                                  '￥${MoneyUtil.changeF2Y(summaryInfo.bprice)}';
                              discountWidget = _buildDiscount2(
                                  double.tryParse(summaryInfo.discount) ??
                                      summaryInfo.sprice / summaryInfo.bprice);
                              //bprice原价 //sprice现价
                            } else {
                              priceText =
                                  '￥${MoneyUtil.changeF2Y(summaryInfo.sprice)}';
                              priceTextStyle = _kBlueTitle;
                              bpriceText = '';
                              discountWidget = null;
                            }

                            return summaryInfo != null && summaryInfo.bundle
                                ? Row(
                                    children: [
                                      RichText(
                                        text: TextSpan(
                                          text: priceText,
                                          style: priceTextStyle,
                                          children: [
                                            WidgetSpan(
                                              child: SizedBox(
                                                width: 4,
                                              ),
                                            ),
                                            WidgetSpan(
                                              alignment:
                                                  PlaceholderAlignment.middle,
                                              child: UnderlineWidget(
                                                child: Text(
                                                  bpriceText,
                                                  style: _kLineThrough,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      if (discountWidget != null)
                                        discountWidget,
                                    ],
                                  )
                                : SizedBox();
                          }),
                        ),
                        SizedBox(
                          height: _subMarginTop,
                        ),
                        SizedBox(
                          height: _subHeight,
                          child: Visibility(
                            visible: includeBundleCurrently,
                            child: includeBundleCurrently
                                ? Text(
                                    YLocal.of(context).kunbangbao,
                                    style: _kSubTitle,
                                  )
                                : SizedBox(),
                          ),
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
          );
        },
        separatorBuilder: (BuildContext context, int index) {
          return SizedBox(width: 8);
        },
      ),
    );
  }

  //查询是否包含捆绑包
  bool isIncludeBundle(List<SubjectModel> subjects) {
    if (subjects != null && subjects.length > 0) {
      for (int i = 0; i < subjects.length; i++) {
        SummaryInfoModel summaryInfo =
            widget.themeModel.subjects[i].summaryInfo;
        if (summaryInfo != null && summaryInfo.bundle) {
          return true;
        }
      }
    }
    return false;
  }
}

Widget _buildDiscount2(double discount) {
  return Container(
    margin: EdgeInsets.only(left: 4),
    padding: EdgeInsets.symmetric(horizontal: 4),
    height: double.infinity,
    decoration: BoxDecoration(
      color: Color(0xff4F7FFE),
      borderRadius: BorderRadius.circular(4),
    ),
    alignment: AlignmentDirectional.center,
    child: Text(
      _computeDiscount2(discount),
      style: _kDiscount,
    ),
  );
}

Widget _buildDiscount(int bprice, int sprice) {
  return Container(
    margin: EdgeInsets.only(left: 4),
    padding: EdgeInsets.symmetric(horizontal: 4),
    height: double.infinity,
    decoration: BoxDecoration(
      color: Color(0xff4F7FFE),
      borderRadius: BorderRadius.circular(4),
    ),
    alignment: AlignmentDirectional.center,
    child: Text(
      _computeDiscount(bprice, sprice),
      style: _kDiscount,
    ),
  );
}

String _computeDiscount(int bprice, int sprice) {
  return '-${100 - (((sprice / bprice) * 100.0).toInt())}%';
}

String _computeDiscount2(double discount) {
  return '-${100 - (((discount) * 100.0).toInt())}%';
}

const double _imageHeight = 104;
const double _titleMarginTop = 12;
const double _titleHeight = 14;
const double _priceMarginTop = 8;
const double _priceHeight = 17.3;
const double _subMarginTop = 8.5;
const double _subHeight = 10;

const TextStyle _kBlueTitle = TextStyle(
  fontWeight: FontWeight.w700,
  fontSize: 17,
  height: 1,
  color: Color(0xff2C2E33),
);

const TextStyle _kWhiteTitle = TextStyle(
  fontWeight: FontWeight.w400,
  fontSize: 16,
  height: 1,
  color: Color(0xff2C2E33),
);

const TextStyle _kLineThrough = TextStyle(
  fontWeight: FontWeight.w400,
  fontSize: 10,
  height: 1.25,
  color: Color(0xffAFB6CC),
);

const TextStyle _kSubTitle = TextStyle(
  fontWeight: FontWeight.w400,
  fontSize: 10,
  height: 1,
  color: Color(0xffAFB6CC),
);

const TextStyle _kDiscount = TextStyle(
  fontWeight: FontWeight.w800,
  fontSize: 12,
  height: 1,
  color: Color(0xffffffff),
);
