import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_swiper/flutter_swiper.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/model/banner_model.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/data_record.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_page_view_indicator/flutter_page_view_indicator.dart';
import 'package:yvr_assistant/utils/log.dart';
import '../../../manager/app_navigator.dart';
import '../../../model/bundled_info_model.dart';
import '../../../network/http.dart';
import '../../../network/request.dart';

final Widget rPlhdImage = Image.asset(
  'assets/images/r_plhd.png',
  fit: BoxFit.cover,
);

final Widget sPlhdImage = Image.asset(
  'assets/images/s_plhd.png',
  fit: BoxFit.cover,
);

class BannerSwiper extends StatefulWidget {
  final List<BannerModel> banners;

  BannerSwiper({Key key, @required this.banners}) : super(key: key);

  @override
  _BannerSwiperState createState() => _BannerSwiperState();
}

class _BannerSwiperState extends State<BannerSwiper>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  SwiperController swiperController = new SwiperController();
  int _bannerSelIdx = 0;
  double viewScale = 0.85;

  @override
  // ignore: must_call_super
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(top: 15),
      height: (Global.screenWidth / 2) * viewScale + 60,
      child: Swiper(
        controller: swiperController,
        scrollDirection: Axis.horizontal,
        // 横向
        itemCount: widget.banners.length,
        // 数量
        autoplay: true,
        // 自动翻页
        loop: (widget.banners.length != 1),
        duration: 1500,
        // 毫秒 图片滚动时间
        itemBuilder: (BuildContext context, int index) {
          return
              // ExposureDetector(
              //   key: Key('column_scroll_$index'),
              //   child:
              Column(
            // 根据子视图高度自适应
            mainAxisSize: MainAxisSize.min,
            children: [
              Material(
                clipBehavior: Clip.hardEdge,
                borderRadius: BorderRadius.circular(16),
                color: Global.kBgColor,
                child: AspectRatio(
                  aspectRatio: 2,
                  child: CachedNetworkImage(
                    fadeInDuration: Duration.zero,
                    fadeInCurve: Curves.easeOut,
                    fit: BoxFit.fitWidth,
                    imageUrl: widget.banners[index].pic,
                    // placeholder: (context, url) => rPlhdImage,
                    errorWidget: (context, url, error) => rPlhdImage,
                  ),
                ),
              ),
            ],
            // ),
            // onExposure: (visibilityInfo) {
            //   Log.d(
            //       '第 $index 块曝光, 展示比例为${visibilityInfo.visibleFraction * 100}%');
            // },
          );
        },
        onIndexChanged: (value) {
          // 处理滚动间隔时间
          swiperController.stopAutoplay();
          Timer(Duration(seconds: 2), () {
            swiperController.startAutoplay();
          });
          _bannerSelIdx = value;
        },
        onTap: (index) {
          bannerTap(widget.banners[index].link);
        },
        pagination: SwiperCustomPagination(
            builder: (BuildContext context, SwiperPluginConfig config) {
          return Container(
              alignment: Alignment.bottomCenter,
              padding: EdgeInsets.only(left: 30, right: 30),
              child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: PageViewIndicator(
                    currentItemColor: (widget.banners.length != 1)
                        ? Color(0xff4F7FFE)
                        : Global.kBgColor,
                    otherItemColor: Color(0xff808080),
                    length: widget.banners.length,
                    otherItemWidth: 6,
                    otherItemHeight: 6,
                    currentItemWidth: 20,
                    currentItemHeight: 6,
                    currentIndex: _bannerSelIdx,
                  )));
        }),
        viewportFraction: (Global.screenWidth - (16 * 2)) / Global.screenWidth,
        // 当前视窗展示比例 小于1可见上一个和下一个视窗
        scale: 0.95, // 两张图片之间的间隔
      ),
    );
  }

  bannerTap(String url) {
    DataRecord().saveData(
        eventId: "assistant_appstore_store_topBanner_banner_pit_click",
        extraData: {"url": url});
    navigateByUrl(context, url, subjectCallback: (int id) async {
      YvrToast.showLoading();
      Map<String, dynamic> parameters = {'subjectId': id};
      try {
        ResponseData<BundledInfoModel> object =
            await YvrRequests.getBundledInfo(parameters);
        if (object.data != null && object.data.subSaleSummaryInfo != null) {
          Navigator.pushNamed(context, '/bundledApplication',
              arguments: {'mpId': id});
        } else {
          Log.d('-----------------跳转到专题应用----------------');
          Navigator.pushNamed(context, '/apps',
              arguments: {"isSingleSubject": true, "title": "", "id": id});
        }
      } catch (e) {
        Log.d('-----------------专题banner返回----------------$e');
        Navigator.pushNamed(context, '/apps',
            arguments: {"isSingleSubject": true, "title": "", "id": id});
      }
      YvrToast.dismiss();
    });
  }
}
