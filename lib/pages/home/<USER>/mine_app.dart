import 'package:flutter/material.dart';
import 'package:yvr_assistant/model/pay_history_model.dart';
import 'package:yvr_assistant/pages/home/<USER>/mine_app_cell.dart';
import 'package:yvr_assistant/pages/social/views/plhd_view.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/provider/provider_widget.dart';
import 'package:yvr_assistant/provider/view_state_widget.dart';
import 'package:yvr_assistant/view_model/pay_history_vmodel.dart';

import '../../../generated/l10n.dart';

class MineAppPage extends StatefulWidget {
  final arguments;

  MineAppPage({Key key, @required this.arguments}) : super(key: key);

  @override
  _MineAppPageState createState() => _MineAppPageState();
}

class _MineAppPageState extends State<MineAppPage> {
  List<Widget> _getListData(List<PayHistoryModel> models, bool isSelectApp) {
    var tempList = models.map((app) {
      return MineAppCell(
        isSelectApp: isSelectApp,
        mineAppModel: app,
      );
    });
    return tempList.toList();
  }

  @override
  Widget build(BuildContext context) {
    int titleIndex = widget.arguments["title"];
    String title = '';
    bool isSelectApp = false;
    switch (titleIndex) {
      case 0:
        title = YLocal.of(context).home_my_app;
        break;
      case 1:
        title = YLocal.of(context).xuanzeyingyongxuanzh;
        isSelectApp = true;
        break;
    }

    return Scaffold(
      appBar: TopNavbar(
        title: title,
      ),
      body: ProviderWidget<PayHistoryVModel>(
        model: PayHistoryVModel(),
        onModelReady: (model) => model.initData(),
        builder: (context, model, child) {
          if (model.busy) {
            return ViewStateBusyWidget();
          } else if (model.error) {
            return ViewStateErrorWidget(
                error: model.viewStateError, onPressed: model.initData);
          }

          List<PayHistoryModel> tempModels = model.historyModels;
          // 过滤退款中 已退款的应用
          List<PayHistoryModel> models =
              tempModels.where((element) => element.status == 1).toList();

          return models.length == 0
              ? Center(
                  child: PagePlhdWidget(
                    message: YLocal.of(context).zanweigoumaiyingyong,
                    imagePath: "assets/images/person_home_plhd.svg",
                    imgWidth: 56.0,
                  ),
                )
              : GridView.count(
                  childAspectRatio: 2 / 3,
                  crossAxisCount: 3,
                  crossAxisSpacing: 8,
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                  children: _getListData(models, isSelectApp),
                );
        },
      ),
    );
  }
}
