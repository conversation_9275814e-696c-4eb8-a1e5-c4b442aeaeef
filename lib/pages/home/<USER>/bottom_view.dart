import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/pages/home/<USER>/tool/app_type.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';
import 'package:yvr_assistant/utils/data_record.dart';

class BottomView extends StatelessWidget {
  const BottomView({Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 28,
        ),
        Text(YLocal.of(context).home_go_appcenter,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color(0xff6E7380),
              fontSize: 16,
            )),
        SizedBox(
          height: 28,
        ),
        Row(
          children: [
            YvrTextButton(
              onPressed: () async {
                DataRecord().saveData(
                    eventId:
                        "assistant_appstore_store_bottom_allApps_pit_click");

                await initAppTypes();
                Navigator.pushNamed(context, '/category');
              },
              child: Text(
                YLocal.of(context).home_go_now,
              ),
            ),
          ],
          mainAxisAlignment: MainAxisAlignment.center,
        ),

        // InkWell(
        //   onTap: () async {},
        //   child: Container(
        //     height: 40,
        //     width: 94,
        //     alignment: Alignment.center,
        //     decoration: BoxDecoration(
        //         color: Color(0xff242527),
        //         borderRadius: BorderRadius.circular(6)),
        //     child: Text(YLocal.of(context).home_go_now,
        //         style: TextStyle(color: Color(0xffE8E8E8), fontSize: 16)),
        //   ),
        // ),
        SizedBox(
          height: 40,
        ),
      ],
    );
  }
}
