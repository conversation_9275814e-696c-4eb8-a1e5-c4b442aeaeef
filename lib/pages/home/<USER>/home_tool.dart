import 'package:flutter/material.dart';
import 'package:wifi_iot/wifi_iot.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/model/home_model.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/pages/home/<USER>/banner_swiper.dart';
import 'package:yvr_assistant/pages/home/<USER>/bottom_view.dart';
import 'package:yvr_assistant/pages/home/<USER>/horizon_scrollView.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';

import '../../../view_model/home_vmodel.dart';

class HomeTool {
  List<Widget> configViewsData({HomeModel homeList, HomeVModel homeVModel}) {
    Future payFuture = Future.value(YvrRequests.getPurchasedAppId());
    Future.wait([payFuture]).then((datas) async {
      if (datas[0] != null) {
        List<dynamic> payIds = datas[0];
        StorageManager.localStorage.setItem(Global.kPayAppList, payIds);
        eventBus.fire(EventFn({Global.kRefreshAppPayStatus: true}));
      }
    });

    List<Widget> homeViews = [];
    homeViews.add(BannerSwiper(banners: homeList.banners));
    for (var i = 0; i < homeList.themes.length; i++) {
      if (homeList.themes[i].apps.length > 0 ||
          homeList.themes[i].subjects.length > 0) {
        homeViews.add(HorizonScrollView(
          homeVModel: homeVModel,
          themeModel: homeList.themes[i],
        ));
      }
    }
    homeViews.add(SizedBox(
      height: 30,
    ));
    homeViews.add(BottomView());
    return homeViews;
  }

  bool appIsPay(int id) {
    bool isPay = false;
    List payAppIds =
        StorageManager.localStorage.getItem(Global.kPayAppList) ?? [];
    if (payAppIds != null && payAppIds.length > 0 && payAppIds.contains(id)) {
      isPay = true;
    }
    return isPay;
  }

  Future launchWebUrl() async {
    // var params = Uri(
    //     scheme: 'https',
    //     host: 'www.wjx.cn',
    //     path: 'jq/37369695.aspx',
    //     queryParameters: {'q1': '1', 'q2': '1', 'q3': '1|5', 'hideq': '1,2'});
    // String url = params.toString();

    // // ignore: deprecated_member_use
    // if (await canLaunch(url)) {
    //   // ignore: deprecated_member_use
    //   await launch(url, forceWebView: true, statusBarBrightness: Brightness.dark);
    // } else {
    //   print('Failed to launch $url');
    // }
  }
}

bool remindLogin({@required String message}) {
  if (DBUtil.instance.userBox.get(kToken) == null) {
    BuildContext context = Global.navigatorKey.currentState.overlay.context;
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) {
          return CustomDialog(
            title: YLocal.of(context).tips,
            content: message,
            cancelText: YLocal.of(context).cancel,
            confirmText: YLocal.of(context).sign_in,
            height: 180,
            confirmCallback: () {
              Navigator.pushNamed(context, '/login');
            },
          );
        });
    return false;
  }
  return true;
}

Future<bool> checkWifiIsChange() async {
  String ssid = await WiFiForIoTPlugin.getSSID();
  String preSsid = StorageManager.foreverData.getItem("kWifiSsid");
  Log.e("监听到网络变化ssid \n现在：$ssid   \n原来：$preSsid");
  if (ssid != preSsid) {
    await StorageManager.foreverData.setItem("kWifiSsid", ssid);
    eventBus.fire(EventFn({Global.phoneWifiChangeKey: true}));
    return true;
  }
  return false;
}
