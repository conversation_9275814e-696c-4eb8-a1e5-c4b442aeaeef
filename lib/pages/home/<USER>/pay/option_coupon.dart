import 'package:flutter/material.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/model/user_model.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/coupon_cell.dart';
import 'package:yvr_assistant/public_ui/widget/common_button.dart';
import 'package:yvr_assistant/public_ui/widget/rich_text.dart';

import '../../../../generated/l10n.dart';

class OptionCouponPage extends StatefulWidget {
  final arguments;

  OptionCouponPage({Key key, this.arguments}) : super(key: key);

  @override
  State<OptionCouponPage> createState() => _OptionCouponPageState();
}

class _OptionCouponPageState extends State<OptionCouponPage> {
  int _canUseCounts = 0;
  int _canNotUseCounts = 0;
  List<AccountCouponDetailList> _canUseList;
  List<AccountCouponDetailList> _canNotUseList;
  String _selectedCouponName = "";
  ValueNotifier<int> _selCouponNotifier = ValueNotifier<int>(0);

  @override
  void initState() {
    super.initState();
    _canUseCounts = widget.arguments["canUseCounts"];
    _canNotUseCounts = widget.arguments["canNotUseCounts"];
    _selCouponNotifier.value = widget.arguments["selCouponId"];
    if (_canUseCounts > 0) {
      _canUseList = widget.arguments["canUseList"];
      if (widget.arguments["selCouponId"] == 0) {
        _selCouponNotifier.value = _canUseList[0].couponId;
        _selectedCouponName = _canUseList[0].couponInfo.name;
      }
    }
  }

  List<Widget> cardCells() {
    List<Widget> cards = [];

    // 可用卡券
    if (_canUseCounts > 0) {
      cards.add(titleWidget(count: _canUseCounts));
      for (var item in _canUseList) {
        cards.add(
          CouponCell(
              isSelected: (_selCouponNotifier.value == item.couponId),
              detlModel: item,
              optionCouponCallback:
                  (bool isSelected, int couponId, String couponName) {
                if (mounted) {
                  _selectedCouponName = isSelected ? couponName : "";
                  _selCouponNotifier.value = isSelected ? couponId : 0;
                }
              }),
        );
      }
    }

    // 不可用卡券
    if (_canNotUseCounts > 0) {
      _canNotUseList = widget.arguments["canNotUseList"];
      cards.add(titleWidget(isCanUse: false, count: _canNotUseCounts));
      for (var item in _canNotUseList) {
        cards.add(
          CouponCell(
            isSelected: false,
            detlModel: item,
          ),
        );
      }
    }

    cards.add(SizedBox(
      height: 68 + Global.paddingBottom,
    ));
    return cards;
  }

  Widget titleWidget({bool isCanUse = true, @required int count}) {
    var intl = isCanUse
        ? YLocal.of(context).keyongyouhuquanNzhan
        : YLocal.of(context).bukeyongyouhuquanNzh;
    return Container(
      padding: EdgeInsets.only(top: 12, bottom: 12),
      child: IntlRichText1(
        intlTextBuilder: intl,
        defaultStyle: AppStyle.style_textTitle_w400_14pt(),
        param: "  $count",
        paramStyle: AppStyle.style_textSub_w400_10pt(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.secondaryBg,
      appBar: TopNavbar(
        title: YLocal.of(context).youhuquanyouhuiquany,
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 20),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Expanded(
              child: SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: ValueListenableBuilder<int>(
                valueListenable: _selCouponNotifier,
                builder: (context, value, child) {
                  return ListView(
                    children: cardCells(),
                  );
                }),
          )),
          CommonButton(
              text: YLocal.of(context).queding,
              onTap: () {
                var result = {
                  "kChangeCouponInfo": true,
                  "kSelectedCouponId": _selCouponNotifier.value,
                  "kSelectedCouponName": _selectedCouponName
                };
                eventBus.fire(EventFn(result));
                Navigator.of(context).pop(result);
              }),
          SizedBox(height: Global.paddingBottom + 20)
        ]),
      ),
    );
  }
}
