import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/pay_cell.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/public_ui/widget/common_button.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/public_ui/widget/rich_text.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/log.dart';

import '../../../../generated/l10n.dart';
import '../../../../manager/app_navigator.dart';
import '../../../../model/app_config_model.dart';

class PayResultPage extends StatefulWidget {
  final arguments;

  PayResultPage({Key key, @required this.arguments}) : super(key: key);

  @override
  _PayResultPageState createState() => _PayResultPageState();
}

class _PayResultPageState extends State<PayResultPage> {
  void initState() {
    super.initState();
    Log.d('arguments ========支付成功页========>${widget.arguments}');
    eventBus.fire(EventFn({'CloseVideo': true}));
    eventBus.fire(EventFn({'HomeRefresh': true}));
    eventBus.fire(EventFn({'BundledRefresh': true}));
    eventBus.fire(EventFn({'ProdRefresh': true}));
    popupAppMessage();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget priceText(
      {@required PayWayType appPayType,
      @required String priceStr,
      int currency = 0}) {
    Widget textWidget;
    String symbol = (currency == 1) ? "\$" : "￥";
    if (priceStr == "0") {
      textWidget = (Text(priceStr,
          style: TextStyle(fontSize: 32, color: Color(0xffB0B2B8))));
    } else {
      switch (appPayType) {
        case PayWayType.payForYCoin:
          textWidget = IntlRichText1(
            intlTextBuilder: YLocal.of(context).NYbi,
            defaultStyle: AppStyle.style_standard_bold_14pt(),
            param: priceStr,
            paramStyle: AppStyle.style_standard_bold_32pt(),
          );
          break;
        case PayWayType.payForWechat:
          textWidget = RichText(
            text: TextSpan(
                text: "$symbol  ",
                style: AppStyle.style_standard_bold_14pt(),
                children: [
                  TextSpan(
                    text: priceStr,
                    style: AppStyle.style_standard_bold_32pt(),
                  )
                ]),
          );
          break;
        case PayWayType.payForCoupon:
          textWidget = RichText(
            text: TextSpan(
                text: priceStr,
                style: AppStyle.style_standard_bold_32pt(),
                children: [
                  TextSpan(
                      text: " ${YLocal.of(context).Nzhang(1)}",
                      style: AppStyle.style_standard_bold_32pt())
                ]),
          );
          break;
          break;
        default:
      }
    }
    return textWidget;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppColors.colorFFFFFF,
        appBar: TopNavbar(
          title: YLocal.of(context).zhifujieguo,
        ),
        body: Container(
          width: double.infinity,
          height: double.infinity,
          child: Column(
            children: [
              const SizedBox(height: 40),
              SvgPicture.asset(
                "assets/svg/pay_success.svg",
                height: 80,
              ),
              const SizedBox(height: 21),
              Text(
                widget.arguments["appPayType"] == PayWayType.payForCoupon
                    ? YLocal.of(context).duihuanchenggong
                    : YLocal.of(context).prod_pay_success,
                style: AppStyle.style_textSub_w600_16pt(),
              ),
              const SizedBox(height: 37),
              priceText(
                  appPayType: widget.arguments["appPayType"],
                  priceStr: widget.arguments["price"]),
              const SizedBox(height: 21),
              Text(
                YLocal.of(context).ninyiwanchengzhifunk,
                textAlign: TextAlign.center,
                style: AppStyle.style_textSub_w400_14pt(),
              ),
              Expanded(child: const SizedBox()),
              Visibility(
                  visible: widget.arguments['isBundled'] == null ||
                      !widget.arguments['isBundled'],
                  child: Column(children: [
                    Text(
                      YLocal.of(context).downloadNow,
                      textAlign: TextAlign.center,
                      style: AppStyle.style_standard_bold_14pt(),
                    ),
                    const SizedBox(height: 14),
                    Text(
                      YLocal.of(context).downloadText,
                      textAlign: TextAlign.center,
                      style: AppStyle.style_textWeak_w500_14pt(),
                    ),
                    const SizedBox(height: 27),
                    Container(
                      height: 48,
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      width: double.infinity,
                      child: Flex(
                        direction: Axis.horizontal,
                        children: [
                          Expanded(
                            child: CommonButton(
                              text: YLocal.of(context).shaohouxiazai,
                              isGradient: false,
                              secondaryBg: AppColors.backgroundItem,
                              textColor: AppColors.textSub,
                              onTap: () {
                                Navigator.pop(context);
                                YvrToast.showToast(
                                    YLocal.of(context).qingqianwangVRshebei);
                              },
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: CommonButton(
                              text: YLocal.of(context).lijixiazai,
                              onTap: () async {
                                if (await requireDownloadApp()) {
                                  Navigator.pop(context);
                                  YvrToast.showToast(
                                      YLocal.current.VRshebeijiangzailian);
                                } else {
                                  YvrToast.showToast(
                                      YLocal.of(context).tongzhiVRxiazaishiba);
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                    )
                  ])),
              const SizedBox(height: 50),
            ],
          ),
        ));
  }

  Future<bool> requireDownloadApp() async {
    bool result = false;
    await http.post<Map>('vrmcsys/appstore/requireDownloadApp',
        data: {"appId": widget.arguments["appId"]}).then((response) {
      if (response.data["errCode"] == 0) {
        result = true;
      }
    }).catchError((onError) {
      result = false;
    });
    return result;
  }

  static const int kMinPopupAppMessageDelayTime = 500;

  Future<void> popupAppMessage() async {
    final int appId = widget.arguments["appId"];
    try {
      int start = DateTime.now().millisecondsSinceEpoch;
      var value = await YvrRequests.getAppMessage(appId);
      if (!checkSuccess(value.errCode) ||
          value.data == null ||
          value.data.appMessage == null) {
        return;
      }
      int diff = kMinPopupAppMessageDelayTime -
          (DateTime.now().millisecondsSinceEpoch - start);
      if (diff > 0) {
        await Future.delayed(Duration(milliseconds: diff));
      }
      if (mounted) {
        final AppMessage msg = value.data.appMessage;
        showDialog(
            context: context,
            builder: (context) {
              return CustomDialog(
                title: YLocal.of(context).wenxindishiwenxintis,
                content: msg.msg,
                confirmText: YLocal.of(context).chakanxiangqingzhaka,
                cancelText: YLocal.of(context).shaohouchakanshaohou,
                confirmCallback: () {
                  navigateByUrl(context, msg.url);
                },
              );
            });
      }
    } catch (e, s) {
      print(s);
    }
  }
}
