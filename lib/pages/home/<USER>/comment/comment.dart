import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/model/comment_model.dart';
import 'package:yvr_assistant/model/prod_model.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';

import 'package:yvr_assistant/pages/home/<USER>/comment/views/comment_cell.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/public_ui/widget/common_button.dart';
import 'package:yvr_assistant/public_ui/widget/star_judge.dart';
import 'package:yvr_assistant/utils/data_record.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/view_model/comments_vmodel.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';

class CommentPage extends StatefulWidget {
  final arguments;
  CommentPage({Key key, @required this.arguments}) : super(key: key);

  @override
  _CommentPageState createState() => _CommentPageState();
}

class _CommentPageState extends State<CommentPage> {
  int _dropIndex = 0;
  int _lastCmtId = 0;
  int _paramStar = 0;
  List<CommentModel> datas = [];
  bool _isShowNoMoreWidget = false;
  bool _isShowPlhdWidget = false;

  RefreshController _controller = RefreshController(initialRefresh: false);
  @override
  void initState() {
    super.initState();
    reqCommentListData();
  }

  @override
  Widget build(BuildContext context) {
    List<String> _starList = [
      YLocal.of(context).quanbu,
      YLocal.of(context).xing_4,
      YLocal.of(context).xing,
      YLocal.of(context).xing_2,
      YLocal.of(context).xing_3,
      YLocal.of(context).xing_1
    ];
    ProdModel prodModel = widget.arguments["prodModel"];

    return WillPopScope(
        onWillPop: () async {
          Navigator.pop(context, "Refresh Previous Page");
          return false;
        },
        child: Scaffold(
            backgroundColor: AppColors.colorFFFFFF,
            appBar: TopNavbar(
              title: YLocal.of(context).prod_ratings_reviews,
            ),
            body: Stack(
              children: [
                SmartRefresher(
                    controller: _controller,
                    header: WaterDropHeader(),
                    footer: CustomFooter(
                      builder: (BuildContext context, LoadStatus mode) {
                        Widget body;
                        if (mode == LoadStatus.idle) {
                          body = Text("");
                        } else if (mode == LoadStatus.loading) {
                          body = CupertinoActivityIndicator();
                        } else if (mode == LoadStatus.failed) {
                          body = Text(YLocal.of(context).state_load_fail);
                        } else if (mode == LoadStatus.canLoading) {
                          _isShowNoMoreWidget = false;
                          body = Text(YLocal.of(context).state_load_more);
                        } else {
                          body = Text("");
                        }
                        return Center(child: body);
                      },
                    ),
                    onLoading: () {
                      reqCommentListData(isMore: true);
                    },
                    enablePullDown: false,
                    enablePullUp: true,
                    child: CustomScrollView(slivers: <Widget>[
                      SliverToBoxAdapter(
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 16),
                          child: Column(
                            children: <Widget>[
                              SizedBox(height: 8),
                              StarJudge(
                                aver: prodModel.aver,
                                starList: [
                                  prodModel.star5,
                                  prodModel.star4,
                                  prodModel.star3,
                                  prodModel.star2,
                                  prodModel.star1,
                                ],
                                peoples: prodModel.count,
                              ),
                              SizedBox(
                                height: 32,
                              ),
                              Row(
                                children: [
                                  Text(
                                    YLocal.of(context).shaixuantiaojian,
                                    style: AppStyle.style_textTitle_w500_16pt(),
                                  ),
                                  SizedBox(
                                    width: 6,
                                  ),
                                  PopupMenuButton<int>(
                                    child: Row(
                                      children: [
                                        Text(_starList[_dropIndex],
                                            style: AppStyle
                                                .style_textTitle_w500_16pt()),
                                        Icon(
                                          Icons.arrow_drop_down,
                                          size: 24,
                                          color: AppColors.textTitle,
                                        ),
                                      ],
                                    ),
                                    color: AppColors.colorFFFFFF,
                                    padding: EdgeInsets.zero,
                                    offset: Offset(-60, -12),
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadiusDirectional.circular(
                                                8)),

                                    elevation: 0, // 去除阴影效果
                                    itemBuilder: (context) {
                                      return starPopupMenuItems(_starList);
                                    },
                                    onSelected: (value) async {
                                      setState(() {
                                        _dropIndex = value;
                                        _paramStar =
                                            (value == 0) ? 0 : (6 - value);
                                        _lastCmtId = 0;
                                        reqCommentListData();
                                      });
                                    },
                                  )
                                ],
                              ),
                              SizedBox(
                                height: 16,
                              ),
                            ],
                          ),
                        ),
                      ),
                      SliverList(
                        delegate: SliverChildBuilderDelegate((content, index) {
                          return Padding(
                            padding: EdgeInsets.symmetric(horizontal: 15),
                            child: CommentCell(
                              isExpand: true,
                              commentModel: datas[index],
                              reportClick: (var reason) {
                                DataRecord().saveData(
                                    eventId:
                                        "assistant_appstore_ratingsNComment_comment_report_pit_click",
                                    extraData: {
                                      "appId": prodModel.id,
                                      "commentId": datas[index].id,
                                      "reason": reason ?? ""
                                    });
                              },
                            ),
                          );
                        }, childCount: datas.length),
                      ),
                      SliverToBoxAdapter(
                          child: _isShowPlhdWidget
                              ? Container(
                                  margin: EdgeInsets.only(
                                    top: 100,
                                  ),
                                  child: Column(
                                    children: [
                                      SvgPicture.asset(
                                        "assets/svg/prod_no_comment.svg",
                                        width: 77,
                                        height: 77,
                                      ),
                                      SizedBox(
                                        height: 20,
                                      ),
                                      Text(YLocal.of(context).prod_sofa,
                                          style: AppStyle
                                              .style_textSub_w400_14pt())
                                    ],
                                  ),
                                )
                              : SizedBox()),
                      SliverToBoxAdapter(
                        child: _isShowNoMoreWidget
                            ? Container(
                                height: 40,
                                alignment: Alignment.center,
                                child: Text(
                                  YLocal.of(context).plhd_no_more_data,
                                  style: TextStyle(
                                      color: Color(0xffE8E8E8), fontSize: 14),
                                ),
                              )
                            : SizedBox(),
                      ),
                      SliverPadding(padding: EdgeInsets.all(20))
                    ])),
                (Provider.of<UserIsComment>(context)
                            .getIsComment(prodModel.id) ||
                        DBUtil.instance.userBox.get(kToken) == null ||
                        prodModel.purchased != 1)
                    ? SizedBox()
                    : Positioned(
                        left: 0,
                        right: 0,
                        bottom: 0,
                        child: Container(
                          height: 76,
                          alignment: Alignment.center,
                          color: AppColors.colorFFFFFF,
                          padding: EdgeInsets.symmetric(horizontal: 16),
                          child: CommonButton(
                              text: YLocal.of(context).home_comment,
                              onTap: () {
                                DataRecord().saveData(
                                    eventId:
                                        "assistant_appstore_ratingsNComment_comment_commentMenu_pit_click",
                                    extraData: {
                                      "appId": prodModel.id,
                                    });
                                Navigator.pushNamed(context, '/judge',
                                    arguments: {
                                      "appId": prodModel.id,
                                      "name": prodModel.name
                                    }).then((param) {
                                  _lastCmtId = 0;
                                  Map judgeParam = param;

                                  // 更新评分数据模型
                                  if (judgeParam["star"] != 0) {
                                    prodModel.count += 1;
                                    switch (judgeParam["star"]) {
                                      case 1:
                                        prodModel.star1 += 1;
                                        break;
                                      case 2:
                                        prodModel.star2 += 1;
                                        break;
                                      case 3:
                                        prodModel.star3 += 1;
                                        break;
                                      case 4:
                                        prodModel.star4 += 1;
                                        break;
                                      case 5:
                                        prodModel.star5 += 1;
                                        break;

                                      default:
                                    }
                                    // 保留一位小数
                                    // (NumUtil.getNumByValueDouble(aver, 1));
                                    // 评分计算规则： 贝叶斯算法公式
                                    prodModel.aver = judgeParam["aver"];
                                  }
                                  reqCommentListData();
                                });
                              }),
                        ))
              ],
            )));
  }

  void reqCommentListData({bool isMore: false}) {
    ProdModel prodModel = widget.arguments["prodModel"];
    http.post<Map>('vrmcsys/appstore/getAppComments', data: {
      "appId": prodModel.id,
      "cmtId": _lastCmtId,
      "star": _paramStar,
      "size": 5
    }).then((response) async {
      if (response.data["errCode"] == 0) {
        await StorageManager.localStorage.setItem(
            "UserIsComment_${prodModel.id}",
            response.data["status"] == 1 ?? false);

        List<CommentModel> tempList = response.data["comments"]
            .map<CommentModel>((item) => CommentModel.fromJson(item))
            .toList();
        _isShowPlhdWidget = false;

        if (!isMore) {
          // 刷新
          _isShowNoMoreWidget = false;
          datas.clear();

          if (tempList.length == 0) {
            _isShowPlhdWidget = true;
          } else {
            datas.addAll(tempList);
          }
          _controller.refreshCompleted();
          if (mounted) setState(() {});
        } else {
          // 加载
          if (tempList.length < 5) {
            setState(() {
              _isShowNoMoreWidget = true;
            });
            // _controller.loadNoData();
          }
          // 上拉加载
          datas.addAll(tempList);
          _controller.loadComplete();
        }
        _lastCmtId = tempList.last.id;
        if (mounted) setState(() {});
      }
    });
  }

  List<PopupMenuEntry<int>> starPopupMenuItems(List<String> _starList) {
    List<PopupMenuEntry<int>> items = [];
    for (int i = 0; i < _starList.length; ++i) {
      var text = _starList[i];
      items.add(PopupMenuItem<int>(
        value: i, //必须给Key，否则点击事件无效
        child: Container(
          height: 24,
          width: 80,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: AppColors.colorFFFFFF,
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
          child: Text(
            text,
            style: AppStyle.style_textTitle_w400_16pt(),
          ),
        ),
      ));
    }
    return items.toList();
  }
}
