import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/resource_mananger.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/model/comment_model.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/pages/home/<USER>/comment/views/readmore.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/rating_star.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/public_ui/widget/picker_tool.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/date_time_utils.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';

class CommentCell extends StatefulWidget {
  final bool isExpand;
  final bool showReport;
  final Function reportClick;
  final CommentModel commentModel;

  CommentCell(
      {Key key,
      @required this.commentModel,
      this.isExpand = false,
      this.reportClick,
      this.showReport = true})
      : super(key: key);

  @override
  _CommentCellState createState() => _CommentCellState();
}

class _CommentCellState extends State<CommentCell> {
  @override
  Widget build(BuildContext context) {
    return widget.commentModel == null
        ? SizedBox()
        : Card(
            elevation: 0,
            color: AppColors.backgroundItem,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8.0)),
            ),
            child: Padding(
              padding: EdgeInsets.only(left: 20, top: 20, bottom: 20),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                          child: SizedBox(
                        width: double.infinity,
                        child: Row(
                          children: [
                            SizedBox(
                                height: 52,
                                width: 52,
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(4),
                                  child: AspectRatio(
                                    aspectRatio: 1,
                                    child: ClipRRect(
                                        //剪裁为圆角矩形
                                        borderRadius: BorderRadius.circular(60),
                                        child: (widget.commentModel.auth == -2)
                                            ? Image.asset(
                                                'assets/images/avatar_boy.jpg',
                                                fit: BoxFit.cover,
                                              )
                                            : CachedNetworkImage(
                                                imageUrl: widget
                                                        .commentModel.avatar ??
                                                    "",
                                                fit: BoxFit.cover,
                                                placeholder: (context, url) =>
                                                    Image.asset(
                                                  widget.commentModel.sex == 2
                                                      ? 'assets/images/avatar_girl.jpg'
                                                      : 'assets/images/avatar_boy.jpg',
                                                  fit: BoxFit.cover,
                                                ),
                                                errorWidget:
                                                    (context, url, error) =>
                                                        Image.asset(
                                                  widget.commentModel.sex == 2
                                                      ? 'assets/images/avatar_girl.jpg'
                                                      : 'assets/images/avatar_boy.jpg',
                                                  fit: BoxFit.cover,
                                                ),
                                              )),
                                  ),
                                )),
                            SizedBox(
                              width: 14,
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                    (widget.commentModel.auth == -2)
                                        ? YLocal.of(context).zhanghaoyizhuxiao
                                        : widget.commentModel.nick ??
                                            YLocal.of(context).nickname,
                                    style:
                                        AppStyle.style_textTitle_w500_14pt()),
                                SizedBox(
                                  height: 6,
                                ),
                                Row(
                                  children: [
                                    RatingBar(
                                      initialRating: widget.commentModel.star ==
                                              null
                                          ? 0
                                          : widget.commentModel.star.toDouble(),
                                      allowHalfRating: true,
                                      itemCount: 5,
                                      itemSize: 12,
                                      ignoreGestures: true,
                                      ratingWidget: ratingWidget,
                                      itemPadding:
                                          EdgeInsets.symmetric(horizontal: 2.0),
                                      onRatingUpdate: (double value) {},
                                    ),
                                    SizedBox(
                                      width: 10,
                                    ),
                                    Text(
                                      DateTimeUtils
                                          .formatToLocalFromDateTimeText(
                                              widget.commentModel.time,
                                              'yyyy-MM-dd'),
                                      style:
                                          AppStyle.style_textWeak_w400_10pt(),
                                    )
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      )),
                      widget.showReport
                          ? PopupMenuButton<String>(
                              color: Colors.transparent,
                              icon: Icon(
                                IconFonts.iconMore,
                                color: AppColors.textSub,
                                size: 18,
                              ),
                              offset: Offset(30, -10),
                              elevation: 0,
                              // 去除阴影效果
                              itemBuilder: (context) {
                                return <PopupMenuEntry<String>>[
                                  PopupMenuItem<String>(
                                    value: YLocal.of(context)
                                        .jubao, //必须给Key，否则点击事件无效
                                    child: Container(
                                      height: 48,
                                      width: 123,
                                      alignment: Alignment(0, 0),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(8),
                                        color: AppColors.colorFFFFFF,
                                      ),
                                      child: Text(
                                        YLocal.of(context).jubao,
                                        style: AppStyle
                                            .style_textTitle_w400_16pt(),
                                      ),
                                    ),
                                  ),
                                ];
                              },
                              onSelected: (value) async {
                                if (StorageManager.localStorage
                                        .getItem(kToken) ==
                                    null) {
                                  Log.d("举报评论的Id：${widget.commentModel.id}");

                                  showDialog(
                                      context: context,
                                      barrierDismissible: false,
                                      builder: (_) {
                                        return CustomDialog(
                                          title: YLocal.of(context)
                                              .wenxindishiwenxintis,
                                          content: YLocal.of(context)
                                              .xuyaodenglucainengju,
                                          confirmText:
                                              YLocal.of(context).qudenglu,
                                          height: 180,
                                          confirmCallback: () {
                                            Navigator.pushNamed(
                                                context, '/login');
                                          },
                                        );
                                      });
                                } else {
                                  commentApp(cmtId: widget.commentModel.id);
                                }
                              },
                            )
                          : SizedBox(),
                    ],
                  ),
                  Container(
                    alignment: Alignment.topLeft,
                    padding: EdgeInsets.only(top: 12, right: 20),
                    child: ReadMoreText(
                      text: widget.commentModel.content,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                      buttonBackgroundColor: AppColors.backgroundItem,
                      style: AppStyle.style_textSub_w400_14pt(),
                      buttonStyle: AppStyle.style_standard_w400_14pt(),
                      expandButtonText: YLocal.of(context).gengduo,
                      closeButtonText: YLocal.of(context).shouqi,
                    ),
                  ),
                ],
              ),
            ));
  }

  commentApp({@required int cmtId}) {
    JhPickerTool.showStringPicker(context, title: '', data: [
      YLocal.of(context).weifaweigui,
      YLocal.of(context).lajiangaolajiguangga,
      YLocal.of(context).disuruma,
      YLocal.of(context).jitaqita,
    ], clickCallBack: (int index, var item) {
      widget.reportClick(item);
      http.post<Map>('vrmcsys/appstore/accuseComment', data: {
        'cmtId': cmtId,
      }).then((response) {
        if (response.data["errCode"] == 0) {
          YvrToast.showToast(YLocal.of(context).ganxienindezhichinwo);
        } else {
          YvrToast.showToast(YLocal.of(context).jubaoshibai);
        }
      });
    });
  }
}
