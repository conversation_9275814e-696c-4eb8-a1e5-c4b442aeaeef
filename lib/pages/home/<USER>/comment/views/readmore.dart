import 'package:flutter/material.dart';
import 'package:expandable/expandable.dart';

class ReadMoreText extends StatefulWidget {
  final String text;
  final String expandButtonText;
  final String closeButtonText;
  final int maxLines;
  final Color buttonBackgroundColor;
  final TextStyle style;
  final TextStyle buttonStyle;
  final TextOverflow overflow;
  ReadMoreText(
      {Key key,
      @required this.text,
      @required this.buttonBackgroundColor,
      @required this.expandButtonText,
      @required this.closeButtonText,
      this.maxLines,
      this.style,
      this.buttonStyle,
      this.overflow = TextOverflow.fade})
      : super(key: key);

  @override
  _ReadMoreTextState createState() => _ReadMoreTextState();
}

class _ReadMoreTextState extends State<ReadMoreText> {
  int get maxLines => widget.maxLines;
  String get text => widget.text;
  String get expandButtonText => widget.expandButtonText;
  String get closeButtonText => widget.closeButtonText;
  TextStyle get style => widget.style;
  Color get buttonBackgroundColor => widget.buttonBackgroundColor;
  TextStyle get buttonStyle => widget.buttonStyle;
  TextOverflow get overflow => widget.overflow;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, size) {
      final span = TextSpan(text: text, style: style);
      final tp = TextPainter(
          text: span, maxLines: maxLines, textDirection: TextDirection.ltr);
      tp.layout(maxWidth: size.maxWidth);

      if (tp.didExceedMaxLines) {
        // 判断文字是否溢出
        return ExpandableNotifier(
          child: Column(crossAxisAlignment: CrossAxisAlignment.end, children: [
            Expandable(
              collapsed: Stack(
                children: [
                  Text(text,
                      maxLines: maxLines, overflow: overflow, style: style),
                  Positioned(
                      right: 0,
                      bottom: 0,
                      child: Container(
                        color: buttonBackgroundColor,
                        child: ExpandableButton(
                          child: RichText(
                              text: TextSpan(
                                  text: "...",
                                  style: style,
                                  children: [
                                TextSpan(
                                    text: ' $expandButtonText',
                                    style: buttonStyle)
                              ])),
                        ),
                      ))
                ],
              ),
              expanded: Stack(children: [
                Text(text, style: style),
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: ExpandableButton(
                    child: Text(closeButtonText, style: buttonStyle),
                  ),
                ),
              ]),
            )
          ]),
        );
      } else {
        return Text(text, style: style);
      }
    });
  }
}
