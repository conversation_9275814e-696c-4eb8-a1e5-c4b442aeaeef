import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/rating_star.dart';
import 'package:yvr_assistant/public_ui/widget/common_button.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/public_ui/widget/feedback_input.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/view_model/comments_vmodel.dart';

import 'package:yvr_assistant/styles/app_style.dart';
import '../../../tabbar/top_navbar.dart';

class JudgePage extends StatefulWidget {
  final arguments;
  JudgePage({Key key, @required this.arguments}) : super(key: key);

  @override
  _JudgePageState createState() => _JudgePageState();
}

class _JudgePageState extends State<JudgePage> {
  String starRemind = "";
  int _star = 0;
  String _content = '';
  bool _isCanBack = false;
  Map _judgeParam = {"star": 0, "aver": 0};
  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          if (isShowNavBackRemind()) {
            return true;
          }
          Navigator.pop(context, _judgeParam);
          return false;
        },
        child: Scaffold(
            backgroundColor: AppColors.colorFFFFFF,
            appBar: TopNavbar(
                title: YLocal.of(context).prod_my_comment,
                actions: [submitButtonWidget()],
                backCallBack: () {
                  if (isShowNavBackRemind()) {
                    return true;
                  }
                  Navigator.pop(context, _judgeParam);
                  return false;
                }),
            body: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    height: 20,
                  ),
                  Text(
                    YLocal.of(context)
                        .qingduiyingyongSdafe(widget.arguments["name"]),
                    style: AppStyle.style_textTitle_w500_16pt(),
                  ),
                  SizedBox(
                    height: 32,
                  ),
                  RatingBar(
                    initialRating: 0,
                    allowHalfRating: false,
                    itemCount: 5,
                    itemSize: 28,
                    updateOnDrag: true,
                    ratingWidget: ratingWidget,
                    itemPadding: EdgeInsets.symmetric(horizontal: 4.0),
                    onRatingUpdate: (rating) {
                      setState(() {
                        _star = rating.round();
                        this.starRemind = _star == 0
                            ? ""
                            : "$_star " + YLocal.of(context).home_star;
                      });
                    },
                  ),
                  SizedBox(
                    height: 12,
                  ),
                  Text(this.starRemind,
                      style: AppStyle.style_textSub_w400_14pt()),
                  SizedBox(
                    height: 38,
                  ),
                  Divider(
                    height: 1,
                    color: AppColors.divider,
                  ),
                  SizedBox(
                    height: 32,
                  ),
                  FeedbackInput(
                      hint: YLocal.of(context).prod_input_comment,
                      onChanged: (text) {
                        _content = text;
                      }),
                  SizedBox(
                    height: 100,
                  )
                ],
              ),
            )));
  }

  bool isShowNavBackRemind() {
    if ((_star > 0) && (!_isCanBack)) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (_) {
            return CustomDialog(
              title: YLocal.of(context).fangqidangqianpinglu,
              content: YLocal.of(context).shifoufangqidangqian,
              confirmText: YLocal.of(context).confirm,
              height: 200,
              confirmColor: Color(0xff52555C),
              confirmCallback: () {
                Navigator.pop(context, _judgeParam);
                return true;
              },
            );
          });
      return true;
    } else {
      return false;
    }
  }

  Widget submitButtonWidget() {
    return Container(
      height: kToolbarHeight,
      margin: EdgeInsets.fromLTRB(
          0, (kToolbarHeight - 24) / 2, 16, (kToolbarHeight - 24) / 2),
      child: CommonButton(
        text: YLocal.of(context).submit,
        height: 24,
        hPadding: 12,
        colors: [Color(0xFF66A6FF), Color(0xFF8E6DF2)],
        fontSize: 12,
        fontWeight: FontWeight.normal,
        radius: 24,
        onTap: () {
          if (_star >= 1) {
            Map data = {'appId': widget.arguments["appId"], 'star': _star};
            if (_content.length > 0) {
              data["content"] = _content.trim();
            }
            http
                .post<Map>('vrmcsys/appstore/commentApp', data: data)
                .then((response) {
              _isCanBack = true;
              if (response.data["errCode"] == 0) {
                Provider.of<UserIsComment>(context, listen: false)
                    .changeToUserIsComment(widget.arguments["appId"]);
                eventBus.fire(
                    EventFn({'kCommentProdId': widget.arguments["appId"]}));
                if (_content.length == 0) {
                  YvrToast.showSuccess(YLocal.of(context).pingfenyidijiaopingf);
                } else {
                  YvrToast.showSuccess(YLocal.of(context).pinglunyidijiaoshenh);
                }
                Log.d('_star =============>$_star');
                Log.d(
                    'response.data["aver"] =============>${response.data["aver"]}');
                _judgeParam["star"] = _star;
                _judgeParam["aver"] = response.data["aver"] ?? 0;
                Navigator.pop(context, _judgeParam);
              } else if (response.data["errCode"] == 12000) {
                YvrToast.showToast(YLocal.of(context).toast_emoticon_icon);
              } else if (response.data["errCode"] == 11001) {
                YvrToast.showToast(
                    YLocal.of(context).houduanjiekouxujianr('content'));
              }
            });
          }
        },
      ),
    );
  }
}
