import 'package:cached_network_image/cached_network_image.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/model/subject_model.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/pages/home/<USER>/banner_swiper.dart';

import '../../../generated/l10n.dart';
import '../../../model/summary_info_model.dart';
import '../../../public_ui/widget/common_button.dart';
import '../../../public_ui/widget/count_down_text.dart';
import '../../../utils/date_time_utils.dart';

class SubjCell extends StatefulWidget {
  final SubjectModel subjectModel;
  final Function clickFunc;
  SubjCell({Key key, @required this.subjectModel, @required this.clickFunc})
      : super(key: key);

  @override
  _SubjCellState createState() => _SubjCellState();
}

class _SubjCellState extends State<SubjCell> {
  @override
  Widget build(BuildContext context) {
    SummaryInfoModel summaryInfo = widget.subjectModel.summaryInfo;
    return GestureDetector(
      onTap: () {
        widget.clickFunc();
        if (summaryInfo != null && summaryInfo.bundle) {
          Navigator.pushNamed(context, '/bundledApplication',
              arguments: {'mpId': summaryInfo.subId});
        } else {
          Navigator.pushNamed(context, '/apps', arguments: {
            "isSingleSubject": true,
            "id": widget.subjectModel.id,
            "title": widget.subjectModel.name,
          });
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: AspectRatio(
                aspectRatio: 1.77,
                child: CachedNetworkImage(
                  fadeInDuration: Duration.zero,
                  fadeInCurve: Curves.easeOut,
                  fit: BoxFit.cover,
                  imageUrl: widget.subjectModel.pic,
                  placeholder: (context, url) => rPlhdImage,
                  errorWidget: (context, url, error) => rPlhdImage,
                ),
              ),
            ),
            Visibility(
                visible: summaryInfo != null && summaryInfo.bundle,
                child: Positioned(
                  left: 6,
                  top: 6,
                  child: CommonButton(
                      text: YLocal.of(context).kunbangbao,
                      width: 47,
                      height: 21,
                      fontSize: 9,
                      radius: 2,
                      colors: <Color>[Color(0xFF4F7FFE), Color(0xFF1656FF)]),
                ))
          ]),
          SizedBox(
            height: 8,
          ),
          Text(
            widget.subjectModel.name,
            style: AppStyle.style_textTitle_w400_14pt(),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),

          ///捆绑包主题
          Visibility(
            visible: summaryInfo != null && summaryInfo.bundle,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 8,
                ),
                Visibility(
                    visible: summaryInfo.hasAllPur ||
                        (!summaryInfo.hasAllPur && summaryInfo.sprice <= 0),
                    child: Text(
                        summaryInfo.hasAllPur
                            ? YLocal.of(context).yigoumai
                            : YLocal.of(context).mianfeiwenfei,
                        style: summaryInfo.hasAllPur
                            ? AppStyle.style_B0B2B8_14pt()
                            : AppStyle.style_standard_bold_14pt())),
                Visibility(
                    visible: !summaryInfo.hasAllPur && summaryInfo.sprice > 0,
                    child: Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text('￥${MoneyUtil.changeF2Y(summaryInfo.sprice)}',
                              style: AppStyle.style_standard_bold_14pt()),
                          SizedBox(width: 8),
                          Text('￥${MoneyUtil.changeF2Y(summaryInfo.bprice)}',
                              style: AppStyle.style_767880_10pt_line())
                        ])),
                Visibility(
                    visible: summaryInfo != null && summaryInfo.bundle,
                    child: SizedBox(height: 4)),
                Visibility(
                  visible: summaryInfo != null && summaryInfo.bundle,
                  child: DateTimeUtils.isShowCountDownText(
                          summaryInfo.promEndDiffTime)
                      ? CountDownText(
                          countSecond: summaryInfo.promEndDiffTime,
                          textStyle: AppStyle.style_B0B2B8_10pt(),
                        )
                      : SizedBox(),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
