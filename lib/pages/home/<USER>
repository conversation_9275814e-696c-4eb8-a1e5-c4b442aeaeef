import 'dart:convert';
import 'package:badges/badges.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:wifi_iot/wifi_iot.dart';
import 'package:yvr_assistant/main.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:common_utils/common_utils.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/utils/agreement.dart';
import 'package:yvr_assistant/model/apps_model.dart';
import 'package:yvr_assistant/utils/data_record.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';
import 'package:yvr_assistant/model/subject_model.dart';
import 'package:yvr_assistant/model/noti_pay_model.dart';
import 'package:yvr_assistant/utils/version_update.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/provider/view_state_model.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/pages/home/<USER>/apps_cell.dart';
import 'package:yvr_assistant/pages/home/<USER>/home_tool.dart';
import 'package:yvr_assistant/view_model/unread_vmodel.dart';
import 'package:yvr_assistant/model/page_list_model.dart';
import 'package:yvr_assistant/manager/resource_mananger.dart';
import 'package:yvr_assistant/pages/home/<USER>/dev_option.dart';
import 'package:yvr_assistant/pages/home/<USER>/search_bar.dart';
import 'package:yvr_assistant/pages/home/<USER>/message.dart';
import 'package:yvr_assistant/public_ui/skeleton/skeleton.dart';
import 'package:yvr_assistant/pages/home/<USER>/pay_noti_view.dart';
import 'package:yvr_assistant/public_ui/widget/refresh_layout.dart';
import 'package:yvr_assistant/pages/home/<USER>/tool/app_type.dart';
import 'package:yvr_assistant/public_ui/skeleton/app_list_skeleton.dart';
import 'package:yvr_assistant/provider/view_state_refresh_list_model.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/category_text.dart';

class HomePage extends StatefulWidget {
  HomePage({Key key}) : super(key: key);

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage>
    with RouteAware, WidgetsBindingObserver, AutomaticKeepAliveClientMixin {
  // 网络监听
  var subscription;
  // 用于刷新消息数量
  var eventBusFn;
  // 用于接收VR端购买通知
  var eventBusSocket;
  bool isCheckVersion = false; //是否检查版本
  ScrollController _scrollController = ScrollController();
  HomeFiltrateVModel _homeFiltrateVModel = HomeFiltrateVModel();
  GlobalKey<_BodyListState> _refreshListKey = GlobalKey<_BodyListState>();
  ValueNotifier<bool> _showNetworkRemindView = ValueNotifier<bool>(false);

  // 保存上次的搜索参数
  int _pIndex = 0;
  int _pTypeIdx = 0;
  HomeFiltrateVModel _pModel;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context));
  }

  @override
  void dispose() {
    super.dispose();
    eventBusFn?.cancel();
    eventBusSocket?.cancel();
    subscription?.cancel();
    routeObserver.unsubscribe(this);
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didPopNext() {
    super.didPopNext();
    Provider.of<UnreadVModel>(context, listen: false).getUnreadNtyNum();
    DataRecord().saveData(eventId: "assistant_appstore_store_0_0_page_view");
  }

  void getAppVerInfo({bool checkPrivacyPolicyAgreed = false}) {
    VersionUpdate.getAppVerInfo(appVersion, context,
        onAgree: checkPrivacyPolicyAgreed
            ? () {
                /// 安卓应用商店审核：
                /// 只有安卓才会再同意隐私政策后再初始化数据(主要是注册网络监听时会获取ssid导致审核失败)；
                /// 其它设备已经在initState时就执行了。
                if (Platform.isAndroid) {
                  initialize();
                }
              }
            : null, onVersionCallBack: () {
      isCheckVersion = true;
    });
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    Future.delayed(const Duration(milliseconds: 200), () {
      Provider.of<UnreadVModel>(context, listen: false).getUnreadNtyNum();
    });
    super.initState();

    Future.delayed(Duration(milliseconds: 1500), () {
      if (mounted) {
        getAppVerInfo(checkPrivacyPolicyAgreed: true);
      }
    });

    ///安卓应用商店审核：如果非安卓设备，可以直接初始化数据;是安卓设备就等用户同意隐私政策后再执行。
    if (!Platform.isAndroid || AgreementUtils.hasAgreed()) {
      initialize();
    }
    _homeFiltrateVModel.reset();
  }

  void initialize() {
    DataRecord().saveData(eventId: "assistant_appstore_store_0_0_page_view");
    StorageManager.foreverData.setItem(Global.kCurrentProdId, 0);
    StorageManager.localStorage.setItem("kIsActivityDetlPageYet", false);
    StorageManager.foreverData.setItem("isLoginPage", false);

    if (eventBusFn == null) {
      eventBusFn = eventBus.on<EventFn>().listen((event) {
        if (event.obj != null &&
            event.obj['HomeRefresh'] != null &&
            event.obj['HomeRefresh']) {
          Log.d('------------------首页刷新------------------');
          _refreshListKey.currentState?.viewModel?.refreshQuietly();
        } else {
          bool isRefresh = event.obj[Global.kRefreshHome] ?? false;
          if (event.obj["isHaveNetwork"] != null &&
              event.obj["isHaveNetwork"] &&
              _showNetworkRemindView.value) {
            _showNetworkRemindView.value = false;
          } else if (isRefresh) {
            _refreshListKey.currentState?.viewModel?.refreshQuietly();
          }
        }
      });
    }

    if (subscription == null) {
      // 网络监听
      subscription = Connectivity()
          .onConnectivityChanged
          .listen((ConnectivityResult result) async {
        Log.e("监听到网络变化");
        String ssid = await WiFiForIoTPlugin.getSSID();
        await StorageManager.foreverData.setItem("kWifiSsid", ssid);
        switch (result) {
          case ConnectivityResult.wifi:
            _showNetworkRemindView.value = false;
            break;
          case ConnectivityResult.mobile:
            _showNetworkRemindView.value = false;
            break;
          default:
            _showNetworkRemindView.value = true;
        }
        eventBus.fire(EventFn({Global.kReloadDevices: true}));
      });
    }

    if (eventBusSocket == null)
      // 注册监听器，订阅 eventbus
      eventBusSocket = eventBus.on<EventSocket>().listen((event) {
        if (event.socketModel.type == "notify" &&
            event.socketModel.tag == "recvAppPayOnMobile") {
          NotiPayModel notiPayModel =
              notiPayModelFromJson(jsonEncode(event.socketModel.data));
          Log.d('notiId ================>${notiPayModel.id}');
          StorageManager.localStorage.setItem(
              "NotiIdForApp_${notiPayModel.appId}", notiPayModel.id.toString());
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) {
                return PayNotiView(notiPayModel: notiPayModel);
              });
          YvrRequests.disposeNotify(
              ntyId: notiPayModel.id, tag: event.socketModel.tag, mark: 1);
        }
      });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return ChangeNotifierProvider<HomeFiltrateVModel>.value(
      value: _homeFiltrateVModel,
      child: Scaffold(
        appBar: AppBar(
          centerTitle: false,
          title: const HomeSearchBar(),
          actions: [
            InkWell(
              child: Badge(
                animationType: BadgeAnimationType.fade,
                showBadge: Provider.of<UnreadVModel>(context).unreadNum != 0,
                badgeContent: Text(
                  Provider.of<UnreadVModel>(context).unreadNum.toString(),
                  style: TextStyle(fontSize: 8, color: Colors.white),
                ),
                position: BadgePosition.topEnd(top: 12, end: -3),
                child: Icon(IconFonts.iconNoti),
              ),
              onTap: () {
                DataRecord().saveData(
                    eventId:
                        "assistant_appstore_store_topBar_notification_pit_click");
                if (remindLogin(
                    message: YLocal.of(context).xuyaodenglucainengch_2)) {
                  Navigator.pushNamed(context, '/message');
                }
              },
            ),
            const SizedBox(width: 10),
            HomeDevOptionView(onTap: () {
              categoryClick(_pIndex, _pTypeIdx, _pModel);
            }),
            const SizedBox(width: 20),
          ],
        ),
        body: Consumer<HomeFiltrateVModel>(
          builder: (context, viewModel, child) {
            _pModel = viewModel;
            return StateLayout(
                onReset: () {
                  viewModel.reset();
                },
                state: viewModel.viewState,
                builder: (context) {
                  return Stack(children: [
                    NestedScrollView(
                        controller: _scrollController,
                        headerSliverBuilder:
                            (BuildContext context, bool innerBoxIsScrolled) {
                          return <Widget>[
                            SliverOverlapAbsorber(
                              handle: NestedScrollView
                                  .sliverOverlapAbsorberHandleFor(context),
                              sliver: SliverToBoxAdapter(
                                child: Column(
                                  children: [
                                    categoryHorRow(0, viewModel),
                                    categoryHorRow(1, viewModel),
                                    categoryHorRow(2, viewModel),
                                    categoryHorRow(3, viewModel),
                                  ],
                                ),
                              ),
                            ),
                          ];
                        },
                        body: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: _BodyList(
                            key: _refreshListKey,
                            indexes: viewModel.indexList,
                            categories: viewModel.model,
                          ),
                        )),
                    Positioned(
                        top: 0,
                        left: 16,
                        right: 16,
                        child: CategoryTextView(
                          text: viewModel.topTitles,
                          scrollController: _scrollController,
                        ))
                  ]);
                },
                viewStateError: viewModel.viewStateError);
          },
        ),
      ),
    );
  }

  Widget categoryHorRow(int typeIdx, HomeFiltrateVModel model) {
    var categorys = model.model[typeIdx];
    return Container(
      height: 30,
      margin: EdgeInsets.fromLTRB(16, typeIdx == 0 ? 6 : 0, 16, 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categorys["msgs"].length,
        itemBuilder: (context, index) {
          return Builder(builder: (context) {
            bool isSelected = model.indexList[typeIdx] == index;
            Color color = isSelected ? null : AppColors.transparent;
            FontWeight fontWeight =
                isSelected ? FontWeight.bold : FontWeight.normal;
            Color textColor =
                isSelected ? AppColors.colorFFFFFF : AppColors.textTitle;
            return YvrTextButton(
              padding: const EdgeInsets.symmetric(horizontal: 14),
              child: Text(
                '${categorys["msgs"][index]}',
                style: TextStyle(
                    fontSize: 14, fontWeight: fontWeight, color: textColor),
              ),
              color: color,
              onPressed: () {
                categoryClick(index, typeIdx, model);
              },
            );
          });
        },
      ),
    );
  }

  //类目选中事件
  void categoryClick(int index, int typeIdx, HomeFiltrateVModel model) {
    // 保存上次的搜索参数
    _pIndex = index;
    _pTypeIdx = typeIdx;

    if (mounted) {
      var categoryList = model.model;
      var indexList = model.indexList;
      int cgy = categoryList[0]["values"][indexList[0]];
      int tag = categoryList[1]["values"][indexList[1]] ?? 0;
      var param = {
        "cgy": cgy,
        "tag": tag,
        "prg": indexList[2],
        "sort": indexList[3],
      };
      DataRecord().saveData(
          eventId: "assistant_appstore_allApps_filter_0_block_click",
          extraData: param);

      model.resetFiltration(typeIdx, index);
    }
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    // Log.d('应用切换状态： ${state.toString()}');
    switch (state) {
      //切换前台时,可回调，初始化时，收不到回调
      case AppLifecycleState.resumed:
        HandelPayMessage.handelMsg();
        if (Platform.isIOS) {
          String ssid = await WiFiForIoTPlugin.getSSID();
          String preSsid = StorageManager.foreverData.getItem("kWifiSsid");
          if (preSsid != ssid) {
            await StorageManager.foreverData.setItem("kWifiSsid", ssid);
            eventBus.fire(EventFn({Global.kReloadDevices: true}));
          }
        }
        eventBus.fire(EventFn({Global.kRefreshRecordingPage: true}));
        if (isCheckVersion) {
          getAppVerInfo();
        }
        break;
      //切换后台时, inactive，pause先后回调
      case AppLifecycleState.inactive:
        eventBus.fire(EventFn({Global.kAppInBackground: true}));
        break;
      // 应用进入非活动状态
      case AppLifecycleState.paused:
        break;
      // 应用进入页面初始化或销毁无视图状态
      case AppLifecycleState.detached:
        break;
      default:
    }
  }

  @override
  bool get wantKeepAlive => true;
}

class _BodyList extends StatefulWidget {
  final List<Map<dynamic, dynamic>> categories;
  final List<int> indexes;

  const _BodyList({
    Key key,
    @required this.categories,
    @required this.indexes,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _BodyListState();
  }
}

class _BodyListState extends State<_BodyList> {
  HomeAppVModel viewModel;

  @override
  void initState() {
    super.initState();
    viewModel = HomeAppVModel(widget.indexes, widget.categories);
  }

  @override
  Widget build(BuildContext context) {
    return SimpleRefreshList<HomeAppVModel>(
      model: viewModel,
      builder: (context, viewModel) {
        return CustomScrollView(
          slivers: [
            SliverOverlapInjector(
              handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
            ),
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (BuildContext context, int index) {
                  return AppsCell(
                    appsModel: viewModel.list[index],
                    clickFunc: () {
                      DataRecord().saveData(
                          eventId:
                              "assistant_appstore_allApps_appList_app_pit_click",
                          extraData: {"appId": viewModel.list[index].id});
                    },
                  );
                },
                childCount: viewModel.list.length, // 仅构建需要的组件
              ),
            ),
          ],
        );
      },
      emptyBuilder: (context, model) {
        return Center(
          child: Column(
            children: [
              const SizedBox(height: 150),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 30),
                child: Text(
                  YLocal.of(context).henbaoqianmeiyouzhao,
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16, color: AppColors.textTitle),
                ),
              ),
              const SizedBox(height: 10),
              Text(
                YLocal.of(context).qingshiyongjitashaix,
                style: TextStyle(fontSize: 14, color: AppColors.textSub),
              )
            ],
          ),
        );
      },
      loadingBuilder: (context, model) {
        return SkeletonList(
          length: 11,
          builder: (context, index) => AppSkeletonItem(),
        );
      },
    );
  }

  @override
  void didUpdateWidget(covariant _BodyList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.categories != widget.categories ||
        oldWidget.indexes != widget.indexes) {
      viewModel.resetData(widget.categories, widget.indexes);
    }
  }
}

class HomeFiltrateVModel extends ViewStateModel2<List<Map<dynamic, dynamic>>> {
  String topTitles = '';
  List<int> indexList = [0, 0, 0, 0];
  static const int kFiltrateCount = 4;

  @override
  Future<List<Map<dynamic, dynamic>>> loadData(
      int pageSize, int pageNum) async {
    await initAppTypes();

    List<Map<dynamic, dynamic>> categoryList =
        (StorageManager.localStorage.getItem("kCategorys") as List)
            ?.cast<Map<dynamic, dynamic>>();

    if (categoryList == null || categoryList.isEmpty) {
      return null;
    }

    if (categoryList.length != 4 ||
        categoryList.indexWhere((e) {
              List values = e['msgs'];
              return values == null || values.isEmpty;
            }) >=
            0) {
      throw Exception(YLocal.current.canshucuowucenshucuo);
    }

    final List<int> indexes = List<int>.from(indexList);

    for (int i = 0; i < categoryList.length; ++i) {
      List values = categoryList[i]['msgs'];
      if (indexes[i] >= values.length) {
        indexes[i] = 0;
      }
    }

    this.indexList = indexes;
    return categoryList;
  }

  @override
  void onRefreshComplete(List<Map> data) {
    resetTopTitles(data);
  }

  void resetTopTitles(List<Map<dynamic, dynamic>> data) {
    if (indexList[0] == 0 && indexList[1] == 0 && indexList[2] == 0) {
      int selIdx = indexList[3];
      topTitles = data[3]["msgs"][selIdx];
    } else {
      List tempList = [];
      for (var i = 0; i < indexList.length; i++) {
        int selIdx = indexList[i];
        if (selIdx != 0 || i == 3) {
          tempList.add(data[i]["msgs"][selIdx]);
        }
      }
      topTitles = tempList.join("·");
    }
  }

  void resetFiltration(int typeIndex, int index) {
    final List<int> indexes = List<int>.from(indexList);
    indexes[typeIndex] = index;
    this.indexList = indexes;
    resetTopTitles(model);
    notifyListeners();
  }
}

class HomeAppVModel extends ViewStateRefreshListModel2<AppsModel> {
  static const int kFiltrateCount = 4;

  List<int> indexes;
  List<Map<dynamic, dynamic>> categories;

  HomeAppVModel(this.indexes, this.categories)
      : super(pageParams: const PageParams(10, 4));

  @override
  Future<PageListModel<AppsModel>> loadData(int pageSize, int pageNum) async {
    if (pageNum == ViewStateModel2.pageNumFirst) {
      String token = DBUtil.instance.userBox.get(kToken) ?? "";
      if (ObjectUtil.isNotEmpty(token)) {
        List payIds = await YvrRequests.getPurchasedAppId();
        await StorageManager.localStorage.setItem(Global.kPayAppList, payIds);
      }
    }

    // Log.e(categories[2]);

    SubjectModel subjectModel = await YvrRequests.getAppsOnStore(
        page: pageNum,
        cgy: categories[0]["values"][indexes[0]],
        tag: categories[1]["values"][indexes[1]],
        // prg: indexes[2],
        sort: indexes[3] + 1,
        lprice: categories[2]['lprice'][indexes[2]],
        hprice: categories[2]['hprice'][indexes[2]],
        psize: pageSize);

    var tempList = subjectModel.apps;
    final jsonList = tempList.map<String>((item) => jsonEncode(item)).toList();
    final uniqJsonList = jsonList.toSet().toList();
    var result = uniqJsonList.map((item) => jsonDecode(item)).toSet().toList();
    var dataList =
        List<AppsModel>.from(result.map((x) => AppsModel.fromJson(x)));
    return PageListModel.create(pageNum, pageSize, dataList);
  }

  void resetData(List<Map<dynamic, dynamic>> categories, List<int> indexes) {
    this.categories = categories;
    this.indexes = indexes;
    reset();
  }
}
