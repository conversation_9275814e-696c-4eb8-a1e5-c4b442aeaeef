// ignore_for_file: unused_local_variable

import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_logan/flutter_logan.dart';
import 'package:yvr_assistant/manager/environment.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';

import '../../generated/l10n.dart';
import '../../utils/crashlytics.dart';
import '../../view_model/user_vmodel.dart';

class LoganPage extends StatefulWidget {
  LoganPage({Key key}) : super(key: key);

  @override
  _LoganPageState createState() => _LoganPageState();
}

class _LoganPageState extends State<LoganPage> {
  String _showText = 'you should init log first';

  Future<void> log() async {
    String result = 'Write log succeed';
    try {
      await FlutterLogan.log("main.dart", 'this is log string', 1);
    } on PlatformException {
      result = 'Failed to write log';
    }
    if (!mounted) return;
    setState(() {
      _showText = result;
    });
  }

  Future<void> _getUploadPath() async {
    String result;
    try {
      final today = DateTime.now();
      final date =
          "${today.year.toString()}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}";
      final String path = await FlutterLogan.getUploadPath(date);
      if (path != null) {
        result = 'upload path = ' + path;
      } else {
        result = 'Failed to get upload path';
      }
    } on PlatformException {
      result = 'Failed to get upload path';
    }
    if (!mounted) return;
    setState(() {
      _showText = result;
    });
  }

  Widget buttonWidge(String title, Function event) {
    return new Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // ignore: deprecated_member_use
        new FlatButton(
          onPressed: event,
          child: Text(
            title,
            style: TextStyle(color: Colors.white),
          ),
          color: Colors.blue,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget buttonSection = new Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            buttonWidge('flush', _flush),
            buttonWidge('cleanAllLog', _cleanAllLog),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            buttonWidge('log', log),
            buttonWidge('getUploadUrl', _getUploadPath),
            buttonWidge('uploadToServer', uploadToServer),
          ],
        ),
        if (Platform.isAndroid)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              buttonWidge('testAndroidJavaCrash', _testAndroidJavaCrash),
              buttonWidge('testAndroidNativeCrash', _testAndroidNativeCrash),
            ],
          )
      ],
    );
    return MaterialApp(
      home: Scaffold(
          appBar: AppBar(
            title: Text(YLocal.of(context).rizhidiaoshirizhitia),
          ),
          body: Column(
            children: [
              new Container(
                margin: const EdgeInsets.only(top: 30.0),
                child: buttonSection,
              ),
              new Container(
                margin: const EdgeInsets.only(top: 30.0, left: 10, right: 10),
                child: new Text(_showText),
              )
            ],
          )),
    );
  }
}

Future<void> _testAndroidJavaCrash() {
  return FlutterLogan.testAndroidJavaCrash();
}

Future<void> _testAndroidNativeCrash() {
  return FlutterLogan.testAndroidNativeCrash();
}

String get _serverUrl {
  // 'http://***************:8080/logan-web-beta-1.0-SNAPSHOT/logan/upload.json',
  return Environment().config.apiHost + '/wpserver/logan/upload.json';
}

// http://***************:8080/logan_web
// https://apiTest.yvrdream.com/wpserver
Future<void> uploadToServer() async {
  await _flush();
  String result = 'Failed upload to server';
  try {
    final date = DateUtil.formatDate(DateTime.now(), format: "yyyy-MM-dd");
    String buildVersion = await PlatformUtils.getBuildNum();
    String appVersion = await PlatformUtils.getAppVersion();
    // unionId can be used to diff upload source, eg: app or device
    final bool back = await FlutterLogan.upload(
      _serverUrl,
      date,
      'com.yvr.fmba',
      'app',
      Platform.operatingSystemVersion,
      buildVersion,
      appVersion,
      Platform.operatingSystem,
    );
    if (back) {
      result = 'Upload to server succeed';
    }
    await _cleanAllLog();
  } on PlatformException {
    result = 'Failed upload to server';
  }
  // Log.d(result);
}

Future<void> _flush() async {
  String result = 'Flush log succeed';
  try {
    await FlutterLogan.flush();
  } on PlatformException {
    result = 'Failed to flush log';
  }
  // Log.d(result);
}

Future<void> _cleanAllLog() async {
  String result = 'Clean log succeed';
  try {
    await FlutterLogan.cleanAllLogs();
  } on PlatformException {
    result = 'Failed to clean log';
  }
  // Log.d(result);
}

Future<void> initLogan() async {
  String result = 'Failed to init log';
  try {
    final bool back = await FlutterLogan.init(
        '0123456789012345', '0123456789012345', 1024 * 1024 * 10);
    if (back) {
      StorageManager.foreverData.setItem("isLoganInitSuccess", true);
      result = 'Init log succeed';
      Crashlytics.instance.startReportTimer();
    }
  } on PlatformException {
    result = 'Failed to init log';
  }
  // Log.d("init：$result");
}

/// 腾讯检测包括规定；隐私协议同意后，再记录日志
void logan(String tag, String summary, [int type = 1]) {
  // if (StorageManager.foreverData.getItem("isLoganInitSuccess") ?? false) {
  //   FlutterLogan.log(tag, summary, type);
  // }
}

void loganInfo(String tag, String summary, [int type = 1]) {
  try {
    StringBuffer sb = StringBuffer();

    String deviceName = StorageManager.deviceModel;
    sb.write('device:');
    sb.writeln(deviceName);

    int actId = DBUtil.instance.userBox.get(kActId) ?? null;
    sb.write('userId:');
    sb.writeln(actId);

    sb.writeln(summary);

    logan(tag, sb.toString());
  } catch (e, s) {
    print(s);
  }
}
