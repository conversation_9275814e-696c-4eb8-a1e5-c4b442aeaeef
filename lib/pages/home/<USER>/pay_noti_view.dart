import 'package:flutter/material.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/model/noti_pay_model.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/styles/app_color.dart';

import '../../../generated/l10n.dart';

typedef BuyCallback = Function();

class PayNotiView extends StatefulWidget {
  final NotiPayModel notiPayModel;

  PayNotiView({Key key, @required this.notiPayModel}) : super(key: key);

  @override
  _PayNotiViewState createState() => _PayNotiViewState();
}

class _PayNotiViewState extends State<PayNotiView> {
  @override
  Widget build(BuildContext context) {
    return Center(
        child: Container(
            height: (194 / 346) * (Global.screenWidth - 20 * 2) + 180,
            child: Card(
              color: AppColors.colorFFFFFF,
              margin: EdgeInsets.fromLTRB(16, 0, 16, 18),
              clipBehavior: Clip.antiAlias,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(6.0)),
              ),
              child: Column(children: [
                AspectRatio(
                  aspectRatio: 346 / 194,
                  child: FadeInImage.assetNetwork(
                    placeholder: 'assets/images/r_plhd.png',
                    image: widget.notiPayModel.rcover ?? "",
                    fit: BoxFit.cover,
                    imageErrorBuilder: (context, error, stackTrace) {
                      return Image.asset(
                        'assets/images/r_plhd.png',
                        fit: BoxFit.cover,
                      );
                    },
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(bottom: 5),
                  child: ListTile(
                    title: Padding(
                        padding: EdgeInsets.only(top: 16, bottom: 16),
                        child: Text(
                          YLocal.of(context).nindeVRshebeixiangni(
                              widget.notiPayModel.appName),
                          style: TextStyle(fontSize: 16, height: 1.5),
                        )),
                    subtitle: Flex(
                      direction: Axis.horizontal,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: <Widget>[
                        TextButton(
                          onPressed: () {
                            notiPayResultForVR(
                                appId: widget.notiPayModel.appId,
                                ntyId: widget.notiPayModel.id.toString(),
                                isPay: false);
                            Navigator.pop(context);
                          },
                          child: Text(YLocal.of(context).shaohouqianwang,
                              style: TextStyle(
                                color: Color(0xFF4F7FFE),
                              )),
                        ),
                        SizedBox(
                          width: 8,
                        ),
                        Container(
                          // ignore: deprecated_member_use
                          child: RaisedButton(
                            color: Color(0xFF4F7FFE),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4)),
                            child: Text(
                              YLocal.of(context).lijiqianwang,
                              style: TextStyle(
                                  fontSize: 14, color: Color(0xFFE8E8E8)),
                            ),
                            onPressed: () {
                              Navigator.pop(context);
                              //star被借用为销售类型， 0:上面的信息都是应用信息， 1：上面的信息都是专题信息
                              if (widget.notiPayModel.star == 1) {
                                if (StorageManager.foreverData
                                        .getItem(Global.kCurrentSubId) !=
                                    widget.notiPayModel.appId) {
                                  Navigator.pushNamed(
                                      context, '/bundledApplication',
                                      arguments: {
                                        'mpId': widget.notiPayModel.appId
                                      });
                                }
                              } else {
                                if (StorageManager.foreverData
                                        .getItem(Global.kCurrentProdId) !=
                                    widget.notiPayModel.appId) {
                                  Navigator.pushNamed(context, '/prod',
                                      arguments: {
                                        "id": widget.notiPayModel.appId
                                      });
                                }
                              }
                            },
                          ),
                        )
                      ],
                    ),
                  ),
                )
              ]),
            )));
  }
}

notiPayResultForVR({
  @required String ntyId,
  @required int appId,
  @required bool isPay,
}) {
  if (ntyId.length != 0 && ntyId != "") {
    /// 应用购买成功之后，若为VR通知购买的应用，需要给予回馈
    http.post<Map>('vrmcsys/appstore/handleDevAppPay',
        data: {"ntyId": ntyId, "status": (isPay ? 2 : 3)}).then((response) {
      if (response.data["errCode"] == 0) {
        StorageManager.localStorage.deleteItem("NotiIdForApp_$appId");
      }
    });
  }
}
