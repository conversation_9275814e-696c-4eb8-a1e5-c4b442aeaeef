import 'package:flutter/material.dart';
import 'package:yvr_assistant/model/pay_history_model.dart';
import 'package:yvr_assistant/public_ui/widget/image_view.dart';
import 'package:yvr_assistant/utils/data_record.dart';

import '../../../styles/app_color.dart';

class MineAppCell extends StatefulWidget {
  final bool isSelectApp;
  final PayHistoryModel mineAppModel;

  MineAppCell({Key key, @required this.mineAppModel, this.isSelectApp})
      : super(key: key);

  @override
  _MineAppCellState createState() => _MineAppCellState();
}

class _MineAppCellState extends State<MineAppCell> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (widget.isSelectApp) {
          Navigator.pop(context, widget.mineAppModel);
        } else {
          DataRecord().saveData(
              eventId: "assistant_appstore_myApps_appList_app_pit_click",
              extraData: {
                "appId": widget.mineAppModel.appId,
              });
          Navigator.pushNamed(context, '/prod', arguments: {
            "id": widget.mineAppModel.appId,
          });
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: AspectRatio(
              aspectRatio: 1,
              child: NetworkImageWidget(
                widget.mineAppModel.scover,
              ),
            ),
          ),
          SizedBox(
            height: 4,
          ),
          Expanded(
            child: Text(
              widget.mineAppModel.appName ?? '',
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
                height: 1.28571,
                color: AppColors.textTitle,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
