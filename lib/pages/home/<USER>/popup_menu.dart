import 'package:flutter/material.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';

class PopupView extends StatefulWidget {
  final int defIndex;
  final Function onTap;
  final double offsetDy;
  final Icon iconWidget;
  final Color itemsecondaryBg;
  final List<String> titles;
  final TextStyle titleStyle;
  final BoxDecoration decoration;
  final EdgeInsetsGeometry padding;
  const PopupView(
      {Key key,
      this.defIndex = 0,
      this.offsetDy = 35,
      this.titles,
      this.onTap,
      this.padding,
      this.titleStyle,
      this.iconWidget,
      this.itemsecondaryBg,
      this.decoration})
      : super(key: key);

  @override
  State<PopupView> createState() => _PopupViewState();
}

class _PopupViewState extends State<PopupView> {
  int _dropIndex = 0;

  @override
  void initState() {
    super.initState();
    _dropIndex = widget.defIndex;
  }

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<int>(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            height: 30,
            padding:
                widget.padding ?? const EdgeInsets.only(left: 12, right: 8),
            decoration: widget.decoration ??
                BoxDecoration(
                  color: AppColors.secondaryBg,
                  borderRadius: BorderRadius.circular(20),
                ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(widget.titles[_dropIndex],
                    style: widget.titleStyle ??
                        AppStyle.style_textSub_w400_14pt()),
                widget.iconWidget ??
                    Icon(
                      Icons.keyboard_arrow_down_rounded,
                      size: 18,
                      color: AppColors.textTitle,
                    ),
              ],
            ),
          ),
        ],
      ),
      color: widget.itemsecondaryBg ?? AppColors.secondaryBg,
      offset: Offset(0, widget.offsetDy),
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadiusDirectional.circular(8)),
      itemBuilder: (context) => popupMenuItems(widget.titles),
      onSelected: (value) async {
        if (value != _dropIndex) {
          setState(() {
            _dropIndex = value;
          });
          widget.onTap?.call(value);
        }
      },
    );
  }

  List<PopupMenuEntry<int>> popupMenuItems(List<String> titles) {
    return List.generate(titles.length, (index) {
      return PopupMenuItem<int>(
        value: index,
        padding: const EdgeInsets.only(left: 12),
        child: Container(
          height: 24,
          width: 90,
          alignment: Alignment.center,
          child: Text(
            titles[index],
            style: index == _dropIndex
                ? AppStyle.style_textTitle_w500_14pt() // 选中的样式
                : AppStyle.style_textSub_w400_14pt(), // 未选中的样式
          ),
        ),
      );
    });
  }
}
