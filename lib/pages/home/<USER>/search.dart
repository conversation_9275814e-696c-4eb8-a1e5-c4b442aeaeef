import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:restart_app/restart_app.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/utils/data_record.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/utils/yvr_utils.dart';
import 'package:common_utils/common_utils.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/environment.dart';
import 'package:yvr_assistant/manager/resource_mananger.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/model/hot_download_model.dart';
import 'package:yvr_assistant/model/theme_data_model.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/result_cell.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/hot_download_cell.dart';

class SearchPage extends StatefulWidget {
  SearchPage({Key key}) : super(key: key);

  @override
  _SearchPageState createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  // var _controller = new TextEditingController();
  String _inputText = "";
  FocusNode _focusNode = FocusNode();
  bool _isResult = false;
  List _historyWords = [];
  TimerUtil mTimerUtil;
  TextEditingController _controller;
  static String _changeEnvKeyword = "切换环境";

  var eventBusFn;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController.fromValue(TextEditingValue(
        text: _inputText,
        // 保持光标在最后
        selection: TextSelection.fromPosition(TextPosition(
            affinity: TextAffinity.downstream, offset: _inputText.length))));

    getHotApps();

    if (StorageManager.localStorage.getItem("SearchWods") != null) {
      String strList = StorageManager.localStorage.getItem("SearchWods");
      _historyWords = json.decode(strList);
    }

    eventBusFn = eventBus.on<EventFn>().listen((event) {
      bool isRefresh = event.obj[Global.kRefreshAppPayStatus] ?? false;
      if (isRefresh) {
        getHotApps();
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    mTimerUtil?.cancel();
    eventBusFn.cancel();
  }

  List<Widget> _getSearchWordsList() {
    var tempList = _historyWords.reversed.map((word) {
      return InkWell(
        onTap: () {
          _isResult = true;
          reqResultListData(word: word, isSave: false);
        },
        child: Container(
          child: new Text(
            word,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
            style: AppStyle.style_textTitle_w400_14pt(),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: AppColors.backgroundItem,
          ),
        ),
      );
    });
    return tempList.toList();
  }

  List<Widget> _getHotDownloadList(List<HotDownloadModel> models) {
    var tempList = models.map((app) {
      return HotDownloadCell(appsModel: app);
    });
    return tempList.toList().take(5).toList();
  }

  List<Widget> _hotList = [];
  List<Widget> _resultList = [];

  List<Widget> _getReslutList(List models) {
    var tempList = models.map((app) {
      return SearchResultCell(
        appsModel: app,
        appClick: () {
          saveWords(app.name);
          DataRecord().saveData(
              eventId: "assistant_appstore_search_appList_app_pit_click",
              extraData: {"searchTerm": app.name, "appId": app.id});
          Navigator.pushNamed(context, '/prod', arguments: {"id": app.id});
        },
      );
    });
    return tempList.toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppColors.colorFFFFFF,
        appBar: AppBar(
          centerTitle: false,
          leading: Container(
            padding: EdgeInsets.only(left: 20),
            child: IconButton(
                icon: Icon(
                  Icons.arrow_back_ios,
                  color: AppColors.textSub,
                ),
                onPressed: () {
                  Navigator.pop(context);
                }),
          ),
          title: Container(
            height: 32,
            margin: EdgeInsets.only(right: 15),
            child: TextField(
              autofocus: true,
              maxLines: 1,
              controller: _controller,
              focusNode: _focusNode,
              onChanged: (value) {
                if (_controller.value.isComposingRangeValid) {
                  return;
                }
                reqResultListData(word: value, isSave: false);
              },
              onSubmitted: (text) {
                _focusNode.unfocus();
                reqResultListData(word: text, isSave: true);
              },
              decoration: InputDecoration(
                  focusedBorder: OutlineInputBorder(
                      borderSide:
                          BorderSide(width: 0, color: Colors.transparent)),
                  disabledBorder: OutlineInputBorder(
                      borderSide:
                          BorderSide(width: 0, color: Colors.transparent)),
                  enabledBorder: OutlineInputBorder(
                      borderSide:
                          BorderSide(width: 0, color: Colors.transparent)),
                  border: OutlineInputBorder(
                      borderSide:
                          BorderSide(width: 0, color: Colors.transparent)),
                  contentPadding: EdgeInsets.symmetric(vertical: 0),
                  fillColor: Color(0xff393A3C),
                  prefixIcon: Icon(
                    IconFonts.iconSearch,
                    size: 17,
                    color: AppColors.color8E94A6,
                  ),
                  suffixIcon: _inputText.length > 0
                      ? IconButton(
                          icon: Icon(
                            Icons.clear,
                            size: 20,
                            color: Color(0xff808080),
                          ),
                          onPressed: () {
                            _controller.clear();
                            setState(() {
                              _inputText = "";
                              _isResult = false;
                              _resultList = [];
                            });
                          },
                        )
                      : Text(""),
                  hintText: YLocal.of(context).home_search_plhd,
                  hintStyle: TextStyle(color: Color(0xffB0B2B8), fontSize: 14)),
            ),
            decoration: BoxDecoration(
              color: AppColors.secondaryBg,
              borderRadius: BorderRadius.circular(16.0),
            ),
          ),
        ),
        body: _isResult
            ? SingleChildScrollView(
                padding: EdgeInsets.only(top: 20),
                child: _resultList.length > 0
                    ? Column(
                        children: _resultList,
                      )
                    : Center(
                        child: Column(
                        children: [
                          SizedBox(height: 100),
                          Icon(IconFonts.iconGroup,
                              size: 56, color: Color(0xff52555c)),
                          SizedBox(
                            height: 24,
                          ),
                          Text(YLocal.of(context).meiyoufugetiaojiande,
                              style: TextStyle(
                                color: Color(0xffB0B2B8),
                                fontSize: 12,
                              ))
                        ],
                      )))
            : SingleChildScrollView(
                padding: EdgeInsets.zero,
                child: Column(
                  children: [
                    _historyWords.length > 0
                        ? ListTile(
                            title: Text(
                              YLocal.of(context).home_search_history,
                              style: AppStyle.style_textTitle_w500_16pt(),
                            ),
                            trailing: IconButton(
                                icon: Icon(IconFonts.iconClear,
                                    size: 18, color: AppColors.textSub),
                                onPressed: () => deleteHistoryWords()))
                        : SizedBox(),
                    Container(
                      alignment: Alignment(-1, -1),
                      margin: EdgeInsets.only(left: 15, right: 15),
                      child: Wrap(
                          spacing: 16.0, // 主轴(水平)方向间距
                          runSpacing: 12.0, // 纵轴（垂直）方向间距
                          alignment: WrapAlignment.start,
                          children: _getSearchWordsList()),
                    ),
                    // HotWordsList(),
                    Container(
                      margin: EdgeInsets.only(
                          left: 15, right: 15, top: 40, bottom: 15),
                      alignment: Alignment.centerLeft,
                      child: Text(YLocal.of(context).home_hot_download,
                          style: AppStyle.style_textTitle_w500_16pt()),
                    ),
                    Column(
                      children: _hotList,
                    )
                  ],
                ),
              ));
  }

  deleteHistoryWords() {
    StorageManager.localStorage.setItem("SearchWods", json.encode([]));
    setState(() {
      _historyWords = [];
    });
  }

  getHotApps() {
    Log.d("开始获取数据");
    http.post<Map>('vrmcsys/appstore/getHotApps', data: {}).then((response) {
      List<HotDownloadModel> dataList = response.data["apps"]
              .map<HotDownloadModel>((item) => HotDownloadModel.fromJson(item))
              .toList() ??
          [];
      if (dataList.length > 0) {
        Log.d("成功获取数据 ${dataList.length}");
        setState(() {
          _hotList = _getHotDownloadList(dataList);
        });
      }
    }).catchError((error) {});
  }

  reqResultListData({String word, bool isSave = false}) {
    if (word.length == 0) {
      setState(() {
        // 否则输入法单个删除到最后一个字符删除后 仍然显示
        _inputText = "";
        _isResult = false;
      });
      mTimerUtil?.cancel();
      return;
    }
    // TODO: 2/2 环境切换彩蛋 - 仅测试时打开注释 🌈

    if (word == _changeEnvKeyword) {
      changeEnvironment();
      return;
    }

    if (isSave) {
      mTimerUtil?.cancel();
      mTimerUtil = new TimerUtil(mInterval: 1000);
      mTimerUtil.startTimer();
      mTimerUtil.setOnTimerTickCallback((int tick) {
        if (isSave) {
          saveWords(word);
        }
        searchAppOnStore(word);
        mTimerUtil.cancel();
      });
    } else {
      searchAppOnStore(word);
    }
  }

  searchAppOnStore(String word) {
    DataRecord().saveData(
        eventId: "assistant_appstore_search_searchHistory_keyword_pit_click",
        extraData: {"searchTerm": word});
    http.post<Map>('vrmcsys/appstore/searchAppOnStore', data: {
      'kword': word,
    }).then((response) {
      _inputText = word;
      ThemeDataModel themeDataModel = ThemeDataModel.fromJson(response.data);
      if (themeDataModel.apps != null) {
        setState(() {
          _isResult = true;
          _resultList = _getReslutList(themeDataModel.apps);
        });
      }
    }).catchError((error) {});
  }

  saveWords(String word) {
    if (_historyWords.length > 9) {
      _historyWords.removeRange(0, 1);
    }
    if (_historyWords.contains(word)) {
      _historyWords.remove(word);
    }
    _historyWords.add(word);
    StorageManager.localStorage
        .setItem("SearchWods", json.encode(_historyWords));

    Log.d(_historyWords.toString());
  }

  changeEnvironment() {
    showModalBottomSheet(
        context: context,
        clipBehavior: Clip.hardEdge,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10.0),
          topRight: Radius.circular(10.0),
        )),
        builder: (_) {
          return Container(
            height: 360,
            child: Stack(
              children: [
                Positioned(
                    top: 20,
                    child: Container(
                      padding: const EdgeInsets.only(left: 20, right: 20),
                      width: Global.screenWidth,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(_changeEnvKeyword,
                              style: const TextStyle(fontSize: 20)),
                          InkWell(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            child: Icon(Icons.clear),
                          )
                        ],
                      ),
                    )),
                Positioned(
                    top: 60,
                    child: Container(
                        height: 0.5,
                        width: Global.screenWidth,
                        color: const Color.fromARGB(255, 168, 168, 168))),
                environmentCells((String envMode) async {
                  await DBUtil.clearAll();
                  await DBUtil.instance.envBox
                      .put(Global.kEnvMode, envMode ?? Environment.PROD);
                  await YvrUtils.resetUserInfo();
                  Restart.restartApp();
                }),
              ],
            ),
          );
        });
  }

  Widget environmentCells(Function callback) {
    List envMap = Environment.envMap;
    String selEnv =
        DBUtil.instance.envBox.get(Global.kEnvMode) ?? Environment.PROD;
    return Container(
      margin: const EdgeInsets.only(top: 70),
      child: ListView.builder(
        scrollDirection: Axis.vertical,
        itemCount: envMap.length,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          bool isSel = (selEnv == envMap[index]["mode"]);
          return Container(
              alignment: Alignment.center,
              width: Global.screenWidth,
              child: GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                  if (!isSel) {
                    callback(envMap[index]["mode"]);
                  }
                },
                child: ListTile(
                  contentPadding: const EdgeInsets.symmetric(horizontal: 20),
                  title: Text(
                    envMap[index]["name"],
                    style: TextStyle(
                        fontWeight: isSel ? FontWeight.w500 : FontWeight.w400),
                  ),
                  selected: (selEnv == envMap[index]["mode"]),
                  selectedColor: Colors.green,
                  selectedTileColor: const Color.fromARGB(31, 78, 242, 220),
                  trailing: Visibility(
                      visible: isSel,
                      child: Icon(
                        Icons.done,
                        color: Colors.green,
                      )),
                ),
              ));
        },
      ),
    );
  }
}
