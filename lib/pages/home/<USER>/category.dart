import 'dart:convert';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:yvr_assistant/base/lifecycle_state_mixin.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/model/apps_model.dart';
import 'package:yvr_assistant/model/page_list_model.dart';
import 'package:yvr_assistant/pages/home/<USER>/apps_cell.dart';
import 'package:yvr_assistant/pages/home/<USER>/tool/app_type.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/category_text.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/public_ui/skeleton/app_list_skeleton.dart';
import 'package:yvr_assistant/public_ui/skeleton/skeleton.dart';
import 'package:yvr_assistant/utils/data_record.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import '../../../model/subject_model.dart';
import '../../../network/request.dart';
import '../../../provider/view_state_model.dart';
import '../../../provider/view_state_refresh_list_model.dart';
import '../../../public_ui/widget/buttons.dart';
import '../../../public_ui/widget/refresh_layout.dart';
import '../../../view_model/user_vmodel.dart';

class CategoryPage extends StatefulWidget {
  CategoryPage({Key key}) : super(key: key);

  @override
  _CategoryPageState createState() => _CategoryPageState();
}

class _CategoryPageState extends LifecycleState<CategoryPage> {
  HomeFiltrateVModel _homeFiltrateVModel = HomeFiltrateVModel();

  GlobalKey<_BodyListState> _refreshListKey = GlobalKey<_BodyListState>();

  ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _homeFiltrateVModel.reset();
  }

  @override
  void onRefresh() {
    _refreshListKey.currentState?.viewModel?.refreshQuietly();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<HomeFiltrateVModel>.value(
      value: _homeFiltrateVModel,
      child: Scaffold(
        appBar: TopNavbar(title: YLocal.of(context).home_all_app, actions: [
          GestureDetector(
              onTap: () {
                Navigator.pushNamed(context, '/search');
              },
              child: Container(
                width: 19,
                height: 19,
                margin: EdgeInsets.only(right: 20),
                child: SvgPicture.asset("assets/svg/ic_title_search.svg"),
              )),
        ]),
        body: Consumer<HomeFiltrateVModel>(
          builder: (context, viewModel, child) {
            return StateLayout(
                onReset: () {
                  viewModel.reset();
                },
                state: viewModel.viewState,
                builder: (context) {
                  return Stack(children: [
                    NestedScrollView(
                      controller: _scrollController,
                      headerSliverBuilder:
                          (BuildContext context, bool innerBoxIsScrolled) {
                        return <Widget>[
                          SliverOverlapAbsorber(
                            handle:
                                NestedScrollView.sliverOverlapAbsorberHandleFor(
                                    context),
                            sliver: SliverToBoxAdapter(
                              child: Container(
                                child: Column(
                                  children: [
                                    SizedBox(
                                      height: 6,
                                    ),
                                    categoryHorRow(0, viewModel),
                                    SizedBox(
                                      height: 16,
                                    ),
                                    categoryHorRow(1, viewModel),
                                    SizedBox(
                                      height: 16,
                                    ),
                                    categoryHorRow(2, viewModel),
                                    SizedBox(
                                      height: 16,
                                    ),
                                    categoryHorRow(3, viewModel),
                                    SizedBox(
                                      height: 16,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ];
                      },
                      body: _BodyList(
                        key: _refreshListKey,
                        indexes: viewModel.indexList,
                        categories: viewModel.model,
                      ),
                    ),
                    Positioned(
                        top: 0,
                        left: 16,
                        right: 16,
                        child: CategoryTextView(
                          text: viewModel.topTitles,
                          scrollController: _scrollController,
                        ))
                  ]);
                },
                viewStateError: viewModel.viewStateError);
          },
        ),
      ),
    );
  }

  Widget categoryHorRow(int typeIdx, HomeFiltrateVModel model) {
    var categorys = model.model[typeIdx];
    return Container(
      height: 30,
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        itemExtent: 80,
        scrollDirection: Axis.horizontal,
        itemCount: categorys["msgs"].length,
        itemBuilder: (context, index) {
          return Builder(builder: (context) {
            bool isSelected = model.indexList[typeIdx] == index;
            Color color = isSelected ? null : AppColors.transparent;
            double fontSize = 14;
            FontWeight fontWeight =
                isSelected ? FontWeight.bold : FontWeight.normal;
            Color textColor =
                isSelected ? AppColors.colorFFFFFF : AppColors.textTitle;
            return YvrTextButton(
              padding: EdgeInsets.symmetric(horizontal: 5),
              child: Text(
                '${categorys["msgs"][index]}',
                style: TextStyle(
                    fontSize: fontSize,
                    fontWeight: fontWeight,
                    color: textColor),
              ),
              color: color,
              onPressed: () {
                categoryClick(context, index, typeIdx, model);
              },
            );
          });
        },
      ),
    );
  }

//类目选中事件
  void categoryClick(
      BuildContext context, int index, int typeIdx, HomeFiltrateVModel model) {
    if (mounted) {
      var categoryList = model.model;
      var indexList = model.indexList;
      int cgy = categoryList[0]["values"][indexList[0]];
      int tag = categoryList[1]["values"][indexList[1]] ?? 0;
      var param = {
        "cgy": cgy,
        "tag": tag,
        "prg": indexList[2],
        "sort": indexList[3],
      };
      DataRecord().saveData(
          eventId: "assistant_appstore_allApps_filter_0_block_click",
          extraData: param);

      model.resetFiltration(typeIdx, index);
    }
  }
}

class _BodyList extends StatefulWidget {
  final List<Map<dynamic, dynamic>> categories;
  final List<int> indexes;

  const _BodyList({
    Key key,
    @required this.categories,
    @required this.indexes,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _BodyListState();
  }
}

class _BodyListState extends State<_BodyList> {
  HomeAppVModel viewModel;

  @override
  void initState() {
    super.initState();
    viewModel = HomeAppVModel(widget.indexes, widget.categories);
  }

  @override
  Widget build(BuildContext context) {
    return SimpleRefreshList<HomeAppVModel>(
      model: viewModel,
      builder: (context, viewModel) {
        return CustomScrollView(
          slivers: [
            SliverOverlapInjector(
              handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
            ),
            SliverList(
              delegate: SliverChildListDelegate(
                //返回组件集合
                List.generate(viewModel.list.length, (int index) {
                  //返回 组件
                  return Container(
                    padding: EdgeInsets.only(left: 16, right: 16),
                    child: AppsCell(
                      appsModel: viewModel.list[index],
                      clickFunc: () {
                        DataRecord().saveData(
                            eventId:
                                "assistant_appstore_allApps_appList_app_pit_click",
                            extraData: {"appId": viewModel.list[index].id});
                      },
                    ),
                  );
                }),
              ),
            ),
          ],
        );
      },
      emptyBuilder: (context, model) {
        return Center(
          child: Column(
            children: [
              SizedBox(
                height: 150,
              ),
              Text(
                YLocal.of(context).henbaoqianmeiyouzhao,
                style: TextStyle(fontSize: 16, color: AppColors.textTitle),
              ),
              SizedBox(
                height: 10,
              ),
              Text(
                YLocal.of(context).qingshiyongjitashaix,
                style: TextStyle(fontSize: 14, color: AppColors.textSub),
              )
            ],
          ),
        );
      },
      loadingBuilder: (context, model) {
        return SkeletonList(
          length: 11,
          builder: (context, index) => AppSkeletonItem(),
        );
      },
    );
  }

  @override
  void didUpdateWidget(covariant _BodyList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.categories != widget.categories ||
        oldWidget.indexes != widget.indexes) {
      viewModel.resetData(widget.categories, widget.indexes);
    }
  }
}

class HomeFiltrateVModel extends ViewStateModel2<List<Map<dynamic, dynamic>>> {
  static const int kFiltrateCount = 4;

  List<int> indexList = [0, 0, 0, 0];

  String topTitles = '';

  @override
  Future<List<Map<dynamic, dynamic>>> loadData(
      int pageSize, int pageNum) async {
    await initAppTypes();

    List<Map<dynamic, dynamic>> categoryList =
        (StorageManager.localStorage.getItem("kCategorys") as List)
            ?.cast<Map<dynamic, dynamic>>();

    if (categoryList == null || categoryList.isEmpty) {
      return null;
    }

    if (categoryList.length != 4 ||
        categoryList.indexWhere((e) {
              List values = e['msgs'];
              return values == null || values.isEmpty;
            }) >=
            0) {
      throw Exception(YLocal.current.canshucuowucenshucuo);
    }

    final List<int> indexes = List<int>.from(indexList);

    for (int i = 0; i < categoryList.length; ++i) {
      List values = categoryList[i]['msgs'];
      if (indexes[i] >= values.length) {
        indexes[i] = 0;
      }
    }

    this.indexList = indexes;
    return categoryList;
  }

  @override
  void onRefreshComplete(List<Map> data) {
    resetTopTitles(data);
  }

  void resetTopTitles(List<Map<dynamic, dynamic>> data) {
    if (indexList[0] == 0 && indexList[1] == 0 && indexList[2] == 0) {
      int selIdx = indexList[3];
      topTitles = data[3]["msgs"][selIdx];
    } else {
      List tempList = [];
      for (var i = 0; i < indexList.length; i++) {
        int selIdx = indexList[i];
        if (selIdx != 0 || i == 3) {
          tempList.add(data[i]["msgs"][selIdx]);
        }
      }
      topTitles = tempList.join("·");
    }
  }

  void resetFiltration(int typeIndex, int index) {
    final List<int> indexes = List<int>.from(indexList);
    indexes[typeIndex] = index;
    this.indexList = indexes;
    resetTopTitles(model);
    notifyListeners();
  }
}

class HomeAppVModel extends ViewStateRefreshListModel2<AppsModel> {
  static const int kFiltrateCount = 4;

  List<int> indexes;
  List<Map<dynamic, dynamic>> categories;

  HomeAppVModel(this.indexes, this.categories)
      : super(pageParams: const PageParams(10, 4));

  @override
  Future<PageListModel<AppsModel>> loadData(int pageSize, int pageNum) async {
    if (pageNum == ViewStateModel2.pageNumFirst) {
      String token = DBUtil.instance.userBox.get(kToken) ?? "";
      if (ObjectUtil.isNotEmpty(token)) {
        List payIds = await YvrRequests.getPurchasedAppId();
        await StorageManager.localStorage.setItem(Global.kPayAppList, payIds);
      }
    }

    SubjectModel subjectModel = await YvrRequests.getAppsOnStore(
        page: pageNum,
        cgy: categories[0]["values"][indexes[0]],
        tag: categories[1]["values"][indexes[1]],
        prg: indexes[2],
        sort: indexes[3] + 1,
        psize: pageSize);

    var tempList = subjectModel.apps;
    final jsonList = tempList.map<String>((item) => jsonEncode(item)).toList();
    final uniqJsonList = jsonList.toSet().toList();
    var result = uniqJsonList.map((item) => jsonDecode(item)).toSet().toList();
    var dataList =
        List<AppsModel>.from(result.map((x) => AppsModel.fromJson(x)));
    return PageListModel.create(pageNum, pageSize, dataList);
  }

  void resetData(List<Map<dynamic, dynamic>> categories, List<int> indexes) {
    this.categories = categories;
    this.indexes = indexes;
    reset();
  }
}
