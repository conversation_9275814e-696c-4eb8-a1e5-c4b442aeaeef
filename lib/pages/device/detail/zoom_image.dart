import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/manager/global.dart';
import "package:yvr_assistant/pages/tabbar/top_navbar.dart";

import '../../../generated/l10n.dart';

class ZoomImagePage extends StatefulWidget {
  final arguments;
  ZoomImagePage({this.arguments});

  @override
  _ZoomImagePageState createState() => new _ZoomImagePageState();
}

class _ZoomImagePageState extends State<ZoomImagePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Global.kBgColor,
        appBar: TopNavbar(title: YLocal.of(context).yulantupian),
        body: Container(
          padding: EdgeInsets.fromLTRB(20, 30, 20, 50),
          alignment: Alignment.center,
          color: Color(0xff242527),
          child: CachedNetworkImage(
            imageUrl: widget.arguments["imgUrl"] ?? "",
            fit: BoxFit.contain,
            placeholder: (context, url) => CircularProgressIndicator(
              color: Colors.black,
            ),
            errorWidget: (context, url, error) => Icon(
              Icons.warning_amber_rounded,
              size: 15,
            ),
          ),
        ));
  }
}
