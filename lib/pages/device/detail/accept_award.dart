import "package:flutter/material.dart";
import 'package:flutter/services.dart';
import 'package:flutter_picker/flutter_picker.dart';
import "package:keyboard_avoider/keyboard_avoider.dart";
import "package:flutter_svg/svg.dart";
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import "package:yvr_assistant/manager/global.dart";
import 'package:yvr_assistant/model/punch_form_model.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import "package:yvr_assistant/pages/tabbar/top_navbar.dart";
import 'package:yvr_assistant/public_ui/widget/common_button.dart';
import "package:yvr_assistant/public_ui/widget/dialog_util.dart";
import 'package:yvr_assistant/public_ui/widget/dialogs.dart';
import 'package:yvr_assistant/public_ui/widget/picker_tool.dart';
import "package:yvr_assistant/public_ui/widget/toast_show.dart";
import "package:yvr_assistant/network/http.dart";
import 'package:yvr_assistant/utils/deep_copy.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import "package:yvr_assistant/utils/log.dart";
import 'package:yvr_assistant/view_model/award_vmodel.dart';
import "package:yvr_assistant/view_model/user_vmodel.dart";
import 'package:yvr_assistant/model/dev_model.dart';
import 'package:yvr_assistant/model/user_model.dart';

import '../../../generated/l10n.dart';

enum UploadImageType {
  orderPicture,
  invoicePicture,
  frontIdPicture,
  backIdPicture,
}

class AcceptAwardPage extends StatefulWidget {
  final arguments;
  AcceptAwardPage({Key key, @required this.arguments}) : super(key: key);

  @override
  _AcceptAwardPageState createState() => _AcceptAwardPageState();
}

class _AcceptAwardPageState extends State<AcceptAwardPage> {
  bool _isDone = false;
  UserModel _userModel = UserVModel().user;
  DateTime _selDate = DateTime.now();

  // 购买渠道值
  String _channelValue = "jd";
  final List _channelValues = [
    "jd",
    "tm",
    "dy",
  ];

  // YVR设备号（SN号）、YVR账号、渠道账号、订单号、金额、支付宝账号、支付宝姓名
  TextEditingController _snValue = TextEditingController(),
      _channelAccount = TextEditingController(),
      _yVRValue = TextEditingController(),
      _orderNum = TextEditingController(),
      _moneyNum = TextEditingController(),
      _alipayNum = TextEditingController(),
      _realName = TextEditingController(),
      _alipayName = TextEditingController();

  // 订单截图、发票截图、身份证正面截图、身份证反面截图、真实姓名、审核编号
  String _orderPicture = "",
      _invoicePicture = "",
      _frontIdPicture = "",
      _backIdPicture = "",
      _orderTime = "",
      _reviewId = "";

  @override
  void dispose() {
    // YVR设备号（SN号）
    _snValue.dispose();
    // YVR账号
    _yVRValue.dispose();
    // 订单号
    _orderNum.dispose();
    // 金额
    _moneyNum.dispose();
    // 支付宝账号
    _alipayNum.dispose();
    // 支付宝姓名
    _alipayName.dispose();
    // 渠道用户名
    _channelAccount.dispose();
    // 真实姓名
    _realName.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    Log.e('信息已提交: ${!widget.arguments["isCanEdit"]}');

    if (widget.arguments["isCanEdit"]) {
      DevModel modelDev = widget.arguments["modelDev"];
      _snValue.text = modelDev.devId;
      _yVRValue.text = _userModel.mobile;
    } else {
      _isDone = true;

      PunchFormModel punchFormModel = widget.arguments["formModel"];
      _snValue.text = punchFormModel.deviceId;
      _yVRValue.text = punchFormModel.userPhone.toString();
      _channelValue = punchFormModel.purchaseChannel;
      _orderPicture = punchFormModel.orderPicture;
      _orderNum.text = punchFormModel.orderNumber;
      _moneyNum.text = punchFormModel.payPrice;
      _invoicePicture = punchFormModel.invoicePicture;
      _alipayNum.text = punchFormModel.alipayNumber;
      _alipayName.text = punchFormModel.alipayUserName;
      _frontIdPicture = punchFormModel.frontIdPicture;
      _backIdPicture = punchFormModel.backIdPicture;
      _channelAccount.text = punchFormModel.channelUserId;
      _realName.text = punchFormModel.receiptTrueName;
      _orderTime = punchFormModel.orderBuyTime;
      _reviewId = punchFormModel.id.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    final List<dynamic> _selectList = [
      {
        "label": YLocal.of(context).jingdong,
        "value": "jd",
        "hint": YLocal.of(context).jingdongyonghuming
      },
      {
        "label": YLocal.of(context).tianmao,
        "value": "tm",
        "hint": YLocal.of(context).taobaozhanghuming
      },
      {
        "label": YLocal.of(context).douyin,
        "value": "dy",
        "hint": YLocal.of(context).douyinhao
      },
    ];
    return Scaffold(
        backgroundColor: AppColors.colorFFFFFF,
        resizeToAvoidBottomInset: false,
        appBar: TopNavbar(title: YLocal.of(context).lingjiangxinxitianxi),
        body: Stack(children: [
          KeyboardAvoider(
            autoScroll: true,
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              // 顶部图片
              topImgView(),

              topRemindView(),

              inputView(context,
                  hint: _snValue.text,
                  title: YLocal.of(context).YVRshebeihaoSNhao),

              inputView(context,
                  hint: _yVRValue.text, title: YLocal.of(context).YVRzhanghao),

              // 购买渠道
              purchaseChannelView(_selectList),

              screenshotView(
                picName: _orderPicture,
                requestType: UploadImageType.orderPicture,
                title: YLocal.of(context).qingshangchuanbaohan,
              ),

              inputView(context,
                  inputController: _orderNum,
                  title: YLocal.of(context).qingshuruningoumaiqu,
                  hint: YLocal.of(context).ruxinxiqueloucuowuze),

              inputView(context,
                  inputController: _moneyNum,
                  title: YLocal.of(context).shurunindingchanzhon,
                  hint: YLocal.of(context).ruxinxiqueloucuowuze),

              configTimeWidget(),

              inputView(context,
                  inputController: _realName,
                  title: YLocal.of(context).shoujianrenzhenshixi,
                  hint: YLocal.of(context).ruxinxiqueloucuowuze),

              screenshotView(
                  picName: _invoicePicture,
                  requestType: UploadImageType.invoicePicture,
                  title: YLocal.of(context).ruyouqingshangchuang,
                  isRequired: false),

              inputView(context,
                  inputController: _alipayNum,
                  title: YLocal.of(context).zhifubaozhanghaoyong,
                  hint: YLocal.of(context).ruxinxiqueloucuowuze),

              inputView(context,
                  inputController: _alipayName,
                  title: YLocal.of(context).zhifubaozhanghaodesh,
                  hint: YLocal.of(context).ruxinxiqueloucuowuze),

              screenshotView(
                type: "IDCard",
                title: YLocal.of(context).qingshangchuanshenfe,
              ),
              SizedBox(
                height: Global.paddingBottom + 80,
              ),
            ]),
          ),
          _isDone
              ? SizedBox()
              : Positioned(
                  left: 0,
                  bottom: 0,
                  child: Padding(
                    padding: EdgeInsets.only(
                        left: 20, right: 20, bottom: Global.paddingBottom + 10),
                    child: CommonButton(
                      text: YLocal.of(context).dijiaotijiao,
                      width: Global.screenWidth - 40,
                      height: 48,
                      onTap: () {
                        if (_orderPicture.length == 0 ||
                            _orderNum.text.length == 0 ||
                            _moneyNum.text.length == 0 ||
                            _alipayNum.text.length == 0 ||
                            _alipayName.text.length == 0 ||
                            _frontIdPicture.length == 0 ||
                            _backIdPicture.length == 0 ||
                            _channelAccount.text.length == 0 ||
                            _realName.text.length == 0 ||
                            _orderTime.length == 0) {
                          YvrToast.showToast(
                              YLocal.of(context).qingxianwanchengbiti);
                          return;
                        }
                        showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (_) {
                              return CustomDialog(
                                title: YLocal.of(context).dishitishi,
                                height: 240,
                                content:
                                    YLocal.of(context).zhuyireshenhetongguo,
                                confirmText: YLocal.of(context).dijiaotijiao,
                                confirmCallback: () {
                                  Map params = {
                                    "deviceId": _snValue.text,
                                    "userId":
                                        DBUtil.instance.userBox.get(kActId),
                                    "userPhone": _yVRValue.text,
                                    "purchaseChannel": _channelValue,
                                    "orderPicture": _orderPicture,
                                    "orderNumber": _orderNum.text,
                                    "payPrice": _moneyNum.text,
                                    "invoicePicture": _invoicePicture,
                                    "alipayNumber": _alipayNum.text,
                                    "alipayUserName": _alipayName.text,
                                    "frontIdPicture": _frontIdPicture,
                                    "backIdPicture": _backIdPicture,
                                    "channelUserId": _channelAccount.text,
                                    "orderBuyTime": _orderTime,
                                    "receiptTrueName": _realName.text,
                                  };
                                  Log.d('params: $params');

                                  http
                                      .post<Map>(
                                          "vrmcsys/account/punch/submitPunchAudit",
                                          data: params)
                                      .then((response) {
                                    int code = response.data["errCode"];

                                    if (code == 0) {
                                      YvrToast.showToast(YLocal.of(context)
                                          .lingjiangxinxidijiao);
                                      Navigator.pop(context);
                                    } else {
                                      if (code == 10809) {
                                        YvrToast.showToast(YLocal.of(context)
                                            .dakatianshumeiyoudad);
                                        Navigator.pop(context);
                                      } else if (code == 10810) {
                                        YvrToast.showToast(YLocal.of(context)
                                            .gaishebeiyijingshenq);
                                        Navigator.pop(context);
                                      } else {
                                        YvrToast.showToast(YLocal.of(context)
                                            .lingjiangxinxidijiao_1);
                                      }
                                    }
                                  }).catchError((error) {
                                    YvrToast.showToast(YLocal.of(context)
                                        .lingjiangxinxidijiao_1);
                                  });
                                },
                              );
                            });
                      },
                    ),
                  ),
                ),
        ]));
  }

  // 顶部图片
  Widget topImgView() {
    return Container(
      margin: EdgeInsets.only(bottom: 20),
      child: Image.asset("assets/images/complete_clock_in.png",
          width: Global.screenWidth, fit: BoxFit.fitWidth),
    );
  }

  Widget topRemindView() {
    return Padding(
      padding: EdgeInsets.fromLTRB(20.0, 0, 20.0, 20),
      child: _isDone
          ? GestureDetector(
              child: Text(
                YLocal.of(context).liuchengmaSqingyongy(_reviewId),
                style: TextStyle(
                    color: AppColors.warning,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    height: 1.4),
              ),
              onLongPress: () {
                Clipboard.setData(ClipboardData(text: _reviewId));
                YvrToast.showToast(YLocal.of(context).liuchengmayifuzhi);
              },
            )
          : Text(
              YLocal.of(context).qinggenjuxiafangzhiy,
              style: AppStyle.style_textSub_w400_16pt(),
            ),
    );
  }

  // 渲染标题
  Widget titleView({bool isRequired, String title}) {
    return Text.rich(TextSpan(children: [
      isRequired
          ? TextSpan(text: "*", style: AppStyle.style_warning_w600_20pt())
          : TextSpan(text: ""),
      TextSpan(
        text: title,
        style: AppStyle.style_textTitle_w600_20pt(),
      ),
    ]));
  }

  // 输入SN号\YVR号\平台订单号\支付金额\支付宝账号\支付宝姓名
  Widget inputView(BuildContext context,
      {TextEditingController inputController,
      String title,
      String hint,
      bool isRequired = true}) {
    return Container(
      padding: EdgeInsets.fromLTRB(20.0, 0, 20.0, 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          titleView(isRequired: isRequired, title: title),
          Container(
            height: 42.0,
            margin: EdgeInsets.only(top: 12),
            padding: EdgeInsets.symmetric(horizontal: 11),
            alignment: Alignment.center,
            child: TextField(
              maxLines: 1,
              enabled: title == YLocal.of(context).YVRshebeihaoSNhao ||
                      title == YLocal.of(context).YVRzhanghao
                  ? false
                  : true,
              controller: inputController,
              readOnly: _isDone,
              style: AppStyle.style_textSub_w400_14pt(),
              decoration: InputDecoration(
                focusedBorder: OutlineInputBorder(
                    borderSide:
                        BorderSide(width: 0, color: Colors.transparent)),
                disabledBorder: OutlineInputBorder(
                    borderSide:
                        BorderSide(width: 0, color: Colors.transparent)),
                enabledBorder: OutlineInputBorder(
                    borderSide:
                        BorderSide(width: 0, color: Colors.transparent)),
                border: OutlineInputBorder(
                    borderSide:
                        BorderSide(width: 0, color: Colors.transparent)),
                contentPadding: EdgeInsets.symmetric(vertical: 0),
                fillColor: Color(0xff242527),
                hintText: hint != null
                    ? hint
                    : "${YLocal.of(context).qingshuruS}$title",
                hintStyle: AppStyle.style_textWeak_w400_14pt(),
              ),
            ),
            decoration: BoxDecoration(
                color: AppColors.backgroundItem,
                borderRadius: BorderRadius.circular(4.0)),
          ),
        ],
      ),
    );
  }

  Widget configTimeWidget() {
    return Container(
      padding: EdgeInsets.fromLTRB(20, 0, 20, 30),
      child: GestureDetector(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              YLocal.of(context).dingchangoumaishijia,
              style: AppStyle.style_textSub_w400_18pt(),
            ),
            Spacer(),
            Text(
              _orderTime.length > 0 ? _orderTime : YLocal.of(context).weizhi,
              style: AppStyle.style_textWeak_w400_16pt(),
            ),
            Icon(
              Icons.chevron_right,
              color: AppColors.textSub,
            )
          ],
        ),
        onTap: () {
          if (_isDone) {
            return;
          }

          JhPickerTool.showDatePicker(context,
              adapter: DateTimePickerAdapter(
                minValue: DateTime(1900, 1, 1),
                maxValue: DateTime.now(),
                value: _selDate,
              ), clickCallBack: (var str, var time) {
            setState(() {
              String tempDate = time.toString().replaceRange(4, 5, "-");
              tempDate = time.toString().replaceRange(7, 8, "-");
              Log.d(tempDate);

              DeepCopy value = DeepCopy(strValue: tempDate);
              _selDate = DateTime.parse(tempDate);

              _orderTime = value.strValue.substring(0, 11);
            });
          });
        },
      ),
    );
  }

  // 购买渠道
  Widget purchaseChannelView(List<dynamic> _selectList) {
    return Container(
      padding: EdgeInsets.fromLTRB(20.0, 0, 20.0, 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          titleView(
              isRequired: true, title: YLocal.of(context).nindegoumaiqudaonind),
          Column(
            children: _selectList.map((item) {
              return TextButton(
                style: ButtonStyle(
                  overlayColor: MaterialStateProperty.all<Color>(
                      Colors.transparent), //splashColor
                  minimumSize:
                      MaterialStateProperty.all(Size(double.infinity, 40)),
                  visualDensity: VisualDensity.compact,
                  padding: MaterialStateProperty.all(EdgeInsets.zero),
                ),
                child: Row(
                  children: <Widget>[
                    SvgPicture.asset(
                      item["value"] == _channelValue
                          ? "assets/svg/radio_check.svg"
                          : "assets/svg/radio_uncheck.svg",
                      width: 16,
                      height: 16,
                    ),
                    Container(
                      padding: EdgeInsets.only(left: 10.0),
                      child: Text(
                        item["label"],
                        /*style: TextStyle(
                            color: _isDone
                                ? Color(0xff767880)
                                : Color(0xffE8E8E8)),*/
                        style: AppStyle.style_textTitle_w500_16pt(),
                      ),
                    )
                  ],
                ),
                onPressed: () {
                  if (!_isDone) {
                    setState(() {
                      _channelValue = item["value"];
                    });
                  }
                },
              );
            }).toList(),
          ),
          Container(
            height: 42,
            margin: EdgeInsets.only(top: 12),
            padding: EdgeInsets.symmetric(horizontal: 11),
            child: TextField(
              maxLines: 1,
              enabled: true,
              controller: _channelAccount,
              readOnly: _isDone,
              decoration: InputDecoration(
                  focusedBorder: OutlineInputBorder(
                      borderSide:
                          BorderSide(width: 0, color: Colors.transparent)),
                  disabledBorder: OutlineInputBorder(
                      borderSide:
                          BorderSide(width: 0, color: Colors.transparent)),
                  enabledBorder: OutlineInputBorder(
                      borderSide:
                          BorderSide(width: 0, color: Colors.transparent)),
                  border: OutlineInputBorder(
                      borderSide:
                          BorderSide(width: 0, color: Colors.transparent)),
                  contentPadding: EdgeInsets.symmetric(vertical: 0),
                  fillColor: Color(0xff242527),
                  hintText: _selectList[_channelValues.indexOf(_channelValue)]
                              ["hint"] !=
                          null
                      ? _selectList[_channelValues.indexOf(_channelValue)]
                          ["hint"]
                      : "${YLocal.of(context).qingshuruS}${_channelAccount.text}",
                  hintStyle: AppStyle.style_textWeak_w400_14pt()),
            ),
            decoration: BoxDecoration(
                color: AppColors.backgroundItem,
                borderRadius: BorderRadius.circular(4.0)),
          )
        ],
      ),
    );
  }

  // 截图
  Widget screenshotView(
      {String picName,
      String title,
      UploadImageType requestType,
      bool isRequired = true,
      String type}) {
    Widget upLoadView;
    if (type == "IDCard") {
      upLoadView = Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          imageUploadWidget(
              imgurl: _frontIdPicture, type: UploadImageType.frontIdPicture),
          imageUploadWidget(
              imgurl: _backIdPicture, type: UploadImageType.backIdPicture),
        ],
      );
    } else {
      upLoadView = imageUploadWidget(imgurl: picName, type: requestType);
    }
    return Container(
      padding: EdgeInsets.fromLTRB(20.0, 0, 20.0, 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          titleView(isRequired: isRequired, title: title),
          Padding(
            padding: EdgeInsets.only(top: 10.0),
            child: upLoadView,
          ),
        ],
      ),
    );
  }

  Widget imageUploadWidget({
    String imgurl,
    UploadImageType type,
  }) {
    return (imgurl != "")
        ? photoWidget(
            imgUrl: imgurl,
            checkBigImage: () {
              imgTap(picName: imgurl);
            },
            deleteImage: () {
              setState(() {
                switch (type) {
                  case UploadImageType.orderPicture:
                    _orderPicture = "";
                    break;
                  case UploadImageType.invoicePicture:
                    _invoicePicture = "";
                    break;
                  case UploadImageType.frontIdPicture:
                    _frontIdPicture = "";
                    break;
                  case UploadImageType.backIdPicture:
                    _backIdPicture = "";
                    break;
                  default:
                }
              });
            })
        : InkWell(
            onTap: () {
              showPictureSelectModal(requestType: type);
            },
            child: Padding(
              padding: EdgeInsets.fromLTRB(0, 10.0, 20.0, 0),
              child: SvgPicture.asset(
                "assets/svg/complete_add.svg",
                width: 60.0,
                height: 60.0,
              ),
            ),
          );
  }

  Widget photoWidget(
      {String imgUrl = "", Function checkBigImage, Function deleteImage}) {
    return Stack(
      children: <Widget>[
        Padding(
          padding: EdgeInsets.fromLTRB(0, 10.0, 20.0, 0),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: GestureDetector(
              onTap: checkBigImage, //点击
              child: CachedNetworkImage(
                imageUrl: imgUrl,
                fit: BoxFit.cover,
                width: 60.0,
                height: 60.0,
                placeholder: (context, url) => Transform.scale(
                    scale: 0.5,
                    child: CircularProgressIndicator(
                      color: Colors.black,
                    )),
                errorWidget: (context, url, error) => Icon(
                  Icons.warning_amber_rounded,
                  size: 15,
                ),
              ),
            ),
          ),
        ),
        if (!_isDone)
          Positioned(
            top: 0.0,
            right: 10.0,
            child: GestureDetector(
              onTap: deleteImage,
              child: SvgPicture.asset(
                "assets/svg/del.svg",
                width: 20.0,
                height: 20.0,
              ),
            ),
          ),
      ],
    );
  }

  void showPictureSelectModal({UploadImageType requestType}) async {
    if (_isDone) {
      return;
    }

    AwardVModel awardVModel = AwardVModel.init(requestType.name);
    Dialogs.showImageSelectorDialog2(context,
            selectedAssets: awardVModel.uploadImageList
                .map<AssetEntity>((e) => e.localFile)
                .toList(),
            maxAssets: 1)
        .then((value) async {
      if (value != null && value.isNotEmpty) {
        awardVModel.replaceLocalImages(value);

        try {
          await awardVModel.uploadImage();
          String imgUrl = awardVModel.uploadImageList
              .map<String>((e) => e.networkUrl)
              .toList()[0];
          if (imgUrl != null) {
            setState(() {
              switch (requestType) {
                case UploadImageType.orderPicture:
                  _orderPicture = imgUrl;
                  break;
                case UploadImageType.invoicePicture:
                  _invoicePicture = imgUrl;
                  break;
                case UploadImageType.frontIdPicture:
                  _frontIdPicture = imgUrl;
                  break;
                case UploadImageType.backIdPicture:
                  _backIdPicture = imgUrl;
                  break;
                default:
              }
            });
          }
        } catch (e) {
          YvrToast.showToast(YLocal.of(context).tupianshangchuanshib);
        }
      }
    });
  }

  // 图片点击
  void imgTap({String picName}) async {
    Navigator.pushNamed(context, '/zoom_image', arguments: {"imgUrl": picName});
  }
}
