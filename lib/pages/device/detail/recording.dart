import 'dart:math';
import 'dart:async';
import 'package:dio/dio.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wakelock/wakelock.dart';
import 'package:fijkplayer/fijkplayer.dart';
import 'package:path_provider/path_provider.dart';
import 'package:disk_space_plus/disk_space_plus.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:ffmpeg_kit_flutter_https_gpl/level.dart';
import 'package:ffmpeg_kit_flutter_https_gpl/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_https_gpl/return_code.dart';
import 'package:ffmpeg_kit_flutter_https_gpl/ffmpeg_kit_config.dart';
import 'package:yvr_assistant/pages/device/tool/dev_tool.dart';

import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/pages/home/<USER>';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/pages/device/tool/ble_state.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/pages/device/tool/ijk_config.dart';
import 'package:yvr_assistant/view_model/device_list_vmodel.dart';
import 'package:yvr_assistant/pages/device/tool/file_handle.dart';
import 'package:yvr_assistant/pages/device/views/merge_dialog.dart';
import 'package:yvr_assistant/pages/device/views/portrait_dialog.dart';
import 'package:yvr_assistant/pages/device/views/progress_dialog.dart';
import 'package:yvr_assistant/pages/device/views/merge_modal_view.dart';
import 'package:yvr_assistant/pages/home/<USER>/tool/orientation_tool.dart';

class RecordingPage extends StatefulWidget {
  final arguments;
  RecordingPage({Key key, @required this.arguments}) : super(key: key);

  @override
  State<RecordingPage> createState() => _RecordingPageState();
}

class _RecordingPageState extends State<RecordingPage>
    with WidgetsBindingObserver {
  String _host;
  var eventBusFn;
  double _diskSpace;
  bool _isReady = false;
  bool _isBackCamera = true;
  CameraController _controller;
  List<CameraDescription> _cameras;
  final FijkPlayer _ijkPlayer = FijkPlayer();
  GlobalKey _playViewContainerKey = GlobalKey();
  bool _isCanChangeCamera = true;
  Timer _timer;
  Timer _progressTimer;
  bool _timeState = true;
  CancelToken _cancelToken = CancelToken();
  String _timeText = '00:00';
  ValueNotifier<int> _seconds = ValueNotifier<int>(0);
  ValueNotifier<int> _progress = ValueNotifier<int>(0);

  /// 1: 竖屏主VR 2: 竖屏主人像 3: 横屏
  ValueNotifier<int> _mergeType = ValueNotifier<int>(1);
  Timer _mergeTimer;
  int _mergeSeconds = 0;
  bool _isGoBack = false;
  bool _vrIs1200x900 = true;
  bool _firstOpenCamrea = true;
  bool _isVRDisconnect = false;
  int _recVRStartTimestamp = 0;
  int _recPhoneStartTimestamp = 0;

  DeviceListVModel _devVModel = DeviceListVModel.instance;

  Future<void> refreshPlayer() async {
    // Android手机 或者 D1设备  可以继续播放
    if (Platform.isAndroid ||
        !DevTool.isDev2OrNewer(widget.arguments["devId"])) {
      _progress.value = 0;
      await _ijkPlayer.reset();
      await _ijkPlayer.setDataSource(_host, autoPlay: true);
      if (mounted) {
        setState(() {});
      }

      if (Platform.isIOS) {
        final CameraController cameraController = _controller;
        if (cameraController.value.isRecordingVideo) {
          exitCurrentPage();
        }
      }
    } else {
      exitCurrentPage();
    }
  }

  Future<void> _handleAppPaused() async {
    // 如果为 D1 设备（使用老代码无以下认定方式），可以设置为停止播放，进入前台继续播放
    // 其他设备如果停止播放会导致 VR 认定无设备拉流，推流停止
    if (Platform.isAndroid ||
        !DevTool.isDev2OrNewer(widget.arguments["devId"])) {
      await _ijkPlayer.stop();
    }
  }

  exitCurrentPage() {
    // VR<D2、D3设备> 和 iPhone连接处于后台时iOS停止拉流 导致 认定无设备拉流停止推送，关闭了推流
    final CameraController cameraController = _controller;
    if (cameraController.value.isRecordingVideo) {
      YvrToast.showToast(YLocal.current.shipinluzhiyizhongdu);
    } else {
      YvrToast.showToast(YLocal.current.VRyanjingyizhongzhit);
    }
    Navigator.of(context).popUntil((route) => route.isFirst);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        Log.d("应用从后台或锁屏返回前台");
        refreshPlayer();
        break;
      case AppLifecycleState.inactive:
        Log.d("应用处于非活动状态（可能是切换应用或锁屏）");
        break;
      case AppLifecycleState.paused:
        Log.d("应用进入后台");
        _handleAppPaused();
        break;
      case AppLifecycleState.detached:
        Log.d("应用已退出");
        break;
    }
  }

  @override
  void dispose() {
    super.dispose();
    _timer?.cancel();
    _mergeTimer?.cancel();
    Wakelock?.disable();
    _ijkPlayer?.release();
    eventBusFn?.cancel();
    _controller?.dispose();
    _devVModel.sendStreamValue(
      BleTransmissionStatus.REQ_STOP_RECORDER_AND_SHARE,
      data: "1",
    );
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void initState() {
    super.initState();
    // 设置日志级别为 quiet 禁止任何日志输出
    FFmpegKitConfig.setLogLevel(Level.avLogStderr);
    WidgetsBinding.instance.addObserver(this);
    OrientationTool().portraitScreen();
    Wakelock.enable();
    configFijkPlayerOptions();
    _cameras = widget.arguments["cameras"];
    initCamera();
    eventBusFn = eventBus.on<EventFn>().listen((event) async {
      String phoneInfo = await PlatformUtils.getPhoneModel();
      bool bleDisconnect = event.obj[Global.kBleDisconnect] ?? false;
      if (bleDisconnect) {
        YvrToast.showToast(YLocal.current.VRyanjingduankailian);
        if (_progress.value == 0) {
          loganInfo('recording_page', '$phoneInfo,蓝牙断开连接，终止同屏录');
          Navigator.of(context).pop();
        }
      }

      bool refresh = event.obj[Global.kRefreshRecordingPage] ?? false;
      if (refresh) {
        _progress.value = 0;
        await _ijkPlayer.reset();
        await _ijkPlayer.setDataSource(_host, autoPlay: true);
        if (mounted) {
          setState(() {});
        }
        return;
      }

      bool vrIs1200x900 = event.obj[Global.kVRRecordingIs1200x900] ?? true;
      if (!vrIs1200x900) {
        _vrIs1200x900 = vrIs1200x900;
        return;
      }

      bool vrStopRecording = event.obj[Global.kVRStopRecording] ?? false;
      bool vrWifiChanged = event.obj[Global.vrNetworkChangeKey] ?? false;
      bool phoneWifiChanged = event.obj[Global.phoneWifiChangeKey] ?? false;
      if (!_isGoBack &&
          (vrWifiChanged || phoneWifiChanged || vrStopRecording)) {
        receiveVRDisconnect(
            isWifiChange: !vrStopRecording, phoneInfo: phoneInfo);
        return;
      }
    });
  }

  /// 收到WiFi改变、区分是否为合成中，合成中大于20 合成中小于 20
  receiveVRDisconnect({bool isWifiChange, String phoneInfo}) async {
    if (_progress.value < 20) {
      _isGoBack = true;
      int type;
      if (isWifiChange) {
        type = Global.phoneWifiChangeValue;
        loganInfo('recording_page', '$phoneInfo,拼接录制页网络状态变化,退出拼接录制');
        YvrToast.showToast(YLocal.current.VRyanjingyushoujiwan);
      } else {
        type = Global.vrCloseScreenValue;
        loganInfo('recording_page', '$phoneInfo,VR端眼镜息屏或者网络变化，退出拼接录制');
        YvrToast.showToast(YLocal.current.qingquebaoVRyanjingl);
      }
      await FFmpegKit?.cancel();
      Navigator.of(context).pop<int>(type);
    } else {
      _isVRDisconnect = true;
    }
  }

  onRecording() async {
    _diskSpace = await DiskSpacePlus.getFreeDiskSpace;
    if (_diskSpace < 1024) {
      YvrToast.showToast(YLocal.current.shoujinacunbuzuqingq);
      return;
    }
    _isCanChangeCamera = false;
    Future.delayed(const Duration(milliseconds: 500), () {
      bool vrStorageEnough =
          DBUtil.instance.projectConfigBox.get(Global.kVRStorageEnough) ?? true;
      if (vrStorageEnough) {
        startRecording();
      } else {
        YvrToast.showToast(YLocal.current.VRyanjingnacunbuzuqi);
      }
    });
  }

  startRecording() {
    _seconds.value = 0;
    _timer?.cancel();
    _timeState = true;
    _timeText = '00:00';
    _progress.value = 1;
    _recVRStartTimestamp = DateTime.now().millisecondsSinceEpoch;
    YvrToast.showLoading();

    /// VR 修改分辨率方式

    // int diff = int.parse(
    //     StorageManager.localStorage.getItem(Global.kBleTimestampDifference));
    // String data = _mergeType.value ?? "1";
    // data = "$data#$diff";

    _devVModel.sendStreamValue(
      BleTransmissionStatus.REQ_RECORDER_AND_SHARE_WRITE_MP4,
      // data: data,
    );

    countDownStart();
    onVideoRecordButtonPressed();
  }

  endRecording() {
    _timer?.cancel();
    onStopButtonPressed();
  }

  initCamera() {
    _controller = CameraController(
      _cameras[_isBackCamera ? 0 : 1],
      ResolutionPreset.veryHigh,
      imageFormatGroup: ImageFormatGroup.yuv420,
    );
    _controller.initialize().then((_) async {
      onCaptureOrientationLockButtonPressed();
      if (mounted) {
        setState(() {
          _isReady = true;
        });
        bool isAlreadyShow =
            StorageManager.foreverData.getItem("kIsAlreadyShowPortraitView") ??
                false;
        if (!isAlreadyShow) {
          StorageManager.foreverData
              .setItem("kIsAlreadyShowPortraitView", true);
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) {
                return PortraitView(
                  nextStep: () {
                    showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (BuildContext context) {
                          return MergeModeView(
                            mergeMode: (bool isPortrait) {
                              _mergeType.value = (isPortrait ? 1 : 3);
                            },
                          );
                        });
                  },
                );
              });
        }
      }
    }).catchError((Object e) async {
      Log.e('相机初始化错误码：$e');
      _devVModel
          .sendStreamValue(BleTransmissionStatus.REQ_STOP_SCREENCAST_STATUS);
      YvrToast.showToast(YLocal.current.qingxiankaiqixiangji_1);
      if (mounted) {
        YvrToast.showToast(YLocal.current.xiangjicuowu);
        String phoneInfo = await PlatformUtils.getPhoneModel();
        loganInfo('recording_page', '$phoneInfo,相机异常,退出拼接录制');
        Navigator.of(context).pop();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          return false;
        },
        child: _isReady
            ? videoContent()
            : Container(
                height: Global.screenHeight,
                width: Global.screenWidth,
                color: AppColors.secondaryBg,
              ));
  }

  Future<void> configFijkPlayerOptions() async {
    _host = await IjkPlayerConfig().configRtspOptions(
        ijkPlayer: _ijkPlayer, snCode: widget.arguments["devId"]);
  }

  Widget videoContent() {
    final CameraController cameraController = _controller;
    final size = MediaQuery.of(context).size;
    final deviceRatio = size.width / size.height;
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black,
      child: ValueListenableBuilder<int>(
          valueListenable: _mergeType,
          builder: (context, type, child) {
            /// 计算手机画面遮挡高度
            double topMaskH = (size.width / 2 * 3 / 4 +
                (Platform.isIOS ? 20 : 35) +
                (Global.statusBarHeight + 55));
            double bottomMaskH = (130 + (Global.paddingBottom + (65)));
            double buttonBottomH = (130 + (Global.paddingBottom + (65)));

            if (type == 1) {
              double tempH = (size.height - (size.width * 810 / 1080)) / 2;
              topMaskH = tempH;
              bottomMaskH = tempH;
            } else if (type == 2) {
              double tempH = (size.height - (size.width * 1110 / 1080)) / 2;
              topMaskH = tempH;
              bottomMaskH = tempH;
            }

            /// 保持高度不变 计算VR视频宽度
            double vrW = (size.width / 2);
            double vrH = (size.width / 2 * 3 / 4);
            if (!_vrIs1200x900 && cameraController.value.isRecordingVideo) {
              double vrScale = 1110 / 1080;
              if (type == 2) {
                vrScale = 810 / 1080;
              } else if (type == 3) {
                vrScale = 1314 / 1080;
              }
              vrW = (vrW * vrScale);
            }

            return Stack(children: [
              ClipRect(
                child: Center(
                  child: Transform.scale(
                    scale: _controller.value.aspectRatio / deviceRatio,
                    child: AspectRatio(
                      aspectRatio: _controller.value.aspectRatio,
                      child: Center(
                          child: CameraPreview(
                        _controller,
                      )),
                    ),
                  ),
                ),
              ),
              Visibility(
                  visible: type != 3,
                  child: Positioned(
                      child: Container(
                          width: size.width,
                          height: topMaskH,
                          color: Color(0xff242527).withOpacity(0.9)))),
              Positioned(
                  bottom: 0,
                  child: (type != 3)
                      ? Container(
                          width: size.width,
                          height: bottomMaskH,
                          decoration: BoxDecoration(
                            color: Color(0xff242527).withOpacity(0.9),
                          ),
                        )
                      : Container(
                          width: size.width,
                          height: bottomMaskH,
                          decoration: BoxDecoration(
                              gradient: LinearGradient(
                                  //渐变位置
                                  begin: Alignment.topCenter, //右上
                                  end: Alignment.bottomCenter, //左下
                                  stops: [
                                0.0,
                                1.0
                              ],
                                  colors: [
                                Color.fromRGBO(23, 25, 27, 0),
                                Color.fromRGBO(23, 25, 27, 1)
                              ])),
                        )),
              Positioned(
                  right: 10,
                  top: Global.statusBarHeight + (Platform.isIOS ? 40 : 55),
                  child: Container(
                      margin: const EdgeInsets.only(top: 25),
                      width: vrW,
                      height: vrH,
                      decoration: BoxDecoration(
                        border: Border.all(
                            color: Color(0xffE8E8E8), width: 2), // border
                        borderRadius: BorderRadius.circular((8)), // 圆角
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: IgnorePointer(
                            child: FijkView(
                          width: vrH,
                          fit: FijkFit.fitHeight,
                          player: _ijkPlayer,
                          color: Colors.black,
                          panelBuilder: fijkPanel2Builder(snapShot: true),
                        )),
                      ))),
              Positioned(
                  bottom: bottomMaskH,
                  width: Global.screenWidth,
                  child: (cameraController.value.isRecordingVideo)
                      ? ValueListenableBuilder<int>(
                          valueListenable: _seconds,
                          builder: (context, value, child) {
                            _timeText = constructTime(value);
                            return Column(
                              children: [
                                Container(
                                  width: 64,
                                  height: 30,
                                  margin: const EdgeInsets.only(bottom: 15),
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    color: Color(0xFFEA4359),
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(4)),
                                  ),
                                  child: Text("$_timeText",
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: AppColors.colorFFFFFF,
                                        fontSize: 20,
                                      )),
                                ),
                                LinearProgressIndicator(
                                  value: (value / 180.0),
                                  minHeight: 5,
                                  backgroundColor: AppColors.divider,
                                  valueColor: AlwaysStoppedAnimation(
                                      AppColors.standard),
                                )
                              ],
                            );
                          })
                      : optionPortraitMode(type)),
              Positioned(
                  child: Container(
                      height: kToolbarHeight + Global.statusBarHeight,
                      width: Global.screenWidth,
                      color: AppColors.colorFFFFFF,
                      padding: EdgeInsets.only(
                          left: 15, right: 15, top: Global.statusBarHeight),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          (!cameraController.value.isRecordingVideo)
                              ? IconButton(
                                  onPressed: () {
                                    if (mounted) {
                                      Navigator.of(context).pop();
                                    }
                                  },
                                  icon: Icon(
                                    Icons.arrow_back_ios,
                                    color: AppColors.textSub,
                                  ),
                                )
                              : const SizedBox(width: 20),
                          Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                  "assets/svg/record_vector.svg",
                                  width: 20,
                                  height: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(YLocal.current.luzhishibaozhengbeil,
                                    style: AppStyle.style_textTitle_w500_16pt())
                              ]),
                          const SizedBox(width: 20),
                        ],
                      ))),
              Positioned(
                  bottom: 0,
                  width: Global.screenWidth,
                  child: Container(
                    width: Global.screenWidth,
                    height: buttonBottomH,
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          !cameraController.value.isRecordingVideo
                              ? GestureDetector(
                                  onTap: () {
                                    showMergeModelView(
                                        context: context,
                                        isPortrait: (_mergeType.value != 3),
                                        callback: (isPortrait) {
                                          _mergeType.value =
                                              (isPortrait ? 1 : 3);
                                        });
                                  },
                                  child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Image.asset(
                                          'assets/images/record_change.png',
                                          width: 32,
                                          height: 32,
                                        ),
                                        SizedBox(height: 8),
                                        Text(YLocal.current.pinjiemoshi,
                                            style: AppStyle.style_ffffff_12pt())
                                      ]),
                                )
                              : GestureDetector(
                                  onTap: () {
                                    if (_seconds.value > 3) {
                                      _firstOpenCamrea = false;
                                      YvrToast.showLoading();
                                      onStopButtonPressed(isCancel: true);
                                      Future.delayed(const Duration(seconds: 2),
                                          () async {
                                        YvrToast.dismiss();
                                        await DBUtil.instance.userBox.put(
                                            Global
                                                .kOnlyRecordingProjWithoutJump,
                                            true);
                                        _devVModel
                                            .sendBleStartRecordingOnlyProj();
                                      });
                                    }
                                  },
                                  child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Image.asset(
                                          'assets/images/record_cancel.png',
                                          width: 32,
                                          height: 32,
                                        ),
                                        SizedBox(height: 8),
                                        Text(YLocal.current.quxiaoluzhi,
                                            style: AppStyle.style_ffffff_12pt())
                                      ]),
                                ),
                          IconButton(
                            iconSize: 81,
                            icon: Image.asset(
                              cameraController.value.isRecordingVideo
                                  ? 'assets/images/record_stop.png'
                                  : 'assets/images/record_start.png',
                            ),
                            onPressed: () async {
                              if (cameraController != null &&
                                  cameraController.value.isInitialized) {
                                if (cameraController.value.isRecordingVideo) {
                                  if (_seconds.value > 3) {
                                    endRecording();
                                  }
                                } else {
                                  // 防止连续多次点击
                                  if (_isCanChangeCamera) {
                                    onRecording();
                                  }
                                }
                              }
                            },
                          ),
                          (!cameraController.value.isRecordingVideo)
                              ? GestureDetector(
                                  onTap: () {
                                    if (_isCanChangeCamera) {
                                      _isCanChangeCamera = false;
                                      _isReady = false;
                                      _isBackCamera = !_isBackCamera;
                                      YvrToast.showLoading();
                                      initCamera();
                                      Future.delayed(const Duration(seconds: 1),
                                          () {
                                        _isCanChangeCamera = true;
                                        YvrToast.dismiss();
                                      });
                                    }
                                  },
                                  child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Image.asset(
                                          'assets/images/record_camera.png',
                                          width: 32,
                                          height: 32,
                                        ),
                                        const SizedBox(height: 8),
                                        Text(YLocal.current.jingtoufanzhuan,
                                            style: AppStyle.style_ffffff_12pt())
                                      ]),
                                )
                              : const SizedBox(width: 52)
                        ]),
                  ))
            ], alignment: AlignmentDirectional.topEnd);
          }),
      key: _playViewContainerKey,
    );
  }

  Widget optionPortraitMode(int type) {
    return Visibility(
        visible: type != 3,
        child: Column(
          children: [
            Container(
                width: 100,
                height: 21,
                alignment: Alignment.center,
                margin: EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  borderRadius: BorderRadius.all(Radius.circular(4.0)),
                ),
                child: Text(
                  (type == 1)
                      ? YLocal.current.VRweizhu
                      : YLocal.current.renxiangweizhu,
                  textAlign: TextAlign.center,
                  style: AppStyle.style_ffffff_12pt(),
                )),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                portraitItemButton(
                    imageName: "main_vr",
                    isSelect: type == 1,
                    selectTap: () {
                      _mergeType.value = 1;
                    }),
                const SizedBox(width: 20),
                portraitItemButton(
                    imageName: "main_person",
                    isSelect: type == 2,
                    selectTap: () {
                      _mergeType.value = 2;
                    })
              ],
            ),
            SizedBox(
              height: 15,
            )
          ],
        ));
  }

  void onStopButtonPressed({bool isCancel = false}) {
    _isCanChangeCamera = true;
    _devVModel.sendStreamValue(
      BleTransmissionStatus.REQ_STOP_RECORDER_AND_SHARE,
      data: isCancel ? "1" : "0",
    );

    _ijkPlayer.pause();

    stopVideoRecording().then((XFile file) async {
      if (mounted) {
        setState(() {});
      }
      if (isCancel) {
        return;
      }
      if (file == null) {
        notiVRDeleteVideo();
        YvrToast.showToast(YLocal.current.hechengshibai);
        return;
      }

      double fileM = getFileSizeInMb(File(file.path));
      // 根据视频时长估算合成耗时
      int totalTime = max(_seconds.value ~/ 3, 5);
      if (Platform.isAndroid) {
        totalTime = max((_seconds.value * 0.767).toInt(), 5);
      }
      totalTime = min(totalTime, 180);
      Log.e('视频路径： ${file.path}\n耗时: $totalTime\n空间大小：$fileM Mb');
      if (fileM > 1024) {
        YvrToast.showToast(YLocal.current.luzhinarongguodamofa);
        return;
      }

      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return WillPopScope(
              onWillPop: () async {
                return false;
              },
              child: ProgressView(
                totalTime: totalTime,
                progress: _progress,
                endMergeCallback: () async {
                  isCancel = true;
                  _progressTimer?.cancel();
                  await FFmpegKit?.cancel();
                  await DBUtil.instance.userBox
                      .put(Global.kOnlyRecordingProjWithoutJump, true);
                  _devVModel.sendBleStartRecordingOnlyProj();
                  YvrToast.showToast(YLocal.current.yiquxiaogechengqingc);
                  Navigator.pop(context);
                },
              ),
            );
          }).then((value) {
        if (value != null && value is String) {
          Navigator.pushNamed(context, '/merge_video', arguments: {
            "path": value,
            "duration": _mergeSeconds,
            "isPortrait": (_mergeType.value != 3),
            "vrIsDisconect": _isVRDisconnect
          });
        }
      });
      Future.delayed(const Duration(seconds: 2), () {
        downloadVideo(mergeVideo: (String vrVideoPath) async {
          // saveMergeVideoToLocal(path: file.path);
          // saveMergeVideoToLocal(path: vrVideoPath);
          // YvrToast.showToast("下载时长：$_mergeSeconds s");

          Directory appDocDir = await getTemporaryDirectory();
          String outPath = appDocDir.path + "/yvr.mp4";
          // 解决Android前置相机录制后画面镜像问题
          String mirroringCmd = "[1:v]";
          if (!_isBackCamera && Platform.isAndroid) {
            mirroringCmd = "[1:v]hflip[front];[front]";
          }

          /// 竖屏 主VR
          String complexCommand = _vrIs1200x900
              ? '[0:v]scale=1480:1110,crop=1080:1110[a];[a]pad=iw:ih+810[b];${mirroringCmd}crop=1080:810[c];[b][c]overlay=0:1110'
              : '[0:v]pad=iw:ih+810[b];${mirroringCmd}scale=1080:1920,crop=1080:810[d];[b][d]overlay=0:1110';

          /// 竖屏 主人像
          if (_mergeType.value == 2) {
            complexCommand = _vrIs1200x900
                ? '[0:v]scale=1080:810[a];[a]pad=iw:ih+1110[b];${mirroringCmd}crop=1080:1110[c];[b][c]overlay=0:810'
                : '[0:v]pad=iw:ih+1110[b];${mirroringCmd}scale=1080:1920,crop=1080:1110[d];[b][d]overlay=0:810';

            /// 横屏
          } else if (_mergeType.value == 3) {
            complexCommand = _vrIs1200x900
                ? '[0:v]crop=1093.75:900,scale=1314:1080[b];[b]pad=iw+608:ih[c];${mirroringCmd}scale=608:1080[d];[c][d]overlay=1314:0'
                : '[0:v]pad=iw+606:ih[c];${mirroringCmd}scale=606:1080[d];[c][d]overlay=1314:0';
          }

          double cutTime = 0;
          double delayTime = 0;
          String concatCmd; // 拼接尾部视频指令
          String resolution;
          String watermarkCmd;
          String endVideoPath;
          String encoder = "-c:v h264 -pix_fmt yuv420p"; //libx264
          String decoder = "-hwaccel mediacodec";
          if (Platform.isIOS) {
            encoder = "-c:v h264";
            decoder = "-hwaccel videotoolbox";
            try {
              String version = Platform.operatingSystemVersion.split(" ")[1];
              int firstNo = int.parse(version.split(".")[0]);
              int secondNo = int.parse(version.split(".")[1]);
              if (!(firstNo == 15 && secondNo == 0)) {
                encoder = "-c:v h264_videotoolbox";
              }
            } catch (e) {
              Log.e('获取iOS版本失败');
            }
            // iOS 首次打开相机黑屏时间较长
            delayTime =
                (_recPhoneStartTimestamp - _recVRStartTimestamp) * 0.001 * 1.8;
            cutTime = max(delayTime * 0.55, 0.6);
            if (_firstOpenCamrea) {
              cutTime += 0.1;
            }
            Log.d(
                '裁剪时间:delayTime:$delayTime cutTime:$cutTime first:$_firstOpenCamrea');
          } else {
            decoder = "";
          }

          String yvrPath =
              await DevTool.getImageAssetsPath(imageName: "wcmx_logo.png");
          if (_mergeType.value != 3) {
            /// 获取竖屏水印指令
            endVideoPath =
                await DevTool.getVideoAssetsPath(videoName: "end_video_v.mp4");

            resolution = "1080x1920";
            concatCmd = "[g];[g][0:a][3:v][3:a]concat=n=2:v=1:a=1";

            if (_mergeType.value == 1) {
              watermarkCmd = "[e];[e][2:v]overlay=x=(W-w)/2:y=1130$concatCmd";
            } else {
              watermarkCmd = "[e];[e][2:v]overlay=x=(W-w)/2:y=830$concatCmd";
            }
          } else {
            // 获取横屏水印指令
            endVideoPath =
                await DevTool.getVideoAssetsPath(videoName: "end_video_h.mp4");

            resolution = "1920x1080";
            concatCmd = "[f];[f][0:a][3:v][3:a]concat=n=2:v=1:a=1";
            watermarkCmd =
                ",crop=1920:1080,setdar=16/9[e];[e][2:v]overlay=x=1314+(606-w)/2:y=20$concatCmd";
          }
          String command = '$decoder -ss $delayTime -i $vrVideoPath '
              '$decoder -ss $cutTime -i ${file.path} '
              '-i $yvrPath '
              '$decoder -i $endVideoPath '
              '-filter_complex  "$complexCommand$watermarkCmd" '
              '$encoder -r 45 -b:v 5M -s $resolution -preset fast -crf 28 -async 1 -y $outPath';

          int totalVideoDuration = (_seconds.value + 3) * 1000;
          FFmpegKit.executeAsync(command, (session) async {
            _firstOpenCamrea = false;
            _mergeTimer?.cancel();
            deleteFile(file.path);
            deleteFile(vrVideoPath);
            final returnCode = await session.getReturnCode();
            if (ReturnCode.isSuccess(returnCode)) {
              _progress.value = 100;
              Log.d(
                  '视频时长：${_seconds.value + 3} s\n预估时长：$totalTime s\n合并总时长：$_mergeSeconds s');

              Future.delayed(const Duration(seconds: 1), () {
                Navigator.pop(context, outPath);
                _progressTimer?.cancel();
              });
            } else {
              String errorMsg = await session.getAllLogsAsString();
              Log.e('合成错误：${session.toString()}\n日志：$errorMsg');

              _progressTimer?.cancel();
              if (!isCancel) {
                YvrToast.showToast(YLocal.current.hechengshibai);
                Navigator.pop(context);
              }
            }
          }, (log) {
            Log.d('FFmpeg' + log.getMessage());
          }, (statistics) {
            if (statistics == null) {
              return;
            }
            // 获取已完成的视频时间 占 视频总长度的比例
            if (statistics.getTime() > 0) {
              // Platform.isIOS ? 80 : 75 -> 安卓合成时真实进度会超过100%
              int totalProgress = 20 +
                  (statistics.getTime() * (Platform.isIOS ? 80 : 78)) ~/
                      totalVideoDuration;
              totalProgress = min(99, totalProgress);
              _progress.value = totalProgress;

              Log.d(
                  '显示进度：$totalProgress %\n合并进度：${(statistics.getTime() * 100) ~/ totalVideoDuration} %');
            }
          });
        });
      });
    });
  }

  Future<void> startVideoRecording() async {
    final CameraController cameraController = _controller;

    if (cameraController == null || !cameraController.value.isInitialized) {
      Log.i('Error: select a camera first.');
      return;
    }

    if (cameraController.value.isRecordingVideo) {
      // A recording is already started, do nothing.
      return;
    }

    try {
      await cameraController.startVideoRecording();
    } on CameraException catch (e) {
      _showCameraException(e);
      return;
    }
  }

  void _showCameraException(CameraException e) {
    Log.e("CameraException Error:" + e.code + "\n" + e.description);
    YvrToast.showToast(YLocal.current.pinjieluzhiguochengz);
    Navigator.pop(context);
  }

  void onVideoRecordButtonPressed() {
    startVideoRecording().then((_) {
      YvrToast.dismiss();
      _recPhoneStartTimestamp = DateTime.now().millisecondsSinceEpoch;
      if (mounted) {
        setState(() {});
      }
    });
  }

  Future<void> onCaptureOrientationLockButtonPressed() async {
    try {
      if (_controller != null) {
        final CameraController cameraController = _controller;
        await cameraController.lockCaptureOrientation();
      }
    } on CameraException catch (e) {
      _showCameraException(e);
    }
  }

  Future<XFile> stopVideoRecording() async {
    final CameraController cameraController = _controller;

    if (cameraController == null || !cameraController.value.isRecordingVideo) {
      return null;
    }

    try {
      return cameraController.stopVideoRecording();
    } on CameraException catch (e) {
      _showCameraException(e);
      return null;
    }
  }

  // 保存视频
  downloadVideo({String videoName = "vr_video", Function mergeVideo}) async {
    String vrHttpPort =
        StorageManager.localStorage.getItem("vr_http_port") ?? "8089";
    String videoUrl =
        'http://${StorageManager.localStorage.getItem("vr_host")}:$vrHttpPort/' +
            'api/doShowShareRecVideo?name=sharevideo.mp4';
    var appDocDir = await getTemporaryDirectory();
    String savePath = appDocDir.path + "/temp.mp4";
    double progress = 0;
    int _total = 0;
    try {
      Log.d("开始下载路径为：$videoUrl");
      _mergeSeconds = 0;
      _mergeTimer = Timer.periodic(Duration(seconds: 1), (timer) {
        _mergeSeconds++;
      });
      await Dio().download(videoUrl, savePath,
          cancelToken: _cancelToken,
          options: Options(responseType: ResponseType.bytes),
          onReceiveProgress: (count, total) {
        _total = total;
        double tempP = count / total * 100;
        if ((tempP - progress >= 30) || tempP == 100) {
          progress = tempP;
          if (tempP == 100) {
            notiVRDeleteVideo();
            mergeVideo(savePath);
          }
        }
        _progress.value = tempP ~/ 5.0;
      }).timeout(Duration(seconds: 180));
    } catch (e) {
      notiVRDeleteVideo();
      _cancelToken?.cancel();
      YvrToast.showToast(YLocal.current.hechengshibai);
      Navigator.pop(context);
      return;
    }

    if (_total == -1) {
      notiVRDeleteVideo();
      _cancelToken?.cancel();
      YvrToast.showToast(YLocal.current.hechengshibai);
      Navigator.pop(context);
    }
  }

  /// 通知 VR 删除已录制视频
  void notiVRDeleteVideo() {
    _devVModel.sendStreamValue(BleTransmissionStatus.RET_VIDEO_MERGED);
  }

  saveMergeVideoToLocal({String path, String vieoName = "vieoName"}) async {
    final result = await ImageGallerySaver.saveFile(path, name: vieoName);
    if (result['isSuccess']) {
      YvrToast.showToast(YLocal.current.yibaocunzhixiangce);
    } else {
      YvrToast.showToast(YLocal.current.mofabaocunqingchongs);
    }
  }

  void countDownStart() {
    _timeState = !_timeState;
    if (!_timeState) {
      startTimer();
    } else {
      _seconds.value = 0;
      _timer?.cancel();
    }
  }

  String constructTime(int seconds) {
    int minute = seconds % 3600 ~/ 60;
    int second = seconds % 60;
    return "${minute.toString().padLeft(2, '0')}:${second.toString().padLeft(2, '0')}";
  }

  void startTimer() {
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      _seconds.value++;
      if (_seconds.value == 150) {
        YvrToast.showToast(YLocal.current.luzhijijiangjieshuqi);
      } else if (_seconds.value == 180) {
        endRecording();
      }
    });
  }
}
/*
    VR 分辨率对照表 w:h  REQ_RECORDER_AND_SHARE_WRITE_MP4
    data:   _  1200x900
            1   1080:1110
            2   1080:810 
            3   1314:1080

    手机分辨率  w:h 1080:1920  ->  720:1280
            1   1080:810      <-   (1080:1920)
            2   1080:1110     <-   (1080:1920)
            3   606:1080      
  */
