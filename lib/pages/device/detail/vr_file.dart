import 'dart:math';
import 'dart:convert';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:fijkplayer/fijkplayer.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/pages/home/<USER>/tool/orientation_tool.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/video_player.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:common_utils/common_utils.dart';
import 'package:path_provider/path_provider.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/utils/data_record.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:yvr_assistant/network/vr_request.dart';
import 'package:yvr_assistant/model/vr_file_model.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/provider/provider_widget.dart';
import 'package:yvr_assistant/utils/permission_util.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';
import 'package:yvr_assistant/view_model/vr_file_vmodel.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:yvr_assistant/provider/view_state_widget.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:yvr_assistant/pages/social/views/plhd_view.dart';
import 'package:interactiveviewer_gallery/hero_dialog_route.dart';
import 'package:yvr_assistant/pages/home/<USER>/banner_swiper.dart';
import 'package:interactiveviewer_gallery/interactiveviewer_gallery.dart';

import '../../../manager/event_bus.dart';

class VrFileListPage extends StatefulWidget {
  final arguments;

  VrFileListPage({Key key, @required this.arguments}) : super(key: key);

  @override
  State<VrFileListPage> createState() => _VrFileListPageState();
}

List<String> _saveYet = <String>[];

class _VrFileListPageState extends State<VrFileListPage> {
  String _host =
      "http://${StorageManager.localStorage.getItem("vr_host")}:8089/";
  String _snCode;
  var eventBusFn;
  VrFileVModel _viewModel;

  @override
  void initState() {
    YvrToast.dismiss();
    VrHttp().initVrHttp(baseUrl: _host);
    _snCode = widget.arguments["snCode"];
    super.initState();
    _viewModel = VrFileVModel();
    initEventBus();
  }

  @override
  void dispose() {
    eventBusFn?.cancel();
    super.dispose();
  }

  bool isShow = false;
  initEventBus() {
    eventBusFn = eventBus.on<EventFn>().listen((event) {
      bool networkSync = event.obj != null &&
          ((event.obj[Global.netWorkSyncKey] != null &&
                  event.obj[Global.netWorkSyncKey]) ||
              (event.obj[Global.phoneWifiChangeKey] != null &&
                  event.obj[Global.phoneWifiChangeKey]));

      if (networkSync && !isShow) {
        Log.d('------------------截屏页网路同步通知------------------');
        isShow = true;
        Future.delayed(Duration(seconds: 3), () {
          isShow = false;
        });
        showDialog(
            context: context,
            barrierDismissible: false,
            builder: (_) {
              return CustomDialog(
                title: YLocal.of(context).dishitishi,
                content: YLocal.of(context).VRyanjingyushoujiwan,
                confirmText: YLocal.of(context).queding,
                confirmCallback: () {},
                isCancel: false,
              );
            });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppColors.colorFFFFFF,
        appBar: TopNavbar(title: YLocal.of(context).jiebingyulubingjiepi),
        body: ProviderWidget<VrFileVModel>(
            model: _viewModel,
            onModelReady: (model) => model.initData(snCode: _snCode),
            builder: (context, model, child) {
              if (model.busy && model.page == 1) {
                return ViewStateBusyWidget();
              } else if (model.error) {
                if (model.rangeList.length > 0) {
                } else {
                  return ViewStateErrorWidget(
                      title: "",
                      message: YLocal.of(context).mofahuoqujielubingwe,
                      error: model.viewStateError,
                      buttonTextData: YLocal.of(context).shuaxin,
                      onPressed: () {
                        model.initData(snCode: _snCode);
                      });
                }
              }

              return SmartRefresher(
                  // key: _listKey,
                  controller: model.controller,
                  header: WaterDropHeader(),
                  footer: CustomFooter(
                    builder: (BuildContext context, LoadStatus mode) {
                      Widget body;
                      if (mode == LoadStatus.idle) {
                        body = SizedBox();
                      } else if (mode == LoadStatus.loading) {
                        body = CupertinoActivityIndicator();
                      } else if (mode == LoadStatus.failed) {
                        body = Text(YLocal.of(context).state_load_fail);
                      } else if (mode == LoadStatus.canLoading) {
                        body = Text(YLocal.of(context).state_load_more);
                      } else {
                        body = SizedBox();
                      }
                      return Center(child: body);
                    },
                  ),
                  onRefresh: () {
                    model.page = 1;
                    model.controller.resetNoData();
                    model.initData(pages: model.page, snCode: _snCode);
                  },
                  onLoading: () {
                    model.page++;
                    model.initData(pages: model.page, snCode: _snCode);
                  },
                  enablePullUp: true,
                  child: CustomScrollView(
                    slivers: [
                      //(model.rangeList.length == 0)
                      (model.rangeList.length == 0)
                          ? SliverToBoxAdapter(
                              child: Center(
                              child: PagePlhdWidget(
                                message:
                                    YLocal.of(context).zanmojiebinghelubing,
                                imagePath: "assets/svg/file_plhd.svg",
                                imgWidth: 56.0,
                                vSpaceHeight: 20,
                              ),
                            ))
                          : SliverList(
                              delegate: SliverChildListDelegate(
                                List.generate(model.rangeList.length,
                                    (int section) {
                                  return Container(
                                      padding:
                                          EdgeInsets.only(left: 16, right: 16),
                                      child: Wrap(
                                          spacing: 16,
                                          runSpacing: 16,
                                          children:
                                              sectionWidget(section, model)));
                                }),
                              ),
                            ),
                      if (model.isShowNoMoreWidget &&
                          model.rangeList != null &&
                          model.rangeList.length > 0)
                        SliverToBoxAdapter(
                          child: Container(
                            margin: const EdgeInsets.only(top: 50),
                            alignment: Alignment.center,
                            child: Text(
                              YLocal.of(context).yimogengduowenjianyi,
                              style: TextStyle(
                                  color: Color(0xffE8E8E8), fontSize: 14),
                            ),
                          ),
                        ),
                      SliverPadding(padding: EdgeInsets.all(20))
                    ],
                  ));
            }));
  }

  final itemW = (Global.screenWidth - 16 * 3) / 2;

  List<Widget> sectionWidget(int section, VrFileVModel model) {
    String title = model.rangeList[section]["title"];
    List fileModels = model.rangeList[section]["files"];
    var tempMap = fileModels.asMap().map((itemIndex, fileModel) {
      String thumUrl = _host + "api/doShowThumbnail?id=" + fileModel.id;
      String duration = "";
      if (fileModel.type == 2) {
        int microseconds = int.parse(fileModel.duration ?? "0") ~/ 1000;
        int minutes = microseconds ~/ 60;
        int seconds = microseconds % 60;
        duration +=
            "${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}";
      }
      return MapEntry(
          int.parse(fileModels[itemIndex].id),
          Container(
              width: itemW,
              height: itemW,
              child: Hero(
                  tag: thumUrl,
                  child: GestureDetector(
                    onTap: () {
                      _openGallery(
                        model,
                        section,
                        itemIndex,
                      );
                      if (mounted) {
                        DataRecord().saveData(
                          eventId:
                              "assistant_device_screenshotRecordDetail_0_0_page_view",
                        );
                      }
                    },
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(4),
                          child: AspectRatio(
                            aspectRatio: 1,
                            child: CachedNetworkImage(
                              imageUrl: thumUrl,
                              cacheManager: null, // 禁用缓存
                              placeholder: (context, url) =>
                                  sPlhdImage, // 加载中占位图
                              errorWidget: (context, url, error) =>
                                  sPlhdImage, // 错误占位图
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        if (fileModel.type == 2)
                          Positioned(
                              top: 10,
                              left: 10,
                              child: Container(
                                  padding:
                                      const EdgeInsets.fromLTRB(8, 4, 8, 4),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF333333)
                                        .withOpacity(0.69),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Row(
                                    children: [
                                      SvgPicture.asset(
                                        "assets/svg/vr_video_play.svg",
                                        width: 6,
                                        height: 6.5,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(duration,
                                          style: TextStyle(
                                              fontSize: 10,
                                              color: Colors.white)),
                                    ],
                                  )))
                      ],
                    ),
                  ))));
    });

    List<Widget> list = tempMap.values.toList();
    Widget header = Container(
      width: Global.screenWidth,
      color: AppColors.colorFFFFFF,
      padding: EdgeInsets.only(top: section == 0 ? 16 : 32),
      child: Text(
        title,
        style: AppStyle.style_textTitle_w500_16pt(),
      ),
    );
    list.insert(0, header);
    return list;
  }

  void _openGallery(VrFileVModel model, int section, int initialIndex) {
    List<String> bigPhotos = <String>[];
    List<String> thumPhotos = <String>[];

    List selFileModels = model.rangeList[section]["files"];

    selFileModels.forEach((e) {
      thumPhotos.add(_host + "api/doShowThumbnail?id=" + e.id);
      bigPhotos.add(_host +
          "api/${e.type == 2 ? 'doShowVideo' : 'doShowImage'}?name=" +
          e.name);
    });
    Log.w(thumPhotos.toString());
    Log.w(bigPhotos.toString());

    Navigator.of(context).push(
      HeroDialogRoute<void>(builder: (BuildContext context) {
        int itemIdx = initialIndex;
        return StatefulBuilder(builder: (context, setState) {
          if (selFileModels.length == 0) {
            return const SizedBox();
          }
          return Stack(
            key: ValueKey(selFileModels.length),
            children: [
              InteractiveviewerGallery<VrFileModel>(
                sources: selFileModels,
                initIndex: initialIndex,
                itemBuilder: (BuildContext context, int index, bool isFocus) {
                  VrFileModel fileModel = selFileModels[index];
                  var thumPhoto = thumPhotos[index];
                  var bigPhoto = bigPhotos[index];
                  return _itemBuilder(fileModel, thumPhoto, bigPhoto);
                },
                onPageChanged: (int pageIndex) {
                  setState(() {
                    itemIdx = pageIndex;
                  });
                },
              ),
              _buildTitle(context, selFileModels[itemIdx].date),
              _buildLeftActions(context, selFileModels[itemIdx]),
              _buildRightActions(context, selFileModels, _snCode, section,
                  itemIdx, bigPhotos, thumPhotos, (index) {
                bigPhotos.removeAt(index);
                thumPhotos.removeAt(index);

                itemIdx = max(index - 1, 0);
                initialIndex = itemIdx;

                if (model.remove(section, index)) {
                  Navigator.of(context).pop();
                } else {
                  setState(() {});
                }
              }),
            ],
          );
        });
      }),
    );
  }

  static Widget _itemBuilder(
      VrFileModel fileModel, String thumPhoto, String bigPhoto) {
    Widget viewItem;
    if (fileModel.type == 2) {
      viewItem = VideoPlayerWidget(
        videoUrl: bigPhoto,
        isMute: false,
        autoPlay: false,
        fit: FijkFit.ar16_9,
        thumPicUrl: thumPhoto,
        allowFullScreen: true,
        backgroundColor: const Color(0xFFF0F2F5),
        fullScreenCallBack: (bool isClickFull) {
          if (isClickFull) {
            OrientationTool().landscapeScreen();
          } else {
            OrientationTool().portraitScreen();
          }
        },
      );
    } else {
      viewItem = CachedNetworkImage(
        imageUrl: bigPhoto,
        fit: BoxFit.cover,
        placeholder: (context, url) {
          return Image.asset(
            'assets/images/s_plhd.png',
            fit: BoxFit.fitWidth,
            width: Global.screenWidth,
          );
        },
        errorWidget: (context, url, error) {
          return Image.asset(
            'assets/images/s_plhd.png',
            fit: BoxFit.fitWidth,
            width: Global.screenWidth,
          );
        },
      );
    }
    return Container(
      width: Global.screenWidth,
      height: Global.screenHeight,
      color: AppColors.backgroundItem,
      child: Center(
        child: Hero(tag: bigPhoto, child: viewItem),
      ),
    );
  }

  static Positioned _buildTitle(BuildContext context, String date) {
    String title = DateUtil.formatDateMs(int.parse(date) * 1000,
        format: "yyyy-MM-dd HH:mm");
    return Positioned(
        top: 0,
        left: 0,
        child: Container(
          width: Global.screenWidth,
          color: AppColors.colorFFFFFF,
          padding: EdgeInsets.only(top: Global.statusBarHeight),
          child: SizedBox(
            width: Global.screenWidth,
            height: kToolbarHeight,
            child: Stack(children: [
              SizedBox(
                  width: double.infinity,
                  height: double.infinity,
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          title,
                          textAlign: TextAlign.center,
                          style: AppStyle.style_textTitle_w600_18pt(),
                        )
                      ])),
              Positioned(
                  left: 0,
                  child: GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      height: kToolbarHeight,
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      alignment: Alignment.center,
                      child: Icon(
                        Icons.arrow_back_ios,
                        color: AppColors.textSub,
                      ),
                    ),
                  ))
            ]),
          ),
        ));
  }

  static Positioned _buildLeftActions(
      BuildContext context, VrFileModel fileModel) {
    double fileM = (NumUtil.getDoubleByValueStr(fileModel.size) / 1024 / 1024);
    String unit = "M";
    String fileByte;
    if (fileM < 1) {
      unit = "K";
      fileByte =
          (NumUtil.getDoubleByValueStr(fileModel.size) / 1024).toString();
    } else {
      fileByte = (NumUtil.getDoubleByValueStr(fileModel.size) / 1024 / 1024)
          .toString();
    }
    double fileSize = NumUtil.getNumByValueStr(fileByte, fractionDigits: 1);
    String showText;
    if (_saveYet.contains(fileModel.id)) {
      showText = YLocal.of(context).yibaocun;
    }
    return Positioned(
      left: 16,
      bottom: Global.paddingBottom + 34,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            "$fileSize $unit",
            style: AppStyle.style_textTitle_w500_14pt(),
            textAlign: TextAlign.center,
          ),
          SizedBox(width: 15),
          if (showText != null)
            Text(showText,
                textAlign: TextAlign.center,
                style: AppStyle.style_textTitle_w500_14pt()),
        ],
      ),
    );
  }

  static Positioned _buildRightActions(
    BuildContext context,
    List selFileModels,
    String snCode,
    int sectionIdx,
    int itemIdx,
    List<String> bigPhotos,
    List<String> thumPhotos,
    ValueChanged<int> onDelete,
  ) {
    int length = selFileModels.length;
    VrFileModel fileModel = selFileModels[itemIdx];
    return Positioned(
        right: 16,
        bottom: Global.paddingBottom + 20,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            GestureDetector(
              onTap: () {
                showDeleteDialog(context, () async {
                  YvrToast.showLoading(
                      message: YLocal.of(context).shanchuzhong);
                  Log.e("该区总共：$length个\n删除第：$itemIdx个");
                  var errcode = await VrHttp.doDeleteFiles(
                      ids: [fileModel.id], snCode: snCode);
                  if (errcode == "0") {
                    YvrToast.dismiss();
                    onDelete?.call(itemIdx);
                  } else {
                    YvrToast.showToast(YLocal.current.shanchushibai,
                        position: EasyLoadingToastPosition.bottom);
                  }
                });
              },
              child: Container(
                width: 45,
                height: 45,
                child: SvgPicture.asset("assets/svg/file_delete.svg"),
              ),
            ),
            SizedBox(width: 24),
            GestureDetector(
              onTap: () {
                PermissionUtil.checkPhotoSavePermission(context)
                    .then((isAllow) {
                  if (isAllow) {
                    // 执行操作
                    fileModel.type == 2
                        ? saveVideo(
                            videoUrl: bigPhotos[itemIdx],
                            size: fileModel.size,
                            videoName: fileModel.name,
                            id: fileModel.id)
                        : saveNetworkImg(
                            imgUrl: bigPhotos[itemIdx],
                            imgName: fileModel.name,
                            id: fileModel.id);
                  }
                });
              },
              child: Container(
                width: 40,
                height: 40,
                child: SvgPicture.asset("assets/svg/file_save.svg"),
              ),
            ),
            /*
            IconButton(
                onPressed: () {
                  // shareToSina(context);
                  // shareDingTalk(context);
                  // shareToWechat(context);

                  // VrFileModel fileModel =
                  //     _rangeList[_sectionIdx]["files"][_itemIdx.value];
                  // if (fileModel.type == 2) {}

                  SSDKMap shareData = SSDKMap()
                    ..setGeneral("YVR分享", "玩出梦想", [_bigPhotos[_itemIdx.value]], "",
                        "", "", "", "", "", "", SSDKContentTypes.image);

                  SharesdkPlugin.showMenu(null, null, shareData,
                      (SSDKResponseState state,
                          ShareSDKPlatform platform,
                          dynamic userData,
                          dynamic contentEntity,
                          SSDKError error) {
                    showAlert(state, error.rawData, context);
                  });
                },
                icon: circleIcon("assets/svg/file_share.svg")),*/
          ],
        ));
  }
}

/// 保存网络图片
saveNetworkImg(
    {@required String imgUrl,
    @required String imgName,
    @required String id}) async {
  YvrToast.showLoading(message: YLocal.current.baocunzhong);
  var response = await VrHttp.dio
      .get(imgUrl, options: Options(responseType: ResponseType.bytes));
  // Log.d("返回值：$response");
  List<int> byteData = json.decode(response.toString()).cast<int>();
  try {
    String stringData = utf8.decode(byteData);
    Map mapData = json.decode(stringData);
    if (mapData["errcode"] != null) {
      YvrToast.showToast(mapData["errmsg"]);
    }
  } catch (e) {
    final result = await ImageGallerySaver.saveImage(
        Uint8List.fromList(response.data),
        quality: 60,
        name: imgName);
    Log.w("保存图片结果：$result");
    if (result['isSuccess'] && !_saveYet.contains(id)) {
      _saveYet.add(id);
    }
    showSaveResult(result['isSuccess']);
  }
}

// 保存视频
saveVideo(
    {@required String videoUrl,
    @required String id,
    @required String videoName,
    @required String size}) async {
  double fileM = (NumUtil.getDoubleByValueStr(size) / 1024 / 1024);
  if (fileM > (1024 * 2)) {
    YvrToast.showToast(YLocal.current.narongguodajianyizhi);
    return;
  }
  YvrToast.showLoading(message: YLocal.current.baocunzhong);
  var appDocDir = await getTemporaryDirectory();
  String savePath = appDocDir.path + "/temp.mp4";
  double progress = 0;
  Response<ResponseBody> response;
  CancelToken token = CancelToken();
  int _total = 0;
  try {
    // 最多下载 10 * 60 分钟，超时时间最少 3 分钟
    fileM = min(600, fileM / 2);
    fileM = max(180, fileM);
    response = await Dio().download(videoUrl, savePath,
        cancelToken: token, options: Options(responseType: ResponseType.bytes),
        onReceiveProgress: (count, total) {
      _total = total;
      double tempP = count / total * 100;
      if ((tempP - progress >= 20) || tempP == 100) {
        progress = tempP;
        Log.d("size:$size \ntotal:$total \n下载进度:${tempP.toStringAsFixed(0)}%");
      }
    }).timeout(Duration(seconds: fileM.toInt()));
  } catch (e) {
    token.cancel();
    YvrToast.showToast(YLocal.current.mofabaocunqingfanhui);
    return;
  }

  if (_total == -1) {
    YvrToast.showToast(YLocal.current.wenjianbucunzai);
    return;
  }

  List<int> byteData;
  try {
    byteData = json.decode(response.data.stream.toString()).cast<int>();
    showSaveResult(false);
  } catch (e) {
    try {
      String stringData = utf8.decode(byteData);
      Map mapData = json.decode(stringData);
      if (mapData["errcode"] != null) {
        YvrToast.showToast(mapData["errmsg"]);
      }
    } catch (e) {
      await File(savePath).setLastModified(DateTime.now());
      final result =
          await ImageGallerySaver.saveFile(savePath, name: videoName);
      Log.w("保存视频结果：$result");
      if (result['isSuccess'] && !_saveYet.contains(id)) {
        _saveYet.add(id);
      }
      showSaveResult(result['isSuccess']);
    }
  }
}

showSaveResult(bool result) {
  if (result) {
    YvrToast.showToast(YLocal.current.yibaocunzhixiangce);
  } else {
    YvrToast.showToast(YLocal.current.mofabaocunqingchongs);
  }
}

showDeleteDialog(BuildContext context, Function callBack) {
  showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) {
        return CustomDialog(
          title: YLocal.of(context).dishitishi,
          content: YLocal.of(context).VRyanjingjiangtongbu,
          cancelText: YLocal.of(context).zanbu,
          confirmText: YLocal.of(context).shanchu,
          height: 200,
          confirmColor: Color(0xFF4F7FFE),
          confirmCallback: callBack,
        );
      });
}

// adb shell am broadcast -a android.intent.action.START_RTSP
// adb shell am broadcast -a android.intent.action.STOT_RTSP

// rtsp://ip:8086
