import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:wakelock/wakelock.dart';
import 'package:fijkplayer/fijkplayer.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/pages/home/<USER>';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/pages/device/tool/ble_state.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/pages/device/tool/ijk_config.dart';
import 'package:yvr_assistant/view_model/device_list_vmodel.dart';
import 'package:yvr_assistant/pages/device/views/proj_wait_view.dart';

class LanProjectionPage extends StatefulWidget {
  final arguments;
  LanProjectionPage({Key key, @required this.arguments}) : super(key: key);

  @override
  State<LanProjectionPage> createState() => _LanProjectionPageState();
}

class _LanProjectionPageState extends State<LanProjectionPage>
    with WidgetsBindingObserver {
  GlobalKey _playViewContainerKey = GlobalKey();
  FijkPlayer _ijkPlayer = FijkPlayer();
  Timer _prepareTimer;
  Size _size;
  String _host;
  var eventBusFn;
  bool isShow = false;
  DeviceListVModel _devVModel = DeviceListVModel.instance;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
    if (_prepareTimer != null) {
      _prepareTimer?.cancel();
      _prepareTimer = null;
    }
    _timer?.cancel();
    _ijkPlayer.removeListener(() {});
    _ijkPlayer?.release();
    Wakelock?.disable();
    _devVModel
        .sendStreamValue(BleTransmissionStatus.REQ_STOP_SCREENCAST_STATUS);
    eventBusFn?.cancel();
    isShow = false;
  }

  @override
  void initState() {
    super.initState();
    Log.d('---------------------播放投屏页----initState--------------');
    YvrToast.dismiss();
    Wakelock.enable();
    _startTimer();
    _prepareTimer = Timer(const Duration(seconds: 3), () {
      _prepareTimer = null;
      setState(() {
        _size = MediaQuery.of(context).size;
        if (_ijkPlayer.state == FijkState.error) {
          _showIjkErrorMsg();
        }
      });
    });

    configFijkPlayerOptions();

    eventBusFn = eventBus.on<EventFn>().listen((event) async {
      if (event.obj != null &&
          event.obj[Global.vrNetworkChangeKey] != null &&
          event.obj[Global.vrNetworkChangeKey] &&
          !isShow) {
        Log.d('------------------投屏页网络同步通知------------------');
        isShow = true;
        String phoneInfo = await PlatformUtils.getPhoneModel();
        loganInfo('lan_projection_page', '$phoneInfo, 投屏页网络状态变化，退出投屏');
        Navigator.of(context).pop<int>(Global.vrWifiChangeValue);
      }

      if (event.obj != null &&
          event.obj[Global.phoneWifiChangeKey] != null &&
          event.obj[Global.phoneWifiChangeKey] &&
          !isShow) {
        Log.d('------------------投屏页网络同步通知------------------');
        isShow = true;
        Navigator.of(context).pop<int>(Global.phoneWifiChangeValue);
      }
    });
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          return false;
        },
        child: _isPlay
            ? videoContent()
            : Scaffold(
                backgroundColor: AppColors.secondaryBg,
                appBar: TopNavbar(title: YLocal.current.toubingtouping),
                body: ProjWaitView(
                    animationDots: animationDots(),
                    arguments: widget.arguments)));
  }

  Timer _timer;
  int _currentIdx = 0;
  _startTimer() {
    _timer = new Timer.periodic(new Duration(milliseconds: 750), (timer) {
      setState(() {
        if (_currentIdx < 5) {
          _currentIdx++;
        } else {
          _currentIdx = 0;
        }
      });
    });
  }

  Future<void> configFijkPlayerOptions() async {
    _host = await IjkPlayerConfig().configRtspOptions(
        ijkPlayer: _ijkPlayer, snCode: widget.arguments["devId"]);

    _ijkPlayer.addListener(() {
      Log.w("播放器状态：${_ijkPlayer.state}");
      if (_ijkPlayer.state == FijkState.completed) {
        _devVModel.resetConnect();
        YvrToast.showToast(YLocal.current.VRyanjingyizhongzhit);
        loganInfo('(PopUntilFirst) lan_projection ijk completed',
            StackTrace.current.toString());
        Navigator.of(context).popUntil((route) => route.isFirst);
      } else if (_ijkPlayer.state == FijkState.error) {
        if (_isPlay) {
          _showIjkErrorMsg();
        }
      }
    });
  }

  bool get _isPlay => _prepareTimer == null;

  Future<void> _showIjkErrorMsg() async {
    var exception = _ijkPlayer.value.exception;
    if (exception != null) {
      YvrToast.showToast(
          "${YLocal.current.VRyanjingtoubingshib}:[${exception.message}]");
      String phoneInfo = await PlatformUtils.getPhoneModel();
      loganInfo('lan_projection_page', '$phoneInfo,投屏播放失败,host：$_host');
    }
  }

  Widget animationDots() {
    List<Widget> dots = [];
    for (var i = 0; i < 6; i++) {
      dots.add(Container(
        height: 4,
        width: 4,
        margin: EdgeInsets.only(bottom: 4),
        decoration: BoxDecoration(
            color: _currentIdx == i ? AppColors.standard : AppColors.textWeak,
            borderRadius: BorderRadius.all(Radius.circular(2))),
      ));
    }
    return Column(children: dots);
  }

  Widget videoContent() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black,
      child: Stack(children: [
        Transform.scale(
            scale: (_size.height / _size.width),
            child: Transform.rotate(
                angle: math.pi / 2,
                child: IgnorePointer(
                    child: FijkView(
                        width: _size.width,
                        fit: FijkFit.fitWidth,
                        player: _ijkPlayer,
                        color: Colors.black,
                        panelBuilder: fijkPanel2Builder(snapShot: true))))),
        Positioned(
            right: 15,
            bottom: Global.paddingBottom + 15,
            child: CloseButton(
                color: AppColors.backgroundItem,
                onPressed: () async {
                  await _ijkPlayer.stop();
                  if (mounted) {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  }
                }))
      ], alignment: AlignmentDirectional.topEnd),
      key: _playViewContainerKey,
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        Log.e('应用进入前台');
        if (_ijkPlayer.isPlayable()) {
          Future.sync(() async {
            await _ijkPlayer.start();
            await _ijkPlayer.seekTo(3000);
          });
        }
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.paused:
        if (_ijkPlayer.isPlayable()) {
          _ijkPlayer.pause();
        }
        break;
      case AppLifecycleState.detached:
        break;
    }
  }
}
