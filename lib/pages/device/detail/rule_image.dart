import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/manager/global.dart';
import "package:yvr_assistant/pages/tabbar/top_navbar.dart";

class RuleImagePage extends StatelessWidget {
  final arguments;
  const RuleImagePage({
    Key key,
    this.arguments,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: TopNavbar(title: arguments["title"]),
        body: SingleChildScrollView(
          child: Center(
              child: Container(
            color: Global.kBgColor,
            child: CachedNetworkImage(
              imageUrl: arguments["imgUrl"] ?? "",
              fit: BoxFit.fitWidth,
              placeholder: (context, url) => Transform.scale(
                  scale: 0.5,
                  child: CircularProgressIndicator(
                    color: Colors.black,
                  )),
              errorWidget: (context, url, error) => Icon(
                Icons.warning_amber_rounded,
                size: 30,
              ),
            ),
          )),
        ));
  }
}
