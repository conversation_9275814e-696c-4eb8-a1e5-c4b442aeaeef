import 'package:flutter/material.dart';
import 'package:common_utils/common_utils.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/view_model/device_list_vmodel.dart';
import 'package:yvr_assistant/pages/device/tool/file_handle.dart';
import 'package:yvr_assistant/public_ui/widget/common_button.dart';
import 'package:yvr_assistant/pages/home/<USER>/views/video_player.dart';
import 'package:yvr_assistant/pages/device/views/progress_dialog.dart';
import 'package:yvr_assistant/pages/home/<USER>/tool/orientation_tool.dart';

class MergeVideoPage extends StatefulWidget {
  final arguments;
  MergeVideoPage({Key key, @required this.arguments}) : super(key: key);

  @override
  State<MergeVideoPage> createState() => _MergeVideoPageState();
}

class _MergeVideoPageState extends State<MergeVideoPage> {
  double _playerH = Global.screenHeight - (Global.statusBarHeight + 60 + 20);
  ValueNotifier<int> _saveStatus = ValueNotifier<int>(0);

  Future<void> popRecordingPage() async {
    deleteFile(widget.arguments["path"]);
    if (widget.arguments["vrIsDisconect"]) {
      YvrToast.showToast(YLocal.current.qingquebaoVRyanjingl);
      Navigator.of(context).popUntil((route) => route.isFirst);
    } else {
      await DBUtil.instance.userBox
          .put(Global.kOnlyRecordingProjWithoutJump, true);
      DeviceListVModel.instance.sendBleStartRecordingOnlyProj();
      Navigator.of(context).pop();
    }
  }

  Future<bool> backButtonClick() async {
    if (_saveStatus.value != 2) {
      showEndSaveDialog(
          context: context,
          endMergeCallback: () {
            popRecordingPage();
            return true;
          });
      return true;
    } else {
      popRecordingPage();
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          return backButtonClick();
        },
        child: Scaffold(
            resizeToAvoidBottomInset: true,
            backgroundColor: const Color(0xFF242527),
            appBar: TopNavbar(
              title: 'Video Preview',
              backCallBack: () {
                backButtonClick();
              },
            ),
            body: Container(
                height: Global.screenHeight,
                child: Stack(children: [
                  Container(
                    height: _playerH,
                    child: VideoPlayerWidget(
                      isMute: false,
                      videoUrl: widget.arguments["path"],
                      allowFullScreen: !widget.arguments["isPortrait"],
                      bottomPadding: widget.arguments["isPortrait"]
                          ? (Global.paddingBottom + 100)
                          : 0,
                      fullScreenCallBack: (bool isClickFull) {
                        if (isClickFull) {
                          OrientationTool().landscapeScreen();
                        } else {
                          OrientationTool().portraitScreen();
                        }
                      },
                    ),
                  ),
                  Positioned(
                      bottom: 0,
                      child: Container(
                          width: Global.screenWidth,
                          height: Global.paddingBottom + 150,
                          padding: const EdgeInsets.fromLTRB(55, 50, 20, 50),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter, //右上
                              end: Alignment.bottomCenter, //左下
                              stops: [0.0, 1.0],
                              colors: [
                                const Color.fromRGBO(23, 25, 27, 0),
                                const Color.fromRGBO(23, 25, 27, 1),
                              ],
                            ),
                          ),
                          child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                GestureDetector(
                                  onTap: () {
                                    backButtonClick();
                                  },
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Image.asset(
                                          'assets/images/record_again.png',
                                          height: 32,
                                          width: 32),
                                      Text(
                                        YLocal.current.chongxinluzhizhongxi,
                                        style: TextStyle(
                                          color: AppColors.colorE8E8E8,
                                          fontSize: 12,
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                                ValueListenableBuilder<int>(
                                    valueListenable: _saveStatus,
                                    builder: (context, value, child) {
                                      String imageName = "ic_save";
                                      String name =
                                          YLocal.current.cundaoxiangce;
                                      if (_saveStatus.value == 1) {
                                        name = YLocal.current.baocunzhong_1;
                                      } else if (_saveStatus.value == 2) {
                                        imageName = "ic_saved";
                                        name = YLocal.current.yibaocun_1;
                                      }
                                      return CommonButton(
                                        text: name,
                                        height: 36,
                                        hPadding: 8,
                                        isGradient: false,
                                        secondaryBg: _saveStatus.value == 2
                                            ? AppColors.color354E8E
                                            : AppColors.standard,
                                        radius: 6,
                                        textWidget: Row(children: [
                                          Visibility(
                                            visible: _saveStatus.value != 1,
                                            child: Image.asset(
                                              'assets/images/$imageName.png',
                                              width: 20,
                                              height: 20,
                                            ),
                                          ),
                                          Visibility(
                                              visible: _saveStatus.value != 1,
                                              child: SizedBox(width: 4)),
                                          Text(name,
                                              style: _saveStatus.value == 2
                                                  ? AppStyle
                                                      .style_textSub_w400_12pt()
                                                  : AppStyle
                                                      .style_e8e8e8_12pt())
                                        ]),
                                        onTap: () {
                                          if (_saveStatus.value == 0) {
                                            _saveStatus.value = 1;
                                            saveMergeVideoToLocal(
                                                path: widget.arguments["path"]);
                                          }
                                        },
                                      );
                                    })
                              ])))
                ]))));
  }

  saveMergeVideoToLocal({String path}) async {
    final result = await ImageGallerySaver.saveFile(path,
        name: "${DateUtil.getNowDateMs()}");
    if (result['isSuccess']) {
      _saveStatus.value = 2;
      YvrToast.showToast(YLocal.current.yibaocunzhixiangce);
    } else {
      _saveStatus.value = 0;
      YvrToast.showToast(YLocal.current.mofabaocunqingchongs);
    }
  }
}
