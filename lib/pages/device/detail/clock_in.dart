import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/model/clock_model.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/pages/device/tool/calendar_header.dart';
import 'package:yvr_assistant/pages/device/views/dev_card_cell.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/provider/provider_widget.dart';
import 'package:yvr_assistant/provider/view_state_widget.dart';
import 'package:yvr_assistant/public_ui/widget/rich_text.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/view_model/clock_vmodel.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';

class ClockInPage extends StatefulWidget {
  final arguments;

  ClockInPage({Key key, @required this.arguments}) : super(key: key);

  @override
  State<ClockInPage> createState() => _ClockInPageState();
}

class _ClockInPageState extends State<ClockInPage> {
  PageController _pageController;
  final ValueNotifier<DateTime> _focusedDay = ValueNotifier(DateTime.now());
  ClockInModel _clockInModel;
  List _records;

  @override
  void dispose() {
    _focusedDay.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _clockInModel = ClockInModel.fromJson(widget.arguments["model"]);
    return Scaffold(
        backgroundColor: AppColors.colorFFFFFF,
        appBar:
            TopNavbar(title: YLocal.of(context).dakafanxiandaqiafanx, actions: [
          IconButton(
            onPressed: () {
              jumpToPunchRulePage(
                  context: context, clockInModel: _clockInModel);
            },
            icon: SvgPicture.asset(
              "assets/svg/clock_info.svg",
              width: 20,
              height: 20,
            ),
          )
        ]),
        body: ProviderWidget<ClockVModel>(
            model: ClockVModel(),
            onModelReady: (model) => model.initData(_clockInModel.devId),
            builder: (context, model, child) {
              if (model.busy) {
                return ViewStateBusyWidget();
              } else if (model.error) {
                return ViewStateErrorWidget(
                    error: model.viewStateError,
                    onPressed: () {
                      model.initData(_clockInModel.devId);
                    });
              }
              _records = model.records;
              DateTime lastDay = DateUtil.getDateTime(_clockInModel.toDate);
              if (_focusedDay.value.isAfter(lastDay)) {
                _focusedDay.value = lastDay;
              }
              return SingleChildScrollView(
                padding: EdgeInsets.all(16),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      topView(),
                      SizedBox(
                        height: 20,
                      ),
                      sectionHeader(
                          titleWidget: Text(
                            YLocal.of(context)
                                .leijidakaNtianleijid(_clockInModel.curDay),
                            style: AppStyle.style_textTitle_w600_20pt(),
                          ),
                          message:
                              "${YLocal.of(context).canyushijiancenyushi}：${_clockInModel.fromDate}-${_clockInModel.toDate}"),
                      calendarCard()
                    ]),
              );
            }));
  }

  Widget topView() {
    String nick = _clockInModel.nick ?? YLocal.of(context).nickname;
    int actId = DBUtil.instance.userBox.get(kActId);
    bool isCurrentUser = (actId == _clockInModel.actId);
    Widget topView = SizedBox();
    String headerMessage = YLocal.current.dakashujuyouyidingya;
    switch (_clockInModel.status) {
      case 1:
        // 默认打卡活动后期展示效果（后期完成后需放置到:case 1区间 ）
        if (_clockInModel.achieve == 1) {
          topView = clockInView(nick: nick, isCurrentUser: isCurrentUser);
        } else {
          topView = sectionHeader(
              titleWidget: titleWidget(
                titleIntlTextBuilder: YLocal.of(context).SjinriweidakaSjinriw,
                nick: nick,
                isCurrentUser: isCurrentUser,
              ),
              message: headerMessage);
        }
        break;
      case 2:
        //当前用户可领奖已跳转至 accept_award
        topView = sectionHeader(
            titleWidget: titleWidget(
                titleIntlTextBuilder: YLocal.of(context).SyiwanchengdakaSyiwa,
                isCurrentUser: false,
                nick: nick),
            message: headerMessage);
        break;
      case 3:
        // 已领奖已跳转至 accept_grant
        topView = sectionHeader(
            titleWidget: titleWidget(
                titleIntlTextBuilder: YLocal.of(context).Syilingjiang,
                isCurrentUser: false,
                nick: nick),
            message: headerMessage);
        break;
      case 4:
        topView = Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            sectionHeader(
                titleWidget: titleWidget(
                    nick: nick,
                    isCurrentUser: isCurrentUser,
                    // titleIntlTextBuilder: YLocal.of(context).Shuodongbucunzai),
                    titleIntlTextBuilder: getTitleText),
                message: YLocal.of(context).ninshengyuhuodongtia),
            Container(
              alignment: Alignment.center,
              margin: EdgeInsets.fromLTRB(60, 8, 60, 20),
              child: Image.asset("assets/images/clock_fail.png"),
            )
          ],
        );
        break;
      default:
    }

    return topView;
  }

  String getTitleText(dynamic value) {
    return YLocal.of(context).huodongcanyushibaihu;
  }

  Widget clockInView({String nick, bool isCurrentUser}) {
    return Container(
        width: Global.screenWidth,
        decoration: BoxDecoration(
            color: AppColors.backgroundItem,
            borderRadius: BorderRadius.all(Radius.circular(7))),
        child: ListTile(
            contentPadding: EdgeInsets.fromLTRB(10, 7, 10, 7),
            leading: Image.asset(
              'assets/images/cup.png',
              fit: BoxFit.cover,
            ),
            title: Text(
              nick,
              style: isCurrentUser
                  ? AppStyle.style_textTitle_w600_20pt()
                  : AppStyle.style_textWeak_w600_20pt(),
            ),
            subtitle: Text(YLocal.current.gongxininjinridakach,
                style: AppStyle.style_success_w400_12pt())));
  }

  Widget titleWidget(
      {@required IntlTextBuilder titleIntlTextBuilder,
      @required bool isCurrentUser,
      @required String nick}) {
    /*return IntlRichText1(
      intlTextBuilder: titleIntlTextBuilder,
      defaultStyle: AppStyle.style_textTitle_w600_20pt(),
      param: nick,
      // paramStyle: AppStyle.style_textWeak_w600_20pt(),
      paramStyle: isCurrentUser?AppStyle.style_textTitle_w600_20pt():AppStyle.style_textWeak_w600_20pt(),
    );*/
    return Container(
      width: double.infinity,
      child: Row(children: [
        Text(nick,
            style: isCurrentUser
                ? AppStyle.style_textTitle_w600_20pt()
                : AppStyle.style_textWeak_w600_20pt()),
        Text(titleIntlTextBuilder(''),
            style: AppStyle.style_textTitle_w600_20pt())
      ]),
    );
  }

  Widget sectionHeader({Widget titleWidget, String message}) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      titleWidget,
      SizedBox(
        height: 8,
      ),
      Text(message, style: AppStyle.style_textWeak_w400_12pt()),
    ]);
  }

  Widget calendarCard() {
    return Container(
      margin: EdgeInsets.only(top: 16, bottom: 16),
      // padding: EdgeInsets.all(12),
      width: double.infinity,
      decoration: BoxDecoration(
          color: AppColors.backgroundItem,
          borderRadius: BorderRadius.all(Radius.circular(7))),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ValueListenableBuilder<DateTime>(
              valueListenable: _focusedDay,
              builder: (context, value, child) {
                return CalendarHeader(
                  focusedDay: DateUtil.formatDate(value, format: "yyyy.M"),
                  todayButtonVisible: false,
                  onTodayButtonTap: () {
                    // setState(() => _focusedDay.value = DateTime.now());
                  },
                  onLeftArrowTap: () {
                    _pageController.previousPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeOut,
                    );
                  },
                  onRightArrowTap: () {
                    _pageController.nextPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeOut,
                    );
                  },
                );
              }),
          Container(
              width: double.infinity,
              child: TableCalendar(
                calendarBuilders: CalendarBuilders(
                    singleMarkerBuilder: (context, date, event) {
                  return Container(
                    decoration: BoxDecoration(
                        shape: BoxShape.circle, color: event.color),
                    width: 2.5,
                    height: 2.5,
                    margin: const EdgeInsets.symmetric(vertical: 2),
                  );
                }, todayBuilder: (context, day, focusedDay) {
                  return Container(
                      width: double.infinity,
                      height: double.infinity,
                      color: AppColors.transparent,
                      alignment: Alignment.center,
                      child: Container(
                        decoration: BoxDecoration(
                            shape: BoxShape.circle, color: AppColors.standard),
                        width: 30,
                        height: 30,
                        alignment: Alignment.center,
                        child: Text('${day.day}',
                            style: AppStyle.style_ffffff_bold_12pt()),
                      ));
                }),
                firstDay: DateTime.parse(_clockInModel.fromDate),
                lastDay: DateTime.parse(_clockInModel.toDate),
                focusedDay: _focusedDay.value,
                daysOfWeekVisible: false,
                headerVisible: false,
                availableGestures: AvailableGestures.horizontalSwipe,
                onCalendarCreated: (controller) => _pageController = controller,
                eventLoader: (day) {
                  String dayStr =
                      DateUtil.formatDate(day, format: "yyyy-MM-dd");
                  String todayStr =
                      DateUtil.formatDate(DateTime.now(), format: "yyyy-MM-dd");
                  if (_records.contains(dayStr)) {
                    return [Event(AppColors.standard)];
                  } else if (Comparable.compare(
                              dayStr, _clockInModel.fromDate) >=
                          0 &&
                      Comparable.compare(dayStr, todayStr) < 0 &&
                      Comparable.compare(dayStr, _clockInModel.toDate) <= 0) {
                    return [Event(AppColors.warning)];
                  }
                  return [];
                },
                calendarStyle: CalendarStyle(
                  outsideDaysVisible: true,
                  disabledTextStyle: AppStyle.style_textWeak_w400_12pt(),
                  defaultTextStyle: AppStyle.style_textTitle_w400_14pt(),
                  todayTextStyle: AppStyle.style_ffffff_bold_12pt(),
                  weekendTextStyle: AppStyle.style_textTitle_w400_14pt(),
                  todayDecoration: BoxDecoration(
                      color: AppColors.standard, shape: BoxShape.circle),
                ),
                onPageChanged: (focusedDay) => _focusedDay.value = focusedDay,
              ))
        ],
      ),
    );
  }
}

/// Example event class.
class Event {
  final Color color;

  const Event(this.color);
}
