import 'dart:async';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/pages/device/tool/dev_tool.dart';
import 'package:yvr_assistant/pages/device/views/match_success.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/utils/data_record.dart';
import 'package:yvr_assistant/utils/svg_provider.dart';

class MatchResultPage extends StatefulWidget {
  final arguments;

  MatchResultPage({Key key, @required this.arguments}) : super(key: key);

  @override
  _MatchResultPageState createState() => _MatchResultPageState();
}

class _MatchResultPageState extends State<MatchResultPage> {
  Timer _timer;
  int _currentIdx = 0;

  _startTimer() {
    _timer = new Timer.periodic(new Duration(milliseconds: 750), (timer) {
      setState(() {
        if (_currentIdx < 5) {
          _currentIdx++;
        } else {
          _currentIdx = 0;
        }
      });
    });
  }

  /// 蓝牙连接结果：0-连接中 1-连接失败 2-VR端拒绝 3-账号绑定成功
  ValueNotifier<int> _resultType = ValueNotifier<int>(0);
  bool isSuccess = false;

  @override
  void dispose() {
    super.dispose();
    _timer?.cancel();
    eventBusFn?.cancel();
    eventBus.fire(EventFn({
      'kBleInfo': {"isReSearch": _resultType.value != 3},
      'isInMatchPage': false,
    }));
  }

  var eventBusFn;

  @override
  void initState() {
    _startTimer();
    super.initState();
    eventBusFn = eventBus.on<EventFn>().listen((event) {
      if (event.obj != null && event.obj["kResultTypeForAddVR"] != null) {
        _resultType.value = event.obj["kResultTypeForAddVR"] ?? 0;
        if (_resultType.value == 3 && !isSuccess) {
          isSuccess = true;
          DataRecord().saveData(
            eventId: "assistant_device_addDeviceSuccess_0_0_page_view",
          );
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppColors.background,
        appBar: TopNavbar(title: YLocal.current.dev_add_dev),
        body: ValueListenableBuilder<int>(
            valueListenable: _resultType,
            builder: (context, value, child) {
              return value == 3
                  ? MatchSuccessView(
                      imagePath:
                          DevTool.vrMatchIconPath(widget.arguments["devId"]))
                  : connectingView(value);
            }));
  }

  Widget connectingView(int type) {
    String title = "";
    String subTitle = "";

    /// 蓝牙连接结果：0-连接中 1-连接失败 2-VR端拒绝 3-账号绑定成功
    switch (type) {
      case 0:
        title = YLocal.current.zhengzailianjieVRyan;
        subTitle = YLocal.current.yixiangVRyanjingfaso;
        break;
      case 1:
        title = YLocal.current.lianjieshibai;
        subTitle = YLocal.current.mofatianjiaciVRyanji;
        break;
      case 2:
        title = YLocal.current.lianjieshibai;
        subTitle = YLocal.current.tianjiaVRyanjingbeij;
        break;
      case 4:
        title = YLocal.current.lianjieshibai;
        subTitle = YLocal.current.VRyanjingchuyuqingsh;
        break;
      default:
    }
    return Stack(
      children: [
        Container(
          height: Global.screenHeight,
          margin: EdgeInsets.only(left: 18, right: 18, top: 54),
          child: SingleChildScrollView(
              child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Container(
                      height: 77,
                      width: 38,
                      decoration: BoxDecoration(
                          image: DecorationImage(
                        fit: BoxFit.fitHeight,
                        image: SvgProvider(
                          'assets/svg/phone.svg',
                        ),
                      ))),
                  type == 0
                      ? animationDots()
                      : Image(
                          image: SvgProvider('assets/svg/dev_fail.svg',
                              size: Size(32, 32))),
                  Image.asset(
                    DevTool.vrGlassIconPath(widget.arguments["devId"]),
                    width: 113,
                    height: 60,
                  )
                ],
              ),
              SizedBox(
                height: 80,
              ),
              Text(
                title,
                style: AppStyle.style_textTitle_w600_20pt(),
              ),
              Container(
                margin: EdgeInsets.only(top: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      subTitle,
                      style: AppStyle.style_textWeak_w400_18pt(),
                    ),
                    SizedBox(
                      height: 16,
                    ),
                    Row(
                      children: [
                        Image(
                            image: SvgProvider('assets/svg/dev_info.svg',
                                size: Size(16, 16))),
                        SizedBox(
                          width: 6,
                        ),
                        Flexible(
                          child: Text(
                            YLocal.current
                                .VRyanjingSNhaoS(widget.arguments["devId"]),
                            style: AppStyle.style_textSub_w400_16pt(),
                          ),
                        )
                      ],
                    )
                  ],
                ),
              ),
            ],
          )),
        ),
      ],
    );
  }

  Widget animationDots() {
    List<Widget> dots = [];
    for (var i = 0; i < 6; i++) {
      dots.add(Container(
        height: 4,
        width: 4,
        margin: EdgeInsets.only(right: 4),
        decoration: BoxDecoration(
            color: _currentIdx == i ? AppColors.standard : AppColors.textWeak,
            borderRadius: BorderRadius.all(Radius.circular(2))),
      ));
    }
    return Row(children: dots);
  }
}
