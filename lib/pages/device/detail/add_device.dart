import 'package:flutter/material.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/view_model/add_device_vmodel.dart';
import 'package:yvr_assistant/pages/device/views/radar_view.dart';
import 'package:yvr_assistant/public_ui/widget/common_button.dart';
import 'package:yvr_assistant/pages/device/views/water_ripple.dart';
import 'package:yvr_assistant/pages/device/views/ble_guide_view.dart';
import 'package:yvr_assistant/pages/device/views/dev_search_cell.dart';
import 'package:yvr_assistant/public_ui/widget/button_progress_indicator.dart';

///添加设备页
class AddDevicePage extends StatefulWidget {
  final arguments;
  AddDevicePage({Key key, @required this.arguments}) : super(key: key);

  @override
  _AddDeviceState createState() => _AddDeviceState();
}

class _AddDeviceState extends State<AddDevicePage>
    with RouteAware, WidgetsBindingObserver, TickerProviderStateMixin {
  var eventBusFn;
  RadarView _radarView;
  WaterRipple _ripple;
  //波纹动画控制器
  AnimationController _rippleController;
  //雷达扫描动画控制器
  AnimationController _radarController;
  AddDeviceVModel _addDeviceVModel = AddDeviceVModel();
  bool _isAddSuccess = false;

  @override
  void initState() {
    super.initState();
    initEventBus();
    _addDeviceVModel.init(context, widget.arguments["devIds"] ?? []);
    _radarController = AnimationController(
        vsync: this, duration: Duration(milliseconds: 4500));
    _rippleController = AnimationController(
        vsync: this, duration: Duration(milliseconds: 2000));
    _addDeviceVModel.setController(_rippleController, _radarController);
    _radarView = RadarView(controller: _radarController);
    _ripple = WaterRipple(
      count: 4,
      color: AppColors.divider,
      controller: _rippleController,
    );
  }

  void initEventBus() {
    eventBusFn = eventBus.on<EventFn>().listen((event) async {
      if (event.obj[Global.scanResultsKey] != null) {
        _addDeviceVModel.setScanDevices(event.obj[Global.scanResultsKey]);
        return;
      }
      if (event.obj != null && event.obj["kResultTypeForAddVR"] != null) {
        int addDevType = event.obj["kResultTypeForAddVR"] ?? 0;
        if (addDevType == 3 && !_isAddSuccess) {
          _isAddSuccess = true;
        }
        return;
      }
    });
  }

  @override
  void dispose() {
    eventBusFn?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          return false;
        },
        child: Scaffold(
            backgroundColor: AppColors.background,
            appBar: TopNavbar(
                title: YLocal.current.tianjiaVRyanjing,
                backCallBack: () {
                  if (_isAddSuccess) {
                    eventBus.fire(EventFn({'selectedTabbarIndex': 2}));
                  } else {
                    Navigator.pop(context);
                  }
                }),
            body: ValueListenableBuilder<bool>(
                valueListenable: _addDeviceVModel.showAddDeviceView,
                builder: (context, value, child) {
                  return SizedBox(
                    width: double.infinity,
                    height: double.infinity,
                    child: value
                        ? addDeviceWidget(context, _addDeviceVModel)
                        : startSearchView(context, _addDeviceVModel),
                  );
                })));
  }

  //添加设备状态的widget
  Widget addDeviceWidget(BuildContext context, AddDeviceVModel model) {
    return BleGuideView(
      searchDevFunc: () {
        //开始搜索设备
        model.scanPermissionCheck();
      },
    );
  }

  Widget startSearchView(BuildContext context, AddDeviceVModel model) {
    return Stack(
      children: [
        SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: CustomScrollView(
            slivers: [
              scanWidget(context, model),
              scanTextWidget(context, model),
              scanListWidget(context, model),
              buildBottomTips(context, model)
            ],
          ),
        ),
        ValueListenableBuilder<int>(
            valueListenable: model.searchState,
            builder: (context, value, child) {
              return Visibility(
                visible: value != 1,
                child: Positioned(
                  left: 16,
                  right: 16,
                  bottom: 30,
                  child: CommonButton(
                    text: YLocal.current.dev_research,
                    width: Global.screenWidth - 32,
                    onTap: () {
                      //开始搜索设备
                      model.scanPermissionCheck();
                    },
                  ),
                ),
              );
            }),
      ],
    );
  }

  Widget scanWidget(BuildContext context, AddDeviceVModel model) {
    return SliverPadding(
      padding:
          EdgeInsets.only(top: 16, left: model.marginW, right: model.marginW),
      sliver: SliverToBoxAdapter(
        child: Stack(
          children: [
            Container(
                width: double.infinity,
                height: model.rippleH,
                decoration: BoxDecoration(
                    borderRadius:
                        BorderRadius.all(Radius.circular(model.rippleH / 2))),
                child: _ripple),
            Positioned.fill(child: _radarView)
          ],
        ),
      ),
    );
  }

  Widget scanTextWidget(BuildContext context, AddDeviceVModel model) {
    return ValueListenableBuilder<int>(
        valueListenable: model.searchState,
        builder: (context, value, child) {
          String title = "";
          String subTitle = "";
          switch (value) {
            case 1:
              title = YLocal.current.zhengzaisousuo;
              subTitle = YLocal.current.zhengzaiquanlisousuo;
              break;
            case 2:
              title = YLocal.current.meiyouzhaodaofujinde;
              subTitle = YLocal.current.baoqianmeiyouzhaodao;
              break;
            case 3:
            case 4:
              // title = "搜索结束";
              // subTitle = "未发现想要的VR眼镜，可“重新搜索”";
              title = YLocal.current.sousuojieshu;
              subTitle = YLocal.current.weifaxianxiangyaodeV;
              break;
            default:
          }
          return SliverToBoxAdapter(
            child: Column(
              children: [
                const SizedBox(height: 40),
                Text(title, style: AppStyle.style_textTitle_w600_20pt()),
                const SizedBox(height: 16),
                Container(
                  child: Text(subTitle,
                      style: AppStyle.style_textWeak_w400_18pt()),
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                ),
              ],
            ),
          );
        });
  }

  Widget scanListWidget(BuildContext context, AddDeviceVModel model) {
    return ValueListenableBuilder<int>(
        valueListenable: model.searchState,
        builder: (context, value, child) {
          if (value == 3) {
            return SliverPadding(
                padding: const EdgeInsets.only(top: 24),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate((content, index) {
                    return DevSearchCell(
                      devSnCode: model.scanDevices[index]['devId'],
                      matchBle: () {
                        //连接设备
                        eventBus.fire(EventFn({
                          Global.connectDeviceKey: model.scanDevices[index]
                        }));
                        jumpToDevSearchPage(model.scanDevices[index]['devId']);
                      },
                    );
                  }, childCount: model.scanDevicesLength),
                ));
          } else if (value == 4) {
            return SliverToBoxAdapter(
              child: Container(
                width: double.infinity,
                alignment: Alignment.center,
                padding: const EdgeInsets.only(top: 24),
                child: ButtonProgressIndicator(
                    size: 40, color: AppColors.colorCCCCCC),
              ),
            );
          }
          return SliverToBoxAdapter();
        });
  }

  Widget buildBottomTips(BuildContext context, AddDeviceVModel model) {
    return ValueListenableBuilder<int>(
        valueListenable: model.searchState,
        builder: (context, value, child) {
          return SliverPadding(
            padding: const EdgeInsets.only(left: 30, right: 30, bottom: 24),
            sliver: SliverToBoxAdapter(
              child: Column(
                children: [
                  const SizedBox(height: 30),
                  Text(
                    YLocal.current.TipszaiVRyanjingzhon,
                    style: AppStyle.style_textSub_w400_14pt(),
                  ),
                  const SizedBox(height: 90)
                ],
              ),
            ),
          );
        });
  }

  bool isJump = true;
  void jumpToDevSearchPage(String devId) {
    if (isJump) {
      isJump = false;
      Future.delayed(const Duration(seconds: 3), () => isJump = true);
      Navigator.pushNamed(context, '/match_result',
          arguments: {"devId": devId});
    }
  }
}
