import "package:flutter/material.dart";
import "package:flutter_svg/svg.dart";
import 'package:yvr_assistant/model/clock_model.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/pages/device/views/dev_card_cell.dart';
import "package:yvr_assistant/pages/tabbar/top_navbar.dart";

import '../../../generated/l10n.dart';

class AcceptGrantPage extends StatefulWidget {
  final arguments;
  AcceptGrantPage({Key key, @required this.arguments}) : super(key: key);

  @override
  _AcceptGrantPageState createState() => _AcceptGrantPageState();
}

class _AcceptGrantPageState extends State<AcceptGrantPage> {
  ClockInModel _clockInModel;
  @override
  Widget build(BuildContext context) {
    _clockInModel = ClockInModel.fromJson(widget.arguments["model"]);
    return Scaffold(
      backgroundColor: AppColors.colorFFFFFF,
      appBar:
          TopNavbar(title: YLocal.of(context).dakafanxiandaqiafanx, actions: [
        IconButton(
          onPressed: () {
            jumpToPunchRulePage(context: context, clockInModel: _clockInModel);
          },
          icon: SvgPicture.asset(
            "assets/svg/clock_info.svg",
            width: 24,
            height: 24,
          ),
        )
      ]),
      body: SingleChildScrollView(
          child: Container(
        padding: EdgeInsets.all(16),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text(
            YLocal.of(context).jiangliyifafangjiang,
            style: AppStyle.style_textTitle_w600_20pt(),
          ),
          SizedBox(
            height: 8,
          ),
          Text(
            YLocal.current.gongxininnindedakahu,
            style: AppStyle.style_textSub_w400_12pt(),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                "assets/images/grant.png",
              ),
            ],
          ),
        ]),
      )),
    );
  }
}
