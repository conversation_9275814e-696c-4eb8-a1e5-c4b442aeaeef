import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';

import 'package:yvr_assistant/generated/l10n.dart';

showMergeModelView(
    {@required context,
    @required isPortrait,
    @required Function callback}) async {
  ValueNotifier<bool> _isPortrait = ValueNotifier<bool>(isPortrait);
  showModalBottomSheet(
      context: context,
      barrierColor: Colors.black.withOpacity(0.8),
      backgroundColor: AppColors.colorFFFFFF,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20.0),
        topRight: Radius.circular(20.0),
      )),
      builder: (_) {
        return Container(
          height: 350,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Flex(
                direction: Axis.horizontal,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  SizedBox(
                    width: 16,
                  ),
                  Spacer(),
                  Container(
                      alignment: Alignment.center,
                      padding: EdgeInsets.symmetric(vertical: 21),
                      child: Text(
                        YLocal.current.qingqiehuanshipindep,
                        style: AppStyle.style_textTitle_w600_18pt(),
                      )),
                  Spacer(),
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: SvgPicture.asset(
                      "assets/svg/circle_close.svg",
                      height: 32,
                      fit: BoxFit.cover,
                    ),
                  ),
                  SizedBox(
                    width: 16,
                  ),
                ],
              ),
              Container(
                width: double.infinity,
                height: 0.5,
                color: AppColors.divider,
              ),
              SizedBox(height: 34),
              ValueListenableBuilder<bool>(
                  valueListenable: _isPortrait,
                  builder: (context, value, child) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        mergeItemButton(
                            images: ["direction_vr", "direction_person"],
                            text: YLocal.current.shubingpinjieshuping,
                            isSelect: value,
                            selectTap: () {
                              callback(true);
                              Navigator.pop(context);
                            }),
                        mergeItemButton(
                            images: ["direction_horizontal"],
                            text: YLocal.current.hengbingpinjiehengpi,
                            isSelect: !value,
                            selectTap: () {
                              callback(false);
                              Navigator.pop(context);
                            })
                      ],
                    );
                  })
            ],
          ),
        );
      });
}

Widget mergeItemButton(
    {List<String> images, String text, bool isSelect, Function selectTap}) {
  bool isVertical = (images.length > 1);
  double itemW = isVertical
      ? (Global.screenWidth * 380 / 750)
      : (Global.screenWidth * 296 / 750);
  return GestureDetector(
      onTap: () {
        selectTap();
      },
      child: Container(
          width: itemW,
          padding: EdgeInsets.zero,
          margin: EdgeInsets.zero,
          decoration: BoxDecoration(
            color: isSelect ? AppColors.secondaryBg : AppColors.colorFFFFFF,
            borderRadius: BorderRadius.all(Radius.circular(8.0)),
            border: Border.all(
              color: isSelect ? AppColors.standard : AppColors.transparent,
              width: 2,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                height: 15,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    "assets/svg/${images[0]}.svg",
                    height: isVertical ? 90 : 65,
                    fit: BoxFit.cover,
                  ),
                  SizedBox(
                    width: isVertical ? 12 : 0,
                  ),
                  if (isVertical)
                    SvgPicture.asset(
                      "assets/svg/${images[1]}.svg",
                      height: 90,
                      fit: BoxFit.cover,
                    ),
                ],
              ),
              SizedBox(
                height: 12,
              ),
              Text("$text\n",
                  style: isSelect
                      ? AppStyle.style_textTitle_w600_20pt()
                      : AppStyle.style_textSub_w400_20pt())
            ],
          )));
}

Widget portraitItemButton(
    {String imageName, bool isSelect, Function selectTap}) {
  return GestureDetector(
      onTap: () {
        selectTap();
      },
      child: Container(
        width: 60,
        padding: EdgeInsets.all(8),
        margin: EdgeInsets.zero,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(8.0)),
          border: Border.all(
            color: isSelect ? AppColors.standard : AppColors.transparent,
            width: 2,
          ),
        ),
        child: SvgPicture.asset(
          "assets/svg/$imageName${isSelect ? '_s' : ''}.svg",
          width: 40,
          fit: BoxFit.cover,
        ),
      ));
}
