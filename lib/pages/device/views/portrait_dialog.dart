import 'package:flutter/material.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/public_ui/widget/common_button.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';

import '../../../generated/l10n.dart';

class PortraitView extends StatelessWidget {
  final Function nextStep;
  const PortraitView({Key key, this.nextStep}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Log.e("分辨率：::${Global.screenWidth.toInt()}X${Global.screenHeight.toInt()}");
    double margin = 30;
    double cardPadding = 15;
    double imgPadding = 15;
    double imgSizeScale = (600 / 660);
    double imgW =
        (Global.screenWidth - margin * 2 - cardPadding * 2 - imgPadding * 2);
    // ignore: unused_local_variable
    double cardH = imgSizeScale * imgW + 210;
    if (Platform.isAndroid && Global.screenWidth > 500) {
      cardH = 580;
    }
    return Container(
      color: Colors.black.withOpacity(0.8),
      width: Global.screenWidth,
      height: Global.screenHeight,
      child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
        // Container(width: 100,height: 100,color: AppColors.colorFF0000,)
        Container(
            margin: EdgeInsets.only(left: 16, right: 16),
            child: Card(
                color: AppColors.colorFFFFFF,
                clipBehavior: Clip.antiAlias,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(12.0)),
                ),
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          YLocal.of(context).qingquanchengshiyong,
                          style: AppStyle.style_textTitle_w600_20pt(),
                        ),
                        SizedBox(
                          height: 24,
                        ),
                        Text(
                          YLocal.of(context).weibaozhengxiaoguoqi,
                          style: AppStyle.style_textSub_w400_14pt(),
                        ),
                        Container(
                          padding: EdgeInsets.only(left: 6, right: 6, top: 40),
                          child: Image.asset(
                            'assets/images/portrait_view.png',
                            fit: BoxFit.cover,
                          ),
                        ),
                        SizedBox(
                          height: 40,
                        ),
                        CommonButton(
                          text: YLocal.of(context).wozhidaoliao,
                          fontSize: 16,
                          onTap: () {
                            Navigator.pop(context);
                            nextStep();
                          },
                        ),
                      ]),
                )))
      ]),

      /*child: Center(
            child: ConstrainedBox(
                constraints: BoxConstraints(maxWidth: 500),
                child:
            )
        )*/
    );
  }
}
