import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/public_ui/widget/common_button.dart';

import 'package:yvr_assistant/generated/l10n.dart';

class MergeModeView extends StatelessWidget {
  final Function mergeMode;
  const MergeModeView({Key key, this.mergeMode}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    ValueNotifier<bool> _isPortrait = ValueNotifier<bool>(true);

    return Container(
      color: Colors.black.withOpacity(0.8),
      child: Container(
        width: Global.screenWidth,
        height: Global.screenHeight,
        child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
          Container(
              margin: EdgeInsets.symmetric(horizontal: 16),
              child: Card(
                  color: AppColors.colorFFFFFF,
                  clipBehavior: Clip.antiAlias,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(Radius.circular(12.0)),
                  ),
                  child: Padding(
                      padding: EdgeInsets.all(16),
                      child: ValueListenableBuilder<bool>(
                          valueListenable: _isPortrait,
                          builder: (context, value, child) {
                            return Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(YLocal.current.qingxuanzeshipindepi,
                                      style:
                                          AppStyle.style_textTitle_w600_20pt()),
                                  SizedBox(
                                    height: 24,
                                  ),
                                  Text(
                                    YLocal.current.gaixuanzejinyingxian,
                                    style: AppStyle.style_textSub_w400_14pt(),
                                  ),
                                  SizedBox(
                                    height: 20,
                                  ),
                                  itemButton(
                                      images: [
                                        "direction_vr",
                                        "direction_person"
                                      ],
                                      text: YLocal.current.hechengshupingshipin,
                                      isSelect: value,
                                      selectTap: () {
                                        _isPortrait.value = true;
                                      }),
                                  itemButton(
                                      images: ["direction_horizontal"],
                                      text: YLocal.current.hechenghengpingshipi,
                                      isSelect: !value,
                                      selectTap: () {
                                        _isPortrait.value = false;
                                      }),
                                  SizedBox(height: 40),
                                  CommonButton(
                                    text: YLocal.current.queding,
                                    fontSize: 16,
                                    onTap: () {
                                      mergeMode(_isPortrait.value);
                                      Navigator.pop(context);
                                    },
                                  ),
                                ]);
                          }))))
        ]),
      ),
    );
  }

  Widget itemButton(
      {List<String> images, String text, bool isSelect, Function selectTap}) {
    bool isVertical = (images.length > 1);
    return GestureDetector(
        onTap: () {
          selectTap();
        },
        child: Container(
            margin: EdgeInsets.only(top: 20, left: 5, right: 5),
            padding: EdgeInsets.only(left: 10),
            width: double.infinity,
            height: 155,
            decoration: BoxDecoration(
              color: isSelect ? AppColors.colorFFFFFF : AppColors.secondaryBg,
              borderRadius: BorderRadius.all(Radius.circular(8.0)),
              border: Border.all(
                color: isSelect ? AppColors.standard : AppColors.transparent,
                width: 2,
              ),
            ),
            child: Flex(
              direction: Axis.vertical,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  flex: 1,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        "assets/svg/${images[0]}.svg",
                        height: isVertical ? 90 : 65,
                        fit: BoxFit.cover,
                      ),
                      SizedBox(
                        width: isVertical ? 40 : 0,
                      ),
                      if (isVertical)
                        SvgPicture.asset(
                          "assets/svg/${images[1]}.svg",
                          height: 90,
                          fit: BoxFit.cover,
                        ),
                    ],
                  ),
                ),
                Container(
                  height: 42,
                  child: Text(
                    "$text",
                    textAlign: TextAlign.center,
                    style: AppStyle.style_textTitle_w600_20pt(),
                  ),
                )
              ],
            )));
  }
}
