import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/manager/resource_mananger.dart';
import 'package:yvr_assistant/utils/textInput_formatter.dart';
import 'package:yvr_assistant/public_ui/widget/common_button.dart';

class WifiSyncDialog extends StatefulWidget {
  final String wifiName;
  final Function cancelCallback; //弹窗关闭回调
  final Function(String) confirmCallback; //点击确定按钮回调

  final text;
  final hintText;
  final maxLength;
  final keyboardType;
  final inputFormatters;
  final bool showErrNote;

  const WifiSyncDialog({
    Key key,
    this.wifiName,
    this.maxLength,
    this.text = "",
    this.cancelCallback,
    this.inputFormatters,
    this.confirmCallback,
    @required this.hintText,
    this.showErrNote = false,
    this.keyboardType = TextInputType.visiblePassword,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _WifiSyncDialogState();
  }
}

class _WifiSyncDialogState extends State<WifiSyncDialog> {
  bool isPwdHide = true;
  bool _showNote = false;
  Color _secondaryBg = AppColors.colorFFFFFF;
  InputBorder _border = UnderlineInputBorder(
    borderSide: BorderSide(color: AppColors.colorFFFFFF),
    borderRadius: BorderRadius.all(const Radius.circular(4)),
  );

  var _input = new TextEditingController();
  _cancelDialog() {
    _dismissDialog();
    if (widget.cancelCallback != null) {
      widget.cancelCallback();
    }
  }

  _dismissDialog() {
    Navigator.of(context).pop();
  }

  _inputConfirmDialog() {
    _dismissDialog();
    if (widget.confirmCallback != null) {
      widget.confirmCallback(_input.text);
    }
  }

  @override
  void initState() {
    super.initState();
    _showNote = widget.showErrNote;
  }

  @override
  Widget build(BuildContext context) {
    return Material(
        type: MaterialType.transparency,
        child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
          Container(
              padding: const EdgeInsets.all(16),
              margin: EdgeInsets.fromLTRB(
                  16, 0, 16, MediaQuery.of(context).viewInsets.bottom),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  color: _secondaryBg,
                  borderRadius: BorderRadius.circular(8.0)),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(YLocal.current.tongbuwanglaotongbuw,
                      style: AppStyle.style_textTitle_w600_20pt()),
                  const SizedBox(height: 18),
                  Text(YLocal.current.tongbuqianqingxianqu,
                      style: AppStyle.style_textSub_w400_16pt()),
                  const SizedBox(height: 12),
                  Container(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      (widget.wifiName.contains("unknown ssid") ||
                              widget.wifiName == null)
                          ? YLocal.current.mofahuoquwifimingche
                          : widget.wifiName,
                      style: AppStyle.style_textTitle_w400_16pt(),
                    ),
                  ),
                  const SizedBox(height: 10),
                  SizedBox(
                      height: 44,
                      child: TextField(
                          inputFormatters: [kNoSpaceFormatter],
                          keyboardType: widget.keyboardType,
                          controller: _input,
                          maxLength: widget.maxLength,
                          obscureText: isPwdHide,
                          textInputAction: TextInputAction.done,
                          onSubmitted: (String text) {
                            setState(() {
                              if (text.length > 0) {
                                _showNote = false;
                              }
                              _input.text = text;
                            });

                            _input.selection = TextSelection.fromPosition(
                                TextPosition(offset: text.length));
                          },
                          onChanged: (value) {
                            if (_input.value.isComposingRangeValid) {
                              return;
                            }
                          },
                          decoration: InputDecoration(
                            counterText: "",
                            filled: true,
                            hintText: widget.hintText,
                            fillColor: AppColors.backgroundItem,
                            contentPadding: const EdgeInsets.only(
                              top: 8,
                              left: 10,
                            ),
                            focusedBorder: _border,
                            enabledBorder: _border,
                            hintStyle: AppStyle.style_textWeak_w400_14pt(),
                            suffixIcon: InkWell(
                                onTap: () {
                                  setState(() {
                                    isPwdHide = !isPwdHide;
                                  });
                                },
                                child: Icon(
                                    isPwdHide
                                        ? IconFonts.iconHide
                                        : IconFonts.iconShow,
                                    size: 15,
                                    color: Color(0xffB0B3B8))),
                          ))),
                  _showNote
                      ? Container(
                          alignment: Alignment.topLeft,
                          margin: const EdgeInsets.only(top: 8, bottom: 12),
                          child: Text(
                            YLocal.current.VRyanjingmofalianjie,
                            style: TextStyle(
                                color: const Color(0xFFE04343), fontSize: 12),
                          ),
                        )
                      : const SizedBox(height: 32),
                  SizedBox(
                      height: 45,
                      child: Row(
                        children: [
                          Expanded(
                            child: CommonButton(
                              text: YLocal.current.cancel,
                              fontSize: 16,
                              isGradient: false,
                              width: double.infinity,
                              secondaryBg: AppColors.backgroundItem,
                              textColor: AppColors.textTitle,
                              onTap: _cancelDialog,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: CommonButton(
                              text: YLocal.current.queding,
                              fontSize: 16,
                              isGradient: false,
                              width: double.infinity,
                              secondaryBg: AppColors.standard,
                              textColor: AppColors.colorFFFFFF,
                              onTap: () {
                                _inputConfirmDialog();
                              },
                            ),
                          ),
                        ],
                      )),
                ],
              )),
        ]));
  }
}
