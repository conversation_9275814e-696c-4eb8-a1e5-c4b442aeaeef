import 'package:flutter/cupertino.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/pages/device/tool/dev_tool.dart';
import 'package:yvr_assistant/public_ui/widget/common_button.dart';

import 'package:yvr_assistant/generated/l10n.dart';

class DevSearchCell extends StatefulWidget {
  final String devSnCode;
  final Function matchBle;
  DevSearchCell({Key key, this.devSnCode = "", this.matchBle})
      : super(key: key);

  @override
  State<DevSearchCell> createState() => _DevSearchCellState();
}

class _DevSearchCellState extends State<DevSearchCell> {
  double textWidth =
      (Global.screenWidth - 70 - 20 - 20 - 16 - 12 - 16 - 16 - 54);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 86,
      margin: EdgeInsets.fromLTRB(16, 0, 16, 12),
      padding: EdgeInsets.fromLTRB(16, 0, 12, 0),
      decoration: BoxDecoration(
        color: AppColors.backgroundItem,
        borderRadius: BorderRadius.all(Radius.circular(14)),
        //
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Image(
              image: DevTool.devAssetImage(widget.devSnCode),
              width: 53,
              height: 34),
          SizedBox(width: 10),
          Expanded(
              child: Text(widget.devSnCode,
                  maxLines: 1, style: AppStyle.style_textTitle_w500_14pt())),
          CommonButton(
            text: YLocal.current.tianjia,
            hPadding: 22,
            height: 31,
            fontSize: 14,
            onTap: () {
              widget.matchBle();
            },
          )
        ],
      ),
    );
  }
}
