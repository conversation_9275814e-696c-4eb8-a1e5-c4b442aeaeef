import 'dart:convert';
import 'package:flutter_svg/svg.dart';
import 'package:flutter/material.dart';
import 'package:common_utils/common_utils.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/model/dev_model.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/model/clock_model.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';
import 'package:yvr_assistant/utils/svg_provider.dart';
import 'package:yvr_assistant/model/punch_form_model.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/manager/resource_mananger.dart';
import 'package:yvr_assistant/pages/device/tool/dev_tool.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';

import '../../../public_ui/widget/common_button.dart';

typedef DeleteCallback = Function(String devId);
typedef ConnectCallBack = Function(int linkState, String devId);

/// 默认相同网络，确认不同网络时显示一键同步
class DevCardCell extends StatefulWidget {
  final DevModel model;
  final Function updateNameCallBack;
  final Function projScreenCallBack;
  final Function syncWiFiCallBack;
  final Function recordingCallBack;
  final Function realityMixerCallBack;
  final ValueChanged<bool> teenCallback;
  final DeleteCallback deleteCallback;
  final ConnectCallBack connectCallBack;

  DevCardCell(
      {Key key,
      @required this.model,
      this.connectCallBack,
      this.updateNameCallBack,
      this.projScreenCallBack,
      this.syncWiFiCallBack,
      this.recordingCallBack,
      this.realityMixerCallBack,
      this.teenCallback,
      this.deleteCallback})
      : super(key: key);

  @override
  State<DevCardCell> createState() => _DevCardCellState();
}

class _DevCardCellState extends State<DevCardCell> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onLongPress: () {
          if (widget.deleteCallback != null) {
            widget.deleteCallback(widget.model.devId);
          }
        },
        child: Container(
          margin: EdgeInsets.only(top: 13),
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          decoration: BoxDecoration(
            color: AppColors.colorFFFFFF,
            borderRadius: BorderRadius.all(Radius.circular(14)),
          ),
          child: Column(
            children: [
              headWidget(),
              if (YLocal.of(context).dev_add_dev == '添加设备') clockInWidget(),
              deviceWidget(),
              batteryWidget(),
              wifiSyncWidget(),
              SizedBox(height: 20),
              Divider(
                color: AppColors.divider,
                height: 0.5,
              ),
              bottomBtnWidget()
            ],
          ),
        ));
  }

  //头部widget,含名称、青少年模式
  Widget headWidget() {
    bool isOpen = widget.model.teen == 1;
    double _opacity = (widget.model.linkState == 0) ? 1 : 0.3;
    ValueNotifier<String> _btName = ValueNotifier<String>(widget.model.btName);
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (widget.model.linkState == 0) {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (_) {
                return CustomDialog(
                  title: YLocal.of(context).dev_modify,
                  content: YLocal.of(context).shiyonglanhehuliansh,
                  height: 500,
                  isCancel: true,
                  dialogType: DialogType.DialogInput,
                  keyboardType: TextInputType.text,
                  hintText: widget.model.btName ?? "",
                  maxLength: 32,
                  inputCommitCallback: (text) {
                    final nameByteData = utf8.encode(text);
                    final regExpStr = r"^[\u4E00-\u9FA5A-Za-z0-9_]+$";
                    if (nameByteData.length > 17) {
                      YvrToast.showToast(
                          YLocal.of(context).ninshurudemingchenyi);
                    } else if (!RegExp(regExpStr).hasMatch(text)) {
                      YvrToast.showToast(
                          YLocal.of(context).jinzhichizhongwendax);
                    } else {
                      widget.updateNameCallBack(text);
                      _btName.value = text;
                      widget.model.btName = text;
                    }
                  },
                );
              });
        } else if (_opacity != 1) {
          showRemindDialog();
        }
      },
      child: Container(
        child: Row(
          children: [
            ValueListenableBuilder<String>(
                valueListenable: _btName,
                builder: (context, value, child) {
                  double rightW = isOpen ? 238 : 150;
                  return ConstrainedBox(
                      constraints:
                          BoxConstraints(maxWidth: Global.screenWidth - rightW),
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Text(
                            value.length > 0 ? value : widget.model.devId,
                            maxLines: 1,
                            style: AppStyle.style_textTitle_w500_16pt()),
                      ));
                }),
            SizedBox(
              width: 5,
            ),
            Opacity(
                opacity: _opacity,
                child: Icon(
                  IconFonts.iconPen,
                  size: 20,
                  color: AppColors.textSub,
                )),
            Spacer(),
            Visibility(
              visible: YLocal.of(context).dev_add_dev == '添加设备',
              child: GestureDetector(
                onTap: () {
                  widget.teenCallback?.call(isOpen);
                },
                child: Container(
                  height: 24,
                  decoration: BoxDecoration(
                    color: AppColors.secondaryBg,
                    borderRadius: BorderRadius.circular(24),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 10),
                  child: Row(
                    children: [
                      SvgPicture.asset(
                        "assets/svg/ic_teen.svg",
                        width: 14,
                        height: 14,
                      ),
                      if (isOpen)
                        Container(
                          margin: EdgeInsets.only(left: 5),
                          child: Text(
                            YLocal.of(context).qingshaonianmoshiyik,
                            style: AppStyle.style_textWeak_w400_12pt(),
                          ),
                        )
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  //打卡返现widget
  Widget clockInWidget() {
    if (widget.model.auth == 1) {
      return GestureDetector(
        onTap: () {
          clockIn();
        },
        child: Container(
          width: double.infinity,
          height: 39,
          margin: EdgeInsets.only(top: 15),
          padding: EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            image: DecorationImage(
              image: AssetImage('assets/images/bg_clock_in.png'),
              fit: BoxFit.fill, // 完全填充
            ),
          ),
          child: Row(children: [
            Image.asset(
              'assets/images/ic_clock_in.png',
              width: 20,
              height: 20,
            ),
            const SizedBox(width: 8),
            Text(
              YLocal.of(context).dakafanxiandaqiafanx,
              style: AppStyle.style_767880_12pt(),
            ),
            Spacer(),
            Text(YLocal.of(context).dianjichakanhuodongx,
                style: TextStyle(
                    color: Color(0xFF808CB5),
                    fontSize: 14,
                    fontWeight: FontWeight.w400))
          ]),
        ),
      );
    }
    return const SizedBox();
  }

  Future<void> clockIn() async {
    YvrToast.showLoading(message: "");
    ClockInModel clockInModel = await YvrRequests.getDeviceDetail(
        devId: widget.model.devId, today: DateUtil.getNowDateStr());

    await StorageManager.foreverData
        .setItem(Global.kPunchAdImageUrl, clockInModel.brdMbPic);
    await StorageManager.foreverData
        .setItem(Global.kPunchRuleImageUrl, clockInModel.ruleMbPic);

    //auth：0:无活动权限（设备不在活动中，或者活动还没开始，或者活动已经结束） 1：有活动权限
    if (clockInModel.status == 0 || clockInModel.auth == 0) {
      YvrToast.dismiss();
      showClockDialog(clockInModel);
    } else {
      int actId = DBUtil.instance.userBox.get(kActId);
      bool isCurrentUser = (actId == clockInModel.actId);
      if (!isCurrentUser) {
        YvrToast.dismiss();
        Navigator.pushNamed(context, '/clock_in',
            arguments: {"model": clockInModel.toJson()});
        return;
      }
      if (clockInModel.status != 2) {
        YvrToast.dismiss();
      }
      // clockInModel.status = 2;//测试
      //0:未参与活动 1：正在参与 2：可领奖 3：已领奖 4：自然结束
      switch (clockInModel.status) {
        case 2:
          PunchFormModel punchFormModel =
              await YvrRequests.punchAuditDetail(widget.model.devId);
          YvrToast.dismiss();
          bool isCanEdit = true;
          if (punchFormModel != null) {
            if (punchFormModel.approvalStatus == "approve_fail" ||
                punchFormModel.rewardStatus == "give_fail") {
              isCanEdit = true;
            } else if (punchFormModel.activityStatus != "no_submit") {
              isCanEdit = false;
            }
          }
          Navigator.pushNamed(context, '/accept_award', arguments: {
            "isCanEdit": isCanEdit,
            "formModel": punchFormModel,
            "model": clockInModel.toJson(),
            "modelDev": widget.model
          });
          break;
        case 3:
          Navigator.pushNamed(context, '/accept_grant',
              arguments: {"model": clockInModel.toJson()});
          break;
        default:
          Navigator.pushNamed(context, '/clock_in',
              arguments: {"model": clockInModel.toJson()});
      }
    }
  }

  //设备图Widget
  Widget deviceWidget() {
    double _opacity = (widget.model.linkState == 0) ? 1 : 0.3;
    String devImage = "dev_cell";
    if (DevTool.isDev3(widget.model.devId)) {
      devImage = "dev_cell3";
    } else if (DevTool.isDev2(widget.model.devId)) {
      devImage = "dev_cell2";
    }
    return Padding(
      padding: const EdgeInsets.only(top: 16),
      child: GestureDetector(
        onTap: () {
          widget.connectCallBack(widget.model.linkState, widget.model.devId);
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Opacity(
              opacity: _opacity,
              child: Image.asset(
                'assets/images/$devImage.png',
                width: Global.screenWidth - 154,
                fit: BoxFit.cover,
              ),
            )
          ],
        ),
      ),
    );
  }

  //电池widget
  Widget batteryWidget() {
    String _batteryPic;
    if (widget.model.battery != null) {
      if (widget.model.battery == 100) {
        _batteryPic = "battery_full";
      } else if (widget.model.battery > 15) {
        _batteryPic = "battery_middle";
      } else {
        _batteryPic = "battery_low";
      }
    }
    if (widget.model.battery != null && widget.model.linkState == 0) {
      return Container(
        width: double.infinity,
        margin: EdgeInsets.only(top: 4),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image(
                image: SvgProvider('assets/svg/$_batteryPic.svg',
                    size: Size(23, 23))),
            SizedBox(
              width: 8,
            ),
            Text(
              "${widget.model.battery}%",
              style: AppStyle.style_textSub_w500_16pt(),
            )
          ],
        ),
      );
    }
    return SizedBox();
  }

  //wifi同步widget
  Widget wifiSyncWidget() {
    if (!widget.model.sameWiFi) {
      return Padding(
        padding: EdgeInsets.only(top: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Row(
                children: [
                  Image(
                      image: SvgProvider('assets/svg/dev_net_info.svg',
                          size: Size(20, 20))),
                  SizedBox(
                    width: 4,
                  ),
                  Flexible(
                    child: Text(
                      YLocal.of(context).WiFibuyizhibufengong,
                      style: AppStyle.style_textSub_w400_14pt(),
                    ),
                  )
                ],
              ),
            ),
            CommonButton(
              text: YLocal.of(context).tongbuwanglaotongbuw,
              height: 32,
              hPadding: 12,
              fontSize: 16,
              fontWeight: FontWeight.normal,
              onTap: () {
                if (widget.model.linkState == 0) {
                  widget.syncWiFiCallBack();
                } else {
                  showRemindDialog();
                }
              },
            ),
          ],
        ),
      );
    }
    return SizedBox();
  }

  //底部按钮widget
  Widget bottomBtnWidget() {
    double _opacity = (widget.model.linkState == 0) ? 1 : 0.3;
    double _opacitySameWifi = ((widget.model.linkState == 0 &&
            widget.model.sameWiFi &&
            widget.model.getWifiIp)
        ? 1
        : 0.3);
    return Opacity(
      opacity: _opacitySameWifi,
      child: Container(
        width: double.infinity,
        child: Row(
          mainAxisAlignment: Platform.isIOS
              ? MainAxisAlignment.spaceBetween
              : MainAxisAlignment.start,
          children: [
            buttonItem(
                iconName: "vr_proj",
                btnName: YLocal.of(context).toubingtouping,
                callBack: () {
                  if (_opacitySameWifi == 1) {
                    widget.projScreenCallBack();
                  } else if (_opacity != 1) {
                    showRemindDialog();
                  }
                }),
            if (Platform.isAndroid) SizedBox(width: 20),
            buttonItem(
                iconName: "vr_file",
                btnName: YLocal.of(context).jiebinghelubingjiepi,
                callBack: () async {
                  if (_opacitySameWifi == 1) {
                    bool isAllowJump = await DevTool.isHaveLocalNetworkPrivacy(
                        snCode: widget.model.devId);
                    if (!isAllowJump) {
                      return;
                    }
                    YvrToast.showLoading();
                    Future.delayed(const Duration(seconds: 3), () {
                      YvrToast.dismiss();
                    });
                    Navigator.pushNamed(context, "/vr_file",
                        arguments: {"snCode": widget.model.devId});
                  } else if (_opacity != 1) {
                    showRemindDialog();
                  }
                }),
            if (Platform.isAndroid) SizedBox(width: 20),
            buttonItem(
                iconName: "vr_record",
                btnName: YLocal.of(context).pinjieluzhi,
                callBack: () {
                  if (_opacitySameWifi == 1) {
                    widget.recordingCallBack();
                  } else if (_opacity != 1) {
                    showRemindDialog();
                  }
                }),
            // Visibility(
            //   visible: Platform.isIOS,
            //   child: Stack(
            //     children: [
            //       buttonItem(
            //           iconName: "vr_mix_record",
            //           btnName: YLocal.of(context).hunheluzhi,
            //           callBack: () async {
            //             if (_opacitySameWifi == 1) {
            //               bool isSupport =
            //                   await PlatformUtils.getIPhoneIsSupportMRC();
            //               if (isSupport) {
            //                 widget.realityMixerCallBack();
            //               } else {
            //                 YvrToast.showToast(YLocal.of(context).buzhichigaiiPhonexin);
            //               }
            //             } else if (_opacity != 1) {
            //               showRemindDialog();
            //             }
            //           }),
            //       Positioned(
            //         right: 10,
            //         top: 10,
            //         child: SvgPicture.asset(
            //           "assets/svg/test_symbol.svg",
            //           height: 15,
            //         ),
            //       ),
            //     ],
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  Widget buttonItem({String iconName, String btnName, Function callBack}) {
    return GestureDetector(
        onTap: () {
          if (callBack != null) {
            callBack();
          }
        },
        child: Container(
            color: AppColors.colorFFFFFF,
            padding: EdgeInsets.only(top: 15),
            child: Column(children: [
              SvgPicture.asset(
                "assets/svg/$iconName.svg",
                width: 24,
                height: 24,
              ),
              SizedBox(
                height: 12,
              ),
              Text(
                btnName,
                style: AppStyle.style_textSub_w400_14pt(),
              ),
            ])));
  }

  void showRemindDialog() {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) {
          return CustomDialog(
            title: YLocal.of(context).qingxianlianjieVRyan,
            content: YLocal.current.dangVRyanjingzaifuji,
            height: 235,
            isCancel: false,
            confirmText: YLocal.of(context).understood,
          );
        });
  }

  void showClockDialog(ClockInModel clockInModel) {
    showDialog(
        context: context,
        barrierDismissible: false,
        barrierColor: Colors.black.withOpacity(0.5),
        builder: (BuildContext context) {
          return Center(
              child: Container(
                  padding: EdgeInsets.all(20),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      clockInModel.type == 2
                          ? Card(
                              color: Color(0xff242527),
                              margin: EdgeInsets.only(bottom: 30),
                              clipBehavior: Clip.antiAlias,
                              shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(12.0)),
                              ),
                              child: Stack(
                                children: [
                                  CachedNetworkImage(
                                    imageUrl: clockInModel.brdMbPic,
                                    fit: BoxFit.fitWidth,
                                  ),
                                  Positioned(
                                      top: 10,
                                      right: 10,
                                      child: GestureDetector(
                                          onTap: () {
                                            jumpToPunchRulePage(
                                                context: context,
                                                clockInModel: clockInModel);
                                          },
                                          child: Container(
                                            padding:
                                                EdgeInsets.fromLTRB(6, 4, 6, 4),
                                            decoration: BoxDecoration(
                                              color: Color(0xFF17191B)
                                                  .withOpacity(0.7),
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(8.0)),
                                            ),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                SvgPicture.asset(
                                                  "assets/svg/clock_pop_info.svg",
                                                  color: Color(0xFFE6E6E6),
                                                  width: 18,
                                                  height: 18,
                                                ),
                                                SizedBox(
                                                  width: 5,
                                                ),
                                                Text(
                                                  YLocal.of(context)
                                                      .huodongguize,
                                                  style: TextStyle(
                                                    color:
                                                        const Color(0xFFE6E6E6),
                                                    decoration: TextDecoration
                                                        .underline,
                                                    fontSize: 12.0,
                                                  ),
                                                )
                                              ],
                                            ),
                                          )))
                                ],
                              ))
                          : activityPreCard(clockInModel: clockInModel),
                      IconButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: SvgPicture.asset(
                          "assets/svg/clock_close.svg",
                          width: 40,
                          height: 40,
                        ),
                      )
                    ],
                  )));
        });
  }

  Widget activityPreCard({ClockInModel clockInModel}) {
    return Center(
        child: Container(
            padding: EdgeInsets.all(20),
            child:
                Column(mainAxisAlignment: MainAxisAlignment.center, children: [
              Card(
                  color: Color(0xff242527),
                  margin: EdgeInsets.only(bottom: 40),
                  clipBehavior: Clip.antiAlias,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(Radius.circular(12.0)),
                  ),
                  child: Column(
                    children: [
                      Image.asset("assets/images/event.png"),
                      SizedBox(
                        height: 15,
                      ),
                      Text(
                        YLocal.current.dakahuo50goujikuanxi,
                        style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 20,
                            color: Colors.white),
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Text(
                        YLocal.current.kaiqihuodongqingqian,
                        style: TextStyle(fontSize: 15, color: Colors.white),
                      ),
                      Padding(
                        padding: EdgeInsets.fromLTRB(20, 10, 20, 45),
                        child: Text(
                          YLocal.current.canjiabingwanchengda,
                          style:
                              TextStyle(fontSize: 13, color: Color(0xFFC0C0C1)),
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          jumpToPunchRulePage(
                              context: context, clockInModel: clockInModel);
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SvgPicture.asset(
                              "assets/svg/clock_pop_info.svg",
                              width: 18,
                              height: 18,
                            ),
                            SizedBox(
                              width: 5,
                            ),
                            Text(
                              YLocal.current.huodongguize,
                              style: TextStyle(
                                color: const Color(0xFFE6E6E6),
                                decoration: TextDecoration.underline,
                                fontSize: 12.0,
                              ),
                            )
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 20,
                      )
                    ],
                  ))
            ])));
  }
}

void jumpToPunchRulePage({BuildContext context, ClockInModel clockInModel}) {
  if (clockInModel.type == 2 && clockInModel.ruleMbPic != null) {
    Navigator.pushNamed(context, '/rule_image', arguments: {
      "title": YLocal.of(context).huodongguize,
      "imgUrl": clockInModel.ruleMbPic,
    });
  } else {
    Navigator.pushNamed(context, '/local_web', arguments: {
      "title": YLocal.of(context).huodongguize,
      "path": "assets/html/clock.html",
      "endDate": clockInModel.endDate,
      "expireDate": clockInModel.expireDate,
    });
  }
}
