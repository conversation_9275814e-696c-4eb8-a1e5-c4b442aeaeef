import 'package:flutter/cupertino.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/public_ui/widget/common_button.dart';

import '../../../generated/l10n.dart';

class MatchSuccessView extends StatefulWidget {
  final String imagePath;
  const MatchSuccessView({Key key, this.imagePath}) : super(key: key);
  @override
  State<MatchSuccessView> createState() => _MatchSuccessViewState();
}

class _MatchSuccessViewState extends State<MatchSuccessView> {
  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      Column(
        children: [
          Image.asset(
            widget.imagePath,
            fit: BoxFit.cover,
          ),
          SizedBox(
            height: 40,
          ),
          Text(
            YLocal.of(context).VRyanjingtianjiachen,
            style: AppStyle.style_success_w600_20pt(),
          ),
          Padding(
              padding: EdgeInsets.only(top: 20, left: 18, right: 18),
              child: Text(
                YLocal.of(context).dangqianVRyanjingyid,
                style: AppStyle.style_textTitle_w400_16pt(),
              )),
        ],
      ),
      Positioned(
          bottom: 56,
          left: 16,
          right: 16,
          child: CommonButton(
            text: YLocal.of(context).wancheng,
            width: Global.screenWidth - 32,
            onTap: () {
              Navigator.of(context).popUntil((route) => route.isFirst);
            },
          ))
    ]);
  }
}
