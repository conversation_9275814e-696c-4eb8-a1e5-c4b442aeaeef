import 'dart:async';
import 'dart:math';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/styles/app_color.dart';

import 'package:yvr_assistant/generated/l10n.dart';

class ProgressView extends StatefulWidget {
  final int totalTime;
  final ValueNotifier<int> progress;
  final Function endMergeCallback;
  ProgressView({Key key, this.totalTime, this.endMergeCallback, this.progress})
      : super(key: key);

  @override
  State<ProgressView> createState() => _ProgressViewState();
}

class _ProgressViewState extends State<ProgressView>
    with TickerProviderStateMixin {
  int _timeLeft;
  Timer _countdownTimer;
  AnimationController _repeatController;
  Animation<double> _animation;

  @override
  void initState() {
    /// 动画持续时间是 3秒，此处的this指 TickerProviderStateMixin
    _repeatController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(); // 设置动画重复播放
    // 创建一个从0到360弧度的补间动画 v * 2 * π
    _animation = Tween<double>(begin: 0, end: 1).animate(_repeatController);

    _timeLeft = widget.totalTime;
    super.initState();
  }

  @override
  void dispose() {
    _repeatController?.dispose();
    _countdownTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Stack(
          children: [
            Positioned(
                left: 15,
                top: 0,
                child: IconButton(
                  onPressed: () {
                    showEndSaveDialog(
                            context: context,
                            endMergeCallback: widget.endMergeCallback)
                        .then((value) {
                      if (value != null && value is String) {
                        Navigator.of(context).pop(value);
                      }
                    });
                  },
                  icon: Icon(
                    Icons.arrow_back_ios,
                    color: Color(0xff979797),
                  ),
                )),
            ValueListenableBuilder<int>(
                valueListenable: widget.progress,
                builder: (context, value, child) {
                  /// 传输占总进度比例20% 因时间进度等比例流动过快 故此阶段时间流速占比设为10%
                  double scale;
                  final int passV = 20;
                  final int delayV = 10;
                  if (widget.totalTime < 10) {
                    scale = (1 - value / 100);
                  } else if (value <= passV) {
                    scale = (1 - (value * (delayV / passV) / 100));
                  } else {
                    scale = (1 - (value - passV + delayV) / (100 - delayV));
                  }
                  _timeLeft = (scale * widget.totalTime).toInt();

                  return (value == 100)
                      ? Center(
                          child: Image.asset(
                            "assets/images/progress_done.png",
                            height: 220,
                            width: 220,
                          ),
                        )
                      : ConstrainedBox(
                          constraints: BoxConstraints.expand(),
                          child: Stack(alignment: Alignment.center, children: [
                            Image.asset(
                              "assets/images/progress_bg.png",
                              height: 210,
                              width: 210,
                            ),
                            RotationTransition(
                              turns: _animation,
                              child: Image.asset(
                                "assets/images/progress_border.png",
                                height: 220,
                                width: 220,
                              ),
                            ),
                            RichText(
                                textAlign: TextAlign.center,
                                text: TextSpan(
                                    text: "${max(widget.progress.value, 1)}%\n",
                                    style: TextStyle(
                                        color: AppColors.colorE8E8E8,
                                        fontSize: 50),
                                    children: [
                                      TextSpan(
                                        text: YLocal.current
                                            .yujihaixuNmiao(max(_timeLeft, 1)),
                                        // text: "预计还需${max(_timeLeft, 1)}s",
                                        style: TextStyle(
                                            color: Color(0xFFB0B2B8),
                                            fontSize: 12),
                                      )
                                    ])),
                            Container(
                              height: 60,
                              width: 150,
                              margin: EdgeInsets.only(top: 320),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(4.0)),
                                  color: Color(0xFF111213).withOpacity(0.62)),
                              child: RichText(
                                  textAlign: TextAlign.center,
                                  text: TextSpan(
                                      text: YLocal.current.qingwutuichu + "\n",
                                      style: TextStyle(
                                          fontSize: 20,
                                          color: AppColors.colorE8E8E8),
                                      children: [
                                        TextSpan(
                                          text: value >= 20
                                              ? YLocal
                                                  .current.shipinnuligechengzho
                                              : YLocal
                                                  .current.VRshipinchuanshuzhon,
                                          style: TextStyle(
                                              color: Color(0xFFB0B2B8),
                                              fontSize: 12),
                                        )
                                      ])),
                            )
                          ]));
                }),
          ],
        ));
  }
}

showEndSaveDialog({BuildContext context, Function endMergeCallback}) {
  return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) {
        return CustomDialog(
          title: YLocal.current.weibaocunpinjieshipi,
          content: YLocal.current.fangqibaocundangqian,
          height: 180,
          confirmCallback: () {
            if (endMergeCallback != null) {
              endMergeCallback();
            }
          },
        );
      });
}
