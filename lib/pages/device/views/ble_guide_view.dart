import 'package:flutter/cupertino.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/public_ui/widget/common_button.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';

class BleGuideView extends StatelessWidget {
  final Function searchDevFunc;
  const BleGuideView({Key key, this.searchDevFunc}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          height: Global.screenHeight,
          child: SingleChildScrollView(
            child: Column(
              children: [
                const SizedBox(height: 52),
                Image.asset(
                  'assets/images/vr_dev.png',
                ),
                const SizedBox(height: 38),
                Text(
                  YLocal.of(context).dev_ble_lead,
                  style: AppStyle.style_textTitle_w600_20pt(),
                ),
                // const Sized<PERSON>ox(height: 38),
                // Text(
                //   YLocal.of(context).please_add_dev,
                //   style: AppStyle.style_textSub_w500_14pt(),
                // ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 37, 20, 52),
                  child: Text(
                    YLocal.of(context).dev_add_remind,
                    textAlign: TextAlign.left,
                    // style: AppStyle.style_textSub_w500_14pt(),
                    style: TextStyle(
                        color: AppColors.textSub,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        height: 1.5),
                  ),
                ),
              ],
            ),
          ),
        ),
        Positioned(
          bottom: 46,
          left: 16,
          child: CommonButton(
            text: YLocal.of(context).dev_search_ble,
            width: Global.screenWidth - 32,
            height: 48,
            onTap: () {
              searchDevFunc();
            },
          ),
        )
      ],
    );
  }
}
