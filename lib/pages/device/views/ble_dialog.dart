import 'package:flutter/material.dart';
import 'package:wifi_iot/wifi_iot.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/pages/device/views/wifi_sync_dialog.dart';

class BleDialog {
  static Future<void> showSyncNetworkDialog(
      {@required BuildContext context,
      @required bool showErrNote,
      @required Function connectWiFi,
      String remidText}) async {
    if (remidText == null) {
      remidText = YLocal.current.mofatongbuwanglaowuf;
    }
    bool isHaveWiFi = await WiFiForIoTPlugin.isConnected();
    if (!isHaveWiFi) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (_) {
            return CustomDialog(
              title: YLocal.current.dishitishi,
              content: YLocal.current.qingxianjiangshoujil,
              height: 200,
              isCancel: false,
              confirmText: YLocal.current.understood,
              confirmCallback: () {
                eventBus.fire(EventFn({'kResultTypeForAddVR': 1}));
              },
            );
          });
    } else {
      String ssid = await WiFiForIoTPlugin.getSSID();
      showDialogWithErrorNote(
          context: context,
          showErrNote: showErrNote,
          connectWiFi: connectWiFi,
          ssid: ssid);
    }
  }

  static bool isCanopenNetworkDialog = true;
  static void showDialogWithErrorNote(
      {@required BuildContext context,
      @required bool showErrNote,
      @required Function connectWiFi,
      String ssid}) {
    if (!isCanopenNetworkDialog) {
      return;
    }
    isCanopenNetworkDialog = false;
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) {
          return WifiSyncDialog(
            wifiName: ssid,
            showErrNote: showErrNote,
            keyboardType: TextInputType.text,
            cancelCallback: () {
              isCanopenNetworkDialog = true;
              eventBus.fire(EventFn({'kResultTypeForAddVR': 1}));
            },
            confirmCallback: (String password) {
              isCanopenNetworkDialog = true;
              YvrToast.showLoading(
                  message: YLocal.current.zhengzaiweiVRyanjing);
              if (password.length > 0 && password.length < 8) {
                Future.delayed(const Duration(seconds: 1), () async {
                  YvrToast.dismiss();
                  showDialogWithErrorNote(
                      context: context,
                      showErrNote: true,
                      connectWiFi: connectWiFi,
                      ssid: ssid);
                });
                return;
              }

              connectWiFi(ssid, password);
              Future.delayed(const Duration(seconds: 15), () async {
                YvrToast.dismiss();
              });
            },
            hintText: YLocal.current.qingshuruWiFimima,
          );
        });
  }
}
