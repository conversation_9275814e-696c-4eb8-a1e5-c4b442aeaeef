import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/manager/resource_mananger.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/public_ui/widget/common_button.dart';
import 'package:yvr_assistant/utils/textInput_formatter.dart';

class NetworkDialog extends StatefulWidget {
  final String wifiName;
  final bool outsideDismiss; //点击弹窗外部，关闭弹窗，默认为true true：可以关闭 false：不可以关闭
  final Function cancelCallback; //弹窗关闭回调
  final Function(String) confirmCallback; //点击确定按钮回调
  final double height;
  final keyboardType;
  final hintText;
  final maxLength;
  final text;
  final inputFormatters;
  final bool showErrNote;

  const NetworkDialog({
    Key key,
    this.wifiName,
    this.outsideDismiss = false,
    this.cancelCallback,
    this.height,
    this.keyboardType = TextInputType.visiblePassword,
    this.maxLength,
    @required this.hintText,
    this.text = "",
    this.inputFormatters,
    this.showErrNote = false,
    this.confirmCallback,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _NetworkDialogState();
  }
}

class _NetworkDialogState extends State<NetworkDialog> {
  bool isPwdHide = true;
  bool _showNote = false;
  var _input = new TextEditingController();
  _cancelDialog() {
    _dismissDialog();
    if (widget.cancelCallback != null) {
      widget.cancelCallback();
    }
  }

  _dismissDialog() {
    Navigator.of(context).pop();
  }

  _inputConfirmDialog() {
    _dismissDialog();
    if (widget.confirmCallback != null) {
      widget.confirmCallback(_input.text);
    }
  }

  @override
  void initState() {
    super.initState();
    _showNote = widget.showErrNote;
  }

  @override
  Widget build(BuildContext context) {
    Color bgColor = AppColors.colorFFFFFF;

    return WillPopScope(
        child: GestureDetector(
          onTap: () => {widget.outsideDismiss ? _dismissDialog() : null},
          child: Material(
            type: MaterialType.transparency,
            child: Container(
                width: Global.screenWidth,
                height: Global.statusBarHeight,
                color: AppColors.transparent,
                margin: EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                          padding: EdgeInsets.all(16),
                          width: double.infinity,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                              color: bgColor,
                              borderRadius: BorderRadius.circular(8.0)),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: <Widget>[
                              Text(YLocal.of(context).tongbuwanglaotongbuw,
                                  style: AppStyle.style_textTitle_w600_20pt()),
                              Container(
                                margin: EdgeInsets.only(
                                  top: 18,
                                  bottom: 12,
                                ),
                                child: Text(
                                    YLocal.of(context).tongbuqianqingxianqu,
                                    style: AppStyle.style_textSub_w400_16pt()),
                              ),
                              Container(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  (widget.wifiName.contains("unknown ssid") ||
                                          widget.wifiName == null)
                                      ? YLocal.of(context).mofahuoquwifimingche
                                      : widget.wifiName,
                                  style: AppStyle.style_textTitle_w400_16pt(),
                                ),
                              ),
                              Container(
                                  height: 42,
                                  color: AppColors.transparent,
                                  margin: EdgeInsets.only(top: 10),
                                  alignment: Alignment.center,
                                  child: TextField(
                                      inputFormatters: [NoSpaceFormatter()],
                                      keyboardType: widget.keyboardType,
                                      controller: _input,
                                      maxLength: widget.maxLength,
                                      obscureText: isPwdHide,
                                      onChanged: (value) {
                                        if (_input
                                            .value.isComposingRangeValid) {
                                          return;
                                        }
                                        setState(() {
                                          if (value.length > 0) {
                                            _showNote = false;
                                          }
                                          _input.text = value;
                                        });

                                        _input.selection =
                                            TextSelection.fromPosition(
                                                TextPosition(
                                                    offset:
                                                        _input.text.length));
                                      },
                                      decoration: InputDecoration(
                                        counterText: "",
                                        hintText: widget.hintText,
                                        hintStyle:
                                            AppStyle.style_textWeak_w400_14pt(),
                                        fillColor: AppColors.backgroundItem,
                                        filled: true,
                                        contentPadding:
                                            EdgeInsets.only(left: 10, top: 8),
                                        focusedBorder: UnderlineInputBorder(
                                          borderSide:
                                              BorderSide(color: bgColor),
                                          borderRadius: BorderRadius.all(
                                            Radius.circular(4),
                                          ),
                                        ),
                                        enabledBorder: new UnderlineInputBorder(
                                          borderSide:
                                              new BorderSide(color: bgColor),
                                          borderRadius: BorderRadius.all(
                                            Radius.circular(4), //边角为5
                                          ),
                                        ),
                                        suffixIcon: InkWell(
                                            onTap: () {
                                              setState(() {
                                                isPwdHide = !isPwdHide;
                                              });
                                            },
                                            child: Icon(
                                                isPwdHide
                                                    ? IconFonts.iconHide
                                                    : IconFonts.iconShow,
                                                size: 15,
                                                color: Color(0xffB0B3B8))),
                                      ))),
                              _showNote
                                  ? Container(
                                      alignment: Alignment.topLeft,
                                      margin:
                                          EdgeInsets.only(top: 8, bottom: 12),
                                      child: Text(
                                        YLocal.of(context).VRyanjingmofalianjie,
                                        style: TextStyle(
                                            color: Color(0xFFE04343),
                                            fontSize: 12),
                                      ),
                                    )
                                  : SizedBox(
                                      height: 32,
                                    ),
                              Container(
                                  height: 45,
                                  child: Row(
                                    children: <Widget>[
                                      Expanded(
                                        child: CommonButton(
                                          text: YLocal.of(context).cancel,
                                          width: double.infinity,
                                          isGradient: false,
                                          secondaryBg: AppColors.backgroundItem,
                                          fontSize: 16,
                                          textColor: AppColors.textTitle,
                                          onTap: _cancelDialog,
                                        ),
                                      ),
                                      SizedBox(
                                        width: 12,
                                      ),
                                      Expanded(
                                        child: CommonButton(
                                          text: YLocal.of(context).queding,
                                          width: double.infinity,
                                          isGradient: false,
                                          secondaryBg: AppColors.standard,
                                          fontSize: 16,
                                          textColor: AppColors.colorFFFFFF,
                                          onTap: () {
                                            // if (_input.text.length > 0) {
                                            _inputConfirmDialog();
                                            // }
                                          },
                                        ),
                                      ),
                                    ],
                                  ))
                            ],
                          )),
                    ])),
          ),
        ),
        onWillPop: () async {
          return widget.outsideDismiss;
        });
  }
}
