import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/styles/app_color.dart';

class CountdownView extends StatefulWidget {
  // final Function iPhoneStartVRRecording;
  final Function countdownOver;
  CountdownView({Key key, this.countdownOver}) : super(key: key);

  @override
  State<CountdownView> createState() => _CountdownViewState();
}

class _CountdownViewState extends State<CountdownView> {
  Timer _countdownTimer;
  ValueNotifier<int> _currentTimer = ValueNotifier<int>(3);
  @override
  void initState() {
    super.initState();
    _countdownTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      _currentTimer.value = max(0, _currentTimer.value - 1);
      if (_currentTimer.value == 0) {
        Navigator.pop(context);
        widget.countdownOver();
      }
    });
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        color: Colors.black.withOpacity(0.8),

        /// 必须设置一个空容器
        child: Container(
          child: Center(
            child: Container(
              height: Global.screenWidth / 2,
              width: Global.screenWidth / 2,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(
                  Global.screenWidth / 4,
                )),
                border: Border.all(
                  color: AppColors.colorFFFFFF,
                  width: 2,
                ),
              ),
              child: ValueListenableBuilder<int>(
                  valueListenable: _currentTimer,
                  builder: (context, value, child) {
                    return Text(
                      "${_currentTimer.value}",
                      style: TextStyle(
                          color: AppColors.colorE8E8E8,
                          fontSize: 120,
                          fontWeight: FontWeight.bold),
                    );
                  }),
            ),
          ),
        ));
  }
}
