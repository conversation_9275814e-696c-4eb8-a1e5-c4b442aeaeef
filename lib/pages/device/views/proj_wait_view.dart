import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/pages/device/tool/dev_tool.dart';
import 'package:yvr_assistant/utils/svg_provider.dart';

import '../../../public_ui/widget/common_button.dart';
import 'package:yvr_assistant/styles/app_style.dart';

class ProjWaitView extends StatelessWidget {
  const ProjWaitView({Key key, Widget animationDots, @required final arguments})
      : _animationDots = animationDots,
        _arguments = arguments,
        super(key: key);
  final _arguments;
  final Widget _animationDots;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: double.infinity,
          height: double.infinity,
          color: AppColors.secondaryBg,
          child: SingleChildScrollView(
              child: Column(
            children: [
              Container(
                  color: AppColors.colorFFFFFF,
                  padding: EdgeInsets.only(
                      top: 42, bottom: Global.screenHeight * 0.03),
                  child: Row(
                    children: [
                      Container(
                        margin: const EdgeInsets.fromLTRB(31, 0, 22, 0),
                        height: 113,
                        width: 113,
                        child: Image.asset(
                            DevTool.vrGlassIconPath(_arguments["devId"])),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(YLocal.of(context).dev_proj_from,
                              style: AppStyle.style_textTitle_w500_14pt()),
                          SizedBox(
                            height: 15,
                          ),
                          Text(_arguments["deviceName"],
                              style: AppStyle.style_textWeak_w400_14pt())
                        ],
                      )
                    ],
                  )),
              Container(
                  color: AppColors.colorFFFFFF,
                  height: math.max(Global.screenHeight * 0.09, 48),
                  padding: EdgeInsets.fromLTRB(80, 0, 0, 0),
                  alignment: Alignment.centerLeft,
                  child: _animationDots),
              Container(
                  color: AppColors.colorFFFFFF,
                  padding: EdgeInsets.only(top: 10, bottom: 42),
                  child: Row(
                    children: [
                      Container(
                          margin: EdgeInsets.fromLTRB(65, 0, 61, 0),
                          height: 81,
                          width: 40,
                          decoration: BoxDecoration(
                              image: DecorationImage(
                            fit: BoxFit.fitHeight,
                            image: SvgProvider(
                              'assets/svg/phone.svg',
                            ),
                          ))),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(YLocal.of(context).dev_proj_to,
                              style: AppStyle.style_textTitle_w500_14pt()),
                          SizedBox(
                            height: 15,
                          ),
                          Text(StorageManager.deviceModel,
                              style: AppStyle.style_textWeak_w400_14pt())
                        ],
                      )
                    ],
                  )),
              Container(
                width: double.infinity,
                padding:
                    EdgeInsets.fromLTRB(16, 20, 16, Global.screenHeight * 0.05),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      YLocal.of(context).tips,
                      textAlign: TextAlign.left,
                      style: AppStyle.style_textTitle_w500_16pt(),
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    Text(
                      YLocal.of(context).dev_proj_desc,
                      style: AppStyle.style_textWeak_w400_16pt(),
                    ),
                  ],
                ),
              ),
              CommonButton(
                  width: Global.screenWidth - 220,
                  height: 41,
                  isGradient: false,
                  radius: 6,
                  secondaryBg: AppColors.color000000_60,
                  textWidget: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SpinKitCircle(
                        size: 17,
                        color: AppColors.colorFFFFFF,
                        duration: Duration(seconds: 2),
                      ),
                      SizedBox(
                        width: 10,
                      ),
                      Text(
                        YLocal.of(context).zhengzailianjiezhong,
                        style: AppStyle.style_ffffff_14pt(),
                      )
                    ],
                  )),
            ],
          )),
        ),
        Positioned(
          bottom: Global.paddingBottom + 46,
          left: 16,
          right: 16,
          child: CommonButton(
            text: YLocal.of(context).tingzhitoubingtingzh,
            width: Global.screenWidth - 32,
            onTap: () {
              Navigator.of(context).popUntil((route) => route.isFirst);
            },
          ),
        )
      ],
    );
  }
}
