import 'dart:async';
import 'package:flutter_svg/svg.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:adaptive_dialog/adaptive_dialog.dart';
import 'package:cached_network_image/cached_network_image.dart';

import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';
import 'package:yvr_assistant/model/teen_model.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/manager/events_bus2.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';
import 'package:yvr_assistant/public_ui/widget/dialogs.dart';
import 'package:yvr_assistant/provider/provider_widget.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/provider/view_state_widget.dart';
import 'package:yvr_assistant/pages/device/tool/ble_state.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/view_model/device_list_vmodel.dart';
import 'package:yvr_assistant/pages/device/views/ble_dialog.dart';
import 'package:yvr_assistant/pages/device/views/dev_card_cell.dart';

///设备列表页
class DeviceListPage extends StatefulWidget {
  DeviceListPage({Key key}) : super(key: key);

  @override
  _DeviceListState createState() => _DeviceListState();
}

class _DeviceListState extends State<DeviceListPage>
    with AutomaticKeepAliveClientMixin<DeviceListPage> {
  var eventBusFn;
  var teenEventBusFn;
  DeviceListVModel _deviceListVModel = DeviceListVModel.instance;

  @override
  void initState() {
    super.initState();
    initEventBus();
  }

  @override
  void dispose() {
    if (eventBusFn != null) {
      eventBusFn?.cancel();
    }
    if (teenEventBusFn != null) {
      teenEventBusFn?.cancel();
    }
    super.dispose();
  }

  void initEventBus() {
    eventBusFn = eventBus.on<EventFn>().listen((event) async {
      if (event.obj == null) return;

      bool appInBackground = event.obj[Global.kAppInBackground] ?? false;
      if (appInBackground) {
        _deviceListVModel.reqDeviceNetworkState();
      }

      //设备tab事件通知
      if (event.obj[Global.deviceTabEventKey] != null &&
          event.obj[Global.deviceTabEventKey]) {
        Log.d('收到设备tab事件通知----绑定设备数:${_deviceListVModel.deviceModels.length}');
        if (!_deviceListVModel.isFirstLoad &&
            _deviceListVModel.deviceModels.isEmpty) {
          _deviceListVModel.loadAllData();
        }
        return;
      }

      if (event.obj[Global.connectDeviceKey] != null) {
        Map<String, dynamic> scanDevice = event.obj[Global.connectDeviceKey];
        Log.d('scanDevices ===========设备列表页收到数据============>$scanDevice');
        _deviceListVModel.resetData();
        _deviceListVModel.blueConnect(
            isAddDevice: true,
            device: scanDevice['device'],
            devId: scanDevice['devId'],
            devName: scanDevice['name']);
        return;
      }

      if (event.obj[Global.deviceListRefreshKey] != null &&
          event.obj[Global.deviceListRefreshKey]) {
        Log.d('---------------------设备列表页收到刷新通知------------------------');
        _deviceListVModel.resetConnect(isFromEventBus: true);
        return;
      }

      if (event.obj[Global.kPhoneLaunchApp] != null &&
          event.obj[Global.kPhoneLaunchApp]) {
        Log.d('---------------------收到打开应用通知------------------------');
        String pkg = event.obj[Global.kPhoneLaunchAppPKG];
        _deviceListVModel.sendStreamValue(BleTransmissionStatus.REQ_START_APP,
            data: pkg);
        return;
      }

      if (event.obj[Global.kNavigateTeenDevId] != null) {
        String navigateTeenDevId = event.obj[Global.kNavigateTeenDevId];
        bool success =
            _deviceListVModel.navigateTeenModePageById(navigateTeenDevId);
        if (!success) YvrToast.showToast(YLocal.current.shebeibucunzai);
        return;
      }
    });

    teenEventBusFn = eventBus.on<EventData>().listen((event) {
      switch (event.type) {
        case AppEvent.EVENT_TEEN_MODE_CHANGED:
          switch (event.subType) {
            case AppEvent.EVENT_SUB_TEEN_DEV_DIRTY:
              break;
            case AppEvent.EVENT_SUB_TEEN_MODE_DIRTY:
              break;
            //青少年模式已关闭，无法操作
            case AppEvent.EVENT_SUB_TEEN_MODE_DISABLED:
              String id = event.dataAs<String>();
              _deviceListVModel.setTeenagersCloseState(id);
              break;
          }
          break;
        case AppEvent.EVENT_TEEN_MODE_APPLY:
          TeenModeModel teen = event.dataAs<TeenModeModel>();
          _deviceListVModel.teenagersResultRefresh(teen);
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: AppColors.secondaryBg,
      appBar: AppBar(
        toolbarHeight: 0,
        backgroundColor: AppColors.colorFFFFFF,
      ),
      body: ProviderWidget<DeviceListVModel>(
          model: _deviceListVModel,
          onModelReady: (model) {
            model.init(context);
          },
          builder: (context, model, child) {
            if (UserVModel().isLogin()) {
              if (model.idle) {
                bool isHaveBindDevs = (model.deviceModels != null &&
                    model.deviceModels.isNotEmpty);

                // if (isHaveBindDevs && Platform.isAndroid) {
                //   model.registerNotificationListenerService();
                // }

                return Container(
                  width: double.infinity,
                  height: double.infinity,
                  child: Column(children: [
                    titleWidget(model),
                    Expanded(
                        child: SizedBox(
                      width: double.infinity,
                      height: double.infinity,
                      child: isHaveBindDevs
                          ? ListView.builder(
                              itemCount: devCellList(model).length,
                              controller: model.childScrollView,
                              padding: EdgeInsets.only(
                                  left: 16, right: 16, bottom: 10),
                              itemBuilder: (context, index) {
                                return devCellList(model)[index];
                              },
                            )
                          : emptyPlhdView(model),
                    ))
                  ]),
                );
              } else if (model.busy) {
                return ViewStateBusyWidget();
              } else if (model.error) {
                return ViewStateErrorWidget(
                    error: model.viewStateError,
                    onPressed: () {
                      _deviceListVModel.resetConnect();
                    });
              }
            }
            return const SizedBox();
          }),
    );
  }

  Widget titleWidget(DeviceListVModel model) {
    return Container(
      height: 72,
      width: Global.screenWidth,
      color: AppColors.colorFFFFFF,
      padding: const EdgeInsets.fromLTRB(16, 0, 6, 0),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
                height: 40,
                width: 40,
                child: ClipRRect(
                  //剪裁为圆角矩形
                  borderRadius: BorderRadius.circular(20),
                  child: CachedNetworkImage(
                    fadeInDuration: Duration.zero,
                    imageUrl: model.user?.avatar ?? "",
                    placeholder: (context, url) => Image.asset(
                      'assets/images/s_plhd.png',
                      fit: BoxFit.contain,
                    ),
                    errorWidget: (context, url, error) {
                      return Image.asset(
                        model.user?.sex == 2
                            ? 'assets/images/avatar_girl.jpg'
                            : 'assets/images/avatar_boy.jpg',
                        fit: BoxFit.cover,
                      );
                    },
                  ),
                )),
            Padding(
              padding: const EdgeInsets.only(left: 14, right: 4),
              child: Text(
                YLocal.current.wodeVRyanjingwodiVRy,
                style: AppStyle.style_textTitle_w500_18pt(),
              ),
            ),
            GestureDetector(
                onTap: () {
                  //刷新设备连接
                  model.clickResetConnect();
                },
                child: SvgPicture.asset(
                  "assets/svg/dev_refresh.svg",
                  width: 20,
                  height: 20,
                ))
          ],
        ),
        Spacer(),
        // IconButton(
        //     onPressed: () {
        //       Navigator.pushNamed(context, '/add_search',
        //           arguments: {"devIds": model.deviceIds});
        //     },
        //     icon: SvgPicture.asset(
        //       "assets/svg/vr_add.svg",
        //       width: 22,
        //       height: 22,
        //     )),
        // IconButton(
        //     onPressed: () {
        //       Navigator.pushNamed(context, '/vr_question');
        //     },
        //     icon: SvgPicture.asset(
        //       "assets/svg/vr_qa.svg",
        //       width: 22,
        //       height: 22,
        //     )),
      ]),
    );
  }

  Widget emptyPlhdView(DeviceListVModel model) {
    return Center(
      heightFactor: Global.screenHeight,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            "assets/svg/dev_vr.svg",
            width: 44,
            height: 30,
            color: Color(0xFFB0B3B8),
          ),
          const SizedBox(height: 16),
          Text(
              // "当前未绑定VR眼镜",
              YLocal.current.dangqianweibangdingV,
              style: AppStyle.style_textSub_w400_14pt()),
          SizedBox(height: 44),
          // CommonButton(
          //   text: YLocal.current.tianjiaVRyanjing,
          //   width: Global.screenWidth - 140,
          //   height: 48,
          //   onTap: () {
          //     Navigator.pushNamed(context, '/add_search',
          //         arguments: {"devIds": null});
          //   },
          // ),
        ],
      ),
    );
  }

  bool isRecordingJump = true;
  List<Widget> devCellList(DeviceListVModel model) {
    model.deviceModels.sort((a, b) => a.linkState.compareTo((b.linkState)));
    return model.deviceModels.map<Widget>((item) {
      return DevCardCell(
        model: item,
        updateNameCallBack: (text) async {
          Log.d('-------------------更新名称回调-----------------------');
          model.sendStreamValue(BleTransmissionStatus.REQ_FIX_BT_NAME_STATUS,
              data: text);
        },
        projScreenCallBack: () async {
          int actId = DBUtil.instance.userBox.get(kActId);
          Log.d('actId ==============投屏回调=============>$actId');
          model.sendStreamValue(
              BleTransmissionStatus.REQ_START_PUBLISH_FROM_MOBILE_STATUS,
              data: actId.toString());
          YvrToast.showLoading();
          Future.delayed(const Duration(seconds: 5), () {
            YvrToast.dismiss();
          });
        },
        syncWiFiCallBack: () async {
          BleDialog.showSyncNetworkDialog(
              context: context,
              showErrNote: false,
              connectWiFi: (String ssid, String password) async {
                Log.i("password ================>$password");
                model.setRemindVRNoWiFi(true);
                model.setPassword(password);
                model.reqNetworkForVR(ssid: ssid, funType: 3);
              });
        },
        recordingCallBack: () async {
          // 发起前先结束上次投屏, 防止iOS系统本地网络框弹出后无法再投屏
          if (Platform.isIOS) {
            scheduleMicrotask(() {
              _deviceListVModel.sendStreamValue(
                  BleTransmissionStatus.REQ_STOP_RECORDER_AND_SHARE,
                  data: "0");
            });
            Future.delayed(const Duration(milliseconds: 800), () {
              startRecording();
            });
          } else {
            startRecording();
          }
        },
        connectCallBack: (int linkState, String devId) async {
          Log.d('切换设备回调--devId：$devId,linkState：$linkState');
          model.switchDevice(linkState, devId);
        },
        teenCallback: (value) {
          model.onTeenModeTap(item);
        },
        deleteCallback: (String devId) {
          showModalActionSheet<String>(
            context: context,
            actions: Dialogs.devActions,
            style: AdaptiveStyle.iOS,
            builder: (context, child) => Theme(
              child: child,
              data: ThemeData(
                cupertinoOverrideTheme:
                    const CupertinoThemeData(primaryColor: AppColors.warning),
              ),
            ),
          ).then((result) {
            if (result == Dialogs.kDelete) {
              model.unBindDevice(devId);
            }
          });
        },
        realityMixerCallBack: () {
          model.sendStreamValue(BleTransmissionStatus.REQ_START_MRC);
          YvrToast.showLoading();
          Future.delayed(const Duration(seconds: 3), () {
            YvrToast.dismiss();
          });
        },
      );
    }).toList();
  }

  startRecording() {
    if (isRecordingJump) {
      isRecordingJump = false;
      DBUtil.instance.userBox.put(Global.kOnlyRecordingProjWithoutJump, false);
      _deviceListVModel.sendBleStartRecordingOnlyProj();
      YvrToast.showLoading();
      Future.delayed(const Duration(seconds: 3), () {
        isRecordingJump = true;
        YvrToast.dismiss();
      });
    }
  }

  @override
  bool get wantKeepAlive => true;
}
