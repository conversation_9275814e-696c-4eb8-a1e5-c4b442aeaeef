/*
  FijkPlayer可参考设置
  https://www.jianshu.com/p/4cb960a9718a
  https://blog.csdn.net/u013270727/article/details/83900062
  参数配置： https://www.jianshu.com/p/c8c755ed61ee
*/

import 'package:flutter/material.dart';
import 'package:fijkplayer/fijkplayer.dart';
// import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/network/vr_request.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/utils/log.dart';
// import 'package:yvr_assistant/public_ui/widget/toast_show.dart';

class IjkPlayerConfig {
  /// 配置 RTSP 播放器选项
  ///
  /// [ijkPlayer] 播放器实例
  /// [snCode] 设备序列号
  Future<String> configRtspOptions({
    @required FijkPlayer ijkPlayer,
    @required String snCode,
  }) async {
    // 读取存储中的 VR 主机地址
    String vrHost = StorageManager.localStorage.getItem("vr_host") ?? "";
    // if (vrHost != null) {
    //   await ijkPlayer.setOption(
    //       FijkOption.playerCategory, "rtsp_transport", "tcp");
    // }

    // 批量设置播放器参数
    final options = {
      FijkOption.playerCategory: {
        "analyzeduration": 1, // 播放前的探测时间
        "max_cached_duration": 3, // 最大缓存秒数
        "mediacodec-hevc": 1, // 开启 H.265 硬件解码
        "videotoolbox": 1, // 开启硬件解码（降低 CPU 消耗）
        "max-fps": 30, // 最大帧率
        "packet-buffering": 0, // 关闭缓冲减少延迟
        "framedrop": 5, // 网络不佳时丢帧减少卡顿
        "render-wait-start": 1, // 等待开始后再绘制
        "find_stream_info": 0, // 直接播放不查询 stream_info
      },
      FijkOption.formatCategory: {
        "reconnect": 1, // 服务器断开后自动重连
        "skip_loop_filter": 48, // 跳过循环滤波提升流畅度
        "analyzemaxduration": 100, // 最大分析时长
        "flush_packets": 1, // 立即清理数据包减少等待
      },
    };

    // 应用播放器参数
    for (var category in options.keys) {
      for (var entry in options[category].entries) {
        try {
          await ijkPlayer.setOption(category, entry.key, entry.value);
        } catch (e) {
          // if (entry.key == "framedrop") {
          //   YvrToast.showToast(YLocal.current.huoquVRduanshipinshi);
          // }
          Log.e("设置播放器参数失败：$e");
        }
      }
    }

    // 生成 RTSP 地址
    Map<String, dynamic> headers = VrHttp.configHeader(snCode);
    String rtspUrl =
        "rtsp://$vrHost:8086/${headers['timestamp']}/${headers['sign']}";

    // 设置播放源并自动播放
    try {
      await ijkPlayer.setDataSource(rtspUrl, autoPlay: true);
    } catch (e) {
      Log.e("Rtsp设置播放源失败：$e");
    }

    return rtspUrl;
  }
}
