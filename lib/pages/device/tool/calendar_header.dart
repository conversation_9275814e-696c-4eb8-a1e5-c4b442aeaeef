import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:yvr_assistant/styles/app_color.dart';
import 'package:yvr_assistant/styles/app_style.dart';

import '../../../generated/l10n.dart';

// ignore: unused_element
class CalendarHeader extends StatelessWidget {
  final String focusedDay;
  final VoidCallback onLeftArrowTap;
  final VoidCallback onRightArrowTap;
  final VoidCallback onTodayButtonTap;
  final bool todayButtonVisible;

  const CalendarHeader({
    Key key,
    @required this.focusedDay,
    @required this.onLeftArrowTap,
    @required this.onRightArrowTap,
    @required this.onTodayButtonTap,
    @required this.todayButtonVisible,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    List<String> _weekdays = YLocal.of(context).riyiersan.split(',');
    List<Widget> diyWeekdayText() {
      var tempList = _weekdays.map((text) {
        return Text(
          text,
          style: AppStyle.style_textWeak_w400_12pt(),
        );
      });
      return tempList.toList();
    }

    return Column(children: [
      Padding(
        padding: EdgeInsets.only(left: 16, right: 8),
        child: Row(
          children: [
            Text(
              focusedDay,
              style: AppStyle.style_textTitle_w600_18pt(),
            ),
            if (todayButtonVisible)
              IconButton(
                icon: Icon(Icons.calendar_today, size: 20.0),
                visualDensity: VisualDensity.compact,
                onPressed: onTodayButtonTap,
              ),
            const Spacer(),
            IconButton(
              icon: SvgPicture.asset(
                "assets/svg/arrow_outline_left.svg",
                width: 32,
                height: 32,
              ),
              onPressed: onLeftArrowTap,
            ),
            IconButton(
              icon: SvgPicture.asset(
                "assets/svg/arrow_outline_right.svg",
                width: 32,
                height: 32,
              ),
              onPressed: onRightArrowTap,
            ),
          ],
        ),
      ),
      Container(
        width: double.infinity,
        height: 1,
        color: AppColors.divider,
        margin: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      ),
      Padding(
        // padding: EdgeInsets.fromLTRB(16, 0, 16, 10),
        padding: EdgeInsets.fromLTRB(20, 0, 20, 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: diyWeekdayText(),
        ),
      ),
    ]);
  }
}
