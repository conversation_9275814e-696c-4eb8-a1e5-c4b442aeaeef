import 'package:flutter/material.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:location/location.dart' hide PermissionStatus;

import 'package:yvr_assistant/generated/l10n.dart';

/// 动态申请权限，ios 要在info.plist 上面添加
/// iOS 首次状态：isDenied 拒绝后isPermanentlyDenied
/// Android 首次、拒绝后都是isDenied 无法区分
Future<bool> requestSavePermission() async {
  if (Platform.isIOS) {
    var status = await Permission.photosAddOnly.status;
    if (status.isDenied) {
      await [
        Permission.photosAddOnly,
      ].request();
    } else if (status.isPermanentlyDenied) {
      showOpenPhotoDialog(remind: YLocal.current.qingxiankaiqibendexi);
    }
    return status.isGranted;
  } else {
    var status = await Permission.storage.status;
    if (status.isDenied) {
      YvrToast.showToast(YLocal.current.qingxiankaiqibendexi);
      await [
        Permission.storage,
      ].request();
    }
    return status.isGranted;
  }
}

requestLocationPermission2(context, {Function callBack}) async {
  PermissionStatus permission = await Permission.location.status;

  Log.e("当前位置权限：$permission");
  if (permission == PermissionStatus.denied) {
    if (Platform.isAndroid) {
      YvrToast.showToast(YLocal.current.saomiaolanhexuyaonin);
    }
    await Permission.location.request().then((status) {
      if (status == PermissionStatus.granted) {
        if (Platform.isAndroid) {
          callBack();
        } else {
          Future.delayed(const Duration(milliseconds: 500), () async {
            PermissionStatus permission = await Permission.location.status;
            if (permission == PermissionStatus.granted) {
              callBack();
            }
          });
        }
      }
    });
    return;
  } else if (permission == PermissionStatus.granted) {
    callBack();
    return;
  }
  if (permission == PermissionStatus.permanentlyDenied) {
    showOpenPhotoDialog(remind: YLocal.current.saomiaolanhexuyaonin);
    return;
  }

  if (!await Location().serviceEnabled()) {
    YvrToast.showToast(YLocal.current.weikaiqidingweifuwub);
    return;
  }
}

showOpenPhotoDialog({@required String remind}) {
  showDialog(
      context: Global.context,
      barrierDismissible: false,
      builder: (_) {
        return CustomDialog(
          title: YLocal.of(Global.context).dishitishi,
          content: remind,
          cancelText: YLocal.of(Global.context).quxiao,
          confirmText: YLocal.of(Global.context).qukaiqi,
          height: 200,
          confirmCallback: openAppSettings,
        );
      });
}

Future<bool> requestPhotoPermission(bool isCarema) async {
  if (Platform.isIOS) {
    var status =
        await (isCarema ? Permission.camera : Permission.photos).status;
    Log.d("权限：$status");
    if (status.isDenied) {
      if (Platform.isAndroid) {
        String toast = '';
        if (isCarema) {
          toast = YLocal.current.qingxiankaiqixiangji;
        } else {
          toast = YLocal.current.qingxiankaiqibendexi;
        }
        YvrToast.showToast(toast);
      }
      await [
        isCarema ? Permission.camera : Permission.photos,
      ].request();
    } else if (status.isPermanentlyDenied) {
      String remind = "";
      if (isCarema) {
        remind = YLocal.current.qingxiankaiqixiangji;
      } else {
        remind = YLocal.current.qingxiankaiqibendexi;
      }
      showOpenPhotoDialog(remind: remind);
    }

    return status.isGranted || status.isLimited;
  } else {
    var status = await Permission.storage.status;
    Log.d("权限：$status");
    if (status.isDenied) {
      String toast = '';
      if (isCarema) {
        toast = YLocal.current.qingxiankaiqixiangji;
      } else {
        toast = YLocal.current.qingxiankaiqibendexi;
      }
      YvrToast.showToast(toast);
      await [
        Permission.storage,
      ].request();
    }
    return status.isGranted;
  }
}

//检查定位权限
checkLocationPermission(context, Function callBack) async {
  Permission permission = Permission.locationAlways;
  PermissionStatus status = await permission.status;
  print('检测权限$status');
  if (status.isGranted) {
    //权限通过
    if (callBack != null) callBack();
  } else if (status.isDenied) {
    //权限拒绝， 需要区分IOS和Android，二者不一样
    requestPermission(permission);
  } else if (status.isPermanentlyDenied) {
    //权限永久拒绝，且不在提示，需要进入设置界面
    openAppSettings();
  } else if (status.isRestricted) {
    //活动限制（例如，设置了家长///控件，仅在iOS以上受支持。
    openAppSettings();
  } else {
    //第一次申请
    requestPermission(permission);
  }
}

//申请权限
void requestPermission(Permission permission) async {
  PermissionStatus status = await permission.request();
  print('权限状态$status');
  if (!status.isGranted) {
    openAppSettings();
  }
}

double getFileSizeInMb(File file) {
  int sizeInBytes = file.lengthSync();
  double sizeInMB = sizeInBytes / (1024 * 1024);
  return sizeInMB;
}

Future<void> deleteFile(String path) async {
  try {
    var file = File(path);
    if (await file.exists()) {
      await file.delete();
    }
  } catch (e) {
    Log.e('删除文件失败：$e');
  }
}
