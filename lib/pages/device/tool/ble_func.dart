import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:wifi_iot/wifi_iot.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/pages/device/views/network_dialog.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:yvr_assistant/pages/device/tool/ble_state.dart';

final int _sendBleMaxLength = 17;

/// 蓝牙数据通讯说明:
/// 第一字节消息类型即 BleTransmissionStatus；
/// 第二字节0表示不分包，分包的话可以用来表示总包数，最小值为2。
/// 第三个字节 表示包的位置1起始值 （两个包分别为 1 、2），
/// 第四个字节开始为数据，每一个分包被收到后，VR端会反馈字节值 -1,默认数据为字符串0。
/// 字符串转成byte数组，按照17个字节分段；
writeStreamValue(
    {@required BluetoothCharacteristic bleChar,
    @required BleTransmissionStatus status,
    String data = "0",
    Function callBack}) async {
  // sleep(Duration(milliseconds: 400));
  // Future.delayed(Duration(milliseconds: 500), () async {
  List<int> bytes = utf8.encode(data);
  int wrapCount = ((bytes.length ~/ 17) + (bytes.length % 17 > 0 ? 1 : 0));
  if (wrapCount < 2) {
    List<int> param = [];
    param
      ..addAll([status.index, 0, 0])
      ..addAll(utf8.encode(data));
    try {
      await bleChar?.write(param);
      Log.d("不分包发出数据：$param");
      if (param != null && param.length > 0 && param[0] == 1) {
        callBack();
      }
    } catch (e) {
      Log.d("蓝牙发出数据失败：$e");
    }
  } else {
    int wrapIndex = 0;
    for (int i = 0; i < bytes.length; i += _sendBleMaxLength) {
      wrapIndex++;
      if ((i + _sendBleMaxLength) < bytes.length) {
        List<int> subWrap = [];
        subWrap
          ..addAll([status.index, wrapCount, wrapIndex])
          ..addAll(bytes.getRange(i, i + _sendBleMaxLength));
        await bleChar?.write(subWrap);
        Log.d("分包发出数据：$subWrap");
        // sleep(Duration(milliseconds: 400));
      } else {
        List<int> subWrap = [];
        subWrap
          ..addAll([status.index, wrapCount, wrapIndex])
          ..addAll(bytes.getRange(i, bytes.length));
        await bleChar?.write(subWrap);
        Log.d("分包最后发出数据：$subWrap");
      }
    }
  }
  // });
}

Future<void> showSyncNetworkDialog(
    {@required BuildContext context,
    @required bool showErrNote,
    @required Function connectWiFi,
    String remidText}) async {
  if (remidText == null) {
    remidText = YLocal.of(context).mofatongbuwanglaowuf;
  }
  bool isHaveWiFi = await WiFiForIoTPlugin.isConnected();
  if (!isHaveWiFi) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) {
          return CustomDialog(
            title: YLocal.of(context).dishitishi,
            content: YLocal.of(context).qingxianjiangshoujil,
            height: 200,
            isCancel: false,
            confirmText: YLocal.of(context).understood,
            confirmCallback: () {
              eventBus.fire(EventFn({'kResultTypeForAddVR': 1}));
            },
          );
        });
  } else {
    String ssid = await WiFiForIoTPlugin.getSSID();
    showDialogWithErrorNote(
        context: context,
        showErrNote: showErrNote,
        connectWiFi: connectWiFi,
        ssid: ssid);
  }
}

bool isCanopenNetworkDialog = true;
void showDialogWithErrorNote(
    {@required BuildContext context,
    @required bool showErrNote,
    @required Function connectWiFi,
    String ssid}) {
  if (!isCanopenNetworkDialog) {
    return;
  }
  isCanopenNetworkDialog = false;
  showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) {
        return NetworkDialog(
          height: 300,
          wifiName: ssid,
          showErrNote: showErrNote,
          keyboardType: TextInputType.text,
          cancelCallback: () {
            isCanopenNetworkDialog = true;
            eventBus.fire(EventFn({'kResultTypeForAddVR': 1}));
          },
          confirmCallback: (String password) {
            isCanopenNetworkDialog = true;
            YvrToast.showLoading(
                message: YLocal.of(context).zhengzaiweiVRyanjing);
            if (password.length > 0 && password.length < 8) {
              Future.delayed(Duration(seconds: 1), () async {
                YvrToast.dismiss();
                showDialogWithErrorNote(
                    context: context,
                    showErrNote: true,
                    connectWiFi: connectWiFi,
                    ssid: ssid);
              });
              return;
            }

            connectWiFi(ssid, password);
            Future.delayed(Duration(seconds: 15), () async {
              YvrToast.dismiss();
            });
          },
          hintText: YLocal.of(context).qingshuruWiFimima,
        );
      });
}
