import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/network/vr_request.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';

/// 配置设备相关功能
class DevTool {
  static bool isDev2(String snCode) {
    return (snCode != null &&
            snCode.length == 18 &&
            snCode.substring(0, 2) == "D2") ??
        false;
  }

  static bool isDev3(String snCode) {
    return (snCode != null &&
            snCode.length == 18 &&
            snCode.substring(0, 2) == "D3") ??
        false;
  }

  static bool isDev2OrNewer(String snCode) {
    return (snCode != null &&
            snCode.length == 18 &&
            (snCode.substring(0, 2) == "D2" ||
                snCode.substring(0, 2) == "D3")) ??
        false;
  }

  static String deviceModelName(String snCode) {
    return isDev3(snCode) ? "New" : (isDev2(snCode) ? "YVR 2" : "YVR 1");
  }

  static String vrMatchIconPath(String snCode) {
    return isDev2OrNewer(snCode)
        ? 'assets/images/match_success2.png'
        : 'assets/images/match_success.png';
  }

  static String vrGlassIconPath(String snCode) {
    return isDev3(snCode)
        ? 'assets/images/vr_glass3.png'
        : ((isDev2OrNewer(snCode)
            ? 'assets/images/vr_glass2.png'
            : 'assets/images/vr_glass.png'));
  }

  static AssetImage devAssetImage(String snCode) {
    return AssetImage(isDev3(snCode)
        ? 'assets/images/vr_glass3.png'
        : (isDev2OrNewer(snCode)
            ? 'assets/images/dev_d2.png'
            : 'assets/images/dev_d1.png'));
  }

  static String devCellIconPath(String snCode) {
    return isDev2OrNewer(snCode)
        ? 'assets/images/dev_cell2.png'
        : 'assets/images/dev_cell.png';
  }

  static Future<bool> isHaveLocalNetworkPrivacy(
      {@required String snCode}) async {
    if (Platform.isIOS) {
      YvrToast.showLoading();
      bool isAllowJump = false;
      Dio dio = Dio();
      dio.options.headers = VrHttp.configHeader(snCode);
      String vrHttpPort =
          StorageManager.localStorage.getItem("vr_http_port") ?? "8089";
      String vrHost = StorageManager.localStorage.getItem("vr_host");
      String host = "http://$vrHost:$vrHttpPort/api/doQueryFiles";
      Map param = {"mimetype": 0, "pages": 1, "nums": 10, "date": 0};
      await dio.post(host, data: param).then((response) {
        Log.d("文件列表数据：${response.data}");
        if (response.data["errcode"] == "0") {
          isAllowJump = true;
        } else {
          isAllowJump = false;
        }
      }).catchError((error) {
        Log.d("文件列表错误提醒：$error");
        isAllowJump = false;
      });

      bool isHaveShow =
          DBUtil.instance.projectConfigBox.get("kSystemShowLocalNetwork") ??
              false;
      if (!isAllowJump) {
        // 系统弹框已显示 被拒绝后显示自定义弹框
        if (isHaveShow) {
          YvrToast.dismiss();
          showDialog(
              context: Global.context,
              barrierDismissible: false,
              builder: (_) {
                return CustomDialog(
                  height: 180,
                  title: YLocal.current.dishitishi,
                  content: YLocal.current.qingxiandakaifangwen,
                  confirmCallback: () {
                    openAppSettings();
                  },
                );
              });
        } else {
          // 首次获取网络权限，显示系统弹框
          DBUtil.instance.projectConfigBox.put("kSystemShowLocalNetwork", true);
        }
        YvrToast.dismiss();
        return false;
      }
    }
    YvrToast.dismiss();
    return true;
  }

  static Future<String> getImageAssetsPath({@required String imageName}) async {
    Directory directory = await getTemporaryDirectory();
    var dbPath = "${directory.path}$imageName";
    ByteData data = await rootBundle.load("assets/images/$imageName");
    List<int> bytes =
        data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);
    await File(dbPath).writeAsBytes(bytes);
    return dbPath;
  }

  static Future<String> getVideoAssetsPath({@required String videoName}) async {
    Directory directory = await getTemporaryDirectory();
    var dbPath = "${directory.path}$videoName";
    ByteData data = await rootBundle.load("assets/video/$videoName");
    List<int> bytes =
        data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);
    await File(dbPath).writeAsBytes(bytes);
    return dbPath;
  }

  static Future<String> imageBytesToFile(
      {@required List<int> byteData, @required String imgName}) async {
    Directory directory = await getTemporaryDirectory();
    var dbPath = "${directory.path}$imgName";
    await File(dbPath).writeAsBytes(byteData);
    return dbPath;
  }

  static Future<String> byteDataToFile(
      {@required ByteData byteData, @required String imgName}) async {
    Directory directory = Platform.isIOS
        ? await getTemporaryDirectory()
        : await getExternalStorageDirectory();
    var dbPath = "${directory.path}/$imgName.JPG";
    await File(dbPath).writeAsBytes(byteData.buffer
        .asUint8List(byteData.offsetInBytes, byteData.lengthInBytes));
    return dbPath;
  }
}
