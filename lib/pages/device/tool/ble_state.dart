/// 蓝牙数据通讯说明:
/// 第一字节消息类型即 BleTransmissionStatus；
/// 第二字节0表示不分包，分包的话可以用来表示总包数，最小值为2。
/// 第三个字节 表示包的位置1起始值 （两个包分别为 1 、2），
/// 第四个字节开始为数据，每一个分包被收到后，VR端会反馈字节值 -1。
/// 字符串转成byte数组，按照17个字节分段；
enum BleTransmissionStatus {
  /// 0: 占位值
  BLE_None,

  /// 1: 助手请添加设备
  REQ_ADD_DEV_STATUS,

  /// 2: 回复手机助手添加设备状态 1：同意 0：拒绝
  RET_ADD_DEV_STATUS,

  /// 3: 助手请求获取设备网络状态
  REQ_NETWORK,

  /// 4: 回复手机助手设备网络状态 0：不连通 1：联通  2：同一个WiFi 3：VR未打开WiFi
  RET_NETWORK,

  /// 5: 助手请求获取设备登录状态
  REQ_LOGIN,

  /// 6: 回复助手设备登录状态 actid：用户号 0：没有登录
  RET_LOGIN,

  /// 7: 助手发送WiFi ssid
  REQ_CONNECT_WIFI_SSID,

  /// 8: 回复助手收到ssid  1：收到ssid
  RET_CONNECT_WIFI_SSID,

  /// 9: 助手发送pwd
  REQ_CONNECT_WIFI_PWD,

  /// 10: 回复助手收到pwd 0:联wifi失败，1：联wifi成功
  RET_CONNECT_WIFI_PWD,

  /// 11: 助手发送账户
  REQ_ACCOUNT_STATUS,

  /// 12：回复助手收到账户 1：成功 0：失败
  RET_ACCOUNT_STATUS,

  /// 13：助手发送账户密码
  REQ_ACCOUNT_PWD_STATUS,

  /// 14：回复助手是否登录成功 1：成功 0：失败
  RET_ACCOUNT_PWD_STATUS,

  /// 15：助手发送停止投屏
  REQ_STOP_SCREENCAST_STATUS,

  /// 16：助手停止投屏 1：成功 0：失败
  RET_STOP_SCREENCAST_STATUS,

  /// 17： 助手请求读电量百分比值
  REQ_READ_BATTERY_LEVEL_STATUS,

  /// 18： 回复助手电量百分比值
  RET_READ_BATTERY_LEVEL_STATUS,

  /// 19： 助手请求读取设备蓝牙名称
  REQ_READ_BT_NAME_STATUS,

  /// 20： 回复助手设备蓝牙名称
  RET_READ_BT_NAME_STATUS,

  /// 21： 助手发送手机型号
  REQ_WRITE_PHONE_TYPE_STATUS,

  /// 22： 回复助手收到手机型号
  RET_WRITE_PHONE_TYPE_STATUS,

  /// 23： 助手请求修改蓝牙名称
  REQ_FIX_BT_NAME_STATUS,

  /// 24： 回复助手修改蓝牙名称完成
  RET_FIX_BT_NAME_STATUS,

  /// 25： 助手请求投屏
  REQ_START_PUBLISH_FROM_MOBILE_STATUS,

  /// 26： 回复助手投屏结果
  RET_START_PUBLISH_FROM_MOBILE_STATUS,

  /// 27： 回复助手设备电量
  RET_BATTERY_LEVEL_STATUS,

  ///  28： 回复助手退出登录
  RET_LOG_OUT_STATUS,

  /// 29： VR端发起投屏
  RET_VR_START_PUBLISH_STATUS,

  ///  30： 助手请求设备ip
  REQ_VR_IP_STATUS,

  /// 31： 回复助手设备ip
  RET_VR_IP_STATUS,

  /// 32： 助手请求局域网投屏
  REQ_WLAN_CAST_STATUS,

  ///  33： 回复助手局域网投屏结果
  RET_WLAN_CAST_STATUS,

  /// 34： 助手请求停止局域网投屏
  REQ_STOP_WLAN_CAST_STATUS,

  ///  35： 回复助手停止局域网投屏结果
  RET_STOP_WLAN_CAST_STATUS,

  ///  36： vr发起局域网投屏停止或结束，通知助手值字符串为1开始，否则为结束0
  RET_SEND_VR_WLAN_STATUS,

  ///  37： 回复助手设备网络发生变化 设备连接wifi时发送ssid,分包；断开wifi发送
  RET_WIFI_CHANGED_STATUS,

  ///  38： 手机助手主动告知是否在同一wifi网络中 在同网络值为1，否则为0
  REQ_WIFI_CHANGED_STATUS,

  /// 39：助手请求开始同屏录中的投屏
  REQ_START_RECORDER_AND_SHARE,

  /// 40：回复助手开始同屏录结果
  RET_START_RECORDER_AND_SHARE,

  /// 41：助手请求停止同屏录中的录屏 字符串参数 0:同屏录流程完成，1:流程未完成时停止 VR无需保存
  REQ_STOP_RECORDER_AND_SHARE,

  /// 42：回复助手停止同屏录
  RET_STOP_RECORDER_AND_SHARE,

  /// 43：助手端请求同屏录开始录制视频文件 字符串参数 1:VR为主，2:人像为主 3:横屏
  REQ_RECORDER_AND_SHARE_WRITE_MP4,

  /// 44：VR端请求同屏录开始录制视频文件
  RET_START_RECORDER_AND_SHARE_FROM_DEV,

  /// 45：VR端端请求停止同屏录
  RET_STOP_RECORDER_AND_SHARE_FROM_DEV,

  /// 46：手机助手发送切后台事件：携带手机型号
  REQ_PAUSE_APP_IOS,

  /// 47：手机助手请求打开应用
  REQ_START_APP,

  /// 48：回复手机助手打开应用结果
  /// success:成功
  /// failed_0:青少年模式
  /// failed_1:应用未安装
  /// failed_2:后台已经运行了一个VR应用
  /// failed_3:安全边界打开中
  RET_START_APP,

  /// 49：手机助手请求 是否可以启动mrc
  REQ_START_MRC,

  /// 50：回复手机助手 是否可以启动mrc 0:可以启动 1:禁止启动(MRC服务端已连接了另一台设备) 2:请求超时，可能MRC服务端挂掉
  RET_START_MRC,

  /// 51：回复手机助手，开始录制视频文件 1: 表示为视频分辨率已设置 否则为固定 1200：900
  RET_START_RECORD,

  /// 52：手机助手请求http端口号
  REQ_GET_INFO,

  /// 53：回复手机助手http端口号
  RET_GET_INFO,

  // --遥控拍摄新增-- 54-63

  /// 54: 手机助手请求 start preview
  REQ_START_PREVIEW,

  /// 55: 回复手机助手 start preview
  RET_START_PREVIEW,

  /// 56: 手机助手请求stop preview
  REQ_STOP_PREVIEW,

  /// 57: 回复手机助手，stop preview
  RET_STOP_PREVIEW,

  /// 58: 手机助手请求拍照
  REQ_3D_TAKE_PHOTH,

  /// 59: 回复手机助手，开始拍照
  RET_3D_TAKE_PHOTH,

  /// 60: 手机助手请求开始摄像
  REQ_3D_START_RECORD_VIDEO,

  /// 61: 回复手机助手，开始摄像
  RET_3D_START_RECORD_VIDEO,

  /// 62: 手机助手请求停止摄像
  REQ_3D_STOP_RECORD_VIDEO,

  /// 63: 回复手机助手，停止摄像
  RET_3D_STOP_RECORD_VIDEO,

  /// 64: 手机助手回复视频合成完成
  RET_VIDEO_MERGED,
}
