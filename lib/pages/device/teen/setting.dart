import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'dart:ui' as ui show PlaceholderAlignment;
import '../../../generated/l10n.dart';
import '../../../manager/event_bus.dart';
import '../../../manager/events_bus2.dart';
import '../../../model/teen_model.dart';
import '../../../public_ui/widget/dialogs.dart';
import '../../../public_ui/widget/period_time_selector.dart';
import '../../../public_ui/widget/refresh_layout.dart';
import '../../../styles/app_color.dart';
import '../../../view_model/teen_vmodel.dart';
import '../../tabbar/top_navbar.dart';
import 'package:dotted_border/dotted_border.dart';

class TeenSettingPage extends StatefulWidget {
  final arguments;

  @override
  State<StatefulWidget> createState() {
    return _TeenSettingPageState();
  }

  const TeenSettingPage({Key key, @required this.arguments}) : super(key: key);
}

class _TeenSettingPageState extends State<TeenSettingPage> {
  String devId;
  String passwd;
  TeenVModel teenVModel;
  StreamSubscription<EventData> teenEventBusFn;

  @override
  void initState() {
    super.initState();
    final String devId = widget.arguments["devId"];
    final String passwd = widget.arguments["passwd"];
    teenVModel = TeenVModel(devId, passwd, onModeDirty: () {
      AppEvent.fireTeenModeChangeEvent(
          AppEvent.EVENT_SUB_TEEN_MODE_DIRTY, devId);
    }, onDevDirty: () {
      AppEvent.fireTeenModeChangeEvent(
          AppEvent.EVENT_SUB_TEEN_DEV_DIRTY, devId);
    }, onPasswdDirty: () {
      Navigator.of(context).pop();
    }, onTeenDisabled: () {
      AppEvent.fireTeenModeChangeEvent(
          AppEvent.EVENT_SUB_TEEN_MODE_DISABLED, devId);
    });

    teenEventBusFn = eventBus.on<EventData>().listen((event) {
      switch (event.type) {
        case AppEvent.EVENT_TEEN_MODE_CHANGED:
          switch (event.subType) {
            case AppEvent.EVENT_SUB_TEEN_DEV_DIRTY:
              Navigator.of(context).pop();
              break;
            case AppEvent.EVENT_SUB_TEEN_MODE_DIRTY:
              Navigator.of(context).pop();
              break;
            case AppEvent.EVENT_SUB_TEEN_MODE_DISABLED:
              Navigator.of(context).pop();
              break;
          }
          break;
        case AppEvent.EVENT_TEEN_MODE_APPLY:
          TeenModeModel teen = event.dataAs<TeenModeModel>();
          teenVModel.applyModel(teen);
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TopNavbar(title: YLocal.of(context).shijiansuoshezhi),
      backgroundColor: AppColors.secondaryBg,
      body: ChangeNotifierProvider<TeenVModel>.value(
        value: teenVModel,
        builder: (context, child) {
          return SimpleRefreshList<TeenVModel>(
            model: teenVModel,
            builder: (BuildContext context, model) {
              return SingleChildScrollView(
                padding: EdgeInsets.only(
                  top: 16,
                  left: 18,
                  right: 18,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    buildWeekdayTime(context, model),
                    SizedBox(
                      height: 36,
                    ),
                    buildWeekendTime(context, model),
                    SizedBox(
                      height: 120,
                    ),
                    Text(
                      YLocal.of(context).feishiyongshiduanVRy,
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                        height: 1.42857,
                        color: AppColors.textWeak,
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget buildWeekdayTime(
    BuildContext context,
    TeenVModel model,
  ) {
    return buildTime(
      model,
      YLocal.of(context).zhouyizhizhouwu,
      model.weekdayTimes,
      onAdd: (values) {
        if (!model.hasFreeWeekdayTime) {
          YvrToast.showToast(YLocal.of(context).meiyoukexuanshijiand);
          return;
        }
        Dialogs.showPeriodTimeSelectorDialog(context, values).then((value) {
          if (value != null) {
            model.updateWeekdayTime(value);
          }
        });
      },
      onRemove: (PeriodTime value) {
        model.removeWeekday(value);
      },
      onEdit: (List<PeriodTime> times, int index) {
        Dialogs.showPeriodTimeSelectorDialog(context, times, editIndex: index)
            .then((value) {
          if (value != null) {
            model.updateWeekdayTime(value);
          }
        });
      },
    );
  }

  Widget buildWeekendTime(
    BuildContext context,
    TeenVModel model,
  ) {
    return buildTime(
      model,
      YLocal.of(context).zhouliuzhizhourizhou,
      model.weekendTimes,
      onAdd: (values) {
        if (!model.hasFreeWeekendTime) {
          YvrToast.showToast(YLocal.of(context).meiyoukexuanshijiand);
          return;
        }
        Dialogs.showPeriodTimeSelectorDialog(context, values).then((value) {
          if (value != null) {
            model.updateWeekendTime(value);
          }
        });
      },
      onRemove: (PeriodTime value) {
        model.removeWeekend(value);
      },
      onEdit: (List<PeriodTime> times, int index) {
        Dialogs.showPeriodTimeSelectorDialog(context, times, editIndex: index)
            .then((value) {
          if (value != null) {
            model.updateWeekendTime(value);
          }
        });
      },
    );
  }

  static const int kMaxTimeCount = 3;

  Widget buildTime(
    TeenVModel model,
    String title,
    List<PeriodTime> times, {
    @required ValueChanged<List<PeriodTime>> onAdd,
    @required ValueChanged<PeriodTime> onRemove,
    @required void Function(List<PeriodTime> times, int index) onEdit,
  }) {
    return Column(
      children: [
        Row(
          children: [
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 16,
                height: 1.40625,
                color: AppColors.textSub,
              ),
            ),
            SizedBox(
              width: 8,
            ),
            Text(
              YLocal.of(context).keshiyongshiduan,
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 16,
                height: 1.40625,
                color: AppColors.textWeak,
              ),
            ),
          ],
        ),
        Column(
          children: List<Widget>.generate(times.length, (index) {
            var e = times[index];
            return Container(
              width: double.infinity,
              margin: EdgeInsets.only(top: 12),
              padding:
                  EdgeInsets.only(left: 16, right: 14, top: 16, bottom: 16),
              decoration: BoxDecoration(
                color: Color(0xffFFFFFF),
                borderRadius: BorderRadius.circular(14),
              ),
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  onEdit?.call(times, index);
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    RichText(
                      text: TextSpan(
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 20,
                          height: 1,
                          letterSpacing: 1,
                          color: AppColors.textTitle,
                        ),
                        children: [
                          TextSpan(text: "${e.start}:00"),
                          WidgetSpan(
                            alignment: ui.PlaceholderAlignment.middle,
                            child: Container(
                              width: 20,
                              height: 2,
                              color: Color(0xff2C2E33),
                            ),
                          ),
                          TextSpan(text: "${e.end}:00"),
                        ],
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        onRemove.call(e);
                      },
                      behavior: HitTestBehavior.opaque,
                      child: Container(
                        alignment: AlignmentDirectional.center,
                        width: 26,
                        height: 26,
                        child: SvgPicture.asset(
                          "assets/svg/ic_close.svg",
                          width: 14,
                          height: 14,
                          color: Color(0xffAFB6CC),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }),
        ),
        if ((times?.length ?? 0) < kMaxTimeCount)
          GestureDetector(
            onTap: () {
              onAdd.call(times);
            },
            child: Container(
              margin: EdgeInsets.only(top: 12),
              width: double.infinity,
              child: DottedBorder(
                dashPattern: [3, 3],
                borderType: BorderType.RRect,
                radius: Radius.circular(14),
                color: Color(0xffAFB6CC),
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 20),
                child: Text(
                  YLocal.of(context).xinzengshijianduan,
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 16,
                    height: 1,
                    letterSpacing: 1,
                    color: AppColors.textWeak,
                  ),
                ),
              ),
            ),
          )
      ],
    );
  }

  @override
  void dispose() {
    super.dispose();

    ///先取消，再发广播,因为这个页面也监听了该apply广播,否则还会收到广播通知。
    teenEventBusFn.cancel();
    if (teenVModel.model != null) {
      AppEvent.fireTeenModeApplyEvent(teenVModel.model);
    }
  }
}
