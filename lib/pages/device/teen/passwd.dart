import 'package:flutter/material.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import '../../../generated/l10n.dart';
import '../../../manager/events_bus2.dart';
import '../../../public_ui/widget/pin_code.dart';
import '../../../styles/app_color.dart';
import '../../tabbar/top_navbar.dart';

class TeenPasswdPage extends StatefulWidget {
  final arguments;

  @override
  State<StatefulWidget> createState() {
    return _TeenPasswdPageState();
  }

  const TeenPasswdPage({Key key, @required this.arguments}) : super(key: key);
}

class _TeenPasswdPageState extends State<TeenPasswdPage> {
  TextEditingController controller;
  String devId;
  String firstInputPasswd;
  bool isRequesting = false;

  @override
  void initState() {
    super.initState();
    controller = TextEditingController();
    devId = widget.arguments["devId"];
  }

  @override
  Widget build(BuildContext context) {
    bool secondary = firstInputPasswd != null;
    return Scaffold(
      appBar: TopNavbar(),
      body: Container(
        width: double.infinity,
        child: SingleChildScrollView(
          padding: EdgeInsets.only(
              left: 16,
              right: 16,
              bottom: MediaQuery.of(context).padding.bottom),
          child: Column(
            children: [
              SizedBox(
                height: 71,
              ),
              Text(
                secondary
                    ? YLocal.of(context).querenmima
                    : YLocal.of(context).shezhimima,
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 30,
                  height: 1.33333,
                  letterSpacing: 1,
                  color: AppColors.textTitle,
                ),
              ),
              SizedBox(
                height: 11,
              ),
              Visibility(
                visible: !secondary,
                maintainSize: true,
                maintainAnimation: true,
                maintainState: true,
                child: Text(
                  YLocal.of(context).cimimaweiguanbiqings,
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 15,
                    height: 1,
                    color: AppColors.textSub,
                  ),
                ),
              ),
              SizedBox(
                height: 48,
              ),
              Container(
                width: 258,
                child: PinCode(
                  onCompleted: onCompleted,
                  controller: controller,
                  autoDismissKeyboard: false,
                  autoFocus: true,
                  autoUnfocus: false,
                  readOnly: isRequesting,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void onCompleted(String text) {
    if (text.isEmpty) {
      return;
    }
    if (firstInputPasswd == null) {
      controller.clear();
      setState(() {
        firstInputPasswd = text;
      });
    } else if (text != firstInputPasswd) {
      YvrToast.showToast(
        YLocal.of(context).yudiyicishurudemimab,
      );
      controller.clear();
    } else {
      setPasswd(text);
    }
  }

  void setPasswd(String passwd) {
    if (isRequesting) {
      return;
    }
    isRequesting = true;
    setState(() {});
    //由于设置了PinCodeTextField的enable属性，所以如果enable为false时，会自动隐藏输入法
    YvrRequests.openTeenagerMode(passwd, devId).then((value) async {
      if (checkAndShowToast(value.errCode)) {
        Navigator.of(context).pop(1);
      } else {
        switch (value.errCode) {
          case 10035:
            AppEvent.fireTeenModeChangeEvent(
                AppEvent.EVENT_SUB_TEEN_DEV_DIRTY, devId);
            Navigator.of(context).pop(-1);
            break;
        }
      }
    }).catchError((e, s) {
      YvrToast.showExceptionMessage(e);
    }).whenComplete(() {
      setState(() {
        isRequesting = false;
      });
    });
  }

  @override
  void dispose() {
    super.dispose();
  }
}
