import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:yvr_assistant/styles/app_divider.dart';
import '../../../generated/l10n.dart';
import '../../../manager/event_bus.dart';
import '../../../manager/events_bus2.dart';
import '../../../model/teen_model.dart';
import '../../../public_ui/widget/dialogs.dart';
import '../../../public_ui/widget/refresh_layout.dart';
import '../../../styles/app_color.dart';
import '../../../view_model/teen_vmodel.dart';
import '../../tabbar/top_navbar.dart';

class TeenMenuPage extends StatefulWidget {
  final arguments;

  @override
  State<StatefulWidget> createState() {
    return _TeenMenuPageState();
  }

  const TeenMenuPage({Key key, @required this.arguments}) : super(key: key);
}

class _TeenMenuPageState extends State<TeenMenuPage> {
  String devId;
  String passwd;
  TeenVModel teenVModel;
  StreamSubscription<EventData> teenEventBusFn;

  @override
  void initState() {
    super.initState();
    devId = widget.arguments["devId"];
    passwd = widget.arguments["passwd"];
    teenVModel = TeenVModel(devId, passwd, onModeDirty: () {
      AppEvent.fireTeenModeChangeEvent(
          AppEvent.EVENT_SUB_TEEN_MODE_DIRTY, devId);
    }, onDevDirty: () {
      AppEvent.fireTeenModeChangeEvent(
          AppEvent.EVENT_SUB_TEEN_DEV_DIRTY, devId);
    }, onPasswdDirty: () {
      Navigator.of(context).pop();
    }, onTeenDisabled: () {
      AppEvent.fireTeenModeChangeEvent(
          AppEvent.EVENT_SUB_TEEN_MODE_DISABLED, devId);
    });

    teenEventBusFn = eventBus.on<EventData>().listen((event) {
      switch (event.type) {
        case AppEvent.EVENT_TEEN_MODE_CHANGED:
          switch (event.subType) {
            case AppEvent.EVENT_SUB_TEEN_DEV_DIRTY:
              Navigator.of(context).pop();
              break;
            case AppEvent.EVENT_SUB_TEEN_MODE_DIRTY:
              Navigator.of(context).pop();
              break;
            case AppEvent.EVENT_SUB_TEEN_MODE_DISABLED:
              teenVModel.setOpen(false);
              break;
          }
          break;
        case AppEvent.EVENT_TEEN_MODE_APPLY:
          TeenModeModel teen = event.dataAs<TeenModeModel>();
          teenVModel.applyModel(teen);
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TopNavbar(title: YLocal.of(context).qingshaonianshouhugo),
      backgroundColor: AppColors.secondaryBg,
      body: ChangeNotifierProvider<TeenVModel>.value(
        value: teenVModel,
        builder: (context, child) {
          return SimpleRefreshList<TeenVModel>(
            model: teenVModel,
            builder: (BuildContext context, model) {
              bool isOpen = model.isOpen;
              return ListView(
                padding: EdgeInsets.symmetric(horizontal: 18, vertical: 16),
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.background,
                      borderRadius: BorderRadius.circular(14),
                    ),
                    padding: EdgeInsets.only(top: 16, bottom: 22),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 12),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: EdgeInsets.all(7),
                                    decoration: BoxDecoration(
                                      color: Color(0xffF5F7FA),
                                      shape: BoxShape.circle,
                                    ),
                                    child: SvgPicture.asset(
                                      "assets/svg/teen_menu_switch.svg",
                                      width: 20,
                                      height: 20,
                                      color: Color(0xff6E7380),
                                    ),
                                  ),
                                  SizedBox(
                                    width: 12,
                                  ),
                                  Text(
                                    YLocal.of(context).qingshaonianmoshiqin,
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      fontSize: 16,
                                      height: 1,
                                      letterSpacing: 0,
                                      color: AppColors.textTitle,
                                    ),
                                  ),
                                ],
                              ),
                              CupertinoSwitch(
                                value: isOpen,
                                onChanged: (value) {
                                  onChanged(model);
                                },
                                activeColor: Color(0xff4F7FFE),
                                trackColor: Color(0xffAFB6CC),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(top: 16, bottom: 22),
                          child: AppDivider.backgroundDivider,
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 12),
                          child: Text(
                            YLocal.of(context).kongzhishichangshouh,
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: 12,
                              height: 1,
                              color: Color(0xff969697),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                  if (isOpen)
                    Container(
                      decoration: BoxDecoration(
                        color: AppColors.background,
                        borderRadius: BorderRadius.circular(14),
                      ),
                      padding: EdgeInsets.only(top: 16, bottom: 22),
                      margin: EdgeInsets.only(top: 12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(left: 12, right: 24),
                            child: GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: () async {
                                await Navigator.pushNamed(
                                    context, '/teen_setting', arguments: {
                                  "devId": devId,
                                  "passwd": passwd
                                });
                              },
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: EdgeInsets.all(7),
                                        decoration: BoxDecoration(
                                          color: Color(0xffF5F7FA),
                                          shape: BoxShape.circle,
                                        ),
                                        child: SvgPicture.asset(
                                          "assets/svg/teen_menu_clock.svg",
                                          width: 20,
                                          height: 20,
                                        ),
                                      ),
                                      SizedBox(
                                        width: 12,
                                      ),
                                      Text(
                                        YLocal.of(context).shijiansuoshezhi,
                                        style: TextStyle(
                                          fontWeight: FontWeight.w500,
                                          fontSize: 16,
                                          height: 1,
                                          letterSpacing: 0,
                                          color: AppColors.textTitle,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SvgPicture.asset(
                                    "assets/svg/arrow_right.svg",
                                    color: Color(0xffAFB6CC),
                                    width: 18,
                                    height: 18,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.only(top: 16, bottom: 22),
                            child: AppDivider.backgroundDivider,
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 12),
                            child: Text(
                              YLocal.current.guanlishiyongshijian,
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 12,
                                height: 1,
                                color: Color(0xff969697),
                              ),
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.only(top: 22, bottom: 18),
                            child: AppDivider.backgroundDivider,
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 12),
                            child: Text(
                              YLocal.of(context).jinrikeshiyongshidua,
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 14,
                                height: 1.14286,
                                color: AppColors.textTitle,
                              ),
                            ),
                          ),
                          Container(
                            margin:
                                EdgeInsets.only(left: 12, top: 20, right: 12),
                            child: buildTime(model),
                          )
                        ],
                      ),
                    ),
                ],
              );
            },
          );
        },
      ),
    );
  }

  Widget buildTime(TeenVModel model) {
    if (model.times == null || model.times.isEmpty) {
      return Text(
        YLocal.of(context).mokeshiyongshiduanqi,
        style: TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 14,
          height: 1.28571,
          letterSpacing: 0,
          color: AppColors.warning,
        ),
      );
    }
    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children: List<Widget>.generate(model.times.length, (index) {
        var e = model.times[index];
        bool value = e.value;
        return GestureDetector(
          onTap: () {
            onCurrentTap(model, index);
          },
          child: Container(
            width: 100,
            constraints: BoxConstraints(
              minHeight: 28,
            ),
            decoration: BoxDecoration(
              color: Color(0xffF5F7FA),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (value)
                  Container(
                    margin: EdgeInsets.only(right: 5),
                    child: SvgPicture.asset(
                      "assets/svg/clock.svg",
                      width: 12,
                      height: 12,
                      color: Color(0xff4F7FFE),
                    ),
                  ),
                Text(
                  model.toTimeText(e.key),
                  style: value
                      ? TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                          height: 1.2,
                          color: AppColors.standard,
                        )
                      : TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                          height: 1.2,
                          color: AppColors.textWeak,
                        ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }

  void onCurrentTap(TeenVModel model, int editIndex) {
    Dialogs.showPeriodTimeSelectorDialog(context, model.times2ParsedTime(),
            editIndex: editIndex)
        .then((value) {
      if (value != null) {
        model.updateCurrentTime(value);
      }
    });
  }

  void onChanged(TeenVModel model) {
    model.toggle();
  }

  @override
  void dispose() {
    super.dispose();

    ///先取消，再发广播,因为这个页面也监听了该apply广播,否则还会收到广播通知。
    teenEventBusFn.cancel();
    if (teenVModel.model != null) {
      AppEvent.fireTeenModeApplyEvent(teenVModel.model);
    }
  }
}
