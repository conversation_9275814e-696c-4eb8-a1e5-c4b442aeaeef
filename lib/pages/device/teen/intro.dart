import 'package:flutter/material.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';

import '../../../generated/l10n.dart';
import '../../../styles/app_color.dart';
import '../../tabbar/top_navbar.dart';

class TeenIntroPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _TeenIntroPageState();
  }

  const TeenIntroPage({Key key}) : super(key: key);
}

class _TeenIntroPageState extends State<TeenIntroPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TopNavbar(title: YLocal.of(context).qingshaonianmoshiqin),
      body: SafeArea(
        child: Container(
          width: double.infinity,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.only(
                      left: 16,
                      right: 16,
                      bottom: MediaQuery.of(context).padding.bottom),
                  child: Column(
                    children: [
                      Image.asset(
                        'assets/images/teen_intro.png',
                        width: 274,
                        height: 228,
                      ),
                      SizedBox(
                        height: 36,
                      ),
                      Text(
                        YLocal.of(context).chuliyuanyuzhouzhong,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 26,
                          height: 1.15385,
                          color: AppColors.textTitle,
                        ),
                      ),
                      SizedBox(
                        height: 36,
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Image.asset(
                            "assets/images/ic_teen_intro.png",
                            width: 36,
                            height: 36,
                          ),
                          SizedBox(
                            width: 18,
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                YLocal.of(context).fangchenmikongzhishi,
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 17,
                                  height: 1.29412,
                                  color: AppColors.textTitle,
                                ),
                              ),
                              SizedBox(
                                height: 6,
                              ),
                              Container(
                                width: 256,
                                child: Text(
                                  YLocal.of(context).shezhikeshiyongshidu,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 12,
                                    height: 1.33333,
                                    color: AppColors.textSub,
                                  ),
                                ),
                              )
                            ],
                          )
                        ],
                      )
                    ],
                  ),
                ),
              ),
              Container(
                width: double.infinity,
                margin: EdgeInsets.only(bottom: 12),
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: YvrTextButton(
                  onPressed: () {
                    Navigator.of(context).pop<int>(1);
                  },
                  child: Text(
                    YLocal.of(context).kaiqiqingshaonianmos,
                    style: TextStyle(
                      fontSize: 18,
                    ),
                  ),
                  style: ButtonStyle(
                      fixedSize:
                          ButtonStyleButton.allOrNull(Size.fromHeight(48))),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
