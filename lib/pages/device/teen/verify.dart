import 'dart:async';
import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import '../../../generated/l10n.dart';
import '../../../public_ui/widget/pin_code.dart';
import '../../../styles/app_color.dart';
import '../../tabbar/top_navbar.dart';

class TeenVerifyPage extends StatefulWidget {
  final arguments;

  @override
  State<StatefulWidget> createState() {
    return _TeenVerifyPageState();
  }

  const TeenVerifyPage({Key key, @required this.arguments}) : super(key: key);
}

class _TeenVerifyPageState extends State<TeenVerifyPage> {
  String devId;
  TextEditingController controller;
  FocusNode focusNode;
  StreamController<ErrorAnimationType> errorController;

  @override
  void initState() {
    super.initState();
    devId = widget.arguments["devId"];
    controller = TextEditingController();
    focusNode = FocusNode();
    errorController = StreamController<ErrorAnimationType>();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TopNavbar(),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        child: SingleChildScrollView(
          padding: EdgeInsets.only(
              left: 16,
              right: 16,
              bottom: MediaQuery.of(context).padding.bottom),
          child: Column(
            children: [
              SizedBox(
                height: 71,
              ),
              Text(
                YLocal.of(context).yanzhengguchangshenf,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 30,
                  height: 1.33333,
                  letterSpacing: 1,
                  color: AppColors.textTitle,
                ),
              ),
              SizedBox(
                height: 11,
              ),
              Text(
                YLocal.of(context).qingshurumimayanzhen,
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 15,
                  height: 1,
                  color: AppColors.textSub,
                ),
              ),
              SizedBox(
                height: 48,
              ),
              Container(
                width: 258,
                child: PinCode(
                  onCompleted: onCompleted,
                  controller: controller,
                  autoDismissKeyboard: true,
                  readOnly: isRequesting,
                  errorAnimationController: errorController,
                ),
              ),
              SizedBox(
                height: 59,
              ),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: onResetPasswd,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    vertical: 10,
                  ),
                  child: RichText(
                    text: TextSpan(
                      text: YLocal.of(context).wangjiliao,
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 15,
                        height: 1.06667,
                        color: AppColors.textWeak,
                      ),
                      children: [
                        TextSpan(
                          text: YLocal.of(context).chongzhimimazhongzhi,
                          style: TextStyle(
                            color: AppColors.warning,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void onResetPasswd() {
    Navigator.pushNamed(context, "/teen_reset_passwd",
        arguments: {"devId": devId}).then(
      (passwd) {
        if (passwd != null) {
          Navigator.of(context).pop(passwd as String);
        }
      },
    );
  }

  bool isRequesting = false;

  void onCompleted(String text) {
    if (isRequesting) {
      return;
    }
    isRequesting = true;
    setState(() {});
    YvrRequests.checkTeenagerCode(text, devId).then((value) {
      if (checkAndShowToast(value.errCode)) {
        int status = value.data.status;
        if (status == 1) {
          Navigator.of(context).pop(text);
        } else {
          YvrToast.showToast(YLocal.of(context).yanzhengshibai);
          controller.clear();
          errorController.add(ErrorAnimationType.shake);
        }
      }
    }).catchError((e, s) {
      YvrToast.showExceptionMessage(e);
    }).whenComplete(() {
      setState(() {
        isRequesting = false;
      });
    });
  }

  @override
  void dispose() {
    errorController.close();
    super.dispose();
  }
}
