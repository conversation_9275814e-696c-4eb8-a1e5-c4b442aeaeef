import 'package:flutter/material.dart';
import 'package:keyboard_avoider/keyboard_avoider.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/styles/app_divider.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';

import '../../../generated/l10n.dart';
import '../../../manager/events_bus2.dart';
import '../../../network/http.dart';
import '../../../public_ui/widget/toast_show.dart';
import '../../../styles/app_color.dart';
import '../../../utils/textInput_formatter.dart';
import '../../../utils/yvr_utils.dart';
import '../../../view_model/user_vmodel.dart';
import '../../login/views/code_button.dart';
import '../../login/views/input_field.dart';
import '../../login/views/login_btn.dart';
import '../../tabbar/top_navbar.dart';

class TeenResetPasswdPage extends StatefulWidget {
  final arguments;

  @override
  State<StatefulWidget> createState() {
    return _TeenResetPasswdPageState();
  }

  const TeenResetPasswdPage({Key key, @required this.arguments})
      : super(key: key);
}

class _TeenResetPasswdPageState extends State<TeenResetPasswdPage> {
  var _phoneNum = new TextEditingController(
      text: DBUtil.instance.userBox.get(kMobile) ?? "");
  var _verifyCode = new TextEditingController();
  var _password = new TextEditingController();
  var _rePassword = new TextEditingController();
  final GlobalKey<AuthCodeButtonState> authCodeKey = GlobalKey();
  String forCodeBtnPhoneNum = DBUtil.instance.userBox.get(kMobile) ?? "";
  String forLoginBtnVFCode = "";
  String forLoginBtnPwd = "";
  String forLoginBtnNewPwd = "";

  bool _pwdErrorIsShow = false;
  bool _rePwdErrorIsShow = false;

  static const int kPasswdLength = 4;
  String devId;

  @override
  void initState() {
    super.initState();
    devId = widget.arguments["devId"];
  }

  @override
  Widget build(BuildContext context) {
    String _safeNum = (_phoneNum.text.length > 0)
        ? _phoneNum.text.replaceRange(3, 7, "****")
        : "";

    return Scaffold(
        appBar: TopNavbar(
          title: YLocal.of(context).chongzhimimazhongzhi,
        ),
        body: KeyboardAvoider(
            autoScroll: true,
            child: Container(
              margin: EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    YLocal.of(context).profile_verify_phone_num +
                        " " +
                        _safeNum,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 18,
                      color: AppColors.textTitle,
                    ),
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  Container(
                      height: 58,
                      child: Row(
                        children: [
                          Expanded(
                            child: ImputFieldWidget(
                              controller: _verifyCode,
                              maxLength: 6,
                              hintText: YLocal.of(context).qingshuruyanzhengma,
                              updateText: (value) {
                                if (value.length >= 3) {
                                  setState(() {
                                    forLoginBtnVFCode = value;
                                  });
                                }
                              },
                            ),
                          ),
                          SizedBox(
                            width: 8,
                          ),
                          SizedBox(
                            height: 49,
                            child: codeButtonWidget(),
                          ),
                        ],
                      )),
                  AppDivider.backgroundDivider,
                  SizedBox(
                    height: 60,
                  ),
                  Text(
                    YLocal.of(context).profile_set_password,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 18,
                      letterSpacing: 0,
                      color: AppColors.textTitle,
                    ),
                  ),
                  SizedBox(
                    height: 22,
                  ),
                  ImputFieldWidget(
                    controller: _password,
                    hintText: YLocal.of(context).profile_new_password,
                    keyboardType: TextInputType.number,
                    inputFormatters: [NumberFormatter()],
                    maxLength: kPasswdLength,
                    hasLock: true,
                    updateText: (value) {
                      if (value.length >= kPasswdLength) {
                        setState(() {
                          forLoginBtnPwd = value;
                        });
                      }
                    },
                  ),
                  AppDivider.backgroundDivider,
                  Container(
                    padding: EdgeInsets.only(left: 0, top: 9),
                    alignment: Alignment(-1, -1),
                    child: (_pwdErrorIsShow)
                        ? Text(
                            YLocal.of(context)
                                .qingshuruNweishuzi(kPasswdLength),
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: 12,
                              height: 1,
                              color: AppColors.warning,
                            ),
                          )
                        : SizedBox(),
                  ),
                  SizedBox(
                    height: 8,
                  ),
                  ImputFieldWidget(
                    controller: _rePassword,
                    hintText: YLocal.of(context).profile_confirm_password,
                    keyboardType: TextInputType.number,
                    inputFormatters: [NumberFormatter()],
                    maxLength: kPasswdLength,
                    hasLock: true,
                    updateText: (value) {
                      if (value.length >= kPasswdLength) {
                        setState(() {
                          forLoginBtnNewPwd = value;
                        });
                      }
                    },
                  ),
                  AppDivider.backgroundDivider,
                  Container(
                    padding: EdgeInsets.only(left: 0, top: 9),
                    alignment: Alignment(-1, -1),
                    height: 25,
                    child: (_rePwdErrorIsShow)
                        ? Text(
                            YLocal.of(context).mimabuyizhiqingheshi,
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: 12,
                              color: AppColors.warning,
                            ),
                          )
                        : SizedBox(
                            height: 25,
                          ),
                  ),
                  SizedBox(
                    height: 44,
                  ),
                  loginButtonWidget()
                ],
              ),
            )));
  }

  Widget codeButtonWidget() {
    return AuthCodeButton(
        key: authCodeKey,
        timeCount: 60,
        isActive: YvrUtils.isPhoneNum(forCodeBtnPhoneNum),
        onPressed: () {
          if (!YvrUtils.isPhoneNum(_phoneNum.text)) {
            YvrToast.showToast(YLocal.of(context).qingshuruyouxiaoshou);
            return;
          }
          authCodeKey.currentState.startAction();
          YvrToast.showLoading();
          http.post<Map>('vrmcsys/account/reqSmscode', data: {
            'mobile': _phoneNum.text,
          }).then((response) {
            YvrToast.showToast(YLocal.of(context).vfcode_send);
          }).catchError((error) {
            YvrToast.showError(YLocal.of(context).get_vfcode_failed);
          });
        });
  }

  Widget loginButtonWidget() {
    return LoginButtonWidget(
      buttonText: YLocal.of(context).confirm,
      isCustomBtn: true,
      isActive: YvrUtils.isPhoneNum(forCodeBtnPhoneNum) &&
          (forLoginBtnVFCode.length >= 4) &&
          (forLoginBtnPwd.length == kPasswdLength),
      onPressed: () {
        if (!YvrUtils.isPhoneNum(_phoneNum.text)) {
          YvrToast.showToast(YLocal.of(context).qingshuruyouxiaoshou);
          return;
        }
        if (_verifyCode.text.length == 0) {
          YvrToast.showToast(YLocal.of(context).input_verify_code);
          return;
        }
        if (_password.text.length < kPasswdLength) {
          setState(() {
            _pwdErrorIsShow = true;
          });
          return;
        } else {
          setState(() {
            _pwdErrorIsShow = false;
          });
        }
        if (_password.text != _rePassword.text) {
          setState(() {
            _rePwdErrorIsShow = true;
          });
          return;
        } else {
          setState(() {
            _rePwdErrorIsShow = false;
          });
        }

        reset(_password.text, _verifyCode.text);
      },
    );
  }

  void reset(String code, String smsCode) {
    YvrRequests.changeTeenagerCode(devId, code, smsCode).then((value) {
      if (checkAndShowToast(
        value.errCode,
        errorMatcher: {
          10001: YLocal.of(context).zhanghaobucunzai,
          10051: YLocal.of(context).yanzhengmabuzhengque,
        },
      )) {
        Navigator.of(context).pop(code);
      } else {
        switch (value.errCode) {
          case 10035:
            YvrToast.showToast(YLocal.current.gaishebeibucunzai);
            AppEvent.fireTeenModeChangeEvent(
                AppEvent.EVENT_SUB_TEEN_DEV_DIRTY, devId);
            break;
          case 10830:
            YvrToast.showToast(YLocal.of(context).qingxiandakaiqingsha);
            AppEvent.fireTeenModeChangeEvent(
                AppEvent.EVENT_SUB_TEEN_MODE_DIRTY, devId);
            break;
        }
      }
    }).catchError((e, s) {
      YvrToast.showExceptionMessage(e);
    });
  }
}
