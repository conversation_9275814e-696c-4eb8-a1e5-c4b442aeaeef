import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:yvr_assistant/model/hall_friends_model.dart';
import 'package:yvr_assistant/public_ui/helper/automatic_keep_alive_state_builder.dart';
import 'package:yvr_assistant/public_ui/widget/avatar_group.dart';
import 'package:yvr_assistant/public_ui/widget/image_view.dart';
import 'package:yvr_assistant/public_ui/widget/refresh_layout.dart';
import 'package:yvr_assistant/public_ui/widget/rich_text.dart';
import 'package:yvr_assistant/utils/yvr_utils.dart';
import 'package:yvr_assistant/view_model/friends_vmodel.dart';
import '../../../generated/l10n.dart';
import '../../../manager/resource_mananger.dart';
import '../../../provider/view_state_widget.dart';
import '../../../styles/app_color.dart';
import '../../../utils/data_record.dart';
import '../../../view_model/hall_friends_vmodel.dart';

class HallPage extends StatefulWidget {
  const HallPage({Key key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return HallPageState();
  }
}

class HallPageState extends State<HallPage>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  HallFriendsVModel _homeVModel;
  HallSearchFriendsVModel _searchFriendsVModel;
  bool _inSearch = false;
  TextEditingController _searchController = TextEditingController();
  FocusNode _searchFocusNode = FocusNode();
  TabController _tabController;

  static const kSearchVisible = false;

  @override
  void initState() {
    super.initState();
    _homeVModel = HallFriendsVModel();
    _searchFriendsVModel = HallSearchFriendsVModel();
    _searchFocusNode.addListener(onSearchFocusChanged);
    _tabController =
        TabController(length: 2, vsync: this, animationDuration: Duration.zero);
    _searchController.addListener(onSearchTextChanged);
  }

  void onSearchTextChanged() {
    _searchFriendsVModel.loadBySearchKey(_searchController.text.trim());
  }

  void onSearchFocusChanged() {
    if (_searchFocusNode.hasFocus && !_inSearch) {
      setState(() {
        _inSearch = true;
      });
      _tabController.index = 1;
    }
  }

  void refresh() {
    _homeVModel.refreshQuietly();
  }

  @override
  void dispose() {
    _searchController.removeListener(onSearchTextChanged);
    _searchFocusNode.removeListener(onSearchFocusChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Column(
      children: [
        SizedBox(
          height: 20,
        ),
        if (kSearchVisible)
          Container(
            margin: EdgeInsets.only(left: 16, top: 0, right: 0, bottom: 25),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    textAlignVertical: TextAlignVertical.center,
                    focusNode: _searchFocusNode,
                    buildCounter: (
                      BuildContext context, {
                      @required int currentLength,
                      @required int maxLength,
                      @required bool isFocused,
                    }) {
                      return null;
                    },
                    cursorColor: Color(0xffFFFFFF),
                    cursorWidth: 1,
                    cursorHeight: 14,
                    style: TextStyle(
                        fontSize: 14,
                        color: Color(0xffE8E8E8),
                        height: 1.0,
                        letterSpacing: 1.4),
                    decoration: InputDecoration(
                      contentPadding:
                          EdgeInsets.only(top: 8, bottom: 6, right: 15),
                      prefixIcon: Container(
                        margin: EdgeInsets.only(left: 15, right: 12),
                        child: Icon(
                          IconFonts.iconSearch,
                          size: 16,
                          color: Color(0xff808080),
                        ),
                      ),
                      prefixIconConstraints: BoxConstraints(),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.all(
                          Radius.circular(20),
                        ),
                        borderSide: BorderSide.none,
                      ),
                      enabledBorder: OutlineInputBorder(
                        /*边角*/
                        borderRadius: BorderRadius.all(
                          Radius.circular(20),
                        ),
                        borderSide: BorderSide.none,
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.all(
                          Radius.circular(20),
                        ),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Color.fromRGBO(57, 58, 60, 0.3),
                      isDense: true,
                      isCollapsed: true,
                      hintText: YLocal.of(context).sousuozaixianyouhuha,
                      hintStyle: TextStyle(
                        color: Color(0xffB0B2B8),
                        fontSize: 14,
                        height: 1.0,
                      ),
                    ),
                  ),
                ),
                !_inSearch
                    ? SizedBox(
                        width: 16,
                      )
                    : GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          _searchFocusNode.unfocus();
                          _searchController.text = "";
                          _tabController.index = 0;
                          setState(() {
                            _inSearch = false;
                          });
                        },
                        child: Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                          child: Text(
                            YLocal.of(context).quxiao,
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: 16,
                              height: 1,
                              color: Color(0xffe8e8e8),
                            ),
                          ),
                        ),
                      )
              ],
            ),
          ),
        Expanded(
          child: TabBarView(
            physics: NeverScrollableScrollPhysics(),
            children: <Widget>[
              KeepAliveStatefulBuilder(
                builder: (BuildContext context,
                    void Function(void Function()) setState) {
                  return Consumer<FriendVModel>(
                      builder: (context, friendVM, child) {
                    return SimpleRefreshList<HallFriendsVModel>(
                      model: _homeVModel,
                      builder: (context, model) {
                        bool appEmpty = model.model.apps.isEmpty;
                        bool friendsEmpty = model.model.recmds.isEmpty;
                        return CustomScrollView(
                          slivers: [
                            SliverToBoxAdapter(
                              child: appEmpty
                                  ? SizedBox()
                                  : Column(
                                      children: [
                                        Container(
                                          margin: EdgeInsets.symmetric(
                                              horizontal: 16),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                YLocal.of(context)
                                                    .faxiantongxingcuhaoy,
                                                style: TextStyle(
                                                    fontSize: 16,
                                                    height: 1.25,
                                                    color: AppColors.textTitle,
                                                    fontWeight:
                                                        FontWeight.w600),
                                              ),
                                              GestureDetector(
                                                behavior:
                                                    HitTestBehavior.opaque,
                                                onTap: () {
                                                  Navigator.pushNamed(
                                                    context,
                                                    '/friend_app_list',
                                                  );
                                                },
                                                child: Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      vertical: 2),
                                                  child: Text(
                                                    YLocal.of(context)
                                                        .chakanquanbuzhakanqu,
                                                    style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      fontSize: 12,
                                                      height: 1.33333,
                                                      color: AppColors.textSub,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Container(
                                          height: 175,
                                          margin: EdgeInsets.only(top: 15),
                                          child: ListView.builder(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 16),
                                            itemBuilder: (BuildContext context,
                                                int index) {
                                              return buildAppItem(model, index);
                                            },
                                            itemCount: model.model.apps.length,
                                            scrollDirection: Axis.horizontal,
                                          ),
                                        ),
                                        SizedBox(
                                          height: 30,
                                        )
                                      ],
                                    ),
                            ),
                            if (!friendsEmpty)
                              SliverToBoxAdapter(
                                child: Container(
                                  margin: EdgeInsets.only(
                                      top: 5, left: 16, right: 16),
                                  child: Text(
                                    YLocal.of(context).kenengrenshikenengre,
                                    style: TextStyle(
                                      fontSize: 16,
                                      height: 1.25,
                                      color: AppColors.textTitle,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            if (!friendsEmpty)
                              SliverPadding(
                                padding: EdgeInsets.symmetric(horizontal: 11),
                                sliver: SliverList(
                                  delegate: SliverChildBuilderDelegate(
                                    (BuildContext context, int index) {
                                      return buildFriendsItem(
                                          model, index, friendVM);
                                    },
                                    childCount: model.model.recmds.length,
                                  ),
                                ),
                              ),
                          ],
                        );
                      },
                    );
                  });
                },
              ),
              KeepAliveStatefulBuilder(
                builder: (BuildContext context,
                    void Function(void Function()) setState) {
                  return Consumer<FriendVModel>(
                      builder: (context, friendVM, child) {
                    return SimpleRefreshList<HallSearchFriendsVModel>(
                      model: _searchFriendsVModel,
                      builder: (context, model) {
                        if (model.model == null) {
                          ///还没有搜索
                          return SizedBox();
                        }

                        return ListView.separated(
                            padding: EdgeInsets.only(left: 13, right: 15),
                            itemBuilder: (context, index) {
                              return buildSearchFriends(model, index, friendVM);
                            },
                            separatorBuilder: (context, index) {
                              return SizedBox(
                                height: 16,
                              );
                            },
                            itemCount: model.model.users.length);
                      },
                      emptyBuilder: (context, model) {
                        return ViewStateSearchEmptyWidget();
                      },
                    );
                  });
                },
              ),
            ],
            controller: _tabController,
          ),
        ),
      ],
    );
  }

  Widget buildAppItem(HallFriendsVModel model, int index) {
    var app = model.model.apps[index];
    return GestureDetector(
      onTap: () {
        DataRecord().saveData(
          eventId: 'assistant_social_mySquare_findFriendFromApp_0_block_click',
          extraData: {'id': app.id},
        );
        Navigator.pushNamed(
          context,
          '/friend_app_details',
          arguments: {
            "id": app.id,
            "image": app.rcover,
            "name": app.name,
          },
        );
      },
      child: Container(
        width: 200,
        margin: EdgeInsets.only(left: index == 0 ? 0 : 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.000000),
          color: AppColors.secondaryBg,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(6),
          child: Column(
            children: [
              NetworkImageWidget(
                app.rcover,
                width: double.infinity,
                height: 105,
              ),
              Expanded(
                child: Container(
                  margin:
                      EdgeInsets.only(left: 12, top: 16, right: 12, bottom: 14),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        child: Text(
                          app.name,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                              fontSize: 16,
                              height: 1,
                              color: AppColors.textTitle,
                              fontWeight: FontWeight.w600),
                        ),
                      ),
                      Row(
                        children: [
                          Text(
                            YLocal.of(context)
                                .NrencanjiaNrencenjia(app.userNum),
                            style: TextStyle(
                                fontSize: 12,
                                height: 1.16667,
                                color: AppColors.textSub,
                                fontWeight: FontWeight.w500),
                          ),
                          SizedBox(),
                          Container(
                            margin: EdgeInsets.only(left: 10),
                            child: AvatarGroupWidget(
                              avatars: app.avatars ?? [],
                              falseNum: max(
                                  app.userNum - (app.avatars?.length ?? 0), 0),
                            ),
                          ),
                          Spacer(),
                          SvgPicture.asset(
                            "assets/svg/arrow_right.svg",
                            color: AppColors.textSub,
                            width: 10,
                            height: 10,
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget buildFriendsItem(
      HallFriendsVModel model, int index, FriendVModel friendVModel) {
    HallFriendsModel friend = model.model.recmds[index];
    HallFriendsUsingAppModel usingApp = friend.usingApp;
    return InkWell(
      onTap: () {
        DataRecord().saveData(
          eventId: 'assistant_social_mySquare_youMayKnow_avatar_pit_click',
        );
        onAvatarTap(context, friend.actId);
      },
      child: Container(
        margin: EdgeInsets.only(left: 0, right: 13, top: 24, bottom: 6),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              child: OnlineAvatarImageWidget(
                friend.avatar,
                width: 60,
                height: 60,
                sex: friend.sex,
                online: friend.online,
              ),
              margin: EdgeInsets.only(right: 6),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 9,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              friend.nick ?? '',
                              style: TextStyle(
                                fontSize: 16,
                                height: 1.25,
                                fontWeight: FontWeight.w500,
                                color: AppColors.textTitle,
                              ),
                            ),
                            friend.fromNick != null
                                ? Container(
                                    margin: EdgeInsets.only(top: 5),
                                    child: IntlRichText1(
                                      intlTextBuilder: YLocal.of(context)
                                          .SyuSshigongtonghaoyo,
                                      defaultStyle: TextStyle(
                                        fontSize: 12,
                                        height: 1.16667,
                                        color: AppColors.textSub,
                                      ),
                                      param: friend.fromNick,
                                      paramStyle: TextStyle(
                                          color: AppColors.textTitle,
                                          fontWeight: FontWeight.w500),
                                    ),
                                  )
                                : Container(
                                    margin: EdgeInsets.only(top: 5),
                                    child: RichText(
                                      text: TextSpan(
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontSize: 12,
                                          height: 1.16667,
                                          color: AppColors.textSub,
                                        ),
                                        children: [
                                          TextSpan(
                                              text: YvrUtils.getSexText(
                                                  friend.sex)),
                                          WidgetSpan(
                                            child: SizedBox(
                                              width: 10,
                                            ),
                                          ),
                                          TextSpan(text: 'ID:${friend.actId}'),
                                        ],
                                      ),
                                    ),
                                  ),
                          ],
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(left: 12),
                        child: friendVModel.isRequest(friend.actId)
                            ? Text(
                                YLocal.of(context).yifasongqingqiuyifei,
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14,
                                  height: 1.28571,
                                  letterSpacing: 0.5,
                                  color: Color(0xff767880),
                                ),
                              )
                            : GestureDetector(
                                onTap: () {
                                  DataRecord().saveData(
                                    eventId:
                                        'assistant_social_mySquare_youMayKnow_addFriend_pit_click',
                                  );
                                  friendVModel.requestFriend(friend.actId);
                                },
                                child: SvgPicture.asset(
                                  "assets/svg/add_friends.svg",
                                  width: 25,
                                  height: 24,
                                  color: Color(0xff6E7380),
                                ),
                              ),
                      ),
                    ],
                  ),
                  if (usingApp != null)
                    GestureDetector(
                      onTap: () {
                        if (friend.actId > 0) {
                          DataRecord().saveData(
                            eventId:
                                'assistant_social_mySquare_youMayKnow_app_pit_click',
                            extraData: {'id': usingApp.appId},
                          );
                          Navigator.pushNamed(context, '/prod', arguments: {
                            "id": usingApp.appId,
                          });
                        }
                      },
                      child: Container(
                        margin: EdgeInsets.only(top: 15),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(7.000000),
                          color: AppColors.backgroundItem,
                        ),
                        padding: EdgeInsets.all(10),
                        child: Row(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(2),
                              child: AppImageWidget(
                                usingApp.scover,
                                width: 40,
                                height: 40,
                              ),
                            ),
                            Expanded(
                              child: Container(
                                margin: EdgeInsets.only(left: 10),
                                child: Text(
                                  YLocal.of(context).zhengzaishiyongyingy(
                                      usingApp.applicationName),
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 14,
                                    height: 1.42857,
                                    letterSpacing: 0,
                                    color: AppColors.textSub,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget buildSearchFriends(
      HallSearchFriendsVModel model, int index, FriendVModel friendVModel) {
    var friend = model.model.users[index];
    return InkWell(
      onTap: () {
        onAvatarTap(context, friend.actId);
      },
      child: Container(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              child: OnlineAvatarImageWidget(
                friend.avatar,
                width: 60,
                height: 60,
                sex: friend.sex,
                online: 1,
              ),
              margin: EdgeInsets.only(right: 5),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 9,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      friend.nick,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 16,
                        height: 1.25,
                        color: Color(0xffe8e8e8),
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(top: 5),
                      child: RichText(
                        text: TextSpan(
                          style: TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 12,
                            height: 1.16667,
                            color: Color(0xffb0b2b8),
                          ),
                          children: [
                            TextSpan(text: YvrUtils.getSexText(friend.sex)),
                            WidgetSpan(
                              child: SizedBox(
                                width: 10,
                              ),
                            ),
                            TextSpan(text: 'ID:${friend.actId}'),
                          ],
                        ),
                      ),
                    ),
                  ],
                )
              ],
            ),
            Spacer(),
            Container(
              margin: EdgeInsets.only(left: 12, top: 22),
              child: friendVModel.isRequest(friend.actId)
                  ? Text(
                      YLocal.of(context).yifasongqingqiuyifei,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                        height: 1.16667,
                        color: AppColors.textSub,
                      ),
                    )
                  : GestureDetector(
                      onTap: () {
                        friendVModel.requestFriend(friend.actId);
                      },
                      child: SvgPicture.asset(
                        "assets/svg/add_friends.svg",
                        width: 25,
                        height: 24,
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
