import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:yvr_assistant/model/Event_square_num_model.dart';
import 'package:yvr_assistant/model/official_event_model.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/network/socket_notify.dart';
import 'package:yvr_assistant/public_ui/helper/automatic_keep_alive_state_builder.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';
import 'package:yvr_assistant/public_ui/widget/refresh_layout.dart';
import 'package:yvr_assistant/utils/date_time_utils.dart';
import '../../../generated/l10n.dart';
import '../../../manager/event_bus.dart';
import '../../../manager/events_bus2.dart';
import '../../../model/event_model.dart';
import '../../../network/socket.dart';
import '../../../network/ws_request.dart';
import '../../../public_ui/widget/dialog_util.dart';
import '../../../public_ui/widget/toast_show.dart';
import '../../../styles/app_color.dart';
import '../../../utils/data_record.dart';
import '../../../view_model/event_vmodel.dart';
import '../views/activity_cell.dart';
import '../views/dev_modal_view.dart';

class EventListPage extends StatefulWidget {
  const EventListPage({Key key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return EventListPageState();
  }
}

class EventListPageState extends State<EventListPage>
    with AutomaticKeepAliveClientMixin, SingleTickerProviderStateMixin
    implements OnEventNotifyListener {
  List<String> kEvents;
  OfficialEventVModel officialEventModel;
  MyEventVModel myEventModel;
  EventSquareVModel eventSquareModel;
  TabController tabController;
  EventSquareNumModel _eventSquareNum;

  StreamSubscription<EventData> eventBusFn;

  static const int kOfficialEventPageIndex = 0;
  static const int kMyEventPageIndex = 1;
  static const int kEventSquarePageIndex = 2;
  static const int kInitialPageIndex = kOfficialEventPageIndex;

  void refresh() {
    _refreshPageIfNeeded();
  }

  @override
  void initState() {
    kEvents = [
      YLocal.current.guanfanghuodong,
      YLocal.current.wodehuodongwodihuodo,
      YLocal.current.huodonganchanghuodon
    ];
    super.initState();
    tabController = TabController(
        initialIndex: kInitialPageIndex, length: kEvents.length, vsync: this);

    _recordPageView(kInitialPageIndex);

    officialEventModel = OfficialEventVModel();
    myEventModel = MyEventVModel();
    eventSquareModel = EventSquareVModel();
    _requestEventSquareNum();
    tabController.addListener(onPageChanged);
    eventBusFn = eventBus.on<EventData>().listen((event) {
      switch (event.type) {
        case AppEvent.EVENT_PERSONAL_EVENT_CHANGED:
          switch (event.subType) {
            case AppEvent.EVENT_SUB_PERSONAL_EVENT_CREATED:
              myEventModel.refreshQuietly();
              _requestEventSquareNum();
              break;
            case AppEvent.EVENT_SUB_PERSONAL_EVENT_DELETE:
              myEventModel.refreshQuietly();
              _requestEventSquareNum();
              break;
            case AppEvent.EVENT_SUB_PERSONAL_EVENT_EDIT:
              myEventModel.refreshQuietly();
              _requestEventSquareNum();
              break;
          }
          break;
        case AppEvent.EVENT_OFFICIAL_EVENT_APPLY:
          EventApplyEventData value = event.dataAs<EventApplyEventData>();
          officialEventModel.applyItemById(
              value.details.eventOfficialInfo.eventOfficialId,
              value.details,
              value.commentNum);
          myEventModel.applyItemById(
              value.details.eventOfficialInfo.eventOfficialId,
              value.details,
              value.commentNum);
          break;
        case AppEvent.EVENT_OFFICIAL_EVENT_CHANGED:
          switch (event.subType) {
            case AppEvent.EVENT_SUB_OFFICIAL_EVENT_DIRTY:
              int eventId = event.dataAs<int>();
              myEventModel.removeById(eventId);
              officialEventModel.removeById(eventId);
              break;
          }
          break;
        case AppEvent.EVENT_EVENT_REFRESH:
          _refreshPageIfNeeded();
          break;
      }
    });
  }

  ///最多10秒钟刷新一次
  static const int kMaxRefreshIntervalMilliseconds = 8 * 1000;

  DateTime _lastRefreshTime;

  void _refreshPageIfNeeded() {
    DateTime nowTime = DateTime.now();
    if (_lastRefreshTime == null ||
        nowTime.difference(_lastRefreshTime).inMilliseconds >=
            kMaxRefreshIntervalMilliseconds) {
      myEventModel.refreshQuietly();
      officialEventModel.refreshQuietly();
      eventSquareModel.refreshQuietly();
      _requestEventSquareNum();
      _lastRefreshTime = nowTime;
    }
  }

  void onPageChanged() {
    if (!tabController.indexIsChanging) {
      setState(() {});
      if (tabController.index == kEventSquarePageIndex ||
          _eventSquareNum == null) {
        _requestEventSquareNum();
      }

      _recordPageView(tabController.index);
    }
  }

  void _recordPageView(int pageIndex) {
    switch (pageIndex) {
      case kOfficialEventPageIndex:
        DataRecord().saveData(
            eventId: "assistant_social_officialActivities_0_0_page_view");
        break;
      case kMyEventPageIndex:
        DataRecord()
            .saveData(eventId: "assistant_social_myActivities_0_0_page_view");
        break;
      case kEventSquarePageIndex:
        DataRecord().saveData(eventId: "assistant_social_square_0_0_page_view");
        break;
      default:
        throw Exception("please invoke DataRecord if added more tabs");
    }
  }

  void _requestEventSquareNum() {
    //获取活动广场的数量
    YvrRequests.getEventSquareNum().then((value) {
      setState(() {
        _eventSquareNum = value;
      });
    });
  }

  @override
  void dispose() {
    _lastRefreshTime = null;
    eventBusFn.cancel();
    eventBusFn = null;
    tabController.removeListener(onPageChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<OfficialEventVModel>.value(
            value: officialEventModel),
        ChangeNotifierProvider<MyEventVModel>.value(value: myEventModel),
        ChangeNotifierProvider<EventSquareVModel>.value(
            value: eventSquareModel),
      ],
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: TabBar(
                    controller: tabController,
                    labelPadding: EdgeInsets.only(right: 12),
                    isScrollable: true,
                    indicator: const BoxDecoration(),
                    padding: EdgeInsets.zero,
                    indicatorWeight: 0,
                    tabs: List<Tab>.generate(kEvents.length, (index) {
                      String e = kEvents[index];
                      if (index == kEventSquarePageIndex &&
                          _eventSquareNum != null) {
                        return Tab(
                          text: '$e (${_eventSquareNum.eventSquareNum})',
                        );
                      }
                      return Tab(
                        text: e,
                      );
                    }),
                    labelColor: AppColors.textTitle,
                    labelStyle: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 18,
                    ),
                    unselectedLabelColor: Color(0xff52555c),
                    unselectedLabelStyle: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                    ),
                  ),
                ),
                Builder(builder: (context) {
                  switch (tabController.index) {
                    case 0:
                      return Consumer<OfficialEventVModel>(
                          builder: (context, value, child) {
                        return _buildPopupMenuButton(
                            value.sortTexts, value.sortIndex, (index) {
                          {
                            value.resetSortIndex(index);
                          }
                        });
                      });
                    case 1:
                      return Consumer<MyEventVModel>(
                          builder: (context, value, child) {
                        return _buildPopupMenuButton(
                            value.sortTexts, value.sortIndex, (index) {
                          {
                            value.resetSortIndex(index);
                          }
                        });
                      });
                    case 2:
                      return Consumer<EventSquareVModel>(
                          builder: (context, value, child) {
                        return _buildPopupMenuButton(
                            value.sortTexts, value.sortIndex, (index) {
                          {
                            value.resetSortIndex(index);
                          }
                        });
                      });
                    default:
                      return SizedBox();
                  }
                }),
              ],
            ),
          ),
          Expanded(
            child: TabBarView(
              controller: tabController,
              children: List.generate(
                kEvents.length,
                (pageIndex) {
                  return KeepAliveStatefulBuilder(
                    builder: (context, setState) {
                      switch (pageIndex) {
                        case kOfficialEventPageIndex:
                          return SimpleRefreshList<OfficialEventVModel>(
                            model: officialEventModel,
                            builder: (context, model) {
                              return ListView.builder(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 4),
                                itemBuilder: (context, index) => buildListItem(
                                    context,
                                    pageIndex,
                                    index,
                                    model.list[index],
                                    model.dateTimeTextList[index],
                                    onOfficialClick: () {
                                  followOfficial(model, index, false, () {
                                    myEventModel.refreshQuietly();
                                  });
                                }, onPersonalClick: (eventStatus, id) {}),
                                itemCount: model.list.length,
                              );
                            },
                          );
                          break;
                        case kMyEventPageIndex:
                          return SimpleRefreshList<MyEventVModel>(
                            model: this.myEventModel,
                            builder: (context, model) {
                              return ListView.builder(
                                padding: EdgeInsets.symmetric(horizontal: 16),
                                itemBuilder: (context, index) => buildListItem(
                                    context,
                                    pageIndex,
                                    index,
                                    model.list[index],
                                    model.dateTimeTextList[index],
                                    onOfficialClick: () {
                                  followOfficial(
                                    model,
                                    index,
                                    true,
                                    () {
                                      officialEventModel.refreshQuietly();
                                    },
                                  );
                                }, onPersonalClick: (status, id) {
                                  handleEvent(status, id);
                                }),
                                itemCount: model.list.length,
                              );
                            },
                          );
                          break;
                        case kEventSquarePageIndex:
                          return SimpleRefreshList<EventSquareVModel>(
                            model: this.eventSquareModel,
                            builder: (context, model) {
                              return ListView.builder(
                                padding: EdgeInsets.symmetric(horizontal: 16),
                                itemBuilder: (context, index) =>
                                    _eventSquareCardList(
                                        model,
                                        model.list[index],
                                        model.dateTimeTextList[index],
                                        pageIndex),
                                itemCount: model.list.length,
                              );
                            },
                          );
                        default:
                          return SizedBox();
                      }
                    },
                  );
                },
              ),
            ),
          )
        ],
      ),
    );
  }

  void followOfficial(
    EventMixinViewModel model,
    int index,
    bool refreshSelfAllList,
    VoidCallback successCallback,
  ) {
    var data = model.list[index];
    if (model.isFollowed(data)) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (_) {
            return CustomDialog(
              title: YLocal.current.dishitishi,
              content: YLocal.of(context).shifouquxiaobaomings,
              height: 180,
              confirmCallback: () {
                model.follow(data, successCallback, refreshSelfAllList);
                return true;
              },
            );
          });
    } else {
      model.follow(data, successCallback, refreshSelfAllList);
    }
  }

  @override
  bool get wantKeepAlive {
    return true;
  }

  Widget buildListItem(BuildContext context, int pageIndex, int index,
      OfficialEventModel data, DateTimeDiffStatusData status,
      {@required VoidCallback onOfficialClick,
      @required void Function(int eventStatus, int id) onPersonalClick}) {
    bool isPersonal = data.eventType == 'personal';

    return GestureDetector(
      onTap: () {
        onItemClicked(pageIndex, data.eventType, data.eventId);
      },
      child: isPersonal
          ? PersonalActivityCell(
              coverImage: data.coverImage,
              eventName: data.eventName,
              organizerName: data.organizerName,
              focusesPortraits: data.focusesPortraits,
              realNum: data.realNum,
              isJoin: data.focusesStatus == null || data.focusesStatus == 'yes',
              organizer: int.parse(data.organizer),
              handleEvent: (eventStatus, id, name) {
                onPersonalClick(eventStatus, id);
              },
              status: status,
              process: status.status == DateTimeDiffStatus.running,
              id: data.eventId,
            )
          : EventListItem(
              data.eventType,
              data.coverImage,
              data.eventName,
              data.organizerName,
              data.focusesPortraits,
              data.falseNum,
              data.realNum,
              data.commentNum,
              buildButton(data, status, onOfficialClick, null),
              status),
    );
  }

  void onPopupMenuPressed(int index, EventMixinViewModel vm) {
    vm.resetSortIndex(index);
  }

  void onItemClicked(int pageIndex, String eventType, int eventId) {
    Map extraData = {
      "activityId": eventId,
      "activityType": eventType,
    };
    debugPrint("pageIndex:$pageIndex,eventType:$eventType,eventId:$eventId");
    switch (pageIndex) {
      case kOfficialEventPageIndex:
        DataRecord().saveData(
          eventId:
              "assistant_social_officialActivities_activities_0_block_click",
          extraData: extraData,
        );
        break;
      case kMyEventPageIndex:
        DataRecord().saveData(
          eventId: "assistant_social_myActivities_activities_0_block_click",
          extraData: extraData,
        );
        break;
      case kEventSquarePageIndex:
        DataRecord().saveData(
          eventId: "assistant_social_square_activities_0_block_click",
          extraData: extraData,
        );
        break;
    }

    if (eventType == 'personal') {
      Navigator.pushNamed(context, "/activity_detl",
          arguments: {"id": eventId});
    } else if (eventType == 'official') {
      Navigator.pushNamed(context, "/event_details",
          arguments: {"id": eventId});
    }
  }

  Widget buildButton(OfficialEventModel data, DateTimeDiffStatusData status,
      VoidCallback onOfficialClick, VoidCallback onPersonalClick) {
    Color color;
    Widget icon;
    Widget text;
    VoidCallback onPressed;
    bool showButton = false;
    if (data.eventType == 'personal') {
      if (data.focusesStatus == null || data.focusesStatus == 'yes') {
        icon = Icon(
          Icons.done,
          color: AppColors.textWeak,
        );
        text = Text(
          YLocal.of(context).yicanjiayicenjiayisa,
          style: TextStyle(color: Color(0xffB0B2B8), fontSize: 14),
        );
        color = AppColors.buttonDisable;
        onPressed = onPersonalClick;
        showButton = true;
      } else if (data.focusesStatus == 'no') {
        icon = Icon(
          Icons.add,
          color: Colors.white,
        );
        text = Text(
          YLocal.of(context).xiangcanjiaxiangcenj,
          style: TextStyle(color: Colors.white, fontSize: 14),
        );
        color = null;
        onPressed = onPersonalClick;
        showButton = true;
      }
    } else if (data.eventType == 'official') {
      if (status.status == DateTimeDiffStatus.expired) {
        color = AppColors.buttonDisable;
        text = Text(
          YLocal.of(context).yijieshu,
          style: TextStyle(color: AppColors.textWeak, fontSize: 14),
        );
        onPressed = null;
        showButton = true;
      } else if (data.focusesStatus == null || data.focusesStatus == 'yes') {
        color = AppColors.buttonDisable;
        text = Text(
          YLocal.of(context).yibaoming,
          style: TextStyle(color: AppColors.textWeak, fontSize: 14),
        );
        onPressed = onOfficialClick;
        showButton = true;
      } else if (data.focusesStatus == 'no') {
        text = Text(
          YLocal.of(context).lijibaoming,
          style: TextStyle(color: Colors.white, fontSize: 14),
        );
        color = null;
        onPressed = onOfficialClick;
        showButton = true;
      }
    }
    return !showButton
        ? SizedBox()
        : IgnorePointer(
            ignoring: onPressed == null,
            child: YvrElevatedButton(
              onPressed: onPressed,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (icon != null)
                    Row(
                      children: [
                        icon,
                        SizedBox(),
                      ],
                    ),
                  text,
                ],
              ),
              width: 95,
              padding: EdgeInsets.symmetric(vertical: 6.5),
              color: color,
            ),
          );
  }

  Widget buildPopupMenuButton(List<String> textList, int selectIndex,
      PopupMenuItemSelected<int> onSelected) {
    return PopupMenuButton<int>(
        padding: EdgeInsets.zero,
        offset: Offset(0, 30),
        onSelected: onSelected,
        elevation: 1,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 4),
          child: Row(
            children: [
              Text(
                textList[selectIndex],
                style: const TextStyle(
                  fontSize: 12,
                  color: const Color(0xff52555C),
                ),
              ),
              SvgPicture.asset(
                "assets/svg/popup_menu_arrow_down.svg",
                width: 16,
                height: 16,
                color: const Color(0xff808080),
              ),
            ],
          ),
        ),
        itemBuilder: (context) {
          return List.generate(textList.length, (index) {
            String e = textList[index];
            return PopupMenuItem<int>(
              value: index,
              height: 30,
              padding: EdgeInsets.symmetric(horizontal: 0, vertical: 10),
              child: Container(
                alignment: AlignmentDirectional.centerStart,
                margin: EdgeInsets.symmetric(horizontal: 30),
                child: Text(
                  e,
                ),
              ),
            );
          });
        });
  }

  Widget _buildPopupMenuButton(
      List<String> sortTexts, int sortIndex, ValueChanged<int> onChanged) {
    return buildPopupMenuButton(sortTexts, sortIndex, (index) {
      onChanged(index);
    });
  }

  Widget _eventSquareCardList(EventSquareVModel vModel, EventModel model,
      DateTimeDiffStatusData status, int pageIndex) {
    return InkWell(
      onTap: () {
        onItemClicked(pageIndex, 'personal', model.id);
      },
      child: PersonalActivityCell(
        coverImage: model.rcover,
        eventName: model.name,
        organizerName: model.cName,
        focusesPortraits: model.focusesPortraits,
        realNum: model.num,
        isJoin: model.isJonin,
        organizer: model.cId,
        handleEvent: (eventStatus, id, name) {
          handleEvent(eventStatus, id);
        },
        status: status,
        process: model.process == 1 ?? false,
        id: model.id,
      ),
    );
  }

  /*
    eventStatus:
        参加正在进行中的活动   1
        参加活动             2
        自己创建活动 无法退出  3
        退出活动             4
  */
  void handleEvent(int eventStatus, int id) {
    switch (eventStatus) {
      case 1:
        showDevices(
            context: context,
            callback: (String devId) {
              // StorageManager.localStorage.setItem("kJumpActivityId", id);
              joinPersonalEvent(id);
            });
        break;
      case 2:
        // StorageManager.localStorage.setItem("kJumpActivityId", id);
        joinPersonalEvent(id);
        break;
      case 3:
        YvrToast.showToast(YLocal.of(context).zijichuangjiandehuod);
        break;
      case 4:
        showDialog(
            context: context,
            barrierDismissible: false,
            builder: (_) {
              return CustomDialog(
                title: YLocal.of(context).social_leave_event,
                content: YLocal.of(context).social_leave_event_desc,
                height: 200,
                isCancel: true,
                confirmText: YLocal.of(context).confirm,
                confirmCallback: () {
                  quitPersonalEvent(id);
                },
              );
            });
        break;
      default:
    }
  }

  void joinPersonalEvent(int eventId) {
    YvrWSRequests.joinEvent(eventId).then((value) {
      ///errorCode为10511时给空字符串，不让其弹出toast，因为需要展示Dialog，避免重复处理。
      if (checkAndShowWSToast(value.errCode, errorMatcher: {
        10760: YLocal.of(context).weizhaodaoxiangguanh,
        10761: YLocal.of(context).huodongyijingguoqi,
        10511: "",
      })) {
        myEventModel.refreshQuietly();
        eventSquareModel.refreshQuietly();
      } else {
        switch (value.errCode) {
          case 10760:
          case 10761:
            myEventModel.refreshQuietly();
            eventSquareModel.refreshQuietly();
            break;
          case 10511:
            showDialog(
                context: context,
                barrierDismissible: false,
                builder: (_) {
                  return CustomDialog(
                    title: YLocal.of(context).xiazaiyingyong,
                    content: YLocal.of(context)
                        .yaocanjiahuodongnixu(value.data.appName),
                    confirmText: YLocal.of(context).quxiazai,
                    confirmCallback: () {
                      Navigator.pushNamed(context, '/prod',
                          arguments: {"id": value.data.appId});
                    },
                  );
                });
            break;
        }
      }
    }).catchError((e, s) {
      YvrToast.showExceptionMessage(e);
    });
  }

  void quitPersonalEvent(int eventId) {
    YvrWSRequests.quitEvent(eventId).then((value) {
      if (checkAndShowWSToast(value.errCode, errorMatcher: {
        10760: YLocal.of(context).weizhaodaoxiangguanh,
        10762: YLocal.of(context).huodongyijingkaishim,
      })) {
        myEventModel.refreshQuietly();
        eventSquareModel.refreshQuietly();
      } else {
        switch (value.errCode) {
          case 10760:
          case 10762:
            myEventModel.refreshQuietly();
            eventSquareModel.refreshQuietly();
            break;
        }
      }
    }).catchError((e, s) {
      YvrToast.showExceptionMessage(e);
    });
  }

  @override
  void onReceiveEventComing(int eventId) {
    myEventModel.refreshQuietly();
    eventSquareModel.refreshQuietly();
  }

  @override
  void onReceiveEventInvited(int eventId) {
    myEventModel.refreshQuietly();
    eventSquareModel.refreshQuietly();
  }

  @override
  void onReceiveEventRemove(int eventId) {
    myEventModel.refreshQuietly();
    eventSquareModel.refreshQuietly();
  }

  @override
  void onReceiveEventToCome(int eventId) {}
}
