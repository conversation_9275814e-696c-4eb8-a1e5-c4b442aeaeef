import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:yvr_assistant/base/bluetooth_messenger_mixin.dart';
import 'package:yvr_assistant/base/multiple_progress_button_state_mixin.dart';
import 'package:yvr_assistant/network/request.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';
import 'package:yvr_assistant/public_ui/widget/refresh_layout.dart';
import 'package:yvr_assistant/view_model/friends_vmodel.dart';

import '../../../generated/l10n.dart';
import '../../../manager/event_bus.dart';
import '../../../manager/global.dart';
import '../../../model/hall_friends_model.dart';
import '../../../model/my_friends_model.dart';
import '../../../public_ui/widget/dialog_util.dart';
import '../../../public_ui/widget/image_view.dart';
import '../../../public_ui/widget/picker_tool.dart';
import '../../../public_ui/widget/toast_show.dart';
import '../../../styles/app_color.dart';
import '../../../utils/yvr_utils.dart';
import '../views/plhd_view.dart';

class FriendsPage extends StatefulWidget {
  const FriendsPage({Key key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return FriendsPageState();
  }
}

class FriendsPageState extends State<FriendsPage>
    with
        AutomaticKeepAliveClientMixin,
        BluetoothMessengerMixin<FriendsPage>,
        MultipleProgressButtonStateMixin<FriendsPage,
            HallFriendsUsingAppModel> {
  FriendVModel _model;
  Map<String, HallFriendsUsingAppModel> _requestLaunchApps =
      Map<String, HallFriendsUsingAppModel>();

  @override
  void initState() {
    super.initState();
    _model = Provider.of<FriendVModel>(context, listen: false);
  }

  @override
  void onEvent(Map event) {
    if (event.containsKey(Global.kLaunchAppResult)) {
      Map result = event[Global.kLaunchAppResult];
      var usingApp = _requestLaunchApps.remove(result['pkg']);
      if (usingApp != null) {
        String appName = usingApp?.applicationName ?? '';

        ///flag:结果
        ///pkg:应用包名
        ///devId:设备id
        ///devName:设备名称
        int flag = result['flag'];
        String devId = result['devId'];
        String devName = result['devName'];
        switch (flag) {
          case 0:
            showSuccessDialog(appName);
            break;
          case 1:
            showTeenDialog(devId);
            break;
          case 2:
            showFailedDialog(YLocal.of(context).anzhuangyingyong,
                YLocal.of(context).SzaiSzhongweian(appName, devName));
            break;
          case 3:
            showFailedDialog(YLocal.of(context).dakaiyingyong,
                YLocal.of(context).kuaiquVRlitiyanSba(appName));
            break;
          case 4:
            showFailedDialog(YLocal.of(context).dakaiyingyong,
                YLocal.of(context).kuaiquVRlitiyanSba(appName));
            break;
          default:
            break;
        }
      }
      progressComplete(usingApp);
    }
  }

  void refresh() {
    _model.refreshQuietly();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Consumer<FriendVModel>(builder: (context, model, child) {
      double itemWidth = MediaQuery.of(context).size.width;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(left: 16, top: 15, bottom: 26),
            child: Text(
              '${YLocal.of(context).wodehaoyouwodihaoyou}（${model.onlineNum}/${model.model?.users?.length ?? 0}）',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
                height: 1.25,
                color: AppColors.textTitle,
              ),
            ),
          ),
          Expanded(
            child: SimpleRefreshList<FriendVModel>(
              model: model,
              builder: (context, model) {
                return ListView.separated(
                    itemBuilder: (context, index) {
                      return buildFriendsItem(model, index, itemWidth);
                    },
                    separatorBuilder: (context, index) {
                      return SizedBox(
                        height: 30,
                      );
                    },
                    itemCount: model.model?.users?.length ?? 0);
              },
              emptyBuilder: (context, model) {
                return PagePlhdWidget(
                    imagePath: 'assets/images/friend_plhd.svg',
                    message: YLocal.of(context).haimeiyouhaoyoukuaiq);
              },
            ),
          )
        ],
      );
    });
  }

  Widget buildFriendsItem(FriendVModel model, int index, double itemWidth) {
    MyFriendsDataUser friend = model.model.users[index];
    HallFriendsUsingAppModel usingApp = friend.usingApp;
    const double paneWidth = 168;
    // return Container(width: itemWidth,height: 50,color: Colors.yellow,);
    return SizedBox(
      width: itemWidth,
      child: Slidable(
        key: ValueKey(friend),
        endActionPane: ActionPane(
          motion: ScrollMotion(),
          extentRatio: paneWidth / itemWidth,
          children: [
            Container(
              height: double.infinity,
              width: paneWidth,
              decoration: BoxDecoration(
                color: Color(0xffF6F6F6),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  GestureDetector(
                    child: Container(
                      child: SvgPicture.asset(
                        "assets/svg/friend_black.svg",
                        width: 40,
                        height: 40,
                      ),
                    ),
                    onTap: () {
                      showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (_) {
                            return CustomDialog(
                              title:
                                  YLocal.of(context).laheiS(friend.nick ?? ''),
                              content: YLocal.of(context).laheiyonghuyiweizhao,
                              height: 240,
                              confirmCallback: () {
                                model.blackFriend(friend.actId);
                              },
                            );
                          });
                    },
                  ),
                  GestureDetector(
                    child: Container(
                      child: SvgPicture.asset(
                        "assets/svg/friend_complain.svg",
                        width: 40,
                        height: 40,
                      ),
                    ),
                    onTap: () {
                      // 增加举报假弹框
                      JhPickerTool.showStringPicker(context, title: '', data: [
                        YLocal.of(context).bushidangnarongzaoch,
                        YLocal.of(context).cizhanghaokenengbeid,
                        YLocal.of(context).cunzaijitaqinquanhan,
                        YLocal.of(context).jitaqita,
                      ], clickCallBack: (int index, var item) {
                        YvrToast.showToast(YLocal.current.ganxienindezhichinwo);
                      });
                    },
                  ),
                  GestureDetector(
                    child: Container(
                      child: SvgPicture.asset(
                        "assets/svg/friend_remove.svg",
                        width: 40,
                        height: 40,
                      ),
                    ),
                    onTap: () {
                      showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (_) {
                            return CustomDialog(
                              title: YLocal.of(context)
                                  .shanchuS(friend.nick ?? ''),
                              content: YLocal.of(context).quedingyaoshanchugai,
                              height: 200,
                              confirmCallback: () {
                                model.removeFriend(friend.actId);
                              },
                            );
                          });
                    },
                  )
                ],
              ),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.only(left: 12, right: 12),
          child: InkWell(
            onTap: () {
              onAvatarTap(context, friend.actId);
            },
            child: Column(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.pushNamed(context, "/person_home",
                            arguments: {"relation": 1, "actId": friend.actId});
                      },
                      child: Container(
                        child: OnlineAvatarImageWidget(
                          friend.avatar,
                          width: 60,
                          height: 60,
                          sex: friend.sex,
                          online: friend.online,
                        ),
                        margin: EdgeInsets.only(right: 6),
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: 9,
                          ),
                          Text(
                            friend.nick,
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 14,
                              height: 1.42857,
                              color: AppColors.textTitle,
                            ),
                          ),
                          SizedBox(
                            height: 5,
                          ),
                          Container(
                            margin: EdgeInsets.only(top: 5),
                            child: RichText(
                              text: TextSpan(
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontSize: 12,
                                  height: 1.16667,
                                  color: AppColors.textSub,
                                ),
                                children: [
                                  TextSpan(
                                      text: YvrUtils.getSexText(friend.sex)),
                                  WidgetSpan(
                                    child: SizedBox(
                                      width: 10,
                                    ),
                                  ),
                                  TextSpan(text: 'ID:${friend.actId}'),
                                ],
                              ),
                            ),
                          ),
                          if (friend.online == 1)
                            GestureDetector(
                              onTap: () {
                                if (usingApp == null) {
                                  return;
                                }
                                if (friend.actId > 0) {
                                  Navigator.pushNamed(context, '/prod',
                                      arguments: {
                                        "id": usingApp.appId,
                                      });
                                }
                              },
                              child: Container(
                                margin: EdgeInsets.only(top: 15),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8.000000),
                                  color: AppColors.backgroundItem,
                                ),
                                padding: EdgeInsets.all(10),
                                child: Row(
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(2),
                                      child: usingApp != null
                                          ? AppImageWidget(
                                              usingApp.scover,
                                              width: 40,
                                              height: 40,
                                            )
                                          : ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(4),
                                              child: SvgPicture.asset(
                                                "assets/images/app_icon.svg",
                                                width: 40,
                                                height: 40,
                                              ),
                                            ),
                                    ),
                                    Expanded(
                                      child: Container(
                                        margin: EdgeInsets.only(left: 10),
                                        child: Text(
                                          usingApp != null
                                              ? YLocal.of(context)
                                                  .zhengzaishiyongyingy(
                                                      usingApp.applicationName)
                                              : YLocal.of(context)
                                                  .zhengzaiYVRdatingzhe,
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 14,
                                            height: 1.42857,
                                            letterSpacing: 0,
                                            color: AppColors.textTitle,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 2,
                                        ),
                                      ),
                                    ),
                                    if (usingApp != null)
                                      ElevatedProgressButton(
                                        onPressed: () {
                                          onJoinApp(usingApp);
                                        },
                                        width: 52,
                                        height: 28,
                                        padding: EdgeInsets.zero,
                                        idleChild: Text(
                                          YLocal.of(context).jiaru,
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontSize: 14,
                                            height: 1,
                                            color: AppColors.background,
                                          ),
                                        ),
                                        state: progressState(usingApp),
                                      ),
                                  ],
                                ),
                              ),
                            ),
                        ],
                      ),
                    )
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void onJoinApp(HallFriendsUsingAppModel usingApp) {
    if (progressSize() > 0) {
      YvrToast.showToast(YLocal.of(context).zhengzaidakaiyingyon);
      return;
    }
    progressExecute(usingApp, computation: () async {
      return (await _checkConnected(usingApp)) ? 1 : null;
    }, onSuccess: (result) {
      _requestLaunchApps[usingApp.pkg] = usingApp;
      sendBleMessage(
        req: Global.kPhoneLaunchApp,
        args: {
          Global.kPhoneLaunchAppPKG: usingApp.pkg,
        },
      );
    }, milliseconds: 10 * 1000);
  }

  @override
  void onTimeout(HallFriendsUsingAppModel e) {
    _requestLaunchApps.remove(e.pkg);
    YvrToast.showToast(YLocal.current.dakaiSchaoshiqingcho(e.applicationName));
  }

  Future<bool> _checkConnected(HallFriendsUsingAppModel usingApp) async {
    var conDev = await connectedDevice();
    if (conDev == null) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (_) {
            return CustomDialog(
              title: YLocal.of(context).lianjieshebei,
              content: YLocal.of(context).nihaimeiyoulianjiesh,
              confirmText: YLocal.of(context).qulianjie,
              confirmCallback: () {
                gotoMainDevicePage();
              },
              contentTextAlignment: Alignment.topCenter,
            );
          });
      return false;
    }
    try {
      int purchased =
          (await YvrRequests.validateAppPurchased(usingApp.appId)).data;
      switch (purchased) {
        case 0:
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (_) {
                return CustomDialog(
                  title: YLocal.current.goumaiyingyong,
                  content: YLocal.of(context).nihaimeiyougaiyingyo,
                  confirmText: YLocal.of(context).qugoumai,
                  confirmCallback: () {
                    Navigator.pushNamed(context, '/prod', arguments: {
                      "id": usingApp.appId,
                    });
                  },
                  contentTextAlignment: Alignment.topCenter,
                );
              });
          return false;
        case 1:
          return true;
        case 2:
          YvrToast.showToast(YLocal.current.gaiyingyongtuikuanzh);
          return false;
      }
    } catch (e) {
      YvrToast.showExceptionMessage(e);
      return false;
    }

    return true;
  }

  void showSuccessDialog(String appName) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) {
          return CustomDialog(
            title: YLocal.of(context).dakaiyingyong,
            content: YLocal.of(context).kuaiquVRlitiyanSba(appName),
            confirmText: YLocal.of(context).zhidaoliao,
            confirmCallback: () {},
            isCancel: false,
            contentTextAlignment: Alignment.topCenter,
          );
        });
  }

  void showTeenDialog(String devId) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) {
          return CustomDialog(
            title: YLocal.of(context).dakaiyingyong,
            content: YLocal.of(context).dangqianshebeichuyuq,
            confirmText: YLocal.of(context).qushezhi,
            confirmCallback: () {
              eventBus.fire(EventFn({Global.kNavigateTeenDevId: devId}));
            },
            contentTextAlignment: Alignment.topCenter,
          );
        });
  }

  void showFailedDialog(String title, String reason) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) {
          return CustomDialog(
            title: title,
            content: reason,
            confirmText: YLocal.of(context).zhidaoliao,
            confirmCallback: () {},
            isCancel: false,
            contentTextAlignment: Alignment.topCenter,
          );
        });
  }

  @override
  bool get wantKeepAlive => true;
}
