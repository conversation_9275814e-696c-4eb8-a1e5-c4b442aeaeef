import 'dart:async';
import 'package:yvr_assistant/base/lifecycle_state_mixin.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/pages/social/pages/friends.dart';
import 'package:yvr_assistant/utils/data_record.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:badges/badges.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';
import 'package:yvr_assistant/view_model/unread_vmodel.dart';
import 'package:yvr_assistant/manager/resource_mananger.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import '../../manager/events_bus2.dart';
import '../../styles/app_color.dart';
import 'pages/events.dart';
import 'pages/hall.dart';

class SocialPage extends StatefulWidget {
  SocialPage({Key key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return SocialPageState();
  }
}

class SocialPageState extends State<SocialPage> {
  String token;
  StreamSubscription<EventData> eventBusFn;

  @override
  void initState() {
    super.initState();
    token = DBUtil.instance.userBox.get(kToken) ?? "";
    eventBusFn = eventBus.on<EventData>().listen((event) {
      switch (event.type) {
        case AppEvent.EVENT_LOGIN_SUCCESS:
          setState(() {
            token = DBUtil.instance.userBox.get(kToken) ?? "";
          });
          break;
      }
    });
  }

  @override
  void dispose() {
    eventBusFn.cancel();
    eventBusFn = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (token.isEmpty) {
      return SizedBox();
    }

    return _SocialPage(
      key: ValueKey<String>(token),
    );
  }
}

class _SocialPage extends StatefulWidget {
  _SocialPage({Key key}) : super(key: key);

  @override
  _SocialPageState createState() => _SocialPageState();
}

class _SocialPageState extends LifecycleState<_SocialPage>
    with AutomaticKeepAliveClientMixin, SingleTickerProviderStateMixin {
  GlobalKey _eventGlobalKey = GlobalKey();
  GlobalKey _hallGlobalKey = GlobalKey();
  GlobalKey _friendGlobalKey = GlobalKey();
  TabController _tabController;

  static const int kEventIndex = 0;
  static const int kHallIndex = 1;
  static const int kFriendIndex = 2;

  List<String> kTabs;

  @override
  bool get wantKeepAlive => true;

  int get tabIdx => _tabController.index;

  @override
  void initState() {
    super.initState();
    kTabs = [
      YLocal.current.huodong,
      YLocal.current.datingdaiting,
      YLocal.current.haoyou
    ];
    _tabController = TabController(
        length: kTabs.length, vsync: this, animationDuration: Duration.zero);
    _tabController.addListener(onPageChanged);
  }

  void onPageChanged() {
    if (!_tabController.indexIsChanging) {
      final int index = _tabController.index;
      String eventId;
      switch (index) {
        case kEventIndex:
          break;
        case kHallIndex:
          eventId = "assistant_social_mySquare_0_0_page_view";
          break;
        case kFriendIndex:
          eventId = "assistant_social_friend_0_0_page_view";
          break;
      }
      if (eventId != null) {
        DataRecord().saveData(eventId: eventId);
      }
      refresh();
      setState(() {});
    }
  }

  @override
  void onRefresh() {
    refresh();
  }

  void refresh() {
    switch (tabIdx) {
      case 0:
        (_eventGlobalKey.currentState as EventListPageState)?.refresh();
        break;
      case 1:
        (_hallGlobalKey.currentState as HallPageState)?.refresh();
        break;
      case 2:
        (_friendGlobalKey.currentState as FriendsPageState)?.refresh();
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      appBar: AppBar(
        titleSpacing: 1,
        centerTitle: false,
        title: TabBar(
          controller: _tabController,
          labelPadding: EdgeInsets.symmetric(horizontal: 15, vertical: 3),
          isScrollable: true,
          padding: EdgeInsets.zero,
          indicatorPadding: EdgeInsets.only(left: 12, right: 12),
          tabs: List<Tab>.generate(
            kTabs.length,
            (index) {
              String e = kTabs[index];
              return Tab(
                text: e,
              );
            },
          ),
        ),
        actions: [
          Container(
            margin: EdgeInsets.only(right: 20),
            child: InkWell(
              child: SvgPicture.asset(
                "assets/svg/add_friends.svg",
                width: 24,
                height: 24,
                color: Color(0xff2C2E33),
              ),
              onTap: () {
                DataRecord().saveData(
                    eventId:
                        'assistant_social_mySquare_topBar_addFriend_pit_click');
                Navigator.pushNamed(context, "/search_friends");
              },
            ),
          ),
          Consumer<UnreadVModel>(builder: (context, value, child) {
            return Container(
              margin: EdgeInsets.only(right: 20),
              child: InkWell(
                child: Badge(
                  animationType: BadgeAnimationType.fade,
                  showBadge: value.unreadNum != 0,
                  badgeContent: Text(
                    value.unreadNum.toString(),
                    style: TextStyle(fontSize: 8, color: Colors.white),
                  ),
                  position: BadgePosition.topEnd(top: 12, end: -3),
                  child: Icon(
                    IconFonts.iconNoti,
                    color: Color(0xff2C2E33),
                    size: 24,
                  ),
                ),
                onTap: () {
                  DataRecord().saveData(
                      eventId:
                          'assistant_social_mySquare_topBar_notification_pit_click');
                  Navigator.pushNamed(context, '/message');
                },
              ),
            );
          }),
        ],
      ),
      floatingActionButton: tabIdx != kEventIndex
          ? null
          : SizedBox(
              height: 50,
              width: 50,
              child: FloatingActionButton(
                foregroundColor: Colors.white,
                backgroundColor: AppColors.standard,
                onPressed: () {
                  if (DBUtil.instance.userBox.get(kActId).toString().isEmpty) {
                    YvrToast.showToast(YLocal.of(context).toast_need_login);
                  } else {
                    if (tabIdx == 0) {
                      Navigator.pushNamed(
                        context,
                        "/activity_create",
                      ).then((value) {
                        if (value != null) {
                          Navigator.pushNamed(
                            context,
                            "/activity_detl",
                            arguments: {
                              "id": value,
                            },
                          );
                        }
                      });
                    } else if (tabIdx == 2) {
                      Navigator.pushNamed(context, "/search_friends");
                    }
                  }
                },
                child: SizedBox(
                    width: 24,
                    child: SvgPicture.asset(tabIdx == 0
                        ? "assets/images/add_activity.svg"
                        : "assets/images/add_friend.svg")),
              ),
            ),
      body: TabBarView(
        controller: _tabController,
        children: [
          EventListPage(key: _eventGlobalKey),
          HallPage(key: _hallGlobalKey),
          FriendsPage(key: _friendGlobalKey),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _tabController.removeListener(onPageChanged);
    super.dispose();
  }
}
