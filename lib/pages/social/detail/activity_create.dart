import 'package:flutter/material.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/pages/social/views/app_select_view.dart';
import 'package:yvr_assistant/pages/social/views/time_view.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/public_ui/widget/feedback_input2.dart';
import 'package:yvr_assistant/public_ui/widget/refresh_layout.dart';
import 'package:yvr_assistant/view_model/personal_event_vmodel.dart';
import '../../../generated/l10n.dart';
import '../../../styles/app_color.dart';
import '../../tabbar/top_navbar.dart';

class ActivityCreatePage extends StatefulWidget {
  final arguments;

  ActivityCreatePage({Key key, this.arguments}) : super(key: key);

  @override
  _ActivityCreatePageState createState() => _ActivityCreatePageState();
}

class _ActivityCreatePageState extends State<ActivityCreatePage> {
  @override
  void dispose() {
    super.dispose();
  }

  PersonalEventVModel _viewModel;

  @override
  void initState() {
    super.initState();
    int id;
    if (widget.arguments != null) {
      id = widget.arguments['id'];
    }
    _viewModel = PersonalEventVModel(editId: id, refreshEnabled: false);
  }

  static const InputBorder _kEditInputBorder = OutlineInputBorder(
    borderRadius: BorderRadius.all(
      Radius.circular(4), //边角为5
    ),
    borderSide: BorderSide.none,
  );

  @override
  Widget build(BuildContext context) {
    return SimpleRefreshList<PersonalEventVModel>(
        model: _viewModel,
        builder: (context, viewModel) {
          final int id = viewModel.model.id;
          final String rcover = viewModel.model.rcover;
          final String scover = viewModel.model.scover;
          return WillPopScope(
            onWillPop: () async {
              if (isShowNavBackRemind(viewModel)) {
                return true;
              }
              Navigator.of(context).pop();
              return false;
            },
            child: Scaffold(
              resizeToAvoidBottomInset: false,
              appBar: TopNavbar(
                  title: viewModel.isEdit
                      ? YLocal.of(context).bianjisirenhuodong
                      : YLocal.of(context).chuangjiansirenhuodo,
                  backCallBack: () {
                    if (isShowNavBackRemind(viewModel)) {
                      return true;
                    }
                    Navigator.of(context).pop();
                    return false;
                  }),
              body: Stack(
                children: [
                  Container(
                    margin: EdgeInsets.only(top: 10),
                    height: Global.screenHeight,
                    child: SingleChildScrollView(
                        child: Padding(
                      padding:
                          EdgeInsets.only(left: 16, right: 16, bottom: 100),
                      child: Column(
                        children: [
                          AppSelectView(
                            isEdit: viewModel.isEdit,
                            id: id,
                            rcover: rcover,
                            scover: scover,
                            appSelectCallback: (id, rcover, scover) {
                              viewModel.updateApp(
                                  id: id, rcover: rcover, scover: scover);
                            },
                          ),
                          Container(
                            margin: EdgeInsets.only(top: 8, bottom: 12),
                            child: TextField(
                              maxLength: 14,
                              controller: viewModel.nameController,
                              style: TextStyle(
                                  fontSize: 14,
                                  color: Color(0xff2C2E33),
                                  letterSpacing: 1.4),
                              decoration: InputDecoration(
                                hintText:
                                    YLocal.of(context).shuruhuodongmingchen,
                                hintStyle: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontSize: 14,
                                  color: AppColors.textWeak,
                                ),
                                contentPadding:
                                    EdgeInsets.symmetric(horizontal: 11),
                                filled: true,
                                fillColor: Color(0xFFF0F2F5),
                                counterText: '',
                                border: _kEditInputBorder,
                                errorBorder: _kEditInputBorder,
                                disabledBorder: _kEditInputBorder,
                                enabledBorder: _kEditInputBorder,
                                focusedBorder: _kEditInputBorder,
                                focusedErrorBorder: _kEditInputBorder,
                              ),
                            ),
                          ),
                          TimeView(
                            isEdit: viewModel.isEdit,
                            duration: viewModel.model.duration ?? 0,
                            defTimeStr: viewModel.model.startTime,
                            timeCallback: (time) {
                              viewModel.updateDateTime(time);
                            },
                            durationCallback: (duration) {
                              viewModel.updateDuration(duration);
                            },
                          ),
                          Container(
                            margin: EdgeInsets.only(top: 12, bottom: 12),
                            child: FeedbackInput2(
                              containerPadding: EdgeInsets.only(
                                  left: 11, top: 14, right: 11, bottom: 36),
                              textFieldPadding: EdgeInsets.zero,
                              controller: viewModel.detlController,
                              hint: YLocal.of(context).huodongshuimingxuant,
                              height: 200,
                              maxLength: 100,
                              fillColor: Color(0xffF0F2F5),
                            ),
                          ),
                          Container(
                            height: 48,
                            margin: EdgeInsets.only(bottom: 10),
                            padding: EdgeInsets.only(left: 11),
                            decoration: BoxDecoration(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(6.0)),
                              color: Color(0xffF0F2F5),
                            ),
                            child: Builder(
                              builder: (context) {
                                return Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      YLocal.of(context).yunxuyijiaruyonghuya,
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: viewModel.isAllowNotifier
                                            ? Color(0xff2C2E33)
                                            : Color(0xffAFB6CC),
                                      ),
                                    ),
                                    Container(
                                      height: 20,
                                      width: 40,
                                      margin: EdgeInsets.only(right: 11),
                                      alignment: Alignment.centerRight,
                                      child: FlutterSwitch(
                                        toggleSize: 14,
                                        padding: 4,
                                        value: viewModel.isAllowNotifier,
                                        toggleColor: Color(0xFFF7F7F8),
                                        activeColor: Color(0xFF177BE6),
                                        inactiveColor: Color(0xFF424245),
                                        onToggle: (value) {
                                          if (!viewModel.isEdit) {
                                            viewModel.updateAuth(value ? 1 : 0);
                                          }
                                        },
                                      ),
                                    )
                                  ],
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    )),
                  ),
                  Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: Container(
                      height: (48 + Global.paddingBottom + 15.0),
                      alignment: Alignment(0, -1),
                      padding: EdgeInsets.only(left: 16, right: 16),
                      child: Flex(
                        direction: Axis.horizontal,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                              flex: 1,
                              child: YvrTextButton(
                                padding: EdgeInsets.zero,
                                height: 48,
                                color: Color(0xffF0F2F5),
                                onPressed: () {
                                  if (!isShowNavBackRemind(viewModel)) {
                                    Navigator.pop(context);
                                  }
                                },
                                child: Text(
                                  YLocal.of(context).quxiao,
                                  style: TextStyle(color: Color(0xFF6E7380)),
                                ),
                              )),
                          SizedBox(width: 10),
                          Expanded(
                            flex: 1,
                            child: Builder(
                              builder: (context) {
                                bool enable = (viewModel.btnStatusNotifier[0] &&
                                    viewModel.btnStatusNotifier[1] &&
                                    viewModel.btnStatusNotifier[2] &&
                                    viewModel.btnStatusNotifier[3]);
                                return YvrTextButton(
                                  height: 48,
                                  color:
                                      enable ? null : AppColors.buttonDisable,
                                  onPressed: () {
                                    viewModel.editOrCreate(
                                        onSuccess: (eventId) {
                                      Navigator.of(context).pop(eventId);
                                    });
                                  },
                                  child: Text(
                                    viewModel.isEdit
                                        ? YLocal.of(context).queding
                                        : YLocal.of(context).chuangjian,
                                    style: TextStyle(
                                      color: enable ? null : Color(0xff6E7380),
                                    ),
                                  ),
                                );
                              },
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        });
  }

  bool isShowNavBackRemind(PersonalEventVModel viewModel) {
    if (viewModel.dataChanged) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (_) {
            return CustomDialog(
              title: YLocal.current.dishitishi,
              content: viewModel.isEdit
                  ? YLocal.of(context).dangqianzhengzaibian
                  : YLocal.of(context).dangqianzhengzaichua,
              height: 180,
              confirmCallback: () {
                Navigator.pop(context);
                return true;
              },
            );
          });
      return true;
    } else {
      return false;
    }
  }
}
