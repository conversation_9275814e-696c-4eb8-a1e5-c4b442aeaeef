import 'dart:math';
import 'package:badges/badges.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:yvr_assistant/public_ui/widget/avatar_group.dart';
import 'package:yvr_assistant/public_ui/widget/rich_text.dart';
import 'package:yvr_assistant/styles/app_color.dart';

import '../../../generated/l10n.dart';
import '../../../manager/resource_mananger.dart';
import '../../../public_ui/widget/image_view.dart';
import '../../../public_ui/widget/refresh_layout.dart';
import '../../../utils/data_record.dart';
import '../../../view_model/hall_friends_vmodel.dart';
import '../../../view_model/unread_vmodel.dart';
import '../../tabbar/top_navbar.dart';

class FriendAppListPage extends StatefulWidget {
  const FriendAppListPage({Key key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _FriendAppListPageState();
  }
}

class _FriendAppListPageState extends State<FriendAppListPage> {
  FindSameAppsVModel _model;

  @override
  void initState() {
    super.initState();
    _model = FindSameAppsVModel();
    DataRecord().saveData(
        eventId: 'assistant_social_findFriendFromAppList_0_0_page_view');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        title: Text(
          YLocal.of(context).faxiantongxingcuhaoy,
        ),
        leading: CustomBackButton(),
        actions: [
          InkWell(
            child: Badge(
              animationType: BadgeAnimationType.fade,
              showBadge: Provider.of<UnreadVModel>(context).unreadNum != 0,
              badgeContent: Text(
                Provider.of<UnreadVModel>(context).unreadNum.toString(),
                style: TextStyle(fontSize: 8, color: Colors.white),
              ),
              position: BadgePosition.topEnd(top: 12, end: -3),
              child: Container(
                // margin: EdgeInsets.only(top: 2),
                child: Icon(
                  IconFonts.iconNoti,
                ),
              ),
            ),
            onTap: () {
              Navigator.pushNamed(context, '/message');
            },
          ),
          SizedBox(
            width: 20,
          )
        ],
      ),
      body: SimpleRefreshList<FindSameAppsVModel>(
        model: _model,
        builder: (context, model) {
          return ListView.separated(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 15),
              itemBuilder: (context, index) {
                return _buildListItem(model, index);
              },
              separatorBuilder: (context, index) {
                return SizedBox(
                  height: 20,
                );
              },
              itemCount: model.list.length);
        },
      ),
    );
  }

  Widget _buildListItem(FindSameAppsVModel model, index) {
    var data = model.list[index];
    return GestureDetector(
      onTap: () {
        DataRecord().saveData(
          eventId:
              'assistant_social_findFriendFromAppList_appList_0_block_click',
          extraData: {'id': data.id},
        );
        Navigator.pushNamed(
          context,
          '/friend_app_details',
          arguments: {
            "id": data.id,
            "image": data.rcover,
            "name": data.name,
          },
        );
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8.000000),
        child: Container(
          decoration: BoxDecoration(
            color: AppColors.backgroundItem,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AspectRatio(
                aspectRatio: 764 / 280,
                child: NetworkImageWidget(
                  data.rcover,
                ),
              ),
              Container(
                margin:
                    EdgeInsets.only(left: 12, top: 12, right: 12, bottom: 18),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      data.name,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: AppColors.textTitle,
                      ),
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    Row(
                      children: [
                        IntlRichText1(
                          intlTextBuilder:
                              YLocal.of(context).NrencanjiaNrencenjia,
                          defaultStyle: TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 13,
                            height: 1.16667,
                            color: Color(0xff767880),
                          ),
                          param: data.userNum,
                          paramStyle: TextStyle(fontWeight: FontWeight.w600),
                        ),
                        Builder(
                          builder: (context) {
                            int n = max(
                                data.userNum - (data.avatars?.length ?? 0), 0);
                            return Container(
                              margin: EdgeInsets.only(left: 10),
                              child: AvatarGroupWidget(
                                avatars: data.avatars ?? [],
                                falseNum: n,
                              ),
                            );
                          },
                        ),
                        Spacer(),
                        SvgPicture.asset(
                          "assets/svg/arrow_right.svg",
                          color: Color(0xff6E7380),
                          width: 10,
                          height: 10,
                        ),
                      ],
                    )
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
