import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:yvr_assistant/public_ui/widget/image_browser.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';
import 'package:yvr_assistant/public_ui/widget/height_constraints.dart';
import 'package:yvr_assistant/public_ui/widget/image_view.dart';
import 'package:yvr_assistant/public_ui/widget/refresh_layout.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/view_model/event_vmodel.dart';
import '../../../generated/l10n.dart';
import '../../../manager/app_navigator.dart';
import '../../../manager/events_bus2.dart';
import '../../../manager/global.dart';
import '../../../manager/resource_mananger.dart';
import '../../../model/event_comment_model.dart';
import '../../../public_ui/widget/avatar_group.dart';
import '../../../public_ui/widget/dialog_util.dart';
import '../../../public_ui/widget/dialogs.dart';
import '../../../public_ui/widget/image_group.dart';
import '../../../public_ui/widget/number_widget.dart';
import '../../../styles/app_color.dart';
import '../../../styles/app_divider.dart';
import '../../../utils/data_record.dart';
import '../../../view_model/user_vmodel.dart';
import '../../../view_model/vmodel_mixin.dart';
import '../../home/<USER>/apps_cell.dart';
import '../../home/<USER>/views/rating_star.dart';
import '../../tabbar/top_navbar.dart';
import 'package:html/dom.dart' as dom;

class EventDetailsPage extends StatefulWidget {
  final arguments;

  EventDetailsPage({Key key, @required this.arguments}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _EventDetailsPageState();
  }
}

class _EventDetailsPageState extends State<EventDetailsPage> {
  OfficialEventDetailsVModel model;
  FocusNode focusNode;

  @override
  void initState() {
    super.initState();
    final int id = widget.arguments['id'];
    model = OfficialEventDetailsVModel(id, onOfficialEventDirty);
    focusNode = FocusNode();
    focusNode.addListener(_onFocusChanged);
    Map extraData = {
      "activityId": id,
      "activityType": "official",
    };
    DataRecord().saveData(
        eventId: "assistant_social_activityDetail_0_0_page_view",
        extraData: extraData);
  }

  bool onOfficialEventDirty() {
    AppEvent.fireOfficialChangeEvent(
        AppEvent.EVENT_SUB_OFFICIAL_EVENT_DIRTY, model.eventId);
    Navigator.of(context).pop();
    return true;
  }

  void _onFocusChanged() {
    setState(() {});
  }

  @override
  void dispose() {
    model.deleteAllCommentImage();
    focusNode.removeListener(_onFocusChanged);
    super.dispose();
  }

  void _pop() {
    if (model.detail != null) {
      AppEvent.fireOfficialEventApplyEvent(
          EventApplyEventData(model.detail, model.totalListCount));
    }
    Global.navigatorKey.currentState.pop();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () {
        _pop();
        return Future.value(false);
      },
      child: ChangeNotifierProvider<OfficialEventDetailsVModel>.value(
        value: model,
        child: Scaffold(
          resizeToAvoidBottomInset: true,
          appBar: AppBar(
            title: Text(
              YLocal.of(context).huodongxiangqing,
            ),
            centerTitle: true,
            leading: CustomBackButton(
              onPressed: _pop,
            ),
          ),
          body: Column(
            children: [
              Expanded(
                child: SimpleRefreshList<OfficialEventDetailsVModel>(
                  model: model,
                  builder: (context, model) {
                    if (model.detail == null) {
                      ///如果活动下线，那么时没有数据的，此时会关闭页面，并发广播
                      return SizedBox();
                    }

                    int falseNum = model.detail.eventOfficialInfo.falseNum ?? 0;
                    int realNum = model.detail.eventOfficialInfo.realNum ?? 0;

                    return CustomScrollView(
                      slivers: <Widget>[
                        SliverToBoxAdapter(
                          child: AspectRatio(
                            aspectRatio: 828 / 414,
                            child: NetworkImageWidget(
                                model.detail.eventOfficialInfo.eventPicture),
                          ),
                        ),
                        SliverToBoxAdapter(
                          child: Container(
                            margin: EdgeInsets.only(top: 20),
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  model.detail.eventOfficialInfo.eventName,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontSize: 27,
                                    height: 1,
                                    color: AppColors.textTitle,
                                  ),
                                ),
                                const SizedBox(
                                  height: 16,
                                ),
                                Text(
                                  model.detail.eventOfficialInfo.eventTime ??
                                      '',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontSize: 16,
                                    height: 1,
                                    color: AppColors.textSub,
                                  ),
                                ),
                                const SizedBox(
                                  height: 9,
                                ),
                                Row(
                                  children: [
                                    if (falseNum + realNum > 2)
                                      Container(
                                        margin: const EdgeInsets.only(right: 4),
                                        child: AvatarGroupWidget(
                                          avatars: model
                                                  .detail
                                                  .eventOfficialInfo
                                                  .focusesPortraits ??
                                              [],
                                          falseNum: falseNum ?? 0,
                                        ),
                                      ),
                                    Builder(builder: (context) {
                                      return NumberWidget(
                                        num: falseNum + realNum,
                                        unit: YLocal.of(context).Nrenyibaoming,
                                        defaultText:
                                            YLocal.of(context).kuailaibaomingba,
                                        defaultStyle: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontSize: 14,
                                          height: 1,
                                          color: AppColors.textWeak,
                                        ),
                                        numStyle: TextStyle(
                                          fontWeight: FontWeight.w500,
                                          fontSize: 14,
                                          height: 1,
                                          color: AppColors.textWeak,
                                        ),
                                      );
                                    })
                                  ],
                                ),
                                const SizedBox(
                                  height: 23,
                                ),
                                AppDivider.backgroundDivider,
                                buildApplication(context, model),
                              ],
                            ),
                          ),
                        ),
                        SliverToBoxAdapter(
                          child: buildRule(context, model),
                        ),
                        // if ((model.detail.eventOfficialInfo.commentNum ?? 0) > 0)
                        SliverToBoxAdapter(
                          child: Column(
                            children: [
                              const SizedBox(
                                height: 24,
                              ),
                              AppDivider.backgroundDivider,
                            ],
                          ),
                        ),
                        // if ((model.detail.eventOfficialInfo.commentNum ?? 0) > 0)
                        SliverAppBar(
                          toolbarHeight: 65,
                          pinned: true,
                          automaticallyImplyLeading: false,
                          title: Container(
                            alignment: AlignmentDirectional.centerStart,
                            margin: EdgeInsets.only(left: 4),
                            child: Text(
                              YLocal.of(context)
                                  .Nzepinglun(model.totalListCount ?? 0),
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                fontSize: 22,
                                height: 1,
                                color: AppColors.textTitle,
                              ),
                            ),
                          ),
                        ),
                        // if ((model.detail.eventOfficialInfo.commentNum ?? 0) > 0)
                        SliverList(
                          delegate: SliverChildBuilderDelegate(
                            (BuildContext context, int index) {
                              return buildCommentItem(index, model);
                            },
                            childCount: model.list.length,
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
              Consumer<OfficialEventDetailsVModel>(
                builder: (context, value, child) => buildComment(model),
              ),
              SizedBox(
                height: focusNode.hasFocus ? 0 : Global.paddingBottom,
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget buildApplication(
      BuildContext context, OfficialEventDetailsVModel model) {
    var value = model.detail.appInfo;
    if (value == null || value.id == null) {
      return SizedBox();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 24,
        ),
        Text(
          YLocal.of(context).xiangguanyingyong,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 22,
            height: 1,
            color: AppColors.textTitle,
          ),
        ),
        SizedBox(
          height: 20,
        ),
        GestureDetector(
          onTap: () {
            Navigator.pushNamed(context, '/prod', arguments: {
              "id": model.detail.appInfo.id,
            });
          },
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: NetworkImageWidget(
                  value.rcover,
                  width: 184,
                  height: 104,
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      value.name,
                      maxLines: 2,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                        height: 1.5,
                        letterSpacing: 0.5,
                        color: AppColors.textTitle,
                      ),
                    ),
                    SizedBox(
                      height: 4,
                    ),
                    Row(
                      children: symbolLabels(tags: model.tagList),
                    ),
                    SizedBox(
                      height: 4,
                    ),
                    if (value.state != -1)
                      Row(
                        children: [
                          Text(
                            YLocal.of(context)
                                .home_person_com(value.count.toString()),
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: 9,
                              color: AppColors.textWeak,
                            ),
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          RatingBar(
                            initialRating: value.count == 0
                                ? 0
                                : ((value.aver.floor() == value.aver.round())
                                    ? value.aver
                                    : (value.aver.floor() + 0.5)),
                            allowHalfRating: true,
                            itemCount: 5,
                            itemSize: 10,
                            updateOnDrag: false,
                            ignoreGestures: true,
                            ratingWidget: ratingWidget,
                            itemPadding: EdgeInsets.symmetric(horizontal: 2.0),
                            onRatingUpdate: (double value) {},
                          ),
                        ],
                      ),
                    SizedBox(
                      height: 5,
                    ),
                    priceWidget(
                      value.state,
                      value.sprice,
                      value.id,
                      value.bprice,
                      freeTextColor: Color(0xff4F7FFE),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget buildRule(BuildContext context, OfficialEventDetailsVModel model) {
    var value = model.detail.eventOfficialInfo.eventDescribe;
    if (value == null || value.isEmpty) {
      return SizedBox();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 24,
        ),
        Container(
          child: Text(
            YLocal.of(context).huodongguize,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 22,
              height: 1,
              color: AppColors.textTitle,
            ),
          ),
          padding: EdgeInsets.symmetric(horizontal: 20),
        ),
        SizedBox(height: 20),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 20),
          child: HeightConstraintWidget(
            maxHeight: 164,
            child: Html(
              data: value,
              style: {
                "body":
                    Style(margin: EdgeInsets.zero, padding: EdgeInsets.zero),
              },
              onLinkTap: (String url, RenderContext renderContext,
                  Map<String, String> attributes, dom.Element element) {
                navigateByUrl(context, url);
              },
              customImageRenders: {
                networkSourceMatcher(): networkImageRender(loadingWidget: () {
                  return SizedBox();
                }),
              },
              onImageTap: (
                String url,
                RenderContext renderContext,
                Map<String, String> attributes,
                dom.Element element,
              ) {
                Navigator.of(context).push(
                  ImageBrowserPageRoute(
                    builder: (BuildContext context) {
                      return NetworkImageBrowserPage(
                        images: [url],
                        index: 0,
                      );
                    },
                  ),
                );
              },
            ),
            expanded: model.ruleExpanded,
            onTap: () {
              model.toggleRuleExpand();
            },
          ),
        ),
      ],
    );
  }

  static const int kCommentMaxRowImageCount = 3;
  static const double kCommentImageMargin = 10;

  Widget buildCommentItem(int index, OfficialEventDetailsVModel model) {
    EventCommentModel comment = model.list[index];
    EventCommentListData commentData = model.eventCommentListData[index];
    bool isLogoutUser = comment.userStatus != null && comment.userStatus == -2;
    return Container(
      padding: EdgeInsets.only(left: 20, right: 6, bottom: 24),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  AvatarImageWidget(
                    isLogoutUser ? '' : comment.userPortrait,
                    width: 52,
                    height: 52,
                    actId: comment.userId,
                  ),
                  SizedBox(
                    width: 14,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isLogoutUser
                            ? YLocal.of(context).zhanghaoyizhuxiao
                            : (comment.userName ?? ''),
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                          height: 1,
                          color: commentData.isSelf
                              ? Color(0xff4F7FFE)
                              : AppColors.textTitle,
                        ),
                      ),
                      const SizedBox(
                        height: 6,
                      ),
                      Text(
                        commentData.commentTime,
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: 12,
                          height: 1,
                          color: AppColors.textWeak,
                        ),
                      )
                    ],
                  )
                ],
              ),
              PopupMenuButton<String>(
                color: Colors.transparent,
                icon: Icon(
                  IconFonts.iconMore,
                  color: Color(0xff2C2E33),
                  size: 22,
                ),
                offset: Offset(-16, -10),
                elevation: 0,
                // 去除阴影效果
                itemBuilder: (context) {
                  return <PopupMenuEntry<String>>[
                    PopupMenuItem<String>(
                      value: commentData.isSelf
                          ? YLocal.of(context).shanchu
                          : YLocal.of(context).jubao,
                      child: Container(
                        height: 48,
                        width: 123,
                        alignment: Alignment(0, 0),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Color(0xffF0F2F5),
                        ),
                        child: Text(
                          commentData.isSelf
                              ? YLocal.of(context).shanchu
                              : YLocal.of(context).jubao,
                          style:
                              TextStyle(color: Color(0xff2C2E33), fontSize: 16),
                        ),
                      ),
                    ),
                  ];
                },
                onSelected: (value) async {
                  if (DBUtil.instance.userBox.get(kToken) == null) {
                    showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (_) {
                          return CustomDialog(
                            title: YLocal.of(context).wenxindishiwenxintis,
                            content: YLocal.of(context).xuyaodenglucainengju,
                            confirmText: YLocal.of(context).qudenglu,
                            height: 180,
                            confirmCallback: () {
                              Navigator.pushNamed(context, '/login')
                                  .then((value) {
                                model.refreshQuietly();
                              });
                            },
                          );
                        });
                  } else {
                    model.deleteOrReportComment(comment, commentData);
                  }
                },
              )
            ],
          ),
          if (commentData.commentText.length > 0)
            Container(
              alignment: AlignmentDirectional.centerStart,
              margin: EdgeInsets.only(top: 10),
              padding: EdgeInsets.only(right: 14),
              child: Text(
                commentData.commentText,
                textAlign: TextAlign.start,
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 16,
                  color: AppColors.textSub,
                ),
              ),
            ),
          if (commentData.commentImages.length > 0)
            Container(
              margin: EdgeInsets.only(
                  top: commentData.commentText.length > 0 ? 12 : 16),
              padding: EdgeInsets.only(right: 14),
              child: LayoutBuilder(
                builder: (BuildContext context, BoxConstraints constraints) {
                  double itemWidth = (constraints.maxWidth -
                          kCommentImageMargin *
                              (kCommentMaxRowImageCount - 1)) /
                      kCommentMaxRowImageCount;
                  return ImageGroupWidget(
                    images: commentData.commentImages,
                    itemWidth: itemWidth,
                    itemHeight: itemWidth,
                    columnNum: kCommentMaxRowImageCount,
                    onPressed: (index) {
                      Navigator.of(context).push(
                        ImageBrowserPageRoute(
                          builder: (BuildContext context) {
                            return NetworkImageBrowserPage(
                              images: commentData.commentImages,
                              index: index,
                            );
                          },
                        ),
                      );
                    },
                  );
                },
              ),
            )
        ],
      ),
    );
  }

  static const int kMaxCommentTextLength = 200;

  Widget buildComment(OfficialEventDetailsVModel model) {
    ///如果活动下线，那么时没有数据的，此时会关闭页面，并发广播
    if (model.detail == null || model.isEmpty) {
      return SizedBox();
    }
    bool hasFocus = focusNode.hasFocus || model.hasCommentFormContent();
    bool isSending = model.isSending;
    return Container(
      child: Builder(
        builder: (context) {
          return Column(
            children: [
              // Divider(),
              Builder(builder: (context) {
                return Container(
                  height: 0.5,
                  decoration: BoxDecoration(
                    color: hasFocus ? Colors.transparent : Colors.transparent,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.04),
                        offset: Offset(0, -1),
                        blurRadius: 4,
                        // spreadRadius: 3,
                      ),
                    ],
                  ),
                );
              }),
              SizedBox(
                height: hasFocus ? 18 : 11,
              ),
              Container(
                margin: EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    Expanded(
                      child: Container(
                        // color: Colors.red,
                        height: 48,
                        child: TextField(
                          enabled: !isSending,
                          controller: model.commentTextEditingController,
                          textAlign:
                              hasFocus ? TextAlign.start : TextAlign.center,
                          textAlignVertical: hasFocus
                              ? TextAlignVertical.top
                              : TextAlignVertical.center,
                          focusNode: focusNode,
                          maxLines: 3,
                          maxLength: hasFocus ? kMaxCommentTextLength : null,
                          buildCounter: (
                            BuildContext context, {
                            @required int currentLength,
                            @required int maxLength,
                            @required bool isFocused,
                          }) {
                            return null;
                          },
                          style: TextStyle(
                              fontSize: 15,
                              color: Color(0xff2C2E33),
                              height: 1.0,
                              letterSpacing: 1.4),
                          decoration: InputDecoration(
                            contentPadding: hasFocus
                                ? EdgeInsets.symmetric(
                                    vertical: 9, horizontal: 10)
                                : EdgeInsets.zero,
                            border: OutlineInputBorder(
                              /*边角*/
                              borderRadius: BorderRadius.all(
                                Radius.circular(4), //边角为5
                              ),
                              borderSide: BorderSide.none,
                            ),
                            enabledBorder: OutlineInputBorder(
                              /*边角*/
                              borderRadius: BorderRadius.all(
                                Radius.circular(4), //边角为5
                              ),
                              borderSide: BorderSide.none,
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                Radius.circular(4), //边角为5
                              ),
                              borderSide: BorderSide.none,
                            ),
                            focusedErrorBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                Radius.circular(4), //边角为5
                              ),
                              borderSide: BorderSide.none,
                            ),
                            errorBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                Radius.circular(4), //边角为5
                              ),
                              borderSide: BorderSide.none,
                            ),
                            disabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                Radius.circular(4), //边角为5
                              ),
                              borderSide: BorderSide.none,
                            ),
                            filled: true,
                            fillColor: hasFocus
                                ? Color(0xffF0F2F5)
                                : Color(0xffF0F2F5),
                            isDense: true,
                            isCollapsed: true,
                            hintText: hasFocus
                                ? YLocal.of(context).shuidianshenmebashuo
                                : YLocal.of(context).fabiaoyixianidekanfa,
                            hintStyle: hasFocus
                                ? TextStyle(
                                    color: Color(0xffAFB6CC), fontSize: 14)
                                : TextStyle(
                                    color: Color(0xffAFB6CC),
                                    fontSize: 18,
                                    height: 2.0,
                                    fontWeight: FontWeight.w500),
                          ),
                        ),
                      ),
                    ),
                    if (!hasFocus && !model.isExpired)
                      FollowNumberButton(
                        num: model.followNum,
                        followed: model.isFollowed,
                        onPressed: model.follow,
                      ),
                  ],
                ),
              ),
              if (hasFocus && model.uploadImageSize() > 0)
                Container(
                  height: 72,
                  margin: EdgeInsets.only(top: 15),
                  child: ListView.builder(
                      padding: EdgeInsets.symmetric(horizontal: 20),
                      scrollDirection: Axis.horizontal,
                      itemBuilder: (context, index) {
                        return buildUploadImageImage(context, index, isSending,
                            index == model.uploadImageList.length - 1);
                      },
                      itemCount: model.uploadImageList.length),
                ),
              if (hasFocus)
                Container(
                  margin: EdgeInsets.only(top: 14, left: 11, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        child: Container(
                          alignment: AlignmentDirectional.center,
                          width: 40,
                          height: 40,
                          child: SvgPicture.asset(
                            "assets/svg/image_select.svg",
                            width: 24,
                            height: 24,
                            color: Color(0xff2C2E33),
                          ),
                        ),
                        onTap: isSending ? null : onSelectImageTap,
                      ),
                      Row(
                        children: [
                          Text(
                            '${model.commentTextEditingController.text.length}/$kMaxCommentTextLength',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: 14,
                              color: AppColors.textWeak,
                            ),
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          ElevatedProgressButton(
                            state: model.sendCommentState,
                            width: 52,
                            height: 28,
                            padding: EdgeInsets.zero,
                            color: Color(0xff4F7FFE),
                            radius: 5,
                            onPressed: !model.hasCommentFormContent()
                                ? null
                                : () {
                                    if (focusNode.hasFocus) {
                                      focusNode.unfocus();
                                    }
                                    model.sendComment();
                                  },
                            idleChild: Text(
                              YLocal.of(context).fabiaofeibiao,
                            ),
                          )
                        ],
                      ),
                    ],
                  ),
                ),
              SizedBox(
                height: 12,
              ),
            ],
          );
        },
      ),
    );
  }

  static const int kMaxImageLength = 9;

  void onSelectImageTap() {
    Dialogs.showImageSelectorDialog2(context,
            selectedAssets: model.uploadImageList
                .map<AssetEntity>((e) => e.localFile)
                .toList(),
            maxAssets: kMaxImageLength)
        .then((value) {
      if (value != null && value.isNotEmpty) {
        model.replaceLocalImages(value);
      }
    });
  }

  Widget buildUploadImageImage(
      BuildContext context, int index, bool isSending, bool isLast) {
    UploadImageData data = model.uploadImageList[index];
    return IgnorePointer(
      ignoring: isSending,
      child: GestureDetector(
        onTap: () {
          Navigator.of(context).push(
            ImageBrowserPageRoute(
              builder: (BuildContext context) {
                return LocalImageBrowserPage2(
                  images: model.uploadImageList
                      .map<AssetEntity>((e) => e.localFile)
                      .toList(),
                  index: index,
                );
              },
            ),
          );
        },
        child: Container(
          width: 76,
          height: 76,
          margin: EdgeInsets.only(right: isLast ? 0 : 3),
          child: Stack(
            alignment: AlignmentDirectional.topEnd,
            children: [
              Positioned(
                left: 0,
                top: 10,
                right: 10,
                bottom: 0,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: data.hasLocal
                      ? Image(
                          image: AssetEntityImageProvider(data.localFile,
                              isOriginal: false),
                          fit: BoxFit.cover,
                        )
                      : data.hasNetwork
                          ? NetworkImageWidget(data.networkUrl)
                          : SizedBox(),
                ),
                // child: Image(image: ),
              ),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  model.deleteCommentImage(index);
                },
                child: Container(
                  width: 18,
                  height: 18,
                  padding: EdgeInsets.all(2),
                  child: SvgPicture.asset("assets/svg/image_delete.svg"),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
