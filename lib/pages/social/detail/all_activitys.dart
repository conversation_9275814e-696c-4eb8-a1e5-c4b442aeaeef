import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/manager/storage_manager.dart';
import 'package:yvr_assistant/model/event_model.dart';
import 'package:yvr_assistant/network/socket.dart';
import 'package:yvr_assistant/pages/social/views/activity_cell.dart';
import 'package:yvr_assistant/pages/social/views/dev_modal_view.dart';
import 'package:yvr_assistant/pages/social/views/plhd_view.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/log.dart';

import '../../../main.dart';

class AllActivitysPage extends StatefulWidget {
  final arguments;
  AllActivitysPage({Key key, @required this.arguments}) : super(key: key);

  @override
  _AllActivitysPageState createState() => _AllActivitysPageState();
}

class _AllActivitysPageState extends State<AllActivitysPage> with RouteAware {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context));
  }

  @override
  void didPopNext() {
    super.didPopNext();
    Log.d('返回活动详情列表');
    SocketManager().sendMessage(json.encode(eventParams));
  }

  bool _showPlhd = false;
  List _myEventIds = [];
  List<Widget> _enentCards = [];
  Map<String, dynamic> eventParams = {
    "type": "request",
    "cmd": "reqGetMyEvents",
  };
  // 获取我的活动和传入的活动进行比较 是否加入

// 定义变量接收 eventBus 监听实例
  var eventBusSocket;
  @override
  void dispose() {
    super.dispose();
    // 取消订阅
    eventBusSocket.cancel();
    routeObserver.unsubscribe(this);
  }

  @override
  void initState() {
    super.initState();
    List<EventModel> pageModels = widget.arguments["models"] ?? [];

    SocketManager().sendMessage(json.encode(eventParams));
    // 注册监听器，订阅 eventbus
    eventBusSocket = eventBus.on<EventSocket>().listen((event) {
      switch (event.socketModel.cmd) {
        case "rspGetMyEvents":
          List<EventModel> eventModels =
              eventModelFromJson(jsonEncode(event.socketModel.data["events"]));

          Log.d('我的活动数量：${eventModels.length}');
          if (eventModels.length > 0) {
            _myEventIds = eventModels.map((model) {
              return model.id;
            }).toList();
            //我的活动
            if (pageModels.length == 0) {
              eventModels.forEach((model) {
                model.isJonin = true;
              });

              setState(() {
                _enentCards = _eventCardList(eventModels);
              });
            }
          }
          if (pageModels.length > 0) {
            pageModels.forEach((model) {
              if (_myEventIds.contains(model.id)) {
                model.isJonin = true;
              } else {
                model.isJonin = false;
              }
            });
            setState(() {
              _enentCards = _eventCardList(pageModels);
            });
          }

          if (eventModels.length == 0 && pageModels.length == 0) {
            setState(() {
              _showPlhd = true;
            });
          }

          break;
        case 'rspJoinEvent':
          SocketManager().sendMessage(json.encode(eventParams));
          // if (!StorageManager.localStorage.getItem("kIsActivityDetlPageYet")) {
          //   Log.d("所有活动页面申请跳转活动详情！");
          //   StorageManager.localStorage.setItem("kIsActivityDetlPageYet", true);
          //   YvrToast.showToast("已成功加入活动！");
          //   Navigator.pushNamed(context, "/activity_detl", arguments: {
          //     "id": StorageManager.localStorage.getItem("kJumpActivityId")
          //   });
          // }
          break;
        case 'rspQuitEvent':
          pageModels.removeWhere((element) =>
              element.id ==
              StorageManager.localStorage.getItem("kJumpActivityId"));
          setState(() {
            _enentCards = _eventCardList(pageModels);
          });
          YvrToast.showToast(YLocal.of(context).yituichugaihuodong);
          break;
        default:
      }
    });
  }

  List<Widget> _eventCardList(List<EventModel> models) {
    List<Widget> cards = [];
    // InkWell 点击范围过大 导航返回直接跳转了活动页？？
    models.forEach((model) {
      cards.add(GestureDetector(
        onTap: () {
          Navigator.pushNamed(context, "/activity_detl",
                  arguments: {"id": model.id})
              .then((value) => value
                  ? SocketManager().sendMessage(json.encode(eventParams))
                  : null);
        },
        child: ActivityCell(
          event: model,
          handleEvent: (eventStatus, id, name) {
            handleEvent(eventStatus, id, name);
          },
        ),
      ));
    });
    return cards;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Color(0xff17191B),
        appBar: TopNavbar(title: widget.arguments["title"] ?? YLocal.of(context).quanbuhuodong),
        body: _showPlhd
            ? PagePlhdWidget(
                imagePath: 'assets/images/friend_plhd.svg',
                message: YLocal.of(context).plhd_no_event)
            : SingleChildScrollView(
                child: Column(
                  children: _enentCards,
                ),
              ));
  }

  handleEvent(eventStatus, id, name) {
    switch (eventStatus) {
      case 1:
        showDevices(
            context: context,
            callback: (String devId) {
              // StorageManager.localStorage.setItem("kJumpActivityId", id);
              Map<String, dynamic> params = {
                "type": "request",
                "cmd": "reqJoinEvent",
                "eventId": id
              };
              SocketManager().sendMessage(json.encode(params));
            });
        break;
      case 2:
        // StorageManager.localStorage.setItem("kJumpActivityId", id);
        Map<String, dynamic> params = {
          "type": "request",
          "cmd": "reqJoinEvent",
          "eventId": id
        };
        SocketManager().sendMessage(json.encode(params));
        break;
      case 3:
        YvrToast.showToast(YLocal.of(context).zijichuangjiandehuod);
        break;
      case 4:
        showDialog(
            context: context,
            barrierDismissible: false,
            builder: (_) {
              return CustomDialog(
                title: YLocal.of(context).social_leave_event,
                content: YLocal.of(context).social_leave_event_desc,
                height: 200,
                isCancel: true,
                confirmText: YLocal.of(context).confirm,
                confirmCallback: () {
                  Map<String, dynamic> params = {
                    "type": "request",
                    "cmd": "reqQuitEvent",
                    "eventId": id
                  };
                  SocketManager().sendMessage(json.encode(params));
                },
              );
            });
        break;
      default:
    }
  }
}
