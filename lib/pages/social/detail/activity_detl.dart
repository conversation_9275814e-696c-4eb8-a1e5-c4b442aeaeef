import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:yvr_assistant/base/lifecycle_state_mixin.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/manager/resource_mananger.dart';
import 'package:yvr_assistant/model/event_detl_model.dart';
import 'package:yvr_assistant/pages/social/views/dev_modal_view.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/public_ui/widget/image_view.dart';
import 'package:yvr_assistant/public_ui/widget/refresh_layout.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import '../../../styles/app_color.dart';
import '../../../utils/data_record.dart';
import '../../../utils/date_time_utils.dart';
import '../../../view_model/personal_event_vmodel.dart';

class ActivityDetlPage extends StatefulWidget {
  final arguments;

  ActivityDetlPage({Key key, @required this.arguments}) : super(key: key);

  @override
  _ActivityDetlPageState createState() => _ActivityDetlPageState();
}

class _ActivityDetlPageState extends LifecycleState<ActivityDetlPage> {
  PersonalEventDetailVModel _viewModel;

  @override
  void onRefresh() {
    _viewModel.refreshQuietly();
  }

  @override
  void initState() {
    super.initState();

    var id = widget.arguments["id"];
    _viewModel = PersonalEventDetailVModel(editId: id);
    Map extraData = {
      "activityId": id,
      "activityType": "personal",
    };
    DataRecord().saveData(
        eventId: "assistant_social_activityDetail_0_0_page_view",
        extraData: extraData);
  }

  List<Widget> userListWidget(List<User> users) {
    var widgets = users.map((user) {
      return Column(
        children: [
          AvatarImageWidget(
            user.avatar,
            width: 60,
            height: 60,
            actId: user.actId,
            sex: user.sex,
          ),
          SizedBox(
            height: 8,
          ),
          SizedBox(
            width: 58,
            child: Text(
              user.nick ?? YLocal.of(context).nickname,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 14,
                height: 1.42857,
                color: AppColors.textTitle,
              ),
            ),
          )
        ],
      );
    });
    return widgets.toList();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<PersonalEventDetailVModel>.value(
      value: _viewModel,
      child: Consumer<PersonalEventDetailVModel>(
        builder: (context, viewModel, child) {
          var eventDetlModel = viewModel.model;

          return Scaffold(
              appBar: TopNavbar(title: eventDetlModel?.name ?? '', actions: [
                viewModel.enentJoinStatus == 0
                    ? PopupMenuButton<String>(
                        icon: Icon(
                          IconFonts.iconMore,
                          color: Color(0xff6E7380),
                          size: 18,
                        ),

                        padding: EdgeInsets.zero,
                        offset: Offset(0, 30),
                        elevation: 0,
                        // 去除阴影效果
                        itemBuilder: (context) {
                          return [
                            PopupMenuItem<String>(
                              value: "0",
                              height: 30,
                              padding: EdgeInsets.symmetric(
                                  horizontal: 0, vertical: 10),
                              child: Container(
                                alignment: AlignmentDirectional.center,
                                child: Text(
                                  YLocal.of(context).bianjihuodong,
                                ),
                              ),
                            ),
                            PopupMenuItem<String>(
                              value: "1",
                              height: 30,
                              padding: EdgeInsets.symmetric(
                                  horizontal: 0, vertical: 10),
                              child: Container(
                                alignment: AlignmentDirectional.center,
                                child: Text(
                                  YLocal.of(context).shanchuhuodong,
                                ),
                              ),
                            ),
                          ];
                        },
                        onSelected: (value) async {
                          switch (value) {
                            case "0":
                              Navigator.pushNamed(context, '/activity_create',
                                  arguments: {"id": eventDetlModel.id});
                              break;
                            case "1":
                              if (eventDetlModel.process == 1) {
                                YvrToast.showToast(
                                    YLocal.of(context).zhengzaijinhangzhong_1);
                              } else {
                                showDialog(
                                    context: context,
                                    barrierDismissible: false,
                                    builder: (_) {
                                      return CustomDialog(
                                        title: YLocal.of(context).dishitishi,
                                        content: YLocal.of(context)
                                            .quedingyaoshanchugai_1,
                                        height: 180,
                                        confirmCallback: () {
                                          viewModel.deleteEvent(onSuccess: () {
                                            Navigator.pop(context, true);
                                          });
                                        },
                                      );
                                    });
                              }

                              break;
                            default:
                          }
                        },
                      )
                    : SizedBox()
              ]),
              body: SimpleRefreshList<PersonalEventDetailVModel>(
                  model: viewModel,
                  builder: (context, viewModel) {
                    final eventDetlModel = viewModel.model;
                    return Stack(
                      children: [
                        CustomScrollView(slivers: [
                          SliverToBoxAdapter(
                              child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Stack(children: [
                                AspectRatio(
                                  aspectRatio: 16 / 9,
                                  child: NetworkImageWidget(
                                    eventDetlModel.rcover,
                                  ),
                                ),
                                Card(
                                  shape: RoundedRectangleBorder(),
                                  margin: EdgeInsets.fromLTRB(
                                      0,
                                      (Global.screenWidth * 9 / 16.0 - 6),
                                      0,
                                      0),
                                  elevation: 0,
                                  child: SizedBox(
                                    height: 50,
                                    width: MediaQuery.of(context).size.width,
                                    child: Builder(builder: (context) {
                                      DateTime dt =
                                          DateTimeUtils.parseServerTime(
                                              eventDetlModel.startTime);
                                      return Flex(
                                        direction: Axis.horizontal,
                                        children: [
                                          SizedBox(
                                            height: 50,
                                            width: 60,
                                            child: Flex(
                                                direction: Axis.vertical,
                                                children: [
                                                  Container(
                                                    alignment: Alignment.center,
                                                    height: 20,
                                                    width: 60,
                                                    decoration: BoxDecoration(
                                                      color: Color.fromRGBO(
                                                          50, 59, 84, 1.0),
                                                      borderRadius:
                                                          BorderRadius.only(
                                                        topLeft:
                                                            Radius.circular(12),
                                                      ),
                                                    ),
                                                    child: Text(
                                                      dt.month.toString() + "月",
                                                      style: TextStyle(
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        fontSize: 12,
                                                        height: 1.25,
                                                        color: AppColors
                                                            .background,
                                                      ),
                                                    ),
                                                  ),
                                                  Container(
                                                    alignment: Alignment.center,
                                                    height: 30,
                                                    width: 60,
                                                    color: Color(0xff2C2E33),
                                                    child: Text(
                                                      dt.day.toString(),
                                                      style: TextStyle(
                                                        fontWeight:
                                                            FontWeight.w600,
                                                        fontSize: 24,
                                                        height: 1,
                                                        color:
                                                            AppColors.standard,
                                                      ),
                                                    ),
                                                  ),
                                                ]),
                                          ),
                                          Expanded(
                                              flex: 1,
                                              child: Container(
                                                  padding: EdgeInsets.fromLTRB(
                                                      15, 5, 20, 5),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      RichText(
                                                        text: TextSpan(
                                                          style: TextStyle(
                                                            fontWeight:
                                                                FontWeight.w400,
                                                            fontSize: 16,
                                                            color: AppColors
                                                                .textTitle,
                                                          ),
                                                          children: [
                                                            TextSpan(
                                                              text: DateTimeUtils
                                                                  .formatToLocalFromDateTime(
                                                                      dt,
                                                                      'HH:mm'),
                                                              style: TextStyle(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600,
                                                                fontSize: 30,
                                                                color: AppColors
                                                                    .textTitle,
                                                              ),
                                                            ),
                                                            WidgetSpan(
                                                              child: SizedBox(
                                                                width: 12,
                                                              ),
                                                            ),
                                                            TextSpan(
                                                              text: eventDetlModel.process ==
                                                                      1
                                                                  ? YLocal.of(
                                                                          context)
                                                                      .jinhangzhongjinhengz
                                                                  : (eventDetlModel
                                                                              .process ==
                                                                          2
                                                                      ? YLocal.of(
                                                                              context)
                                                                          .yijieshu
                                                                      : YLocal.of(
                                                                              context)
                                                                          .kaishi),
                                                            )
                                                          ],
                                                        ),
                                                      ),
                                                      Container(
                                                        padding: EdgeInsets
                                                            .symmetric(
                                                                horizontal: 6,
                                                                vertical: 4),
                                                        decoration: BoxDecoration(
                                                            color: Color(
                                                                0xffF0F2F5),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .all(Radius
                                                                        .circular(
                                                                            10))),
                                                        child: Row(children: [
                                                          Container(
                                                            width: 12,
                                                            height: 12,
                                                            child: SvgPicture
                                                                .asset(
                                                              "assets/images/event_clock.svg",
                                                              color: Color(
                                                                  0xff2C2E33),
                                                            ),
                                                          ),
                                                          SizedBox(
                                                            width: 5,
                                                          ),
                                                          Text(
                                                            minuteToHour(
                                                                eventDetlModel
                                                                    .duration),
                                                            style: TextStyle(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w400,
                                                              height: 1.2,
                                                              fontSize: 10,
                                                              color: AppColors
                                                                  .textTitle,
                                                            ),
                                                          )
                                                        ]),
                                                      ),
                                                    ],
                                                  ))),
                                        ],
                                      );
                                    }),
                                  ),
                                ),
                              ]),
                              Container(
                                margin: EdgeInsets.only(top: 21),
                                padding: EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      eventDetlModel.name ?? "",
                                      style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        fontSize: 16,
                                        height: 1,
                                        letterSpacing: 0,
                                        color: AppColors.textTitle,
                                      ),
                                    ),
                                    SizedBox(
                                      height: 6,
                                    ),
                                    Text(
                                      YLocal.of(context).social_organizers +
                                          '：' +
                                          (eventDetlModel.cName == null
                                              ? YLocal.of(context).nickname
                                              : eventDetlModel.cName),
                                      style: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontSize: 12,
                                        height: 1,
                                        color: AppColors.textWeak,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              eventDetlModel.detail?.isEmpty ?? true
                                  ? SizedBox()
                                  : Container(
                                      margin: EdgeInsets.only(
                                          left: 16,
                                          top: 14,
                                          right: 16,
                                          bottom: 0),
                                      child: Text(
                                        eventDetlModel.detail,
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontSize: 14,
                                          height: 1.57143,
                                          color: AppColors.textTitle,
                                        ),
                                      ),
                                    ),
                              Container(
                                padding: EdgeInsets.fromLTRB(16, 30, 16, 0),
                                child: Text(
                                  YLocal.of(context).social_people_attend(
                                      eventDetlModel.num.toString()),
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 14,
                                    height: 1,
                                    color: AppColors.textTitle,
                                  ),
                                ),
                              ),
                              Container(
                                padding: EdgeInsets.fromLTRB(16, 18, 16, 0),
                                margin: EdgeInsets.only(bottom: 110),
                                child: Wrap(
                                  spacing: 10.75, // 主轴(水平)方向间距
                                  runSpacing: 15, // 纵轴（垂直）方向间距
                                  alignment: WrapAlignment.start, //沿主轴方向居中
                                  children:
                                      userListWidget(eventDetlModel.users),
                                ),
                              ),
                            ],
                          ))
                        ]),
                        eventDetlModel.process == 2
                            ? SizedBox()
                            : bottomButtons(viewModel),
                      ],
                    );
                  })

              // This trailing comma makes auto-formatting nicer for build methods.
              );
        },
      ),
    );
  }

  String minuteToHour(int duration) {
    if (duration == null) {
      return "";
    }

    String hour = "";
    if (duration ~/ 60 > 0) {
      hour = YLocal.of(context).Nxiaoshi((duration ~/ 60).toString());
    }
    if (duration % 60 > 0) {
      hour += YLocal.of(context).Nfenzhong((duration % 60).toString());
    }
    return hour;
  }

  /*
     活动参加情况：
        我创建的活动：      0
        其他人创建的活动：
            可邀请好友
                未参加    1
                已参加    2
            不可邀请好友
                未参加    3
                已参加    4
  */
  Widget bottomButtons(PersonalEventDetailVModel viewModel) {
    int process = viewModel.model.process;
    int enentJoinStatus = viewModel.enentJoinStatus;
    Widget bottomWidget = SizedBox();
    switch (enentJoinStatus) {
      case 0:
        bottomWidget = YvrElevatedButton(
          height: 48,
          padding: EdgeInsets.zero,
          onPressed: () {
            Navigator.pushNamed(context, '/invite_friends', arguments: {
              "id": widget.arguments["id"],
              "userIdList": viewModel.getUserIdList()
            });
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 17,
                height: 17,
                child: SvgPicture.asset("assets/svg/add_event.svg"),
              ),
              SizedBox(
                width: 5,
              ),
              Text(
                YLocal.of(context).yaoqinghaoyou,
                style: TextStyle(
                    fontSize: 18,
                    letterSpacing: 1,
                    fontWeight: FontWeight.w500),
              ),
            ],
          ),
        );
        break;
      case 1:
      case 2:
        bottomWidget = Flex(
            direction: Axis.horizontal,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                flex: 1,
                child: YvrElevatedButton(
                  height: 48,
                  padding: EdgeInsets.zero,
                  color: enentJoinStatus == 1 ? Color(0xFFF0F2F5) : null,
                  onPressed: () {
                    if (enentJoinStatus == 1) {
                      YvrToast.showToast(
                          YLocal.of(context).ninjiaruhuodonghouca);
                    } else {
                      Navigator.pushNamed(context, '/userIdList', arguments: {
                        "id": widget.arguments["id"],
                        "userIdList": viewModel.getUserIdList()
                      });
                    }
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                          width: 17,
                          height: 17,
                          child: SvgPicture.asset(
                            "assets/svg/add_event.svg",
                            color: enentJoinStatus == 1
                                ? Color(0xff6E7380)
                                : Colors.white,
                          )),
                      SizedBox(
                        width: 5,
                      ),
                      Text(
                        YLocal.of(context).yaoqinghaoyou,
                        style: TextStyle(
                            color: enentJoinStatus == 1
                                ? Color(0xff6E7380)
                                : Colors.white,
                            fontSize: 18,
                            letterSpacing: 1,
                            fontWeight: FontWeight.w500),
                      )
                    ],
                  ),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: YvrElevatedButton(
                  height: 48,
                  padding: EdgeInsets.zero,
                  color: enentJoinStatus == 1 ? null : Color(0xFFF0F2F5),
                  onPressed: () {
                    if (process == 1 && enentJoinStatus == 1) {
                      showDevices(
                          context: context,
                          callback: (String devId) {
                            viewModel.joinEvent();
                          });
                    } else {
                      if (enentJoinStatus == 1) {
                        viewModel.joinEvent();
                      } else if (process == 1) {
                        YvrToast.showToast(
                            YLocal.of(context).huodongyikaishimofat);
                      } else {
                        showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (_) {
                              return CustomDialog(
                                title: YLocal.of(context).social_leave_event,
                                content:
                                    YLocal.of(context).social_leave_event_desc,
                                height: 200,
                                isCancel: true,
                                confirmText: YLocal.of(context).confirm,
                                confirmCallback: () {
                                  viewModel.quitEvent();
                                },
                              );
                            });
                      }
                    }
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      enentJoinStatus == 1
                          ? (process == 1
                              ? SizedBox()
                              : Container(
                                  margin: EdgeInsets.only(right: 5),
                                  child: SvgPicture.asset(
                                    'assets/svg/ic_add.svg',
                                    color: Color(0xFFFFFFFF),
                                    width: 17,
                                    height: 17,
                                  ),
                                ))
                          : Icon(Icons.check, color: Color(0xFF6E7380)),
                      enentJoinStatus == 1
                          ? (process == 1
                              ? Text(YLocal.of(context).zaiVRzhongjiaru,
                                  style: TextStyle(
                                      color: Color(0xFFFFFFFF),
                                      fontSize: 18,
                                      letterSpacing: 1,
                                      fontWeight: FontWeight.w500))
                              : Text(YLocal.of(context).qucanjiaqucenjiaqusa,
                                  style: TextStyle(
                                      color: Color(0xFFFFFFFF),
                                      fontSize: 18,
                                      letterSpacing: 1,
                                      fontWeight: FontWeight.w500)))
                          : Text(YLocal.of(context).yicanjiayicenjiayisa,
                              style: TextStyle(
                                  color: Color(0xFF6E7380),
                                  fontSize: 18,
                                  letterSpacing: 1,
                                  fontWeight: FontWeight.w500))
                    ],
                  ),
                ),
              )
            ]);

        break;
      case 3:
      case 4:
        bottomWidget = YvrElevatedButton(
          height: 48,
          padding: EdgeInsets.zero,
          color: enentJoinStatus == 3 ? null : Color(0xFFF0F2F5),
          onPressed: () {
            if (process == 1 && enentJoinStatus == 3) {
              showDevices(
                  context: context,
                  callback: (String devId) {
                    viewModel.joinEvent();
                  });
            } else {
              if (enentJoinStatus == 3) {
                viewModel.joinEvent();
              } else {
                showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (_) {
                      return CustomDialog(
                        title: YLocal.of(context).social_leave_event,
                        content: YLocal.of(context).social_leave_event_desc,
                        height: 200,
                        isCancel: true,
                        confirmText: YLocal.of(context).confirm,
                        confirmCallback: () {
                          viewModel.quitEvent();
                        },
                      );
                    });
              }
            }
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              enentJoinStatus == 3
                  ? (process == 1
                      ? SizedBox()
                      : Container(
                          margin: EdgeInsets.only(right: 5),
                          child: SvgPicture.asset(
                            'assets/svg/ic_add.svg',
                            color: Color(0xFFFFFFFF),
                            width: 17,
                            height: 17,
                          ),
                        ))
                  : Icon(Icons.check, color: Color(0xFF6E7380)),
              enentJoinStatus == 3
                  ? (process == 1
                      ? Text(YLocal.of(context).zaiVRzhongjiaru,
                          style: TextStyle(
                              color: Color(0xFFFFFFFF),
                              fontSize: 18,
                              letterSpacing: 1,
                              fontWeight: FontWeight.w500))
                      : Text(YLocal.of(context).qucanjiaqucenjiaqusa,
                          style: TextStyle(
                              color: Color(0xFFFFFFFF),
                              fontSize: 18,
                              letterSpacing: 1,
                              fontWeight: FontWeight.w500)))
                  : Text(YLocal.of(context).yicanjiayicenjiayisa,
                      style: TextStyle(
                          color: Color(0xFF6E7380),
                          fontSize: 18,
                          letterSpacing: 1,
                          fontWeight: FontWeight.w500))
            ],
          ),
        );
        break;
      default:
    }
    return Positioned(
      left: 0,
      right: 0,
      bottom: Global.paddingBottom + 10,
      child: Container(
          padding: EdgeInsets.only(left: 16, right: 16, top: 12),
          child: bottomWidget),
    );
  }
}
