import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/model/user_model.dart';
import 'package:yvr_assistant/network/socket.dart';
import 'package:yvr_assistant/pages/social/views/people_cell.dart';
import 'package:yvr_assistant/pages/social/views/plhd_view.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/utils/log.dart';

import '../../../generated/l10n.dart';
import '../../../main.dart';

class BlackListPage extends StatefulWidget {
  BlackListPage({Key key}) : super(key: key);

  @override
  _BlackListPageState createState() => _BlackListPageState();
}

class _BlackListPageState extends State<BlackListPage> with RouteAware {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context));
  }

  @override
  void didPush() {
    super.didPush();
    Log.d('进入黑名单页面');
    SocketManager().sendMessage(json.encode(friendParams));
  }

  @override
  void didPopNext() {
    super.didPopNext();
    Log.d('返回黑名单页面');
    SocketManager().sendMessage(json.encode(friendParams));
  }

  Map<String, dynamic> friendParams = {
    "type": "request",
    "cmd": "reqGetMyBlacks",
  };

  bool _showFriendPlhd = false;
  List<Widget> _friendsCells = [];

  var eventBusSocket;

  @override
  void dispose() {
    super.dispose();
    // 取消订阅
    eventBusSocket.cancel();
    routeObserver.unsubscribe(this);
  }

  @override
  void initState() {
    super.initState();
    // 注册监听器，订阅 eventbus
    eventBusSocket = eventBus.on<EventSocket>().listen((event) {
      switch (event.socketModel.cmd) {
        case "rspGetMyBlacks":
          List<UserModel> friendsModels = event.socketModel.data["users"]
              .map<UserModel>((item) => UserModel.fromJson(item))
              .toList();

          setState(() {
            _showFriendPlhd = true;
            if (friendsModels.length > 0) {
              _friendsCells = _getFriendsList(friendsModels);
              _showFriendPlhd = friendsModels.length == 0;
            }
          });

          Log.d('黑名单好友数量：${friendsModels.length}');

          break;
        default:
      }
    });
  }

  List<Widget> _getFriendsList(List<UserModel> friendsModels) {
    var tempList = friendsModels.map((user) {
      return InkWell(
          onTap: () {
            Navigator.pushNamed(context, "/person_home",
                arguments: {"relation": 2, "actId": user.actId});
          },
          child: PeopleCell(
            people: user,
            isInvite: false,
            isBlacklist: true,
            searchType: SearchFriendsType.FriendForStrange,
          ));
    });
    List<Widget> list = tempList.toList();

    Widget topSpace = InkWell(child: SizedBox(height: 10));
    list.insert(0, topSpace);
    return list;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TopNavbar(title: YLocal.of(context).heimingchanheimingda),
      body: _showFriendPlhd
          ? PagePlhdWidget(
              imagePath: 'assets/images/friend_plhd.svg',
              message: YLocal.of(context).social_no_block)
          : ListView(
              children: _friendsCells,
            ),
    );
  }
}
