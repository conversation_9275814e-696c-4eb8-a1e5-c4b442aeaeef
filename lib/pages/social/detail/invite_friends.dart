import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:yvr_assistant/manager/event_bus.dart';
import 'package:yvr_assistant/model/user_model.dart';
import 'package:yvr_assistant/network/socket.dart';
import 'package:yvr_assistant/pages/social/views/people_cell.dart';
import 'package:yvr_assistant/pages/social/views/plhd_view.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/log.dart';

import '../../../generated/l10n.dart';
import '../../../styles/app_color.dart';

class InviteFriendsPage extends StatefulWidget {
  final arguments;

  InviteFriendsPage({Key key, this.arguments}) : super(key: key);

  @override
  _InviteFriendsPageState createState() => _InviteFriendsPageState();
}

class _InviteFriendsPageState extends State<InviteFriendsPage> {
  List friendsIds = [];
  bool _showFriendPlhd = false;
  List<Widget> _friendsCells = [];
  Map<String, dynamic> friendParams = {
    "type": "request",
    "cmd": "reqGetMyFriends",
  };
  var eventBusSocket;

  @override
  void dispose() {
    super.dispose();
    // 取消订阅
    eventBusSocket.cancel();
  }

  @override
  void initState() {
    super.initState();

    SocketManager().sendMessage(json.encode(friendParams));

    // 注册监听器，订阅 eventbus
    eventBusSocket = eventBus.on<EventSocket>().listen((event) {
      switch (event.socketModel.cmd) {
        case "rspGetMyFriends":
          List<UserModel> friendsModels = event.socketModel.data["users"]
              .map<UserModel>((item) => UserModel.fromJson(item))
              .toList();

          setState(() {
            _showFriendPlhd = true;
            if (friendsModels.length > 0) {
              _friendsCells = _getFriendsList(friendsModels);
              _showFriendPlhd = friendsModels.length == 0;
            }
          });

          Log.d('好友数量：${friendsModels.length}');

          break;
        case 'rspInviteEvent':
          YvrToast.showToast(YLocal.of(context).yichenggongfachuyaoq);
          Navigator.pop(context);
          break;
        default:
      }
    });
  }

  List<Widget> _getFriendsList(List<UserModel> friendsModels) {
    List joninUserIds = widget.arguments['userIdList'];
    debugPrint("joninUserIds:$joninUserIds");
    var tempList = friendsModels.map((user) {
      return PeopleCell(
        people: user,
        isInvite: true,
        isBlacklist: false,
        searchType: SearchFriendsType.FriendForStrange,
        isJoin: joninUserIds.contains(user.actId),
        personClick: (bool isSelected) {
          setState(() {
            if (isSelected) {
              friendsIds.add(user.actId);
            } else {
              if (friendsIds.contains(user.actId)) {
                friendsIds.remove(user.actId);
              }
            }
            Log.d("已选好友ID：$friendsIds");
          });
        },
      );
    });
    List<Widget> list = tempList.toList();
    return list;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(YLocal.of(context).yaoqinghaoyou),
          actions: [submitButtonWidget()],
          leading: CustomBackButton(),
        ),
        body: _showFriendPlhd
            ? PagePlhdWidget(
                iconData: Icons.people,
                message: YLocal.of(context).zanmohaoyouzanwuhaoy)
            : ListView(
                children: [
                  SizedBox(
                    height: 10,
                  ),
                  Column(
                    children: _friendsCells,
                  )
                ],
              ));
  }

  Widget submitButtonWidget() {
    return Container(
      margin: EdgeInsets.only(right: 16),
      alignment: AlignmentDirectional.center,
      child: YvrElevatedButton(
          width: 48,
          height: 24,
          padding: EdgeInsets.zero,
          radius: 16,
          child: Text(
            YLocal.of(context).queding,
            style: TextStyle(
                color: (friendsIds.length > 0) ? null : Color(0xff6E7380),
                fontSize: 14),
            textAlign: TextAlign.center,
          ),
          color: (friendsIds.length > 0) ? null : AppColors.buttonDisable,
          onPressed: () {
            //点击方法不实现 按钮颜色无法改变
            if (friendsIds.length > 0) {
              reqInviteEvent();
            }
          }),
    );
  }

  reqInviteEvent() {
    Log.d("发出邀请");

    Map<String, dynamic> friendParams = {
      "type": "request",
      "cmd": "reqInviteEvent",
      "eventId": widget.arguments["id"],
      "fdIds": friendsIds
    };
    SocketManager().sendMessage(json.encode(friendParams));
  }
}
