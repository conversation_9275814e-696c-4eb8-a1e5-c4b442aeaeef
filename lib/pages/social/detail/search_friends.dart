import 'package:badges/badges.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:yvr_assistant/view_model/friends_vmodel.dart';

import '../../../generated/l10n.dart';
import '../../../manager/resource_mananger.dart';
import '../../../provider/view_state_widget.dart';
import '../../../public_ui/widget/image_view.dart';
import '../../../public_ui/widget/refresh_layout.dart';
import '../../../styles/app_color.dart';
import '../../../utils/yvr_utils.dart';
import '../../../view_model/hall_friends_vmodel.dart';
import '../../../view_model/unread_vmodel.dart';

class SearchFriendsPage extends StatefulWidget {
  final arguments;

  SearchFriendsPage({Key key, @required this.arguments}) : super(key: key);

  @override
  _SearchPageState createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchFriendsPage> {
  TextEditingController _searchController = TextEditingController();
  HallSearchFriendsVModel _searchFriendsVModel;
  bool _inSearch = true;

  @override
  void initState() {
    super.initState();
    _searchFriendsVModel = HallSearchFriendsVModel();
    _searchController.addListener(onSearchTextChanged);
  }

  void onSearchTextChanged() {
    _searchFriendsVModel.loadBySearchKey(_searchController.text.trim());
  }

  @override
  void dispose() {
    _searchController.removeListener(onSearchTextChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          title: Text(
            YLocal.of(context).tianjiahaoyou,
          ),
          actions: [
            Consumer<UnreadVModel>(builder: (context, value, child) {
              return Container(
                margin: EdgeInsets.only(right: 20),
                child: InkWell(
                  child: Badge(
                    animationType: BadgeAnimationType.fade,
                    showBadge: value.unreadNum != 0,
                    badgeContent: Text(
                      value.unreadNum.toString(),
                      style: TextStyle(fontSize: 8, color: Colors.white),
                    ),
                    position: BadgePosition.topEnd(top: 12, end: -3),
                    child: Icon(
                      IconFonts.iconNoti,
                    ),
                  ),
                  onTap: () {
                    Navigator.pushNamed(context, '/message');
                  },
                ),
              );
            }),
          ],
        ),
        body: Column(
          children: [
            SizedBox(
              height: 15,
            ),
            Container(
              margin: EdgeInsets.only(left: 16, top: 0, right: 0, bottom: 25),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      autofocus: true,
                      textAlignVertical: TextAlignVertical.center,
                      buildCounter: (
                        BuildContext context, {
                        @required int currentLength,
                        @required int maxLength,
                        @required bool isFocused,
                      }) {
                        return null;
                      },
                      cursorColor: Color(0xff4F7FFE),
                      cursorWidth: 1,
                      cursorHeight: 14,
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                        color: Color(0xff2c2e33),
                      ),
                      decoration: InputDecoration(
                        contentPadding:
                            EdgeInsets.only(top: 7, bottom: 9, right: 15),
                        prefixIcon: Container(
                          margin: EdgeInsets.only(left: 15, right: 12),
                          child: SvgPicture.asset(
                            'assets/svg/ic_search.svg',
                            width: 18,
                            height: 18,
                            color: Color(0xff8E94A6),
                          ),
                        ),
                        suffix: InkWell(
                          child: Container(
                            padding: EdgeInsets.only(top: 3),
                            child: SvgPicture.asset(
                              'assets/svg/ic_close.svg',
                              color: AppColors.textSub,
                              width: 14,
                              height: 14,
                            ),
                          ),
                          onTap: () {
                            _searchController.clear();
                          },
                        ),
                        prefixIconConstraints: BoxConstraints(),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.all(
                            Radius.circular(20),
                          ),
                          borderSide: BorderSide.none,
                        ),
                        enabledBorder: OutlineInputBorder(
                          /*边角*/
                          borderRadius: BorderRadius.all(
                            Radius.circular(20),
                          ),
                          borderSide: BorderSide.none,
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.all(
                            Radius.circular(20),
                          ),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Color(0xffF5F7FA),
                        isDense: true,
                        isCollapsed: true,
                        hintText: YLocal.of(context).sousuonichenheIDhaot,
                        hintStyle: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                          color: Color(0xff8e94a6),
                        ),
                      ),
                    ),
                  ),
                  !_inSearch
                      ? SizedBox(
                          width: 16,
                        )
                      : GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 16, vertical: 6),
                            child: Text(
                              YLocal.of(context).quxiao,
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                                height: 1,
                                color: AppColors.textSub,
                              ),
                            ),
                          ),
                        )
                ],
              ),
            ),
            Expanded(
              child:
                  Consumer<FriendVModel>(builder: (context, friendVM, child) {
                return SimpleRefreshList<HallSearchFriendsVModel>(
                  model: _searchFriendsVModel,
                  builder: (context, model) {
                    if (model.model == null) {
                      ///还没有搜索
                      return SizedBox();
                    }

                    return ListView.separated(
                        padding: EdgeInsets.only(left: 16, right: 16),
                        itemBuilder: (context, index) {
                          return buildSearchFriends(model, index, friendVM);
                        },
                        separatorBuilder: (context, index) {
                          return SizedBox(
                            height: 16,
                          );
                        },
                        itemCount: model.model.users.length);
                  },
                  emptyBuilder: (context, model) {
                    return ViewStateSearchEmptyWidget();
                  },
                );
              }),
            ),
          ],
        ));
  }

  Widget buildSearchFriends(
      HallSearchFriendsVModel model, int index, FriendVModel friendVModel) {
    var friend = model.model.users[index];
    return InkWell(
      onTap: () {
        onAvatarTap(context, friend.actId);
      },
      child: Container(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              child: OnlineAvatarImageWidget(
                friend.avatar,
                width: 60,
                height: 60,
                sex: friend.sex,
                online: 1,
              ),
              margin: EdgeInsets.only(right: 10),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 9,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      friend.nick,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 16,
                        height: 1.25,
                        color: AppColors.textTitle,
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(top: 5),
                      child: RichText(
                        text: TextSpan(
                          style: TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: 12,
                            height: 1.16667,
                            color: AppColors.textSub,
                          ),
                          children: [
                            TextSpan(text: YvrUtils.getSexText(friend.sex)),
                            WidgetSpan(
                              child: SizedBox(
                                width: 10,
                              ),
                            ),
                            TextSpan(text: 'ID:${friend.actId}'),
                          ],
                        ),
                      ),
                    ),
                  ],
                )
              ],
            ),
            Spacer(),
            Container(
              margin: EdgeInsets.only(left: 12, top: 22),
              child: friendVModel.isRequest(friend.actId)
                  ? Text(
                      YLocal.of(context).yifasongqingqiuyifei,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                        height: 1.16667,
                        color: AppColors.textSub,
                      ),
                    )
                  : GestureDetector(
                      onTap: () {
                        friendVModel.requestFriend(friend.actId);
                      },
                      child: SvgPicture.asset(
                        "assets/svg/add_friends.svg",
                        width: 25,
                        height: 24,
                        color: Color(0xff6E7380),
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
