import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:yvr_assistant/base/lifecycle_state_mixin.dart';
import 'package:yvr_assistant/pages/tabbar/top_navbar.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';
import 'package:yvr_assistant/public_ui/widget/image_view.dart';
import 'package:yvr_assistant/public_ui/widget/refresh_layout.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/view_model/friends_vmodel.dart';
import 'package:yvr_assistant/view_model/personal_home_vmodel.dart';

import '../../../generated/l10n.dart';
import '../../../manager/global.dart';
import '../../../manager/resource_mananger.dart';
import '../../../network/request.dart';
import '../../../public_ui/sliver/sliver_grid.dart';
import '../../../public_ui/widget/personal_home_flexible_space_bar.dart';
import '../../../styles/app_color.dart';
import '../../../view_model/user_vmodel.dart';
import '../../../view_model/vmodel_mixin.dart';
import '../../../public_ui/widget/image_browser.dart';

class PersonalHomePage extends StatefulWidget {
  final arguments;

  const PersonalHomePage({Key key, this.arguments}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _PersonalHomePageState();
}

class _PersonalHomePageState extends LifecycleState<PersonalHomePage> {
  static const double kCountHeight = 90;
  static const double kRoundedHeight = 20;

  double get contentHeight =>
      _viewModel.mottoHeight + kCountHeight + kRoundedHeight;

  static const TextStyle kTextStyle = const TextStyle(
    fontWeight: FontWeight.w400,
    fontSize: 13,
    height: 1.61538,
    color: Color(0xffffffff),
  );

  static const double kTextPadding = 20;

  PersonalHomeVModel _viewModel;

  @override
  void initState() {
    super.initState();

    int actId;

    int actIdParam = widget.arguments['actId'];
    if (actIdParam == null ||
        actIdParam == DBUtil.instance.userBox.get(kActId)) {
      actId = null;
    } else {
      actId = actIdParam;
    }

    _viewModel = PersonalHomeVModel(
      Global.screenWidth - kTextPadding,
      kTextStyle,
      actId: actId,
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<FriendVModel>.value(
      value: FriendVModel.getInstance(),
      builder: (context, child) {
        return Consumer<FriendVModel>(builder: (context, friendVM, child) {
          return Scaffold(
            body: SimpleRefreshList<PersonalHomeVModel>(
                model: _viewModel,
                builder: (context, viewModel) {
                  int sex = viewModel.model.sex;
                  String background = viewModel.model.bgPic;
                  return CustomScrollView(
                    slivers: <Widget>[
                      SliverAppBar(
                        leading: CustomBackButton(
                          color: Colors.white,
                        ),
                        actions: [
                          if (viewModel.friendOptionVisible())
                            buildFriendsPopupMenu(viewModel.actId, friendVM),
                          if (viewModel.blackListOptionVisible())
                            buildBlacklistPopupMenu(),
                          if (viewModel.removeBlackListVisible())
                            buildRemoveBlacklistPopupMenu(
                                viewModel.actId, friendVM),
                        ],
                        pinned: true,
                        expandedHeight: 180 + contentHeight,
                        centerTitle: true,
                        flexibleSpace: GestureDetector(
                          onTap: () {
                            onBackgroundTap(viewModel.model.bgPic);
                          },
                          child: PersonalHomeFlexibleSpaceBar(
                            background: background == null || background.isEmpty
                                ? Image.asset(
                                    'assets/images/profile_bg.png',
                                    fit: BoxFit.cover,
                                    width: double.infinity,
                                    height: double.infinity,
                                  )
                                : NetworkImageWidget(
                                    background,
                                    fit: BoxFit.cover,
                                    width: double.infinity,
                                    height: double.infinity,
                                  ),
                            nick: viewModel.model.nick ?? '',
                            avatar: GestureDetector(
                              child: OnlineAvatarImageWidget(
                                viewModel.model.avatar,
                                online: viewModel.isOnline(),
                                sex: viewModel.model.sex,
                              ),
                              onTap: () {
                                if (viewModel.model.avatar != null &&
                                    viewModel.model.avatar.isNotEmpty) {
                                  Navigator.of(context).push(
                                    ImageBrowserPageRoute(
                                      builder: (BuildContext context) {
                                        return NetworkImageBrowserPage(
                                          images: [viewModel.model.avatar],
                                          index: 0,
                                        );
                                      },
                                    ),
                                  );
                                }
                              },
                            ),
                            titleBuilder: (context, t) {
                              return ConstrainedBox(
                                constraints: BoxConstraints(minHeight: 35),
                                child: Row(
                                  children: [
                                    viewModel.isSelf()
                                        ? SizedBox()
                                        : Container(
                                            margin: EdgeInsets.only(left: 8),
                                            child: sex == 1
                                                ? Image.asset(
                                                    'assets/images/sex_man.png',
                                                    width: 20,
                                                    height: 20,
                                                  )
                                                : sex == 2
                                                    ? Image.asset(
                                                        'assets/images/sex_women.png',
                                                        width: 20,
                                                        height: 20,
                                                      )
                                                    : SizedBox(),
                                          ),
                                    SizedBox(
                                      width: 26,
                                    ),
                                    viewModel.addFriendVisible() &&
                                            t >= 1.0 &&
                                            !friendVM.isRequest(viewModel.actId)
                                        ? buildAddFriends(viewModel.actId,
                                            padding: EdgeInsets.symmetric(
                                                vertical: 7, horizontal: 6),
                                            friendViewModel: friendVM)
                                        : SizedBox(),
                                  ],
                                ),
                              );
                            },
                            content: Column(
                              children: [
                                Container(
                                  alignment: AlignmentDirectional.centerStart,
                                  padding: EdgeInsets.symmetric(
                                      horizontal: kTextPadding),
                                  height: viewModel.mottoHeight,
                                  child: Text(
                                    viewModel.model.motto ?? '',
                                    style: kTextStyle,
                                  ),
                                ),
                                Container(
                                  padding: EdgeInsets.only(
                                    left: 20,
                                    top: 28,
                                    right: 20,
                                    bottom: 20,
                                  ),
                                  height: kCountHeight,
                                  child: Row(
                                    children: [
                                      Column(
                                        children: [
                                          Text(
                                            viewModel.model.social.friend
                                                    ?.toString() ??
                                                '0',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w500,
                                              fontSize: 19,
                                              height: 1,
                                              color: Color(0xffffffff),
                                            ),
                                          ),
                                          SizedBox(
                                            height: 4,
                                          ),
                                          Text(
                                            YLocal.of(context).haoyou,
                                            style: TextStyle(
                                              fontWeight: FontWeight.w400,
                                              fontSize: 13,
                                              height: 1,
                                              color: Color(0x7fffffff),
                                            ),
                                          ),
                                        ],
                                      ),
                                      if (!viewModel.isSelf())
                                        Container(
                                          margin: EdgeInsets.only(left: 30),
                                          child: Column(
                                            children: [
                                              Text(
                                                viewModel.model.social.mutualfd
                                                        ?.toString() ??
                                                    '0',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 19,
                                                  height: 1,
                                                  color: Color(0xffffffff),
                                                ),
                                              ),
                                              SizedBox(
                                                height: 4,
                                              ),
                                              Text(
                                                YLocal.of(context)
                                                    .gongtonghaoyou,
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w400,
                                                  fontSize: 13,
                                                  height: 1,
                                                  color: Color(0x7fffffff),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      SizedBox(
                                        width: 30,
                                      ),
                                      Column(
                                        children: [
                                          Text(
                                            viewModel.model.social.app
                                                    ?.toString() ??
                                                '0',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w500,
                                              fontSize: 19,
                                              height: 1,
                                              color: Color(0xffffffff),
                                            ),
                                          ),
                                          SizedBox(
                                            height: 4,
                                          ),
                                          Text(
                                            YLocal.of(context)
                                                .youhushuyouhuishuyou,
                                            style: TextStyle(
                                              fontWeight: FontWeight.w400,
                                              fontSize: 13,
                                              height: 1,
                                              color: Color(0x7fffffff),
                                            ),
                                          ),
                                        ],
                                      ),
                                      Spacer(),
                                      if (viewModel.editVisible())
                                        TextButton(
                                          onPressed: () {
                                            Navigator.pushNamed(
                                              context,
                                              "/user",
                                              arguments: {
                                                "actId": DBUtil.instance.userBox
                                                    .get(kActId)
                                              },
                                            );
                                          },
                                          child: Row(
                                            children: [
                                              SvgPicture.asset(
                                                "assets/svg/information_edit.svg",
                                                width: 9.5,
                                                height: 9.5,
                                              ),
                                              SizedBox(
                                                width: 6,
                                              ),
                                              Text(
                                                YLocal.of(context).bianjiziliao,
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 13,
                                                  height: 1,
                                                  color: Color(0xffffffff),
                                                ),
                                              ),
                                            ],
                                          ),
                                          style: ButtonStyle(
                                            // fixedSize: ButtonStyleButton.allOrNull<Size>(
                                            //   Size(95, 30),
                                            // ),
                                            minimumSize:
                                                ButtonStyleButton.allOrNull(
                                                    Size(0, 0)),
                                            padding:
                                                ButtonStyleButton.allOrNull(
                                              EdgeInsets.symmetric(
                                                  horizontal: 9.5,
                                                  vertical: 9.5),
                                            ),
                                            backgroundColor:
                                                ButtonStyleButton.allOrNull(
                                              Color.fromRGBO(0, 0, 0, 0.2),
                                            ),
                                            textStyle:
                                                ButtonStyleButton.allOrNull(
                                              TextStyle(
                                                fontWeight: FontWeight.w500,
                                                fontSize: 12,
                                                height: 1,
                                                color: Color(0xffffffff),
                                              ),
                                            ),
                                          ),
                                        ),
                                      if (viewModel.addFriendVisible())
                                        buildAddFriends(
                                          viewModel.actId,
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 9, vertical: 9),
                                          friendViewModel: !friendVM
                                                  .isRequest(viewModel.actId)
                                              ? friendVM
                                              : null,
                                        ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            idText: 'ID:${viewModel.model.actId}',
                            contentHeight: contentHeight,
                            // title: Container(color: ,),
                          ),
                        ),
                        bottom: PreferredSize(
                          preferredSize: Size.fromHeight(kRoundedHeight),
                          child: Container(
                            height: kRoundedHeight,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(20),
                                topRight: Radius.circular(20),
                              ),
                              color: Color(0xffFFFFFF),
                              boxShadow: [
                                BoxShadow(
                                  color: Color(0xffFFFFFF), // 阴影的颜色
                                  // offset: Offset(0, 0), // 阴影与容器的距离
                                  blurRadius: 0, // 高斯的标准偏差与盒子的形状卷积。
                                  spreadRadius: 1, // 在应用模糊之前，框应该膨胀的量。
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      SliverPadding(
                        padding: EdgeInsets.symmetric(horizontal: 20),
                        sliver: SliverGrid(
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCountExtra(
                            crossAxisCount: 3,
                            mainAxisSpacing: 10.0,
                            crossAxisSpacing: 16,
                            childAspectRatio: 1.0,
                            extraMainAxisExtent: 27,
                            // mainAxisExtent: 500
                          ),
                          delegate: SliverChildBuilderDelegate(
                            (BuildContext context, int index) {
                              var app = viewModel.model.apps[index];
                              return LayoutBuilder(builder:
                                  (BuildContext context,
                                      BoxConstraints constraints) {
                                return InkWell(
                                  onTap: () {
                                    Navigator.pushNamed(context, '/prod',
                                        arguments: {
                                          "id": app.appId,
                                        });
                                  },
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: constraints.maxWidth,
                                        height: constraints.maxWidth,
                                        child: Stack(
                                          children: [
                                            Positioned.fill(
                                              child: ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(
                                                        10.00),
                                                child: NetworkImageWidget(
                                                  app.scover,
                                                ),
                                              ),
                                            ),
                                            Positioned(
                                              left: 0,
                                              bottom: 0,
                                              child: Container(
                                                padding: EdgeInsets.symmetric(
                                                  horizontal: 8.5,
                                                  vertical: 5,
                                                ),
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          7.000000),
                                                  color: Color(0xe5789be7),
                                                ),
                                                child: Row(
                                                  children: [
                                                    SvgPicture.asset(
                                                      'assets/svg/time_clock.svg',
                                                      width: 8.5,
                                                      height: 8.5,
                                                      color: Color.fromRGBO(
                                                          255, 255, 255, 0.6),
                                                    ),
                                                    SizedBox(
                                                      width: 2,
                                                    ),
                                                    Text(
                                                      viewModel
                                                          .appDurations[index],
                                                      style: TextStyle(
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        fontSize: 12,
                                                        height: 1,
                                                        color:
                                                            Color(0xffe8e8e8),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Spacer(),
                                      Text(
                                        app.appName,
                                        textAlign: TextAlign.start,
                                        style: TextStyle(
                                          overflow: TextOverflow.ellipsis,
                                          fontWeight: FontWeight.w400,
                                          fontSize: 15,
                                          height: 1,
                                          color: AppColors.textTitle,
                                        ),
                                      )
                                    ],
                                  ),
                                );
                              });
                            },
                            childCount: viewModel.model.apps?.length ?? 0,
                          ),
                        ),
                      ),
                      SliverToBoxAdapter(
                        child: Container(
                          margin: EdgeInsets.only(
                              top: 40,
                              bottom: Global.screenHeight - contentHeight),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                height: 0.5,
                                width: 60,
                                margin: EdgeInsets.only(right: 8),
                                color: Color(0xffEEEEEE),
                              ),
                              Text(
                                YLocal.of(context).zanmogengduodongtaiz,
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontSize: 12,
                                  height: 1,
                                  color: AppColors.textWeak,
                                ),
                              ),
                              Container(
                                height: 0.5,
                                width: 60,
                                margin: EdgeInsets.only(left: 8),
                                color: Color(0xffEEEEEE),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  );
                }),
          );
        });
      },
    );
  }

  Future<void> onBackgroundTap(String old) async {
    if (_viewModel.isSelf()) {
      if (old != null && old.isNotEmpty) {
        // ignore: unused_local_variable
        int result = await Navigator.of(context).push(
          ImageBrowserPageRoute(
            builder: (context) {
              return BackgroundImageBrowserPage(
                image: old,
                upload: upload,
              );
            },
          ),
        );
      } else {
        int result = await doSelectAndCropImage(context, upload);
        if (result == 1) {
          _viewModel.refreshQuietly();
        }
      }
    } else if (old != null && old.isNotEmpty) {
      await Navigator.of(context).push(
        ImageBrowserPageRoute(
          builder: (BuildContext context) {
            return NetworkImageBrowserPage(
              images: [old],
              index: 0,
            );
          },
        ),
      );
    }
  }

  static Future<String> upload(Uint8List data, String fileName) async {
    var result =
        await YvrRequests.uploadPersonalBackgroundImage(data, fileName);
    switch (result.errCode) {
      case 0:
        return result.data;
      case 10540:
        throw UploadImageException(YLocal.current.nindebeijingtupiansh);
      default:
        throw UploadImageException(YLocal.current.shangchuanshibaishan);
    }
  }

  Widget buildAddFriends(int actId,
      {@required EdgeInsets padding, FriendVModel friendViewModel}) {
    if (friendViewModel != null) {
      return YvrElevatedButton(
        padding: EdgeInsets.symmetric(horizontal: 10),
        height: 32,
        onPressed: () {
          friendViewModel.requestFriend(actId);
        },
        child: Row(
          children: [
            SvgPicture.asset(
              "assets/svg/add_friends2.svg",
              width: 12,
              height: 12,
            ),
            SizedBox(
              width: 4,
            ),
            Text(
              YLocal.of(context).tianjiahaoyou,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 12,
                height: 1,
                color: Color(0xffffffff),
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        padding: EdgeInsets.symmetric(
          horizontal: 10,
          vertical: 10,
        ),
        decoration: BoxDecoration(
            color: Color.fromRGBO(0, 0, 0, 0.2),
            borderRadius: BorderRadius.circular(40)),
        child: Row(
          children: [
            SvgPicture.asset(
              'assets/svg/add_friend_requested.svg',
              width: 12,
              height: 12,
            ),
            SizedBox(
              width: 4,
            ),
            Text(
              YLocal.of(context).yifasongqingqiuyifei,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
                height: 1,
                color: Color(0xffffffff),
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget buildBlacklistPopupMenu() {
    return buildPopupMenu(context, <PopupMenuEntry<int>>[
      PopupMenuItem<int>(
        value: 0,
        height: 44,
        child: Container(
          padding: EdgeInsets.only(left: 14),
          height: 44,
          width: 136,
          alignment: AlignmentDirectional.centerStart,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(
              Radius.circular(8),
            ),
            color: Color(0xffFFFFFF),
          ),
          child: Row(
            children: [
              SvgPicture.asset(
                'assets/svg/blacklist.svg',
                width: 16,
                height: 16,
                color: Color(0xff2C2E33),
              ),
              SizedBox(
                width: 7,
              ),
              Text(
                YLocal.of(context).wodeheimingdan,
                style: TextStyle(
                  color: Color(0xff2C2E33),
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    ], (value) {
      switch (value) {
        case 0:
          Navigator.pushNamed(context, '/blacklist');
          break;
      }
    }, offset: Offset(-25, 0));
  }

  Widget buildFriendsPopupMenu(int actId, FriendVModel friendViewModel) {
    return buildPopupMenu(context, <PopupMenuEntry<int>>[
      PopupMenuItem<int>(
        value: 1,
        height: 44,
        child: Container(
          padding: EdgeInsets.only(left: 16),
          height: 44,
          width: 115,
          alignment: AlignmentDirectional.centerStart,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(8),
              topLeft: Radius.circular(8),
            ),
            color: Colors.white,
          ),
          child: Text(
            YLocal.of(context).shanchu,
            style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 16,
              letterSpacing: 2,
              color: AppColors.textTitle,
            ),
          ),
        ),
      ),
      PopupMenuItem<int>(
        value: 2,
        height: 44,
        child: Container(
          height: 44,
          width: 115,
          padding: EdgeInsets.only(left: 16),
          alignment: AlignmentDirectional.centerStart,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(8),
              bottomRight: Radius.circular(8),
            ),
            color: Colors.white,
          ),
          child: Text(
            YLocal.of(context).social_block,
            style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 16,
              letterSpacing: 2,
              color: AppColors.textTitle,
            ),
          ),
        ),
      ),
    ], (value) {
      switch (value) {
        case 1:
          friendViewModel.removeFriend(actId, onSuccess: () {
            _viewModel.refreshQuietly();
          });
          break;
        case 2:
          friendViewModel.blackFriend(actId, onSuccess: () {
            _viewModel.refreshQuietly();
          });
          break;
      }
    });
  }

  Widget buildRemoveBlacklistPopupMenu(
      int actId, FriendVModel friendViewModel) {
    return buildPopupMenu(context, <PopupMenuEntry<int>>[
      PopupMenuItem<int>(
        value: 1,
        height: 44,
        child: Container(
          padding: EdgeInsets.only(left: 16),
          height: 44,
          width: 115,
          alignment: AlignmentDirectional.centerStart,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(8)),
            color: Colors.white,
          ),
          child: Text(
            YLocal.of(context).yichuheimingdan,
            style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 16,
              letterSpacing: 2,
              color: AppColors.textTitle,
            ),
          ),
        ),
      ),
    ], (value) {
      switch (value) {
        case 1:
          friendViewModel.restoreFriend(actId, onSuccess: () {
            _viewModel.refreshQuietly();
          });
          break;
      }
    });
  }

  static Widget buildPopupMenu(BuildContext context,
      List<PopupMenuEntry<int>> items, PopupMenuItemSelected<int> onSelected,
      {Offset offset = const Offset(0, 0)}) {
    return PopupMenuButton<int>(
      color: Colors.transparent,
      icon: Icon(
        IconFonts.iconMore,
        color: Color(0xffD9D9D9),
        size: 22,
      ),
      offset: offset,
      elevation: 0,
      itemBuilder: (context) {
        return items;
      },
      onSelected: onSelected,
    );
  }

  @override
  void onRefresh() {
    _viewModel.refreshQuietly();
  }
}
