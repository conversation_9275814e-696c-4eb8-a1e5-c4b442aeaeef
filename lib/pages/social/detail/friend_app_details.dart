import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:yvr_assistant/public_ui/widget/rich_text.dart';
import '../../../generated/l10n.dart';
import '../../../public_ui/widget/image_view.dart';
import '../../../public_ui/widget/refresh_layout.dart';
import '../../../styles/app_color.dart';
import '../../../utils/data_record.dart';
import '../../../view_model/friends_vmodel.dart';
import '../../../view_model/hall_friends_vmodel.dart';
import '../../tabbar/top_navbar.dart';

class FriendAppDetailsPage extends StatefulWidget {
  final arguments;

  const FriendAppDetailsPage({Key key, @required this.arguments})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _FriendAppDetailsPageState();
  }
}

class _FriendAppDetailsPageState extends State<FriendAppDetailsPage> {
  AppFriendsVModel _model;

  @override
  void initState() {
    super.initState();
    final int id = widget.arguments['id'];
    String image = widget.arguments["image"] as String;
    String name = widget.arguments["name"] as String;
    _model = AppFriendsVModel(id: id, image: image, name: name);
    DataRecord().saveData(
      eventId: 'assistant_social_findFriendFromAppDetail_0_0_page_view',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _model.name,
        ),
        leading: CustomBackButton(),
      ),
      body: Consumer<FriendVModel>(
        builder: (context, friendVM, child) {
          return SimpleRefreshList<AppFriendsVModel>(
            model: _model,
            builder: (context, model) {
              double width = MediaQuery.of(context).size.width;
              double height = width / (764 / 280);
              return CustomScrollView(
                slivers: <Widget>[
                  SliverAppBar(
                    backgroundColor: Colors.white,
                    toolbarHeight: height,
                    pinned: true,
                    automaticallyImplyLeading: false,
                    titleSpacing: 0,
                    title: Container(
                      width: width,
                      height: height,
                      alignment: AlignmentDirectional.centerStart,
                      child: Stack(
                        children: [
                          Positioned.fill(
                            child: NetworkImageWidget(model.image),
                          ),
                          Positioned(
                            left: 0,
                            right: 0,
                            bottom: 0,
                            height: 35,
                            child: Container(
                              alignment: AlignmentDirectional.centerEnd,
                              color: Color(0x9917191b),
                              padding: EdgeInsets.only(right: 16),
                              child: IntlRichText1(
                                intlTextBuilder:
                                    YLocal.of(context).dangqianzaixianrensh,
                                defaultStyle: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 12,
                                  color: Color(0xffd8d8d8),
                                ),
                                param: model.totalListCount ?? 0,
                                paramStyle: TextStyle(
                                  fontSize: 12.5,
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  SliverPadding(
                    sliver: SliverAppBar(
                      toolbarHeight: 56.0 + 14.0,
                      pinned: true,
                      automaticallyImplyLeading: false,
                      title: Container(
                        margin: EdgeInsets.only(left: 6, top: 7),
                        alignment: AlignmentDirectional.centerStart,
                        child: Text(
                          YLocal.of(context).faxiantongxingcuhaoy,
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 18,
                            height: 1.11111,
                            color: AppColors.textTitle,
                          ),
                        ),
                      ),
                    ),
                    padding: EdgeInsets.zero,
                  ),
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (BuildContext context, int index) {
                        return buildFriendsItem(model, index, friendVM);
                      },
                      childCount: model.list.length,
                    ),
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }

  Widget buildFriendsItem(
      AppFriendsVModel model, int index, FriendVModel friendVModel) {
    var friend = model.list[index];
    return InkWell(
      onTap: () {
        DataRecord().saveData(
          eventId:
              'assistant_social_findFriendFromAppDetail_friendsList_avatar_pit_click',
        );
        onAvatarTap(context, friend.actId);
      },
      child: Container(
        margin: EdgeInsets.only(left: 16, right: 16, top: 0, bottom: 35),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              child: OnlineAvatarImageWidget(
                friend.avatar,
                width: 60,
                height: 60,
                sex: friend.sex,
                online: 1,
              ),
              margin: EdgeInsets.only(right: 6),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 9,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      friend.nick,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 16,
                        height: 1.25,
                        color: AppColors.textTitle,
                      ),
                    ),
                    if (friend.duration != null && friend.duration > 0)
                      Container(
                        margin: EdgeInsets.only(top: 10),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(7.000000),
                          color: Color(0xff4F7FFE).withOpacity(0.1),
                        ),
                        padding: EdgeInsets.only(
                            left: 15, top: 9, right: 15, bottom: 13),
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              "assets/svg/gamepad.svg",
                              width: 20,
                              height: 20,
                              color: Color(0xff6E7380),
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            ValueListenableBuilder<int>(
                              valueListenable: ValueNotifier(friend.duration),
                              builder: (context, value, child) {
                                IntlTextBuilder unit;
                                int num;
                                Duration time = Duration(minutes: value ?? 0);
                                num = time.inMinutes;
                                if (num < 30) {
                                  num = 30;
                                  unit = YLocal.of(context).yiwanNfenzhong;
                                } else {
                                  num = max<int>(time.inHours, 1);
                                  unit = YLocal.of(context).yiwanNxiaoshi;
                                }
                                return IntlRichText1(
                                  intlTextBuilder: unit,
                                  defaultStyle: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 14,
                                    color: Color(0xffb0b3b8),
                                  ),
                                  param: num,
                                  paramStyle: TextStyle(
                                    color: Color(0xff6E7380),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                  ],
                )
              ],
            ),
            Spacer(),
            friendVModel.isRequest(friend.actId)
                ? Text(
                    YLocal.of(context).yifasongqingqiuyifei,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      height: 1.28571,
                      letterSpacing: 0.5,
                      color: Color(0xff767880),
                    ),
                  )
                : GestureDetector(
                    onTap: () {
                      DataRecord().saveData(
                        eventId:
                            'assistant_social_findFriendFromAppDetail_friendsList_addFriend_pit_click',
                      );
                      friendVModel.requestFriend(friend.actId);
                    },
                    child: Container(
                      margin: EdgeInsets.only(left: 12, top: 22),
                      child: SvgPicture.asset(
                        "assets/svg/add_friends.svg",
                        width: 25,
                        height: 24,
                        color: Color(0xff6E7380),
                      ),
                    ),
                  ),
          ],
        ),
      ),
    );
  }
}
