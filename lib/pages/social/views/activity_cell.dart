import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/model/event_model.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/styles/app_divider.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';
import '../../../public_ui/widget/avatar_group.dart';
import '../../../public_ui/widget/image_view.dart';
import '../../../public_ui/widget/number_widget.dart';
import '../../../styles/app_color.dart';
import '../../../utils/date_time_utils.dart';

class EventListItem extends StatelessWidget {
  final String eventType;
  final String coverImage;
  final String eventName;
  final String organizerName;
  final List<String> focusesPortraits;
  final int falseNum;
  final int realNum;
  final int commentNum;
  final Widget buttonWidget;
  final DateTimeDiffStatusData status;

  const EventListItem(
    this.eventType,
    this.coverImage,
    this.eventName,
    this.organizerName,
    this.focusesPortraits,
    this.falseNum,
    this.realNum,
    this.commentNum,
    this.buttonWidget,
    this.status, {
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return buildListItem2(
        context,
        eventType,
        coverImage,
        eventName,
        organizerName,
        focusesPortraits,
        falseNum,
        realNum,
        commentNum,
        buttonWidget,
        status);
  }

  static Widget buildListItem2(
      BuildContext context,
      String eventType,
      String coverImage,
      String eventName,
      String organizerName,
      List<String> focusesPortraits,
      int falseNum,
      int realNum,
      int commentNum,
      Widget buttonWidget,
      DateTimeDiffStatusData status) {
    bool isPersonal = eventType == 'personal';
    bool isOfficial = eventType == 'official';
    if (falseNum == null) {
      falseNum = 0;
    }
    if (realNum == null) {
      realNum = 0;
    }
    return Container(
      width: double.infinity,
      child: Column(
        children: [
          Stack(
            children: [
              AspectRatio(
                aspectRatio: 764 / 382,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: NetworkImageWidget(
                    coverImage,
                    width: double.infinity,
                    height: 191,
                  ),
                ),
              ),
              if (!isOfficial || status.status != DateTimeDiffStatus.expired)
                Positioned(
                  left: 10,
                  top: 10,
                  child: Builder(builder: (context) {
                    Color textColor;
                    Color pointColor;
                    switch (status.status) {
                      case DateTimeDiffStatus.expired:
                        textColor = Color(0xff767880);
                        pointColor = Color(0xff767880);
                        break;
                      case DateTimeDiffStatus.running:
                        textColor = Colors.white;
                        pointColor = Color(0xff2C9A0E);
                        break;
                      case DateTimeDiffStatus.today:
                      case DateTimeDiffStatus.nextDay:
                      case DateTimeDiffStatus.later:
                        textColor = Colors.white;
                        pointColor = Color(0xff4F7FFE);
                        break;
                        break;
                      default:
                        break;
                    }
                    return Container(
                      decoration: BoxDecoration(
                        color: Color.fromRGBO(0, 0, 0, 0.7),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 8),
                      child: Row(
                        children: [
                          SvgPicture.asset(
                            "assets/svg/event_time_status.svg",
                            width: 24,
                            height: 24,
                            color: Colors.white,
                          ),
                          const SizedBox(
                            width: 12,
                          ),
                          Text(
                            status.text,
                            style: TextStyle(
                                color: textColor, fontSize: 14, height: 1.1),
                          ),
                          const SizedBox(
                            width: 4,
                          ),
                          Container(
                            width: 10,
                            height: 10,
                            decoration: BoxDecoration(
                                color: pointColor, shape: BoxShape.circle),
                          )
                        ],
                      ),
                    );
                  }),
                ),
            ],
          ),
          Container(
            margin: const EdgeInsets.only(top: 12),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (isOfficial)
                  Container(
                    padding: const EdgeInsets.only(
                        left: 5, right: 5, top: 5, bottom: 3),
                    margin: const EdgeInsets.only(right: 12, top: 0.5),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: const Color(0xff4F7FFE),
                      ),
                      borderRadius: BorderRadius.circular(3),
                    ),
                    child: Text(
                      YLocal.of(context).guanfang,
                      style: const TextStyle(
                          fontSize: 14,
                          color: const Color(0xff4F7FFE),
                          height: 1.0),
                    ),
                  ),
                Expanded(
                  child: Text(
                    eventName,
                    style: const TextStyle(
                      color: const Color(0xff2C2E33),
                      fontSize: 16,
                      height: 48 / 32,
                      fontWeight: FontWeight.w700,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                )
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.only(top: 9),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (isPersonal)
                      Container(
                        margin: EdgeInsets.only(bottom: 5),
                        child: Text(
                          '${YLocal.of(context).jubanzhe}$organizerName',
                          style: const TextStyle(
                            color: const Color(0xff52555C),
                            fontSize: 12,
                          ),
                        ),
                      ),
                    Container(
                      constraints:
                          BoxConstraints(minHeight: isPersonal ? 0 : 30),
                      child: Row(
                        children: [
                          if (falseNum + realNum > 0)
                            Container(
                              margin: const EdgeInsets.only(right: 4),
                              child: AvatarGroupWidget(
                                avatars: focusesPortraits ?? [],
                                falseNum: falseNum ?? 0,
                                placeholder: true,
                              ),
                            ),
                          Builder(builder: (context) {
                            if (isPersonal) {
                              return NumberWidget(
                                num: falseNum + realNum,
                                unit: YLocal.of(context).NrenyicanjiaNrenyice,
                                defaultStyle: const TextStyle(
                                    color: const Color(0xff52555c),
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400),
                                numStyle: const TextStyle(
                                    fontWeight: FontWeight.w500),
                              );
                            } else if (isOfficial) {
                              return NumberWidget(
                                num: falseNum + realNum,
                                unit: YLocal.of(context).Nrenyibaoming,
                                defaultText:
                                    YLocal.of(context).kuailaibaomingba,
                                defaultStyle: const TextStyle(
                                    color: const Color(0xff52555c),
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400),
                                numStyle: const TextStyle(
                                    fontWeight: FontWeight.w500),
                              );
                            }
                            return SizedBox();
                          })
                        ],
                      ),
                    )
                  ],
                ),
                Row(
                  children: [
                    if (isOfficial)
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 16),
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              'assets/svg/comment_num.svg',
                              color: Color(0xff52555C),
                              width: 15,
                              height: 15,
                            ),
                            SizedBox(
                              width: 6,
                            ),
                            Text(
                              (commentNum ?? 0).toString(),
                              style: const TextStyle(
                                  color: const Color(0xff52555C),
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400),
                            )
                          ],
                        ),
                      ),
                    buttonWidget,
                  ],
                )
              ],
            ),
          ),
          const SizedBox(
            height: 11,
          ),
          AppDivider.backgroundDivider,
          const SizedBox(height: 19),
        ],
      ),
    );
  }
}

class PersonalActivityCell extends StatelessWidget {
  final String coverImage;
  final String eventName;
  final String organizerName;
  final List<String> focusesPortraits;
  final int realNum;
  final bool isJoin;
  final int organizer;
  final void Function(int status, int id, String name) handleEvent;
  final DateTimeDiffStatusData status;
  final bool process;
  final int id;

  const PersonalActivityCell(
      {Key key,
      @required this.coverImage,
      @required this.eventName,
      @required this.organizerName,
      @required this.focusesPortraits,
      @required this.realNum,
      @required this.isJoin,
      @required this.organizer,
      @required this.status,
      @required this.process,
      @required this.id,
      this.handleEvent})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return EventListItem(
        'personal',
        coverImage,
        eventName,
        organizerName,
        focusesPortraits,
        0,
        realNum,
        0,
        // ignore: deprecated_member_use
        YvrElevatedButton(
          width: 95,
          height: 30,
          padding: EdgeInsets.zero,
          color: (isJoin ?? false ? AppColors.buttonDisable : null),
          child: Text(
            isJoin ?? false
                ? YLocal.of(context).social_joined
                : (process
                    ? YLocal.of(context).social_join_in_vr
                    : YLocal.of(context).social_join_in),
            style: TextStyle(
                fontSize: 14,
                color: (isJoin ?? false ? Color(0xFFB0B2B8) : Colors.white)),
          ),
          onPressed: () {
            /*
                        eventStatus:

                            参加正在进行中的活动   1
                            参加活动             2
                            自己创建活动 无法退出  3
                            退出活动             4
                    */
            int eventStatus = 0;
            if (isJoin) {
              // 已开始活动无法退出
              if (process) {
                YvrToast.showToast(YLocal.of(context).huodongyikaishimofat);
                return;
              }
              if (organizer == DBUtil.instance.userBox.get(kActId)) {
                eventStatus = 3;
              } else {
                eventStatus = 4;
              }
            } else {
              eventStatus = process ? 1 : 2;
            }
            handleEvent(eventStatus, id, eventName ?? "");
          },
        ),
        status);
  }
}

class ActivityCell extends StatefulWidget {
  final EventModel event;
  final Function handleEvent;

  ActivityCell({Key key, @required this.event, this.handleEvent})
      : super(key: key);

  @override
  _ActivityCellState createState() => _ActivityCellState();
}

class _ActivityCellState extends State<ActivityCell> {
  @override
  Widget build(BuildContext context) {
    String time = widget.event.startTime
        .toString()
        .replaceRange(7, 8, "月")
        .replaceRange(10, 11, "日 ");
    time = time.substring(5, 17);
    String date = time.substring(0, 6);

    return Card(
      color: Color(0xff242527),
      margin: EdgeInsets.fromLTRB(16, 0, 16, 18),
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(6.0)),
      ),
      child: Column(children: [
        Stack(
          children: [
            AspectRatio(
              aspectRatio: 280 / 120,
              child: FadeInImage.assetNetwork(
                placeholder: 'assets/images/r_plhd.png',
                image: widget.event.rcover ?? "",
                fit: BoxFit.cover,
                imageErrorBuilder: (context, error, stackTrace) {
                  return Image.asset(
                    'assets/images/r_plhd.png',
                    fit: BoxFit.cover,
                  );
                },
              ),
            ),
            widget.event.process == 1
                ? Positioned(
                    top: 0,
                    left: 0,
                    child: Container(
                      height: 35,
                      width: MediaQuery.of(context).size.width,
                      alignment: Alignment.centerLeft,
                      decoration: BoxDecoration(
                        color: Color(0xB3000000),
                        borderRadius:
                            BorderRadius.only(bottomRight: Radius.circular(6)),
                      ),
                      child: Padding(
                        padding: EdgeInsets.only(left: 10),
                        child: Flex(
                          direction: Axis.horizontal,
                          children: <Widget>[
                            Expanded(
                              flex: 1,
                              child: Text(
                                  date +
                                      "   ${YLocal.of(context).zhengzaijinhangzhong}",
                                  style: TextStyle(
                                      fontSize: 14, color: Color(0xffE8E8E8))),
                            ),
                            Container(
                              width: 10,
                              height: 10,
                              decoration: new BoxDecoration(
                                  // 边色与边宽度
                                  color: Color(0xFF2C9A0E), // 底色
                                  borderRadius:
                                      new BorderRadius.circular((5.0))),
                            ),
                            SizedBox(
                              width: 40,
                            )
                          ],
                        ),
                      ),
                    ))
                : Positioned(
                    top: 0,
                    left: 0,
                    child: Container(
                      height: 35,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: Color(0xB3000000),
                        borderRadius:
                            BorderRadius.only(bottomRight: Radius.circular(6)),
                      ),
                      child: Padding(
                        padding: EdgeInsets.only(left: 10, right: 10),
                        child: Text(time,
                            style: TextStyle(
                                fontSize: 14, color: Color(0xffE8E8E8))),
                      ),
                    ))
          ],
        ),
        Padding(
          padding: EdgeInsets.only(bottom: 5),
          child: ListTile(
            title: SizedBox(
              height: 32,
              child: Text(
                widget.event.name ?? "",
                maxLines: 1,
                style: TextStyle(fontSize: 16, height: 2),
              ),
            ),
            subtitle: Flex(
              direction: Axis.horizontal,
              children: <Widget>[
                Expanded(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          YLocal.of(context).social_organizers +
                              '：' +
                              (widget.event.cName == null
                                  ? YLocal.of(context).nickname
                                  : widget.event.cName),
                          style: TextStyle(fontSize: 12, height: 1.5),
                        ),
                        Text(
                          YLocal.of(context).social_people_attend(
                              widget.event.num.toString()),
                          style: TextStyle(fontSize: 12, height: 1.5),
                        ),
                      ],
                    )),
                Container(
                  // ignore: deprecated_member_use
                  child: RaisedButton.icon(
                    color: (widget.event.isJonin ?? false
                        ? Color(0xFF52555C)
                        : Color(0xFF4F7FFE)),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4)),
                    icon: widget.event.isJonin ?? false
                        ? Icon(
                            Icons.done,
                            color: Color(0xFFB0B2B8),
                          )
                        : (widget.event.process == 1 ?? false
                            ? SizedBox()
                            : Icon(Icons.add)),
                    label: Text(
                      widget.event.isJonin ?? false
                          ? YLocal.of(context).social_joined
                          : (widget.event.process == 1
                              ? YLocal.of(context).social_join_in_vr
                              : YLocal.of(context).social_join_in),
                      style: TextStyle(
                          fontSize: 14,
                          color: (widget.event.isJonin ?? false
                              ? Color(0xFFB0B2B8)
                              : Colors.white)),
                    ),
                    onPressed: () {
                      /*
                        eventStatus:

                            参加正在进行中的活动   1
                            参加活动             2
                            自己创建活动 无法退出  3
                            退出活动             4
                    */
                      int eventStatus = 0;
                      if (widget.event.isJonin) {
                        // 已开始活动无法退出
                        if (widget.event.process == 1) {
                          YvrToast.showToast(
                              YLocal.of(context).huodongyikaishimofat);
                          return;
                        }
                        if (widget.event.cId ==
                            DBUtil.instance.userBox.get(kActId)) {
                          eventStatus = 3;
                        } else {
                          eventStatus = 4;
                        }
                      } else {
                        eventStatus = widget.event.process == 1 ? 1 : 2;
                      }
                      widget.handleEvent(eventStatus, widget.event.id,
                          widget.event.name ?? "");
                    },
                  ),
                )
              ],
            ),
          ),
        )
      ]),
    );
  }
}
