import 'package:flutter/material.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/public_ui/widget/toast_show.dart';
import 'package:yvr_assistant/utils/log.dart';
import '../../../generated/l10n.dart';
import '../../../styles/app_color.dart';
import '../../../styles/app_divider.dart';

showDevices({@required context, @required Function callback}) async {
  http.post<Map>('vrmcsys/account/getLoginDevIds', data: {}).then((response) {
    if (response.data["errCode"] == 0) {
      List<String> devices = response.data["devIds"]
              .map<String>((item) => item.toString())
              .toList() ??
          [];
      if (devices.length == 0) {
        YvrToast.showToast(YLocal.of(context).qingxianbangdingnind);
      } else {
        showModalBottomSheet(
            context: context,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.0),
              topRight: Radius.circular(16.0),
            )),
            builder: (_) {
              return Container(
                height: 350,
                width: double.infinity,
                decoration: BoxDecoration(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 60,
                      width: double.infinity,
                      child: Stack(
                        alignment: AlignmentDirectional.center,
                        children: [
                          Text(
                            YLocal.of(context).xuanzeshebeixuanzhai,
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 18,
                              height: 1,
                              color: AppColors.textTitle,
                            ),
                          ),
                          Positioned(
                            right: 16,
                            child: GestureDetector(
                              onTap: () {
                                Navigator.pop(context);
                              },
                              child: Container(
                                width: 32,
                                height: 32,
                                alignment: AlignmentDirectional.center,
                                decoration: BoxDecoration(
                                  color: Color(0xffF0F2F5),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.clear,
                                  color: Color(0xff6E7380),
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                    AppDivider.backgroundDivider,
                    Container(
                      margin: EdgeInsets.only(top: 34, left: 16),
                      child: Text(
                        YLocal.of(context).xuanzeshebeiqushebei,
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                          letterSpacing: 0.5,
                          height: 1.5,
                          color: AppColors.textSub,
                        ),
                      ),
                    ),
                    Expanded(
                      child: devCells(context, devices, callback),
                    ),
                  ],
                ),
              );
            });
      }
    } else {
      YvrToast.showToast(YLocal.of(context).huoqushebeiliebiaosh);
    }
  });
}

Widget devCells(BuildContext context, List<String> devices, Function callback) {
  return Container(
    margin: EdgeInsets.only(left: 16, top: 30, right: 16),
    child: ListView.separated(
      separatorBuilder: (context, index) {
        return SizedBox(
          height: 26,
        );
      },
      padding: EdgeInsets.only(
          top: 0, bottom: MediaQuery.of(context).padding.bottom),
      scrollDirection: Axis.vertical,
      itemCount: devices.length,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () {
            Navigator.pop(context);
            callback(devices[index]);
            Log.d("设备id: ${devices[index]}");
          },
          child: Row(
            children: [
              SizedBox(
                width: 39,
                height: 39,
                child: CircleAvatar(
                  backgroundImage:
                      AssetImage('assets/images/dev_vr_circle.png'),
                ),
              ),
              SizedBox(
                width: 16,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    devices[index] ?? '',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      color: AppColors.textTitle,
                    ),
                  ),
                  SizedBox(
                    height: 11,
                  ),
                  Row(
                    children: [
                      Icon(
                        Icons.circle,
                        color: Color(0xff52555C),
                        size: 8,
                      ),
                      SizedBox(
                        width: 8,
                      ),
                      Text(
                        YLocal.of(context).weilianjie,
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                          height: 1,
                          color: AppColors.textSub,
                        ),
                      ),
                    ],
                  ),
                ],
              )
            ],
          ),
        );
      },
    ),
  );
}
