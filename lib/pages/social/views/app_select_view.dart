import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/model/pay_history_model.dart';
import 'package:yvr_assistant/public_ui/widget/dialog_util.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';
import '../../../generated/l10n.dart';
import '../../../styles/app_color.dart';

class AppSelectView extends StatelessWidget {
  final Function(int id, String rcover, String scover) appSelectCallback;
  final bool isEdit;
  final int id;
  final String rcover;
  final String scover;

  const AppSelectView({
    Key key,
    @required this.isEdit,
    this.appSelectCallback,
    this.id,
    this.rcover,
    this.scover,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
        margin: EdgeInsets.all(0),
        color: Color(0xFFF0F2F5),
        clipBehavior: Clip.antiAlias,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(6.0)),
        ),
        elevation: 0,
        child: rcover == null || rcover.isEmpty
            ? SizedBox(
                width: Global.screenWidth,
                height: Global.screenWidth * 3 / 7,
                child: InkWell(
                  onTap: () {
                    if (DBUtil.instance.userBox.get(kToken) == null) {
                      showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (_) {
                            return CustomDialog(
                              title: YLocal.of(context).wenxindishiwenxintis,
                              content:
                                  YLocal.of(context).xuyaodenglucainengch_1,
                              confirmText: YLocal.of(context).denglu,
                              height: 180,
                              confirmCallback: () {
                                Navigator.pushNamed(context, '/login');
                              },
                            );
                          });
                    } else {
                      Navigator.pushNamed(context, '/mine_app', arguments: {
                        "title": 1,
                      }).then((value) {
                        PayHistoryModel model = value as PayHistoryModel;
                        appSelectCallback(
                            model.appId, model.rcover, model.scover);
                      });
                    }
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.add,
                        color: Color(0xffAFB6CC),
                      ),
                      Text(
                        YLocal.of(context).xuanzeyingyongxuanzh,
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                          color: AppColors.textWeak,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            : Stack(
                children: [
                  AspectRatio(
                    aspectRatio: 280 / 120,
                    child: FadeInImage.assetNetwork(
                      placeholder: 'assets/images/r_plhd.png',
                      image: rcover,
                      fit: BoxFit.cover,
                      imageErrorBuilder: (context, error, stackTrace) {
                        return Image.asset(
                          'assets/images/r_plhd.png',
                          fit: BoxFit.cover,
                        );
                      },
                    ),
                  ),
                  isEdit
                      ? SizedBox()
                      : Positioned(
                          top: 12,
                          right: 12,
                          width: 30,
                          height: 30,
                          child: GestureDetector(
                            onTap: () {
                              appSelectCallback(null, null, null);
                            },
                            child: SvgPicture.asset('assets/images/close.svg'),
                          )),
                ],
              ));
  }
}
