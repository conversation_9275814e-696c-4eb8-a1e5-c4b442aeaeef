import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/model/user_model.dart';
import 'package:yvr_assistant/utils/log.dart';

import '../../../styles/app_color.dart';
import '../../../utils/yvr_utils.dart';

enum SearchFriendsType {
  FriendIsYet,
  FriendForStrange,
  FriendForSendAddReq,
}

// ignore: must_be_immutable
class PeopleCell extends StatefulWidget {
  final UserModel people;
  final Function personClick;
  final Function addFriendPress;
  bool isJoin = false; // 活动邀请 是否已参加
  bool isInvite = false;
  bool isBlacklist = false;
  SearchFriendsType searchType = SearchFriendsType.FriendForStrange;

  PeopleCell(
      {Key key,
      @required this.people,
      this.isJoin,
      this.personClick,
      this.addFriendPress,
      @required this.isInvite,
      @required this.isBlacklist,
      this.searchType})
      : super(key: key);

  @override
  _FriendsCellState createState() => _FriendsCellState();
}

class _FriendsCellState extends State<PeopleCell> {
  bool isSelected = false;

  @override
  Widget build(BuildContext context) {
    return Container(
        height: 72,
        child: Column(
          children: [
            Flex(
              direction: Axis.horizontal,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                    child: ListTile(
                        onTap: widget.isBlacklist
                            ? null
                            : () {
                                if (widget.isInvite) {
                                  if (!widget.isJoin) {
                                    setState(() {
                                      isSelected = !isSelected;
                                    });
                                    widget.personClick(isSelected);
                                  }
                                } else {
                                  widget.personClick(widget.searchType ==
                                      SearchFriendsType.FriendForSendAddReq);
                                }
                              },
                        leading: Stack(
                          children: [
                            SizedBox(
                                height: 50,
                                width: 50,
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(4),
                                  child: AspectRatio(
                                    aspectRatio: 1,
                                    child: ClipRRect(
                                      //剪裁为圆角矩形
                                      borderRadius: BorderRadius.circular(60),
                                      child: FadeInImage.assetNetwork(
                                        placeholder: 'assets/images/s_plhd.png',
                                        image: widget.people.avatar ?? "",
                                        fit: BoxFit.cover,
                                        imageErrorBuilder:
                                            (context, error, stackTrace) {
                                          return Image.asset(
                                            widget.people.sex == 2
                                                ? 'assets/images/avatar_girl.jpg'
                                                : 'assets/images/avatar_boy.jpg',
                                            fit: BoxFit.cover,
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                )),
                            widget.people.online == 1
                                ? Positioned(
                                    right: 5,
                                    child: Container(
                                      width: 10,
                                      height: 10,
                                      decoration: new BoxDecoration(
                                          // 边色与边宽度
                                          color: Color(0xFF2C9A0E), // 底色
                                          borderRadius:
                                              new BorderRadius.circular((5.0))),
                                    ),
                                  )
                                : SizedBox()
                          ],
                        ),
                        title: Text(
                          widget.people.nick ?? YLocal.of(context).nickname,
                          maxLines: 2,
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 16,
                            height: 1.25,
                            color: AppColors.textTitle,
                          ),
                        ),
                        subtitle: RichText(
                          text: TextSpan(
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: 12,
                              height: 1.16667,
                              color: Color(0xffb0b2b8),
                            ),
                            children: [
                              TextSpan(
                                  text: YvrUtils.getSexText(widget.people.sex)),
                              WidgetSpan(
                                child: SizedBox(
                                  width: 10,
                                ),
                              ),
                              TextSpan(text: 'ID:${widget.people.actId}'),
                            ],
                          ),
                        ))),
                rightView()
              ],
            )
          ],
        ));
  }

  Widget rightView() {
    Widget rightView = SizedBox();

    if (widget.isInvite) {
      rightView = Container(
          margin: EdgeInsets.only(right: 15),
          child: widget.isJoin
              ? Text(
                  YLocal.of(context).yicanjiagaihuodongyi,
                  style: TextStyle(color: Color(0xFF767880), fontSize: 14),
                )
              : (isSelected
                  ? Icon(
                      Icons.check,
                      color: Color(0xFF4F7FFE),
                    )
                  : SizedBox()));
    } else if (widget.isBlacklist) {
      rightView = Container(
        margin: EdgeInsets.only(right: 15),
        child: SvgPicture.asset(
          "assets/svg/arrow_right.svg",
          color: AppColors.textSub,
          width: 10,
          height: 10,
        ),
      );
    } else {
      rightView = Container(
        margin: EdgeInsets.only(right: 15),
        child: GestureDetector(
          child: widget.searchType == SearchFriendsType.FriendForSendAddReq
              ? Text(
                  YLocal.of(context).yifasongqingqiuyifei,
                  style: TextStyle(color: Color(0xFF767880), fontSize: 14),
                )
              : (widget.searchType == SearchFriendsType.FriendIsYet
                  ? SizedBox()
                  : SizedBox(
                      width: 24,
                      child: SvgPicture.asset("assets/images/add_friend.svg"),
                    )),
          onTap: () {
            switch (widget.searchType) {
              case SearchFriendsType.FriendIsYet:
                // do nothing
                break;
              case SearchFriendsType.FriendForStrange:
                Log.d("去发送请求了");
                setState(() {
                  widget.searchType = SearchFriendsType.FriendForSendAddReq;
                });

                widget.addFriendPress(widget.people.actId);
                break;
              case SearchFriendsType.FriendForSendAddReq:
                Log.d("已发送请求了");
                widget.personClick(true);
                break;
              default:
            }
          },
        ),
      );
    }

    return rightView;
  }
}
