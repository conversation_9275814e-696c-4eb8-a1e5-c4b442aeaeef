import 'package:expandable_text/expandable_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_picker/flutter_picker.dart';
import 'package:yvr_assistant/public_ui/widget/picker_tool.dart';
import 'package:yvr_assistant/utils/date_time_utils.dart';
import '../../../generated/l10n.dart';

// ignore: must_be_immutable
class TimeView extends StatefulWidget {
  String defTimeStr;
  int duration;
  final StringCallback timeCallback;
  final Function durationCallback;
  bool isEdit;

  TimeView(
      {Key key,
      this.isEdit = false,
      this.defTimeStr,
      this.duration,
      @required this.timeCallback,
      @required this.durationCallback})
      : super(key: key);

  @override
  _TimeViewState createState() => _TimeViewState();
}

class _TimeViewState extends State<TimeView> {
  DateTime _minTime = DateTime.fromMillisecondsSinceEpoch(
      DateTime.now().millisecondsSinceEpoch + (29 * 60 * 1000));
  DateTime _maxTime = DateTime.fromMillisecondsSinceEpoch(
      DateTime.now().millisecondsSinceEpoch + (15 * 24 * 60 * 60 * 1000));
  static List<String> _timeDurations = [
    "30分钟",
    "1小时",
    "90分钟",
    "2小时",
    "3小时",
    "4小时",
    "5小时"
  ];

  DateTime _selTime;

  String dateTimeText;
  String durationText;

  @override
  void initState() {
    super.initState();
    _updateData(widget.defTimeStr, widget.duration);
  }

  @override
  void didUpdateWidget(covariant TimeView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.defTimeStr != oldWidget.defTimeStr ||
        widget.duration != oldWidget.duration) {
      _updateData(widget.defTimeStr, widget.duration);
    }
  }

  void _updateData(String selTime, int duration) {
    if (selTime == null || selTime.isEmpty) {
      dateTimeText = '';
      _selTime = null;
    } else {
      DateTime time = DateTimeUtils.parseServerTime(selTime);
      dateTimeText =
          DateTimeUtils.formatToLocalFromDateTime(time, 'MM月dd日 HH:mm');
      _selTime = time;
    }

    if (duration == null || duration <= 0) {
      durationText = '';
    } else {
      durationText = minuteToHour(duration);
    }
  }

  List<Widget> selectButtons(String defTimeStr, int duration) {
    bool isEdit = widget.isEdit;
    List<Widget> buttons = [];
    for (var i = 0; i < 3; i++) {
      if (i == 1) {
        buttons.add(SizedBox(
          width: 10,
        ));
      } else {
        String text;
        Color color;
        if (i == 0) {
          if (dateTimeText.isEmpty) {
            text = YLocal.of(context).riqishijian;
            color = Color(0xffAFB6CC);
          } else {
            text = dateTimeText;
            if (isEdit) {
              color = Color(0xffAFB6CC);
            } else {
              color = Color(0xff2C2E33);
            }
          }
        } else if (i == 2) {
          if (durationText.isEmpty) {
            text = YLocal.of(context).shichangshizhang;
            color = Color(0xffAFB6CC);
          } else {
            text = durationText;
            if (isEdit) {
              color = Color(0xffAFB6CC);
            } else {
              color = Color(0xff2C2E33);
            }
          }
        }

        buttons.add(Expanded(
          child: SizedBox(
            height: 44,
            child: ElevatedButton(
              style: ButtonStyle(
                textStyle: MaterialStateProperty.all(
                  TextStyle(fontSize: 14, color: Color(0xFF767880)),
                ),
                padding: ButtonStyleButton.allOrNull(
                    EdgeInsets.symmetric(horizontal: 11)),
                elevation: ButtonStyleButton.allOrNull(0),
                backgroundColor: MaterialStateProperty.all(Color(0xFFF0F2F5)),
              ),
              onPressed: () {
                FocusScope.of(context).requestFocus(FocusNode());
                if (isEdit) {
                  return;
                }

                if (i == 0) {
                  JhPickerTool.showDatePicker(context,
                      adapter: DateTimePickerAdapter(
                        minValue: _minTime,
                        maxValue: _maxTime,
                        value: _selTime ?? _minTime,
                      ), clickCallBack: (var dt, var time) {
                    String timeString = DateTimeUtils.formatToServerDateTime(
                        dt, 'yyyy-MM-dd HH:mm:ss');
                    widget.timeCallback(timeString);
                  });
                } else if (i == 2) {
                  JhPickerTool.showStringPicker(context,
                      normalIndex: 1, title: '', data: _timeDurations,
                      clickCallBack: (int index, var item) {
                    int minutes =
                        (index < 3) ? (30 * (index + 1)) : (60 * (index - 1));
                    widget.durationCallback(minutes);
                  });
                }
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    text,
                    style: TextStyle(color: color, fontSize: 14),
                  ),
                  Icon(
                    Icons.expand_more,
                    color: Color(0xffAFB6CC),
                  ),
                ],
              ),
            ),
          ),
          flex: 1,
        ));
      }
    }
    return buttons;
  }

  @override
  Widget build(BuildContext context) {
    return Flex(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      direction: Axis.horizontal,
      children: selectButtons(widget.defTimeStr, widget.duration),
    );
  }

  String minuteToHour(int duration) {
    String hour = "";
    if (duration ~/ 60 > 0) {
      hour = YLocal.current.Nxiaoshi((duration ~/ 60).toString());
    }
    if (duration % 60 > 0) {
      hour += YLocal.current.Nfenzhong((duration % 60).toString());
    }
    return hour;
  }
}
