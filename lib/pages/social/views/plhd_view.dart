import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:yvr_assistant/manager/global.dart';
import 'package:yvr_assistant/styles/app_style.dart';

class PagePlhdWidget extends StatelessWidget {
  final String imagePath;
  final double imgWidth;
  final double vSpaceHeight;
  final IconData iconData;
  final String message;

  const PagePlhdWidget({
    Key key,
    this.imgWidth,
    this.imagePath,
    this.vSpaceHeight,
    this.iconData,
    @required this.message,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          SizedBox(
            height: Global.screenHeight / 4,
          ),
          imagePath != null
              ? Container(
                  width: imgWidth ?? 44,
                  child: SvgPicture.asset(
                    imagePath,
                    color: Color(0xff979797),
                  ),
                )
              : Icon(
                  iconData,
                  color: Color(0xff979797),
                  size: 60,
                ),
          SizedBox(
            height: vSpaceHeight ?? 50,
          ),
          Text(
            message,
            style: AppStyle.style_textSub_w400_14pt(),
          ),
        ],
      ),
    );
  }
}
