import 'package:flutter/material.dart';
import '../../main.dart';

///适用于监听页面生命周期和页面push、pop、前后台切换的数据刷新。
abstract class LifecycleState<T extends StatefulWidget> extends State<T>
    with RouteAware, WidgetsBindingObserver {
  bool stackTop = true;
  bool inForeground = true;
  int refreshInterval = 1 * 60 * 1000;
  DateTime _lastRefreshTime;

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();
    _lastRefreshTime = DateTime.now();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        inForeground = true;
        if (stackTop) {
          onResumed();
          _onResumeRefresh();
        }
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.paused:
        inForeground = false;
        if (stackTop) {
          onPaused();
        }
        break;
      case AppLifecycleState.detached:
        break;
    }
  }

  void _onPopRefresh() {
    _doRefresh(true);
  }

  void _onResumeRefresh() {
    _doRefresh(false);
  }

  void _doRefresh(bool force) {
    if (force ||
        (DateTime.now().difference(_lastRefreshTime).inMilliseconds >=
            refreshInterval)) {
      onRefresh();
      _lastRefreshTime = DateTime.now();
    }
  }

  void onRefresh() {}

  void onResumed() {}

  void onPaused() {}

  @override
  void didPush() {
    super.didPush();
  }

  @override
  void didPushNext() {
    ///处于后台，如果进行了push操作，会执行didPushNext，但页面并不会立即push，而是等切换前台后才会执行
    stackTop = false;
    if (inForeground) {
      onPaused();
    }
  }

  @override
  void didPop() {
    super.didPop();
  }

  @override
  void didPopNext() {
    ///处于后台，如果进行了pop操作，会执行didPushNext，但页面并不会立即pop，而是等切换前台后才会执行
    stackTop = true;
    if (inForeground) {
      onResumed();
      _onPopRefresh();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver?.subscribe(this, ModalRoute.of(context));
  }

  @override
  void dispose() {
    super.dispose();
    routeObserver.unsubscribe(this);
    WidgetsBinding.instance.removeObserver(this);
  }
}
