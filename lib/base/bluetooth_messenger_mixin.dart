import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../manager/event_bus.dart';

///适用于设备蓝牙通讯
mixin BluetoothMessengerMixin<T extends StatefulWidget> on State<T> {
  FlutterBluePlus flutterBluePlus;
  StreamSubscription<EventFn> eventBusFn;

  @override
  void initState() {
    super.initState();
    eventBusFn = eventBus.on<EventFn>().listen((e) {
      if (e.obj is Map) {
        onEvent(e.obj);
      }
    });
  }

  void onEvent(Map eventData) {}

  Future<String> connectedDevice() async {
    List<BluetoothDevice> connected = FlutterBluePlus.connectedDevices;
    if (connected.isEmpty) {
      return null;
    }
    return connected.first.remoteId.str;
  }

  void gotoMainDevicePage() {
    eventBus.fire(EventFn({'selectedTabbarIndex': 2}));
  }

  void sendBleMessage({@required String req, Map args}) {
    Map params = args == null ? Map() : Map.from(args);
    params[req] = true;
    eventBus.fire(EventFn(params));
  }

  @override
  void dispose() {
    eventBusFn.cancel();
    eventBusFn = null;
    super.dispose();
  }
}
