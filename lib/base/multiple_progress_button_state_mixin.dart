import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';

class _State {
  ProgressState state;
  Timer timer;
}

class MultipleProgressButtonProgressImpl<E> {
  Map<E, _State> _maps = Map<E, _State>();

  ValueChanged<E> onTimeout;

  MultipleProgressButtonProgressImpl({this.onTimeout});

  void progressExecute<T>(VoidCallback notify, E e,
      {@required FutureOr<T> computation(),
      ValueChanged<T> onSuccess,
      Function onError,
      int milliseconds}) {
    var old = _maps.remove(e);
    if (old != null) {
      old.timer?.cancel();
    }
    _State state = _State();
    state.state = ProgressState.loading;
    _maps[e] = state;
    notify();
    Future<T>.sync(computation).then((value) {
      if (value != null) {
        if (milliseconds != null) {
          state.timer = Timer(Duration(milliseconds: milliseconds), () {
            _timeout(notify, e);
          });
        }
        onSuccess?.call(value);
      } else {
        _error(notify, e);
      }
    }).catchError((err, s) {
      _error(notify, e);
      onError?.call(err, s);
    });
  }

  void _error(VoidCallback notify, E e) {
    _State state = _maps.remove(e);
    if (state != null) {
      state.state = ProgressState.idle;
      if (state.timer != null) {
        state.timer.cancel();
        state.timer = null;
      }
      notify();
    }
  }

  void _timeout(VoidCallback notify, E e) {
    _State s = _maps.remove(e);
    if (s != null) {
      s.state = ProgressState.idle;
      s.timer = null;
      onTimeout?.call(e);
      notify();
    }
  }

  void progressComplete(VoidCallback notify, E e) {
    _State s = _maps.remove(e);
    if (s != null) {
      if (s.timer != null) {
        s.timer.cancel();
        s.timer = null;
      }
      s.state = ProgressState.idle;
      notify();
    }
  }

  int progressSize() {
    return _maps.length;
  }

  ProgressState progressState(E e) {
    return _maps[e]?.state ?? ProgressState.idle;
  }

  void release() {
    _maps.removeWhere((key, value) {
      value.state = ProgressState.idle;
      if (value.timer != null) {
        value.timer.cancel();
        value.timer = null;
      }
      return true;
    });
  }
}

///适用于有多个ProgressButton的页面。还可以给button点击执行设置个超时时间防止ProgressButton一直loading导致无法点击。
mixin MultipleProgressButtonStateMixin<T extends StatefulWidget, E>
    on State<T> {
  MultipleProgressButtonProgressImpl<E> _impl;

  @override
  void initState() {
    _impl = MultipleProgressButtonProgressImpl<E>(onTimeout: onTimeout);
    super.initState();
  }

  void progressExecute<T>(E e,
      {@required FutureOr<T> computation(),
      ValueChanged<T> onSuccess,
      Function onError,
      int milliseconds}) {
    _impl.progressExecute<T>(() {
      setState(() {});
    }, e,
        computation: computation,
        onSuccess: onSuccess,
        onError: onError,
        milliseconds: milliseconds);
  }

  void progressComplete(E e) {
    _impl.progressComplete(() {
      setState(() {});
    }, e);
  }

  int progressSize() {
    return _impl.progressSize();
  }

  ProgressState progressState(E e) {
    return _impl.progressState(e);
  }

  void onTimeout(E e) {}

  @override
  void dispose() {
    _impl.release();
    super.dispose();
  }
}

mixin MultipleProgressButtonProviderMixin<E> on ChangeNotifier {
  MultipleProgressButtonProgressImpl<E> _impl =
      MultipleProgressButtonProgressImpl();

  void progressExecute<T>(E e,
      {@required FutureOr<T> computation(),
      ValueChanged<T> onSuccess,
      Function onError,
      int milliseconds}) {
    _impl.progressExecute<T>(notifyListeners, e,
        computation: computation,
        onSuccess: onSuccess,
        onError: onError,
        milliseconds: milliseconds);
  }

  void progressComplete(E e) {
    _impl.progressComplete(notifyListeners, e);
  }

  int progressSize() {
    return _impl.progressSize();
  }

  ProgressState progressState(E e) {
    return _impl.progressState(e);
  }

  void release() {
    _impl.release();
  }
}
