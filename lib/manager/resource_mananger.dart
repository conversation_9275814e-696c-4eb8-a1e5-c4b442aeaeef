import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class IconFonts {
  IconFonts._();

  /// iconfont:flutter base
  static const String fontFamily = 'iconfont';

  static const IconData tabbar_home = IconData(0xe6c0, fontFamily: fontFamily);
  static const IconData tabbar_social =
      IconData(0xe6ba, fontFamily: fontFamily);
  static const IconData tabbar_device =
      IconData(0xe6bc, fontFamily: fontFamily);
  static const IconData tabProfile = IconData(0xe6bf, fontFamily: fontFamily);
  static const IconData tabbar_homes = IconData(0xe6b8, fontFamily: fontFamily);
  static const IconData tabbar_socials =
      IconData(0xe6b9, fontFamily: fontFamily);
  static const IconData tabbar_devices =
      IconData(0xe6bb, fontFamily: fontFamily);
  static const IconData tabProfiles = IconData(0xe6be, fontFamily: fontFamily);

  static const IconData iconHealth = IconData(0xe6c1, fontFamily: fontFamily);
  static const IconData iconFeedback = IconData(0xe6c2, fontFamily: fontFamily);
  static const IconData iconVersion = IconData(0xe6c4, fontFamily: fontFamily);
  static const IconData iconPhone = IconData(0xe6c5, fontFamily: fontFamily);
  static const IconData iconPurchase = IconData(0xe6c7, fontFamily: fontFamily);
  static const IconData iconExchange = IconData(0xe6c8, fontFamily: fontFamily);
  static const IconData iconPwd = IconData(0xe6c9, fontFamily: fontFamily);
  static const IconData iconUpdate = IconData(0xe6ca, fontFamily: fontFamily);
  static const IconData iconDeveloper =
      IconData(0xe6cb, fontFamily: fontFamily);
  static const IconData iconSafe = IconData(0xe6cc, fontFamily: fontFamily);
  static const IconData iconWifi = IconData(0xe6cd, fontFamily: fontFamily);
  static const IconData iconClock = IconData(0xe6ce, fontFamily: fontFamily);
  static const IconData iconLogo = IconData(0xe6cf, fontFamily: fontFamily);
  static const IconData iconLanguage = IconData(0xe6d0, fontFamily: fontFamily);
  static const IconData iconInfo = IconData(0xe6d1, fontFamily: fontFamily);
  static const IconData iconMemory = IconData(0xe6d2, fontFamily: fontFamily);
  static const IconData iconStar = IconData(0xe6d3, fontFamily: fontFamily);
  static const IconData iconEmptyStar =
      IconData(0xe6d5, fontFamily: fontFamily);
  static const IconData iconHalfStar = IconData(0xe6d4, fontFamily: fontFamily);
  static const IconData iconNoti = IconData(0xe6d7, fontFamily: fontFamily);
  static const IconData iconApps = IconData(0xe6d8, fontFamily: fontFamily);
  static const IconData iconHide = IconData(0xe6da, fontFamily: fontFamily);
  static const IconData iconShow = IconData(0xe6d9, fontFamily: fontFamily);

  static const IconData iconEdit = IconData(0xe6db, fontFamily: fontFamily);
  static const IconData iconScreen = IconData(0xe6dc, fontFamily: fontFamily);
  static const IconData iconLaw = IconData(0xe6dd, fontFamily: fontFamily);
  static const IconData iconVR = IconData(0xe6de, fontFamily: fontFamily);
  static const IconData iconReload = IconData(0xe6df, fontFamily: fontFamily);
  static const IconData iconTV = IconData(0xe6e2, fontFamily: fontFamily);
  static const IconData iconPC = IconData(0xe6e1, fontFamily: fontFamily);
  static const IconData iconBTPhone = IconData(0xe6e3, fontFamily: fontFamily);
  static const IconData iconBT = IconData(0xe6e4, fontFamily: fontFamily);
  static const IconData iconMyApp = IconData(0xe6e5, fontFamily: fontFamily);
  static const IconData iconSearch = IconData(0xe6e6, fontFamily: fontFamily);
  static const IconData iconPen = IconData(0xe6e7, fontFamily: fontFamily);
  static const IconData iconClear = IconData(0xe6e8, fontFamily: fontFamily);
  static const IconData iconArrow = IconData(0xe6e9, fontFamily: fontFamily);

  static const IconData iconGroup = IconData(0xe6ea, fontFamily: fontFamily);
  static const IconData iconRemind = IconData(0xe6eb, fontFamily: fontFamily);
  static const IconData iconNoWifi = IconData(0xe6ec, fontFamily: fontFamily);
  static const IconData iconComment = IconData(0xe6ed, fontFamily: fontFamily);
  static const IconData iconNoDev = IconData(0xe6ee, fontFamily: fontFamily);
  static const IconData iconMore = IconData(0xe6f1, fontFamily: fontFamily);
}
