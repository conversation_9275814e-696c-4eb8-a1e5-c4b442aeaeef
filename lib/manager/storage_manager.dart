import 'package:localstorage/localstorage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yvr_assistant/utils/platform_utils.dart';
import '../utils/agreement.dart';
import 'package:synchronized/synchronized.dart';

class StorageManager {
  /// app全局配置 eg:theme
  static SharedPreferences sharedPreferences;

  /// 初始化必备操作 eg:user数据
  static SyncLocalStorage localStorage;

  /// 用于存储与业务无关需要永久保存的数据 eg:首次安装程序
  static SyncLocalStorage foreverData;

  static String _deviceModel;

  /// 必备数据的初始化操作
  ///
  /// 由于是同步操作会导致阻塞,所以应尽量减少存储容量
  static init() async {
    // async 异步操作
    // sync 同步操作
    sharedPreferences = await SharedPreferences.getInstance();
    localStorage = SyncLocalStorage('LocalStorage');
    await localStorage.ready;

    foreverData = SyncLocalStorage('foreverData');
    await foreverData.ready;

    if (AgreementUtils.hasAgreed()) {
      await loadDeviceInfo();
    }
  }

  static Future<void> loadDeviceInfo() async {
    deviceModel = await PlatformUtils.getDeviceName();
  }

  static set deviceModel(String v) {
    _deviceModel = v;
  }

  // ignore: unnecessary_getters_setters
  static get deviceModel {
    return _deviceModel;
  }
}

class SyncLocalStorage {
  Lock _lock;

  LocalStorage localStorage;

  SyncLocalStorage(String key)
      : _lock = Lock(),
        localStorage = LocalStorage(key);

  Future<bool> get ready => localStorage.ready;

  ///由于读取无法异步执行，所以不能加锁，请预防空指针。
  dynamic getItem(String key) {
    return localStorage.getItem(key);
  }

  Future<void> setItem(
    String key,
    value,
  ) {
    return _lock.synchronized<void>(() async {
      await localStorage.setItem(key, value);
    });
  }

  Future<void> deleteItem(String key) {
    return _lock.synchronized<void>(() async {
      await localStorage.deleteItem(key);
    });
  }

  Future<void> clear() {
    return _lock.synchronized<void>(() async {
      return localStorage.clear();
    });
  }
}
