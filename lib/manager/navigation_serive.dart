import 'package:flutter/material.dart';
import 'package:yvr_assistant/manager/global.dart';

class NavigationService {
  static dynamic pushNamed(String route, {dynamic arguments}) {
    return Global.navigatorKey.currentState
        ?.pushNamed(route, arguments: arguments);
  }

  static dynamic push<T extends Object>(Route<T> route) {
    return Global.navigatorKey.currentState?.push(route);
  }

  static dynamic goBack<T extends Object>([T result]) {
    return Global.navigatorKey.currentState?.pop(result);
  }

  static pushLoginAndRemoveUntilFirst() async {
    await Global.navigatorKey.currentState
        .pushNamedAndRemoveUntil("/login", (route) => route.isFirst);
  }
}
