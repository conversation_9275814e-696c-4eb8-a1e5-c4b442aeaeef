import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class Global {
  // ignore: unused_field
  static SharedPreferences _prefs;
  // 单例
  static Global instance = Global();

  // 测试 wxd930ea5d5a258f4f<官方>    wx6cb2aac952d884cc
  static String wechatAppid = "wx6cb2aac952d884cc";
  static String kUniversalLink = "https://api.yvrdream.com/ios/";

  static String btIOSDevUUID = "1000ffe2";
  static String btAndroidDevUUID = "1000ffe2-0000-1000-8000-00805f9b34fb";
  static String btServiceUUID = "1000ffe2-0000-1000-8000-00805f9b34fb";
  static String btBaseCharUUID = "0000ffd5-0000-1000-8000-00805f9b34fb";

  static String bleCharUUID = "ffd5";
  static String bleServiceUUID = "1000ffe2";

  // 屏幕信息
  static MediaQueryData mediaQueryData = MediaQueryData.fromWindow(window);
  static double screenWidth = mediaQueryData.size.width;
  static double screenHeight = mediaQueryData.size.height;
  static double paddingBottom = mediaQueryData.padding.bottom;
  static double statusBarHeight = mediaQueryData.padding.top;

  static double winPxRatio = WidgetsBinding.instance.window.devicePixelRatio;
  static int cacheWidth = (Global.screenWidth * winPxRatio).toInt();
  static final double appCellImageW = (screenWidth - 12 - 32) / 2;

  // 主题颜色
  static Color kBgColor = Color(0xFF17191B);
  static Color kTextColor = Color(0xFFE8E8E8);
  static Color kButtonColor = Color(0xFF4F7FFE);

  // 前一个月时间
  static String kPreMonthDate =
      DateTime.now().add(Duration(days: -30)).toString();

  // 是否为release版
  static bool get isRelease => bool.fromEnvironment("dart.vm.product");

  /// 环境
  static String kEnvMode = "EnvMode";

  // 通知名称
  static String kLogout = "kLogout";
  static String kNotiDevId = "kNotiDevId";
  static String kVRStopProj = "kVRStopProj";
  static String kPayAppList = "kPayAppList";
  static String kRefreshHome = "kRefreshHome";
  static String kNotiBleModel = "kNotiBleModel";
  static String kReloadDevices = "kReloadDevices";
  static String kPhoneStopProj = "kPhoneStopProj";
  static String kStartRecording = "kStartRecording";
  static String kRecordingAgain = "kRecordingAgain";
  static String kAppStopServe = "kAppStopServe";
  static String kBleDisconnect = "kBleDisconnect";
  static String kUserHaveDevice = "kUserHaveDevice";
  static String kUserAvatar = "kUserAvatar";
  static String kPunchAdImageUrl = "kPunchAdImageUrl";
  static String kPunchRuleImageUrl = "kPunchRuleImageUrl";
  static String kCurrentProdId = "kCurrentProdId";
  static String kCurrentSubId = "kCurrentSubId";
  //最近连接成功的设备id
  static String kConnectedDevId = "kConnectedDevId";

  /// 投屏状态值 0：设备页未开始 1：局域网投屏 2：拼接录制投屏
  static String kInProjection = "kInProjection";
  static String kPhoneRecordingDone = "kPhoneRecordingDone";
  static String kPhoneStopRecording = "kPhoneStopRecording";
  static String kRefreshAppPayStatus = "kRefreshAppPayStatus";
  static String kStartRecordingOnlyProj = "kStartRecordingOnlyProj";
  static String kVRStorageEnough = "kVRStorageEnough";
  static String kRefreshRecordingPage = "kRefreshRecordingPage";
  static String kAppInBackground = "kAppInBackground";
  static String kVRStopRecording = "kVRStopRecording";
  static String kBleSendTimestamp = "kBleSendTimestamp";
  static String kBleTimestampDifference = "kBleTimestampDifference";
  static String kVRRecordingIs1200x900 = "kVRRecordingIs1200x900";
  static String kOnlyRecordingProjWithoutJump = "kOnlyRecordingProjWithoutJump";

  ///蓝牙设备连接相关
  static String connectDeviceKey = "connectDeviceKey";
  static String connectSuccessKey = "connectSuccessKey";
  static String deviceListRefreshKey = "deviceListRefreshKey";
  static String deviceTabEventKey = "deviceTabEventKey";
  static String netWorkSyncKey = "netWorkSyncKey";
  static String vrNetworkChangeKey = "vrNetworkChangeKey";
  static String phoneWifiChangeKey = "phoneWifiChangeKey"; //手机端网络发生变化
  static String scanNoticeKey = "scanNoticeKey"; //启动扫描
  static String scanResultsKey = "scanResultsKey"; //扫描返回
  static String kResultTakingPhotos = "kResultTakingPhotos"; // VR回复拍照结果
  static String kResultStartRecording = "kResultStartRecording"; // VR回复开始录像结果
  static String kResultStopRecording = "kResultStopRecording"; // VR回复结束录像结果

  //切换首页tab事件key
  static String switchTabEventKey = "switchTabEventKey";

  // 显示待发布应用的账号: auth 0普通用户 1内部测试人员
  static String kIsTestUser = "kIsTestUser";
  // 开发者账号：auth 0普通用户 1开发者
  static String kIsDeveloper = "kIsDeveloper";

  static String kPhoneLaunchApp = "kPhoneLaunchApp";
  static String kPhoneLaunchAppPKG = "kPhoneLaunchAppPKG";
  static String kLaunchAppResult = "kLaunchAppResult";
  static String kNavigateTeenDevId = "kNavigateTeenDevId";

  static GlobalKey<NavigatorState> navigatorKey = GlobalKey();
  int kSocialTabIdx = 0;
  static BuildContext context =
      Global.navigatorKey.currentState.overlay.context;

  ///当投屏、拼接录制时，若vr发生网络变化，关闭页面并返回-1
  static const int vrWifiChangeValue = -1;

  ///当投屏、拼接录制时，若手机发生网络变化，关闭页面并返回-2
  static const int phoneWifiChangeValue = -2;

  ///vr关息屏时
  static const int vrCloseScreenValue = -3;

  /// 本地设备数据名称
  static const String kDevModel = "DevModel";

  /// VR设备型号列表： ['MR', 'YVR 2', 'YVR 1']
  static const List<String> kDevModelOptions = ['MR', 'YVR 2', 'YVR 1'];
}
