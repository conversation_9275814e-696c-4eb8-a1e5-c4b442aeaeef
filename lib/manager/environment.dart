import 'package:yvr_assistant/manager/host_config.dart';

class Environment {
  HostConfig config;
  Environment._internal();

  static const String DEV = 'DEV';
  static const String UAT = 'UAT';
  static const String PROD = 'PROD';
  static const String STAGING = 'STAGING';
  static final Environment _singleton = Environment._internal();

  static final List<Map<String, dynamic>> envMap = [
    {"name": "生产", "mode": "PROD"},
    {"name": "准生产", "mode": "UAT"},
    {"name": "测试", "mode": "STAGING"},
    {"name": "开发", "mode": "DEV"},
  ];

  factory Environment() {
    return _singleton;
  }

  initConfig(String environment) {
    config = _getConfig(environment);
  }

  HostConfig _getConfig(String environment) {
    switch (environment) {
      case Environment.DEV:
        return DevConfig();

      case Environment.UAT:
        return UatConfig();

      case Environment.STAGING:
        return StagingConfig();

      default:
        return ProdConfig();
    }
  }
}
