import 'package:provider/provider.dart';

import 'package:provider/single_child_widget.dart';
import 'package:yvr_assistant/view_model/comments_vmodel.dart';
import 'package:yvr_assistant/view_model/friends_vmodel.dart';
import 'package:yvr_assistant/view_model/home_vmodel.dart';
import 'package:yvr_assistant/view_model/locale_model.dart';
import 'package:yvr_assistant/view_model/theme_model.dart';
import 'package:yvr_assistant/view_model/unread_vmodel.dart';
import 'package:yvr_assistant/view_model/user_vmodel.dart';

List<SingleChildStatelessWidget> providers = [
  ...independentServices,
  ...uiConsumableProviders
];

/// 独立的model
List<SingleChildStatelessWidget> independentServices = [
  ChangeNotifierProvider<ThemeModel>(
    create: (context) => ThemeModel(),
  ),
  ChangeNotifierProvider<LocaleModel>(
    create: (context) => LocaleModel(),
  ),
];

List<SingleChildStatelessWidget> uiConsumableProviders = [
  ChangeNotifierProvider<HomeVModel>(
    create: (context) => HomeVModel(),
  ),
  ChangeNotifierProvider<UserVModel>(
    create: (context) => UserVModel(),
  ),
  ChangeNotifierProvider<UserIsComment>(
    create: (context) => UserIsComment(),
  ),
  ChangeNotifierProvider<UnreadVModel>.value(
    value: UnreadVModel(),
  ),
  ChangeNotifierProvider<FriendVModel>.value(
    value: FriendVModel.getInstance(),
  )
];
