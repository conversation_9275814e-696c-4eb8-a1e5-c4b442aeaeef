import 'package:flutter/widgets.dart';

import 'package:yvr_assistant/generated/l10n.dart';

abstract class HostConfig {
  String get apiHost;

  String get docHost;

  String get socketHost;

  String get zegoAppSign;

  int get zegoAppID;

  bool get zegoIsTestEnv;

  bool get useHttps;

  bool get trackEvents;

  bool get reportErrors;

  String getRechargeServiceAgreementUrl(BuildContext context);

  String getAccountCancellationAgreementUrl(BuildContext context);
}

class DevConfig implements HostConfig {
  String get apiHost => "https://apidev.yvrdream.com/";

  String get docHost => "https://apitest.yvrdream.com/yvrdvcenter/#/";

  String get socketHost =>
      "wss://apidev.yvrdream.com:9099/vrmcsys/fdserver/websocket";

  int get zegoAppID => **********;

  String get zegoAppSign =>
      "ec1b28a2818f1a64e0dff795830319c3058bd3b9b71e24cf534ee2463f89aa09";

  bool get zegoIsTestEnv => false;

  bool get reportErrors => false;

  bool get trackEvents => false;

  bool get useHttps => false;

  @override
  String getAccountCancellationAgreementUrl(BuildContext context) {
    return YLocal.current.account_cancellation_agreement_url_dev;
  }

  @override
  String getRechargeServiceAgreementUrl(BuildContext context) {
    return YLocal.current.recharge_service_agreement_url_dev;
  }
}

class StagingConfig implements HostConfig {
  String get apiHost => "https://apitest.yvrdream.com/";

  String get docHost => "https://apitest.yvrdream.com/yvrdvcenter/#/";

  String get socketHost =>
      "wss://apitestlc.yvrdream.com/vrmcsys/fdserver/websocket";

  int get zegoAppID => **********;

  String get zegoAppSign =>
      "ec1b28a2818f1a64e0dff795830319c3058bd3b9b71e24cf534ee2463f89aa09";

  bool get zegoIsTestEnv => false;

  bool get reportErrors => true;

  bool get trackEvents => false;

  bool get useHttps => true;

  @override
  String getAccountCancellationAgreementUrl(BuildContext context) {
    return YLocal.current.account_cancellation_agreement_url_dev;
  }

  @override
  String getRechargeServiceAgreementUrl(BuildContext context) {
    return YLocal.current.recharge_service_agreement_url_dev;
  }
}

class UatConfig implements HostConfig {
  String get apiHost => "https://apiuat.yvrdream.com/";

  String get docHost => "https://apiuat.yvrdream.com/yvrdvcenter/#/";
  String get socketHost =>
      "wss://apiuatlc.yvrdream.com/vrmcsys/fdserver/websocket";

  int get zegoAppID => **********;

  String get zegoAppSign =>
      "ec1b28a2818f1a64e0dff795830319c3058bd3b9b71e24cf534ee2463f89aa09";

  bool get zegoIsTestEnv => false;

  bool get reportErrors => true;

  bool get trackEvents => true;

  bool get useHttps => true;

  @override
  String getAccountCancellationAgreementUrl(BuildContext context) {
    return YLocal.current.account_cancellation_agreement_url_release;
  }

  @override
  String getRechargeServiceAgreementUrl(BuildContext context) {
    return YLocal.current.recharge_service_agreement_url_release;
  }
}

class ProdConfig implements HostConfig {
  String get apiHost => "https://api.yvrdream.com/";

  String get docHost => "https://developer.yvrdream.com/#/";

  String get socketHost =>
      "wss://apilc.yvrdream.com/vrmcsys/fdserver/websocket";

  int get zegoAppID => **********;

  String get zegoAppSign =>
      "ec1b28a2818f1a64e0dff795830319c3058bd3b9b71e24cf534ee2463f89aa09";

  bool get zegoIsTestEnv => false;

  bool get reportErrors => true;

  bool get trackEvents => true;

  bool get useHttps => true;

  @override
  String getAccountCancellationAgreementUrl(BuildContext context) {
    return YLocal.current.account_cancellation_agreement_url_release;
  }

  @override
  String getRechargeServiceAgreementUrl(BuildContext context) {
    return YLocal.current.recharge_service_agreement_url_release;
  }
}
