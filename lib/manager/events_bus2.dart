import '../model/official_event_detail_model.dart';
import '../model/teen_model.dart';
import 'event_bus.dart';

class EventData {
  int type; //发送事件的类型
  int subType; //发送事件的二级类型
  dynamic data; //发送事件的数据

  EventData(this.type, {this.subType, this.data});

  factory EventData.build(int type, {int subType, dynamic data}) =>
      EventData(type, subType: subType, data: data);

  T dataAs<T>() {
    return data as T;
  }
}

typedef EventFilterCallback<T> = void Function(int subType, T value);

class AppEvent {
  static const int EVENT_PERSONAL_EVENT_CHANGED = 1; //活动
  static const int EVENT_SUB_PERSONAL_EVENT_CREATED = 11; //活动创建
  static const int EVENT_SUB_PERSONAL_EVENT_EDIT = 12; //活动编辑
  static const int EVENT_SUB_PERSONAL_EVENT_DELETE = 13; //活动删除

  static const int EVENT_OFFICIAL_EVENT_APPLY = 2; //数据详情更新，需要更新列表。

  static const int EVENT_OFFICIAL_EVENT_CHANGED = 3; //官方活动
  static const int EVENT_SUB_OFFICIAL_EVENT_DIRTY =
      31; //官方活动脏数据，该数据服务端已经过期，本地不应该存储，需要更新列表

  static const int EVENT_EVENT_REFRESH = 4;

  static const int EVENT_TEEN_MODE_CHANGED = 5; //青少年模式
  static const int EVENT_SUB_TEEN_DEV_DIRTY = 51; //设备不存在
  static const int EVENT_SUB_TEEN_MODE_DIRTY = 52; //青少年模式不存在
  static const int EVENT_SUB_TEEN_MODE_DISABLED = 53; //青少年模式已关闭，无法操作

  static const int EVENT_TEEN_MODE_APPLY = 6;

  static const int EVENT_LOGIN_SUCCESS = 100;

  static void firePersonalChangeEvent(int subType, int eventId) {
    eventBus.fire(
      EventData(EVENT_PERSONAL_EVENT_CHANGED, subType: subType, data: eventId),
    );
  }

  static void fireLoginSuccessEvent(int userId) {
    eventBus.fire(
      EventData(EVENT_LOGIN_SUCCESS, data: userId),
    );
  }

  static void fireOfficialEventApplyEvent(EventApplyEventData data) {
    eventBus.fire(
      EventData(EVENT_OFFICIAL_EVENT_APPLY, data: data),
    );
  }

  static void fireOfficialChangeEvent(int subType, int eventId) {
    eventBus.fire(
      EventData(EVENT_OFFICIAL_EVENT_CHANGED, subType: subType, data: eventId),
    );
  }

  static void fireEventRefreshEvent() {
    eventBus.fire(
      EventData(EVENT_EVENT_REFRESH),
    );
  }

  static void fireTeenModeChangeEvent(int subType, String devId) {
    eventBus.fire(
      EventData(EVENT_TEEN_MODE_CHANGED, subType: subType, data: devId),
    );
  }

  static void fireTeenModeApplyEvent(TeenModeModel data) {
    eventBus.fire(
      EventData(EVENT_TEEN_MODE_APPLY, data: data),
    );
  }
}

class EventApplyEventData {
  final OfficialEventDetailModel details;
  final int commentNum;

  EventApplyEventData(this.details, this.commentNum);
}
