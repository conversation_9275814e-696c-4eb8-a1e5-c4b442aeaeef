import 'package:flutter/material.dart';
import 'package:yvr_assistant/pages/device/detail/accept_award.dart';
import 'package:yvr_assistant/pages/device/detail/accept_grant.dart';
import 'package:yvr_assistant/pages/device/detail/clock_in.dart';
import 'package:yvr_assistant/pages/device/detail/lan_projection.dart';
import 'package:yvr_assistant/pages/device/detail/merge_video.dart';
import 'package:yvr_assistant/pages/device/detail/recording.dart';
import 'package:yvr_assistant/pages/device/detail/rule_image.dart';
import 'package:yvr_assistant/pages/device/detail/vr_file.dart';
import 'package:yvr_assistant/pages/device/detail/zoom_image.dart';
import 'package:yvr_assistant/pages/home/<USER>/pay/option_coupon.dart';
import 'package:yvr_assistant/pages/login/register_user.dart';
import 'package:yvr_assistant/pages/profile/account/account_safe.dart';
import 'package:yvr_assistant/pages/profile/account/withdraw/apply_withdraw.dart';
import 'package:yvr_assistant/pages/profile/account/withdraw/confirm_withdraw.dart';
import 'package:yvr_assistant/pages/profile/account/withdraw/log_off.dart';
import 'package:yvr_assistant/pages/profile/account/withdraw/log_off_protocol.dart';
import 'package:yvr_assistant/pages/profile/account/withdraw/verify_identity.dart';
import 'package:yvr_assistant/pages/profile/pay/consumption.dart';
import 'package:yvr_assistant/pages/profile/pay/wallet.dart';
import 'package:yvr_assistant/pages/social/detail/friend_app_details.dart';
import 'package:yvr_assistant/pages/social/detail/personal_homepage.dart';
import 'package:yvr_assistant/pages/social/social.dart';
import 'package:yvr_assistant/pages/tabbar/main_tab_foreign.dart';

// 首页
import 'package:yvr_assistant/pages/home/<USER>';
import 'package:yvr_assistant/pages/home/<USER>/mine_app.dart';
import 'package:yvr_assistant/pages/home/<USER>/apps.dart';
import 'package:yvr_assistant/pages/home/<USER>/category.dart';
import 'package:yvr_assistant/pages/home/<USER>/comment/comment.dart';
import 'package:yvr_assistant/pages/home/<USER>/comment/judge.dart';
import 'package:yvr_assistant/pages/home/<USER>/prod.dart';
import 'package:yvr_assistant/pages/home/<USER>/search.dart';
import 'package:yvr_assistant/pages/home/<USER>/message.dart';
import 'package:yvr_assistant/pages/home/<USER>/pay/pay_result.dart';
import 'package:yvr_assistant/pages/home/<USER>';

// 社交
import 'package:yvr_assistant/pages/social/detail/activity_create.dart';
import 'package:yvr_assistant/pages/social/detail/search_friends.dart';
import 'package:yvr_assistant/pages/social/detail/activity_detl.dart';
import 'package:yvr_assistant/pages/social/detail/all_activitys.dart';
import 'package:yvr_assistant/pages/social/detail/blacklist.dart';
import 'package:yvr_assistant/pages/social/detail/invite_friends.dart';

// 设备
import 'package:yvr_assistant/pages/device/detail/match_result.dart';

// 我的
import 'package:yvr_assistant/pages/profile/profile.dart';
import 'package:yvr_assistant/pages/profile/account/phone.dart';
import 'package:yvr_assistant/pages/profile/others/feedback.dart';
import 'package:yvr_assistant/pages/profile/others/question.dart';
import 'package:yvr_assistant/pages/profile/others/privacy.dart';
import 'package:yvr_assistant/pages/profile/pay/card_code.dart';
import 'package:yvr_assistant/pages/profile/pay/pay_history.dart';

// 登录
// import 'package:yvr_assistant/pages/login/login.dart';
import 'package:yvr_assistant/pages/login/register.dart';
import 'package:yvr_assistant/pages/login/pwd_login.dart';
import 'package:yvr_assistant/pages/login/reset_pwd.dart';
import 'package:yvr_assistant/pages/webview/paypal_web.dart';

import 'package:yvr_assistant/pages/webview/yvr_web.dart';
import 'package:yvr_assistant/pages/webview/local_web.dart';
import 'package:yvr_assistant/utils/data_event_id.dart';
import 'package:yvr_assistant/utils/data_record.dart';
import '../pages/device/detail/add_device.dart';
import '../pages/device/device_list.dart';
import '../pages/home/<USER>/bundled_application.dart';
import '../pages/login/email_validation.dart';
import '../pages/profile/account/user_info.dart';
import '../pages/social/detail/event_details.dart';
import 'package:yvr_assistant/pages/device/teen/intro.dart';
import 'package:yvr_assistant/pages/device/teen/reset_passwd.dart';
import 'package:yvr_assistant/pages/device/teen/setting.dart';
import '../pages/device/teen/menu.dart';
import '../pages/device/teen/passwd.dart';
import '../pages/device/teen/verify.dart';
import '../pages/social/detail/friend_app_list.dart';

final routes = {
  //命名路由
  '/': (context) => MainTab(),
  // 首页
  '/home': (context) => HomePage(),
  '/yvr_web': (context, {arguments}) => YVRWebPage(arguments: arguments),
  '/local_web': (context, {arguments}) => LocalWebPage(arguments: arguments),

  '/paypal_web': (context, {arguments}) => PayPalWebPage(arguments: arguments),
  '/mine_app': (context, {arguments}) => MineAppPage(arguments: arguments),
  '/apps': (context, {arguments}) => AppsPage(arguments: arguments),
  '/category': (context) => CategoryPage(),
  '/comment': (context, {arguments}) => CommentPage(arguments: arguments),
  '/judge': (context, {arguments}) => JudgePage(arguments: arguments),
  '/prod': (context, {arguments}) => ProdPage(arguments: arguments),
  '/option_coupon': (context, {arguments}) =>
      OptionCouponPage(arguments: arguments),
  '/pay_result': (context, {arguments}) => PayResultPage(arguments: arguments),
  '/search': (context) => SearchPage(),
  '/message': (context) => MessagePage(),
  '/logan': (context) => LoganPage(),
  '/bundledApplication': (context, {arguments}) =>
      BundledApplicationPage(arguments: arguments),

// 活动
  '/social': (context) => SocialPage(),
  '/activity_create': (context, {arguments}) =>
      ActivityCreatePage(arguments: arguments),
  '/activity_detl': (context, {arguments}) =>
      ActivityDetlPage(arguments: arguments),
  '/person_home': (context, {arguments}) =>
      PersonalHomePage(arguments: arguments),
  '/search_friends': (context, {arguments}) =>
      SearchFriendsPage(arguments: arguments),
  '/blacklist': (context) => BlackListPage(),
  '/invite_friends': (context, {arguments}) =>
      InviteFriendsPage(arguments: arguments),
  '/all_activitys': (context, {arguments}) =>
      AllActivitysPage(arguments: arguments),
  '/event_details': (context, {arguments}) =>
      EventDetailsPage(arguments: arguments),

// 设备
  '/deviceList': (context, {arguments}) => DeviceListPage(),
  '/match_result': (context, {arguments}) =>
      MatchResultPage(arguments: arguments),
  '/add_search': (context, {arguments}) => AddDevicePage(arguments: arguments),
  '/clock_in': (context, {arguments}) => ClockInPage(arguments: arguments),
  '/vr_file': (context, {arguments}) => VrFileListPage(arguments: arguments),
  '/lan_proj': (context, {arguments}) =>
      LanProjectionPage(arguments: arguments),
  '/recording': (context, {arguments}) => RecordingPage(arguments: arguments),
  '/merge_video': (context, {arguments}) =>
      MergeVideoPage(arguments: arguments),
  '/accept_award': (context, {arguments}) =>
      AcceptAwardPage(arguments: arguments),
  '/accept_grant': (context, {arguments}) =>
      AcceptGrantPage(arguments: arguments),
  '/rule_image': (context, {arguments}) => RuleImagePage(arguments: arguments),
  '/zoom_image': (context, {arguments}) => ZoomImagePage(arguments: arguments),

// 我的
  '/profile': (context) => ProfilePage(),
  '/user': (context, {arguments}) => UserInfoPage(),
  '/phone': (context) => PhonePage(),
  '/feedback': (context) => FeedbackPage(),
  '/question': (context) => QuestionPage(),
  '/privacy': (context) => PrivacyPage(),
  '/account_safe': (context) => AccountSafePage(),
  '/card_code': (context) => CardCodePage(),
  '/pay_history': (context) => PayHistoryPage(),
  '/wallet': (context, {arguments}) => CouponPage(arguments: arguments),
  '/consumption': (context, {arguments}) =>
      ConsumptionPage(arguments: arguments),
  '/log_off': (context) => LogOffAccountPage(),
  '/verify_identity': (context) => VerifyIdentityPage(),
  '/apply_withdraw': (context) => ApplyWithdrawPage(),
  '/confirm_withdraw': (context) => ConfirmWithdrawPage(),
  '/log_off_protocol': (context) => LogOffProtocolPage(),

// 登录
//   '/login': (context) => LoginPage(),
  '/login': (context) => PwdLoginPage(),
  '/register': (context) => RegisterPage(),
  '/register_user': (context) => RegisterUserPage(),
  '/pwd_login': (context) => PwdLoginPage(),
  '/reset_pwd': (context, {arguments}) => ResetPwdPage(arguments: arguments),
  '/email_validation': (context, {arguments}) => EmailValidationPage(arguments),

  //青少年
  '/teen_intro': (context) => TeenIntroPage(),
  '/teen_passwd': (context, {arguments}) =>
      TeenPasswdPage(arguments: arguments),
  '/teen_verify': (context, {arguments}) =>
      TeenVerifyPage(arguments: arguments),
  '/teen_menu': (context, {arguments}) => TeenMenuPage(arguments: arguments),
  '/teen_setting': (context, {arguments}) =>
      TeenSettingPage(arguments: arguments),
  '/teen_reset_passwd': (context, {arguments}) =>
      TeenResetPasswdPage(arguments: arguments),
  '/friend_app_list': (context) => FriendAppListPage(),
  '/friend_app_details': (context, {arguments}) => FriendAppDetailsPage(
        arguments: arguments,
      ),
};

// ignore: missing_return, top_level_function_literal_block
var onGenerateRoute = (RouteSettings settings) {
  final String name = settings.name;
  if (DataForEventId().eventId.containsKey(name)) {
    String eventId = DataForEventId().eventId[name];
    DataRecord().saveData(eventId: eventId);
  }
  final Function pageContentBuilder = routes[name];
  if (pageContentBuilder != null) {
    if (settings.arguments != null) {
      final Route route = MaterialPageRoute(
          builder: (context) =>
              pageContentBuilder(context, arguments: settings.arguments));
      return route;
    } else {
      final Route route =
          MaterialPageRoute(builder: (context) => pageContentBuilder(context));
      return route;
    }
  }
};

/// Pop路由
class PopRoute extends PopupRoute {
  final Duration _duration = Duration(milliseconds: 300);
  Widget child;

  PopRoute({@required this.child});

  @override
  Color get barrierColor => null;

  @override
  bool get barrierDismissible => true;

  @override
  String get barrierLabel => null;

  @override
  Widget buildPage(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation) {
    return child;
  }

  @override
  Duration get transitionDuration => _duration;
}
