import 'package:flutter/widgets.dart';
import 'package:yvr_assistant/manager/environment.dart';
import 'package:yvr_assistant/utils/hive_box/hive_boxes.dart';
import 'package:yvr_assistant/utils/log.dart';
import '../view_model/user_vmodel.dart';
import 'global.dart';

typedef SubjectCallback = Function(int id);
Future<bool> navigateByUrl(BuildContext context, String url,
    {VoidCallback onLoginCallback,
    String title = "",
    SubjectCallback subjectCallback}) async {
  Uri uri = Uri.parse(url);
  if (uri.scheme == "yvrurl") {
    int id = int.parse(uri.queryParameters['id']);
    switch (uri.host) {
      case "app":
        await Navigator.pushNamed(context, '/prod', arguments: {"id": id});
        return true;
      case "subject":
        await Navigator.pushNamed(context, '/apps',
            arguments: {"isSingleSubject": true, "title": "", "id": id});
        return true;
      case "theme":
        await Navigator.pushNamed(context, '/apps',
            arguments: {"isSingleSubject": false, "title": "", "id": id});
        return true;
      case "activity":
        bool isLoggedIn = await _checkLoggedIn();
        if (isLoggedIn) {
          await Navigator.pushNamed(context, "/event_details",
              arguments: {"id": id});
          return true;
        } else {
          onLoginCallback?.call();
        }
        return false;
      default:
        return false;
    }
  } else {
    await Navigator.pushNamed(context, '/yvr_web',
        arguments: {"url": url, "title": title});
    return true;
  }
}

Future<bool> _checkLoggedIn() async {
  if (DBUtil.instance.userBox.get(kToken) != null) {
    return true;
  }
  await Global.navigatorKey.currentState
      .pushNamedAndRemoveUntil("/login", (route) => route.isFirst);
  return DBUtil.instance.userBox.get(kToken) != null;
}

void navigateToAgreementPage(
    {@required BuildContext context,
    @required String pageName,
    String title = "YVR",
    Function callback}) {
  String url = Environment().config.docHost + pageName;
  Log.d('message: $url');
  Navigator.pushNamed(context, '/yvr_web',
      arguments: {"url": url, "title": title, "isNeedLogin": 1}).then((value) {
    if (callback != null) {
      callback();
    }
  });
}
