import 'package:yvr_assistant/model/apps_model.dart';
import 'package:yvr_assistant/model/subject_model.dart';

class ThemeDataModel {
  ThemeDataModel({
    this.flag,
    this.name,
    this.id,
    this.type,
    this.apps,
    this.subjects,
  });

  int flag;
  String name;
  int id;
  int type;
  List<AppsModel> apps;
  List<SubjectModel> subjects;

  factory ThemeDataModel.fromJson(Map<String, dynamic> json) => ThemeDataModel(
        flag: json["flag"],
        name: json["name"],
        id: json["id"],
        type: json["type"],
        apps: json["apps"] == null
            ? []
            : List<AppsModel>.from(
                json["apps"].map((x) => AppsModel.fromJson(x))),
        subjects: json["subjects"] == null
            ? []
            : List<SubjectModel>.from(
                json["subjects"].map((x) => SubjectModel.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "flag": flag,
        "name": name,
        "id": id,
        "type": type,
        "apps":
            apps == null ? [] : List<dynamic>.from(apps.map((x) => x.toJson())),
        "subjects": subjects == null
            ? []
            : List<dynamic>.from(subjects.map((x) => x.toJson())),
      };
}
