class AppInfo {
  AppInfo({
    this.id,
    this.pkg,
    this.scover,
    this.rcover,
    this.name,
    this.cgy,
    this.tag,
    this.sprice,
    this.bprice,
    this.state,
    this.count,
    this.aver,
  });

  AppInfo.fromJson(dynamic json) {
    id = json['id'];
    pkg = json['pkg'];
    scover = json['scover'];
    rcover = json['rcover'];
    name = json['name'];
    cgy = json['cgy'];
    tag = json['tag'];
    sprice = json['sprice'];
    bprice = json['bprice'];
    state = json['state'];
    count = json['count'];
    aver = json['aver'];
  }

  int id;
  String pkg;
  String scover;
  String rcover;
  String name;
  int cgy;
  String tag;
  int sprice;
  int bprice;
  int state;
  int count;
  double aver;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['pkg'] = pkg;
    map['scover'] = scover;
    map['rcover'] = rcover;
    map['name'] = name;
    map['cgy'] = cgy;
    map['tag'] = tag;
    map['sprice'] = sprice;
    map['bprice'] = bprice;
    map['state'] = state;
    map['count'] = count;
    map['aver'] = aver;
    return map;
  }
}

class EventOfficialInfo {
  EventOfficialInfo({
    this.id,
    this.eventOfficialId,
    this.eventName,
    this.eventTime,
    this.startTime,
    this.endTime,
    this.appId,
    this.appName,
    this.pictureType,
    this.eventPicture,
    this.eventDescribe,
    this.whetherShow,
    this.eventStatus,
    this.falseNum,
    this.sort,
    this.commentNum,
    this.realNum,
    this.publishTime,
  });

  EventOfficialInfo.fromJson(dynamic json) {
    id = json['id'];
    eventOfficialId = json['eventOfficialId'];
    eventName = json['eventName'];
    eventTime = json['eventTime'];
    startTime = json['startTime'];
    endTime = json['endTime'];
    appId = json['appId'];
    appName = json['appName'];
    pictureType = json['pictureType'];
    eventPicture = json['eventPicture'];
    eventDescribe = json['eventDescribe'];
    whetherShow = json['whetherShow'];
    eventStatus = json['eventStatus'];
    falseNum = json['falseNum'];
    sort = json['sort'];
    commentNum = json['commentNum'];
    realNum = json['realNum'];
    focusesPortraits = (json['focusesPortraits'] as List<dynamic>)?.cast<String>();
    publishTime = json['publishTime'];
    focusesStatus = json['focusesStatus'];
  }

  int id;
  int eventOfficialId;
  String eventName;
  String eventTime;
  String startTime;
  String endTime;
  int appId;
  dynamic appName;
  dynamic pictureType;
  String eventPicture;
  String eventDescribe;
  dynamic whetherShow;
  String eventStatus;
  int falseNum;
  int sort;
  int commentNum;
  int realNum;
  List<String> focusesPortraits;
  String publishTime;
  String focusesStatus;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['eventOfficialId'] = eventOfficialId;
    map['eventName'] = eventName;
    map['eventTime'] = eventTime;
    map['startTime'] = startTime;
    map['endTime'] = endTime;
    map['appId'] = appId;
    map['appName'] = appName;
    map['pictureType'] = pictureType;
    map['eventPicture'] = eventPicture;
    map['eventDescribe'] = eventDescribe;
    map['whetherShow'] = whetherShow;
    map['eventStatus'] = eventStatus;
    map['falseNum'] = falseNum;
    map['sort'] = sort;
    map['commentNum'] = commentNum;
    map['realNum'] = realNum;
    map['focusesPortraits'] = focusesPortraits;
    map['publishTime'] = publishTime;
    map['focusesStatus'] = focusesStatus;
    return map;
  }
}

class OfficialEventDetailModel {
  OfficialEventDetailModel({
    this.eventOfficialInfo,
    this.appInfo,
  });

  OfficialEventDetailModel.fromJson(dynamic json) {
    eventOfficialInfo = json['eventOfficialInfo'] != null
        ? EventOfficialInfo.fromJson(json['eventOfficialInfo'])
        : null;
    appInfo =
        json['appInfo'] != null ? AppInfo.fromJson(json['appInfo']) : null;
  }

  EventOfficialInfo eventOfficialInfo;
  AppInfo appInfo;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (eventOfficialInfo != null) {
      map['eventOfficialInfo'] = eventOfficialInfo.toJson();
    }
    if (appInfo != null) {
      map['appInfo'] = appInfo.toJson();
    }
    return map;
  }
}
