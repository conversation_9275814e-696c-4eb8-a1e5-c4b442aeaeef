// To parse this JSON data, do
//
//     final commentModel = commentModelFromJson(jsonString);

import 'dart:convert';

CommentModel commentModelFromJson(String str) =>
    CommentModel.fromJson(json.decode(str));

String commentModelToJson(CommentModel data) => json.encode(data.toJson());

class CommentModel {
  CommentModel({
    this.id,
    this.nick,
    this.avatar,
    this.time,
    this.star,
    this.sex,
    this.content,
    this.auth,
  });

  int id;
  String nick;
  String avatar;
  String time;
  int star;
  int sex;
  String content;
  int auth;

  factory CommentModel.fromJson(Map<String, dynamic> json) => CommentModel(
        id: json["id"],
        nick: json["nick"],
        avatar: json["avatar"],
        time: json["time"],
        star: json["star"],
        sex: json["sex"],
        content: json["content"],
        auth: json["auth"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "nick": nick,
        "avatar": avatar,
        "time": time,
        "star": star,
        "sex": sex,
        "content": content,
        "auth": auth,
      };
}
