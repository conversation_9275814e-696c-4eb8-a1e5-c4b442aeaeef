// To parse this JSON data, do
//
//     final verionModel = verionModelFromJson(jsonString);

import 'dart:convert';

VerionModel verionModelFromJson(String str) => VerionModel.fromJson(json.decode(str));

String verionModelToJson(VerionModel data) => json.encode(data.toJson());

class VerionModel {
  VerionModel({
    this.version,
    this.note,
    // this.forced,
    this.enforce,
    this.supVer,
    this.popup
  });

  String version;
  String note;
  // int forced;
  int enforce;//新版本升级判断字段 0非强制 1强制
  String supVer;//最低支持版本号
  int popup;//0不弹窗 1弹窗
  //此字段为了传参方便手动判断并传的值
  bool isLessThanMinVer = false;//当前版本是否低于最低兼容版本

  factory VerionModel.fromJson(Map<String, dynamic> json) => VerionModel(
    version: json["version"],
    note: json["note"],
    // forced: json["forced"],
    enforce: json["enforce"],
    supVer: json["supVer"],
    popup: json["popup"],
  );

  Map<String, dynamic> toJson() => {
    "version": version,
    "note": note,
    // "forced": forced,
    "enforce": enforce,
    "supVer": supVer,
    "popup": popup,
  };
}
