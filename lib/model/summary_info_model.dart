class SummaryInfoModel {
  int subId;
  int promEndTimeStamp;
  int promId;
  int promEndDiffTime;
  String discount;
  int sprice;
  int bprice;
  bool bundle;
  bool hasAllPur;//是否已购买

  SummaryInfoModel(
      {this.subId,
        this.promEndTimeStamp,
        this.promId,
        this.promEndDiffTime,
        this.discount,
        this.sprice,
        this.bprice,
        this.bundle,
        this.hasAllPur});

  SummaryInfoModel.fromJson(Map<String, dynamic> json) {
    subId = json['subId'];
    promEndTimeStamp = json['promEndTimeStamp'];
    promId = json['promId'];
    promEndDiffTime = json['promEndDiffTime'];
    discount = json['discount'];
    sprice = json['sprice'];
    bprice = json['bprice'];
    bundle = json['bundle'];
    hasAllPur = json['hasAllPur'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['subId'] = this.subId;
    data['promEndTimeStamp'] = this.promEndTimeStamp;
    data['promId'] = this.promId;
    data['promEndDiffTime'] = this.promEndDiffTime;
    data['discount'] = this.discount;
    data['sprice'] = this.sprice;
    data['bprice'] = this.bprice;
    data['bundle'] = this.bundle;
    data['hasAllPur'] = this.hasAllPur;
    return data;
  }
}