// To parse this JSON data, do
//
//     final userAssetsModel = userAssetsModelFromJson(jsonString);

import 'dart:convert';

UserAssetsModel userAssetsModelFromJson(String str) =>
    UserAssetsModel.fromJson(json.decode(str));

String userAssetsModelToJson(UserAssetsModel data) =>
    json.encode(data.toJson());

class UserAssetsModel {
  UserAssetsModel({
    this.app,
    this.ycoin,
    this.coupon,
  });

  int app;
  int ycoin;
  int coupon;

  factory UserAssetsModel.fromJson(Map<String, dynamic> json) =>
      UserAssetsModel(
        app: json["app"],
        ycoin: json["ycoin"],
        coupon: json["coupon"],
      );

  Map<String, dynamic> toJson() => {
        "app": app,
        "ycoin": ycoin,
        "coupon": coupon,
      };
}
