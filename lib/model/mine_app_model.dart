// To parse this JSON data, do
//
//     final mineAppModel = mineAppModelFromJson(jsonString);

import 'dart:convert';

MineAppModel mineAppModelFromJson(String str) =>
    MineAppModel.fromJson(json.decode(str));

String mineAppModelToJson(MineAppModel data) => json.encode(data.toJson());

class MineAppModel {
  MineAppModel({
    this.id,
    this.pkg,
    this.name,
    this.time,
    this.price,
    this.scover,
  });

  int id;
  String pkg;
  String name;
  String scover;
  DateTime time;
  int price;

  factory MineAppModel.fromJson(Map<String, dynamic> json) => MineAppModel(
        id: json["id"],
        pkg: json["pkg"],
        name: json["name"],
        scover: json["scover"],
        time: DateTime.parse(json["time"]),
        price: json["price"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "pkg": pkg,
        "name": name,
        "time": time.toIso8601String(),
        "price": price,
        "scover": scover,
      };
}
