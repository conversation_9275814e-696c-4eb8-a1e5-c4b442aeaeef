// To parse this JSON data, do
//
//     final notiPayModel = notiPayModelFromJson(jsonString);

import 'dart:convert';

NotiPayModel notiPayModelFromJson(String str) =>
    NotiPayModel.fromJson(json.decode(str));

String notiPayModelToJson(NotiPayModel data) => json.encode(data.toJson());

class NotiPayModel {
  NotiPayModel({
    this.id,
    this.appId,
    this.appName,
    this.scover,
    this.rcover,
    this.time,
    this.star
  });

  int id;
  int appId;
  String appName;
  String scover;
  String rcover;
  String time;
  int star;//该字段被借用为销售类型， 0:上面的信息都是应用信息， 1：上面的信息都是专题信息

  factory NotiPayModel.fromJson(Map<String, dynamic> json) => NotiPayModel(
        id: json["id"],
        appId: json["appId"],
        appName: json["appName"],
        scover: json["scover"],
        rcover: json["rcover"],
        time: json["time"],
        star: json["star"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "appId": appId,
        "appName": appName,
        "scover": scover,
        "rcover": rcover,
        "time": time,
        "star":star
      };
}
