// To parse this JSON data, do
//
//     final clockInModel = clockInModelFromJson(jsonString);

import 'dart:convert';

ClockInModel clockInModelFromJson(String str) =>
    ClockInModel.fromJson(json.decode(str));

String clockInModelToJson(ClockInModel data) => json.encode(data.toJson());

class ClockInModel {
  ClockInModel({
    this.auth,
    this.devId,
    this.status,
    this.fromDate,
    this.toDate,
    this.achieve,
    this.actId,
    this.curDay,
    this.nick,
    this.atvId,
    this.needDay,
    this.dailyMins,
    this.startDate,
    this.endDate,
    this.expireDate,
    this.brdMbPic,
    this.ruleMbPic,
    this.type,
  });

  int auth;
  String devId;
  int status;
  String fromDate;
  String toDate;
  int achieve;
  int actId;
  int curDay;
  String nick;
  int atvId;
  int needDay;
  int dailyMins;
  String startDate;
  String endDate;
  String expireDate;
  String brdMbPic;
  String ruleMbPic;
  int type;

  factory ClockInModel.fromJson(Map<String, dynamic> json) => ClockInModel(
        auth: json["auth"],
        devId: json["devId"],
        status: json["status"],
        fromDate: json["fromDate"],
        toDate: json["toDate"],
        achieve: json["achieve"],
        actId: json["actId"],
        curDay: json["curDay"],
        nick: json["nick"],
        atvId: json["atvId"],
        needDay: json["needDay"],
        dailyMins: json["dailyMins"],
        startDate: json["startDate"],
        endDate: json["endDate"],
        expireDate: json["expireDate"],
        brdMbPic: json["brdMbPic"],
        ruleMbPic: json["ruleMbPic"],
        type: json["type"] ?? 1,
      );

  Map<String, dynamic> toJson() => {
        "auth": auth,
        "devId": devId,
        "status": status,
        "fromDate": fromDate,
        "toDate": toDate,
        "achieve": achieve,
        "actId": actId,
        "curDay": curDay,
        "nick": nick,
        "atvId": atvId,
        "needDay": needDay,
        "dailyMins": dailyMins,
        "startDate": startDate,
        "endDate": endDate,
        "expireDate": expireDate,
        "brdMbPic": brdMbPic,
        "ruleMbPic": ruleMbPic,
        "type": type,
      };
}
