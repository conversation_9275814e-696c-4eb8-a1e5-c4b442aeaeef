// To parse this JSON data, do
//
//     final userModel = userModelFromJson(jsonString);

import 'dart:convert';

UserModel userModelFromJson(String str) => UserModel.fromJson(json.decode(str));

String userModelToJson(UserModel data) => json.encode(data.toJson());

class UserModel {
  UserModel({
    this.sex,
    this.actId,
    this.mobile,
    this.birth,
    this.avatar,
    this.nick,
    this.ycount,
    this.status,
    this.token,
    this.relate,
    this.online,
    this.totalCouponCount,
    this.usingApp,
    this.accountCouponDetailList,
  });

  int sex;
  int actId;
  String mobile;
  String birth;
  String avatar;
  String nick;
  int ycount;
  int status;
  String token;
  int relate;
  int online;
  int totalCouponCount;
  UsingApp usingApp;
  List accountCouponDetailList;

  factory UserModel.fromJson(Map<String, dynamic> json) => UserModel(
        sex: json["sex"],
        actId: json["actId"],
        mobile: json["mobile"],
        birth: json["birth"],
        avatar: json["avatar"],
        nick: json["nick"],
        ycount: json["ycount"],
        status: json["status"],
        token: json["token"],
        relate: json["relate"],
        online: json["online"],
        totalCouponCount: json["totalCouponCount"],
        usingApp: json["usingApp"] != null
            ? UsingApp.fromJson(json["usingApp"])
            : null,
        accountCouponDetailList: json["accountCouponDetailList"] ?? null,
      );

  Map<String, dynamic> toJson() => {
        "sex": sex,
        "actId": actId,
        "mobile": mobile,
        "birth": birth,
        "avatar": avatar,
        "nick": nick,
        "ycount": ycount,
        "status": status,
        "token": token,
        "relate": relate,
        "online": online,
        "totalCouponCount": totalCouponCount,
        "usingApp": usingApp != null ? usingApp.toJson() : null,
        "accountCouponDetailList": accountCouponDetailList != null
            ? accountCouponDetailList.toList()
            : null,
      };
}

class AccountCouponDetailList {
  AccountCouponDetailList({
    this.surplus,
    this.couponInfo,
    this.startTime,
    this.endTime,
    this.couponId,
    this.canUse,
  });

  int surplus;
  CouponInfo couponInfo;
  int startTime;
  int endTime;
  int couponId;
  bool canUse;

  factory AccountCouponDetailList.fromJson(Map<String, dynamic> json) =>
      AccountCouponDetailList(
        surplus: json["surplus"],
        couponInfo: json["couponInfo"] == null
            ? null
            : CouponInfo.fromJson(json["couponInfo"]),
        startTime: json["startTime"],
        endTime: json["endTime"],
        couponId: json["couponId"],
        canUse: json["canUse"],
      );

  Map<String, dynamic> toJson() => {
        "surplus": surplus,
        "couponInfo": couponInfo.toJson(),
        "startTime": startTime,
        "endTime": endTime,
        "couponId": couponId,
        "canUse": canUse,
      };
}

class CouponInfo {
  CouponInfo({
    this.detailDescription,
    this.promEnd,
    this.remark,
    this.updateTime,
    this.shortDescription,
    this.promStart,
    this.ruleContent,
    this.validityPeriod,
    this.createTime,
    this.couponType,
    this.ruleType,
    this.name,
    this.id,
    this.status,
  });

  String detailDescription;
  int promEnd;
  String remark;
  int updateTime;
  String shortDescription;
  int promStart;
  String ruleContent;
  int validityPeriod;
  int createTime;
  int couponType;
  int ruleType;
  String name;
  int id;
  int status;

  factory CouponInfo.fromJson(Map<String, dynamic> json) => CouponInfo(
        detailDescription: json["detailDescription"],
        promEnd: json["promEnd"],
        remark: json["remark"],
        updateTime: json["updateTime"],
        shortDescription: json["shortDescription"],
        promStart: json["promStart"],
        ruleContent: json["ruleContent"],
        validityPeriod: json["validityPeriod"],
        createTime: json["createTime"],
        couponType: json["couponType"],
        ruleType: json["ruleType"],
        name: json["name"],
        id: json["id"],
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "detailDescription": detailDescription,
        "promEnd": promEnd,
        "remark": remark,
        "updateTime": updateTime,
        "shortDescription": shortDescription,
        "promStart": promStart,
        "ruleContent": ruleContent,
        "validityPeriod": validityPeriod,
        "createTime": createTime,
        "couponType": couponType,
        "ruleType": ruleType,
        "name": name,
        "id": id,
        "status": status,
      };
}

class UsingApp {
  UsingApp({
    this.scover,
    this.type,
    this.pkg,
    this.id,
    this.applicationName,
  });

  String scover;
  int type;
  String pkg;
  int id;
  String applicationName;

  factory UsingApp.fromJson(Map<String, dynamic> json) => UsingApp(
        scover: json["scover"],
        type: json["type"],
        pkg: json["pkg"],
        id: json["id"],
        applicationName: json["applicationName"],
      );

  Map<String, dynamic> toJson() => {
        "scover": scover,
        "type": type,
        "pkg": pkg,
        "id": id,
        "applicationName": applicationName,
      };
}
