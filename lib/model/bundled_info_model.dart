class BundledInfoModel {
  dynamic flag;
  String name;
  int id;
  String pic;
  SubSaleSummaryInfo subSaleSummaryInfo;
  int type;
  List<Apps> apps;

  BundledInfoModel(
      {this.flag,
        this.name,
        this.id,
        this.pic,
        this.subSaleSummaryInfo,
        this.type,
        this.apps});

  BundledInfoModel.fromJson(Map<String, dynamic> json) {
    flag = json['flag'];
    name = json['name'];
    id = json['id'];
    pic = json['pic'];
    subSaleSummaryInfo = json['subSaleSummaryInfo'] != null
        ? new SubSaleSummaryInfo.fromJson(json['subSaleSummaryInfo'])
        : null;
    type = json['type'];
    if (json['apps'] != null) {
      apps = <Apps>[];
      json['apps'].forEach((v) {
        apps.add(new Apps.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['flag'] = this.flag;
    data['name'] = this.name;
    data['id'] = this.id;
    data['pic'] = this.pic;
    if (this.subSaleSummaryInfo != null) {
      data['subSaleSummaryInfo'] = this.subSaleSummaryInfo.toJson();
    }
    data['type'] = this.type;
    if (this.apps != null) {
      data['apps'] = this.apps.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SubSaleSummaryInfo {
  dynamic bprice;
  bool bundle;
  double discount;
  int promEndDiffTime;
  int promEndTimeStamp;
  int promId;
  dynamic sprice;
  int subId;
  String promInfo;//促销活动介绍
  String subIntroduce;//主题介绍
  bool hasAllPur;//是否已购买
  //以下为了支付方便手动添加的字段
  String name;//姓名
  String rcover;//封面图
  bool isBundle = true;
  List<Apps> apps;

  SubSaleSummaryInfo(
      {this.bprice,
        this.bundle,
        this.discount,
        this.promEndDiffTime,
        this.promEndTimeStamp,
        this.promId,
        this.sprice,
        this.subId,
        this.promInfo,
        this.subIntroduce,
        this.hasAllPur});

  SubSaleSummaryInfo.fromJson(Map<String, dynamic> json) {
    bprice = json['bprice'];
    bundle = json['bundle'];
    if(json['discount'] is String){
      discount = double.parse(json['discount']);
    }else{
      discount = json['discount'];
    }
    promEndDiffTime = json['promEndDiffTime'];
    promEndTimeStamp = json['promEndTimeStamp'];
    promId = json['promId'];
    sprice = json['sprice'];
    subId = json['subId'];
    promInfo = json['promInfo'];
    subIntroduce = json['subIntroduce'];
    hasAllPur = json['hasAllPur'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['bprice'] = this.bprice;
    data['bundle'] = this.bundle;
    data['discount'] = this.discount;
    data['promEndDiffTime'] = this.promEndDiffTime;
    data['promEndTimeStamp'] = this.promEndTimeStamp;
    data['promId'] = this.promId;
    data['sprice'] = this.sprice;
    data['subId'] = this.subId;
    data['name'] = this.name;
    data['rcover'] = this.rcover;
    data['isBundle'] = this.isBundle;
    data['hasAllPur'] = this.hasAllPur;
    if (this.apps != null) {
      data['apps'] = this.apps.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Apps {
  String scover;
  String pkg;
  dynamic aver;
  dynamic cgy;
  int id;
  int state;
  List<int> tag;
  dynamic count;
  String rcover;
  dynamic sprice;
  int purchaseStatus;//0 未购买，1：已购买 2：退款中
  String name;
  dynamic bprice;

  Apps(
      {this.scover,
        this.pkg,
        this.aver,
        this.cgy,
        this.id,
        this.state,
        this.tag,
        this.count,
        this.rcover,
        this.sprice,
        this.purchaseStatus,
        this.name,
        this.bprice});

  Apps.fromJson(Map<String, dynamic> json) {
    scover = json['scover'];
    pkg = json['pkg'];
    aver = json['aver'];
    cgy = json['cgy'];
    id = json['id'];
    state = json['state'];
    tag = json['tag'].cast<int>();
    count = json['count'];
    rcover = json['rcover'];
    sprice = json['sprice'];
    purchaseStatus = json['purchaseStatus'];
    name = json['name'];
    bprice = json['bprice'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['scover'] = this.scover;
    data['pkg'] = this.pkg;
    data['aver'] = this.aver;
    data['cgy'] = this.cgy;
    data['id'] = this.id;
    data['state'] = this.state;
    data['tag'] = this.tag;
    data['count'] = this.count;
    data['rcover'] = this.rcover;
    data['sprice'] = this.sprice;
    data['purchaseStatus'] = this.purchaseStatus;
    data['name'] = this.name;
    data['bprice'] = this.bprice;
    return data;
  }
}