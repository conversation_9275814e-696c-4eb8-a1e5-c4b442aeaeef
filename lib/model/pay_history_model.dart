// To parse this JSON data, do
//
//     final payHistoryModel = payHistoryModelFromJson(jsonString);

import 'dart:convert';

PayHistoryModel payHistoryModelFromJson(String str) =>
    PayHistoryModel.fromJson(json.decode(str));

String payHistoryModelToJson(PayHistoryModel data) =>
    json.encode(data.toJson());

class PayHistoryModel {
  PayHistoryModel({
    this.appId,
    this.requestId,
    this.appName,
    this.version,
    this.sprice,
    this.scover,
    this.rcover,
    this.buyTime,
    this.status,
    this.canRefund,
    this.payType,
    this.couponName,
    this.couponType,
    this.couponId,
    this.currency,
  });

  int appId;
  String requestId;
  String appName;
  String version;
  int sprice;
  String scover;
  String rcover;
  int buyTime;
  int status;
  int canRefund;
  int payType;
  String couponName;
  int couponType;
  int couponId;
  int currency;

  factory PayHistoryModel.fromJson(Map<String, dynamic> json) =>
      PayHistoryModel(
        appId: json["appId"],
        requestId: json["requestId"],
        appName: json["appName"],
        version: json["version"],
        sprice: json["sprice"],
        scover: json["scover"],
        rcover: json["rcover"],
        buyTime: json["buyTime"],
        status: json["status"],
        canRefund: json["canRefund"],
        payType: json["payType"],
        couponName: json["couponName"],
        couponType: json["couponType"],
        couponId: json["couponId"],
        currency: json["currency"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "appId": appId,
        "requestId": requestId,
        "appName": appName,
        "version": version,
        "sprice": sprice,
        "scover": scover,
        "rcover": rcover,
        "buyTime": buyTime,
        "status": status,
        "canRefund": canRefund,
        "payType": payType,
        "couponName": couponName,
        "couponType": couponType,
        "couponId": couponId,
        "currency": currency,
      };
}
