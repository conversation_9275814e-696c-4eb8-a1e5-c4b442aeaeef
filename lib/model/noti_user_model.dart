// // To parse this JSON data, do
// //
// //     final notiUserModel = notiUserModelFromJson(jsonString);

// import 'dart:convert';

// NotiUserModel notiUserModelFromJson(String str) =>
//     NotiUserModel.fromJson(json.decode(str));

// String notiUserModelToJson(NotiUserModel data) => json.encode(data.toJson());

// class NotiUserModel {
//   NotiUserModel({
//     this.id,
//     this.actId,
//     this.nick,
//     this.avatar,
//     this.sex,
//     this.time,
//   });

//   int id;
//   int actId;
//   String nick;
//   String avatar;
//   int sex;
//   String time;

//   factory NotiUserModel.fromJson(Map<String, dynamic> json) => NotiUserModel(
//         id: json["id"],
//         actId: json["actId"],
//         nick: json["nick"],
//         avatar: json["avatar"],
//         sex: json["sex"],
//         time: json["time"],
//       );

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "actId": actId,
//         "nick": nick,
//         "avatar": avatar,
//         "sex": sex,
//         "time": time,
//       };
// }
