// To parse this JSON data, do
//
//     final hotDownloadModel = hotDownloadModelFromJson(jsonString);

import 'dart:convert';

HotDownloadModel hotDownloadModelFromJson(String str) =>
    HotDownloadModel.fromJson(json.decode(str));

String hotDownloadModelToJson(HotDownloadModel data) =>
    json.encode(data.toJson());

class HotDownloadModel {
  HotDownloadModel({
    this.id,
    this.pkg,
    this.name,
    this.sprice,
    this.bprice,
    this.tag,
    this.scover,
    this.downloads,
    this.currency,
  });

  int id;
  String pkg;
  String name;
  int sprice;
  int bprice;
  List<int> tag;
  String scover;
  int downloads;
  int currency;

  factory HotDownloadModel.fromJson(Map<String, dynamic> json) =>
      HotDownloadModel(
        id: json["id"],
        pkg: json["pkg"],
        name: json["name"],
        scover: json["scover"],
        downloads: json["downloads"],
        sprice: json["sprice"],
        bprice: json["bprice"],
        tag: List<int>.from(json["tag"].map((x) => x)),
        currency: json["currency"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "pkg": pkg,
        "name": name,
        "scover": scover,
        "downloads": downloads,
        "sprice": sprice,
        "bprice": bprice,
        "currency": currency,
        "tag": List<dynamic>.from(tag.map((x) => x)),
      };
}
