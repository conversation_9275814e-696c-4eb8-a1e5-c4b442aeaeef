// To parse this JSON data, do
//
//     final appPriceModel = appPriceModelFromJson(jsonString);

import 'dart:convert';

AppPriceModel appPriceModelFromJson(String str) =>
    AppPriceModel.fromJson(json.decode(str));

String appPriceModelToJson(AppPriceModel data) => json.encode(data.toJson());

class AppPriceModel {
  int lprice;
  int hprice;
  Descp descp;

  AppPriceModel({
    this.lprice,
    this.hprice,
    this.descp,
  });

  factory AppPriceModel.fromJson(Map<String, dynamic> json) => AppPriceModel(
        lprice: json["lprice"],
        hprice: json["hprice"],
        descp: Descp.fromJson(json["descp"]),
      );

  Map<String, dynamic> toJson() => {
        "lprice": lprice,
        "hprice": hprice,
        "descp": descp.toJson(),
      };
}

class Descp {
  String enUs;
  String zhCn;

  Descp({
    this.enUs,
    this.zhCn,
  });

  factory Descp.fromJson(Map<String, dynamic> json) => Descp(
        enUs: json["en_US"],
        zhCn: json["zh_CN"],
      );

  Map<String, dynamic> toJson() => {
        "en_US": enUs,
        "zh_CN": zhCn,
      };
}
