class AppsModel {
  AppsModel({
    this.scover,
    this.rcover,
    this.sprice,
    this.pkg,
    this.cgy,
    this.name,
    this.id,
    this.tag,
    this.state,
    this.bprice,
    this.count,
    this.aver,
    this.currency,
  });

  String scover;
  String rcover;
  int sprice;
  String pkg;
  int cgy;
  String name;
  int id;
  List<int> tag;
  int state;
  int bprice;
  int count;
  double aver;
  int currency;

  factory AppsModel.fromJson(Map<String, dynamic> json) => AppsModel(
        scover: json["scover"],
        rcover: json["rcover"],
        sprice: json["sprice"],
        pkg: json["pkg"],
        cgy: json["cgy"],
        name: json["name"],
        id: json["id"],
        tag: List<int>.from(json["tag"].map((x) => x)),
        state: json["state"],
        bprice: json["bprice"],
        count: json["count"] ?? 0,
        aver: json["aver"] ?? 0,
        currency: json["currency"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "scover": scover,
        "rcover": rcover,
        "sprice": sprice,
        "pkg": pkg,
        "cgy": cgy,
        "name": name,
        "id": id,
        "tag": List<dynamic>.from(tag.map((x) => x)),
        "state": state,
        "bprice": bprice,
        "count": count,
        "aver": aver,
        "currency": currency,
      };
}
