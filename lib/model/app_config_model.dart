class AppConfigModel {
  AppConfigModel({
    this.appMessage,
  });

  AppConfigModel.fromJson(dynamic json) {
    appMessage = json['appMessage'] != null
        ? AppMessage.fromJson(json['appMessage'])
        : null;
  }

  AppMessage appMessage;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (appMessage != null) {
      map['appMessage'] = appMessage.toJson();
    }
    return map;
  }
}

class AppMessage {
  AppMessage({
    this.id,
    this.appId,
    this.msg,
    this.url,
  });

  AppMessage.fromJson(dynamic json) {
    id = json['id'];
    appId = json['appId'];
    msg = json['msg'];
    url = json['url'];
  }

  int id;
  int appId;
  String msg;
  String url;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['appId'] = appId;
    map['msg'] = msg;
    map['url'] = url;
    return map;
  }
}
