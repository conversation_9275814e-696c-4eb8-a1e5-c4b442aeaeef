// To parse this JSON data, do
//
//     final socketModel = socketModelFromJson(jsonString);

import 'dart:convert';

SocketModel socketModelFromJson(String str) =>
    SocketModel.fromJson(json.decode(str));

String socketModelToJson(SocketModel data) => json.encode(data.toJson());

class SocketModel {
  SocketModel({
    this.data,
    this.errCode,
    this.errMsg,
    this.cmd,
    this.type,
    this.tag,
  });

  Map data;
  int errCode;
  String errMsg;
  String cmd;
  String type;
  String tag;

  factory SocketModel.fromJson(Map<String, dynamic> json) => SocketModel(
        data: json["data"],
        errCode: json["errCode"],
        errMsg: json["errMsg"],
        cmd: json["cmd"],
        type: json["type"],
        tag: json["tag"],
      );

  Map<String, dynamic> toJson() => {
        "data": data,
        "errCode": errCode,
        "errMsg": errMsg,
        "cmd": cmd,
        "type": type,
        "tag": tag,
      };
}
