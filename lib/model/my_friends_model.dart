import 'hall_friends_model.dart';

class MyFriendsDataModel {
  MyFriendsDataModel({
    this.users,
  });

  MyFriendsDataModel.fromJson(dynamic json) {
    if (json['users'] != null) {
      users = [];
      json['users'].forEach((v) {
        users.add(MyFriendsDataUser.fromJson(v));
      });
    }
  }

  List<MyFriendsDataUser> users;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (users != null) {
      map['users'] = users.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

class MyFriendsDataUser {
  MyFriendsDataUser({
    this.actId,
    this.nick,
    this.age,
    this.sex,
    this.avatar,
    this.online,
    this.usingApp,
  });

  MyFriendsDataUser.fromJson(dynamic json) {
    actId = json['actId'];
    nick = json['nick'];
    age = json['age'];
    sex = json['sex'];
    avatar = json['avatar'];
    online = json['online'];
    usingApp =
        json['usingApp'] != null ? HallFriendsUsingAppModel.fromJson(json['usingApp']) : null;
  }

  int actId;
  String nick;
  int age;
  int sex;
  String avatar;
  int online;
  HallFriendsUsingAppModel usingApp;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['actId'] = actId;
    map['nick'] = nick;
    map['age'] = age;
    map['sex'] = sex;
    map['avatar'] = avatar;
    map['online'] = online;
    if (usingApp != null) {
      map['usingApp'] = usingApp.toJson();
    }
    return map;
  }
}
