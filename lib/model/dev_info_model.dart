// To parse this JSON data, do
//
//     final devInfoModel = devInfoModelFromJson(jsonString);

import 'dart:convert';

DevInfoModel devInfoModelFromJson(String str) =>
    DevInfoModel.fromJson(json.decode(str));

String devInfoModelToJson(DevInfoModel data) => json.encode(data.toJson());

class DevInfoModel {
  DevInfoModel({
    this.devId,
    this.auth,
    this.teen,
  });

  String devId;
  int auth;
  int teen;

  factory DevInfoModel.fromJson(Map<String, dynamic> json) => DevInfoModel(
        devId: json["devId"],
        auth: json["auth"],
        teen: json["teen"],
      );

  Map<String, dynamic> toJson() => {
        "devId": devId,
        "auth": auth,
        "teen": teen,
      };
}
