class OfficialEventModel {
  OfficialEventModel({
      this.id, 
      this.eventType, 
      this.eventId, 
      this.eventName, 
      this.coverImage, 
      this.appId, 
      this.commentNum, 
      this.organizer, 
      this.organizerName, 
      this.falseNum, 
      this.realNum, 
      this.startTime, 
      this.endTime, 
      this.focusesStatus, 
      this.focusesPortraits,});

  OfficialEventModel.fromJson(dynamic json) {
    id = json['id'];
    eventType = json['eventType'];
    eventId = json['eventId'];
    eventName = json['eventName'];
    coverImage = json['coverImage'];
    appId = json['appId'];
    commentNum = json['commentNum'];
    organizer = json['organizer'];
    organizerName = json['organizerName'];
    falseNum = json['falseNum'];
    realNum = json['realNum'];
    startTime = json['startTime'];
    endTime = json['endTime'];
    focusesStatus = json['focusesStatus'];
    focusesPortraits =(json['focusesPortraits'] as List<dynamic>)?.cast<String>();
  }
  int id;
  dynamic eventType;
  int eventId;
  String eventName;
  String coverImage;
  dynamic appId;
  int commentNum;
  String organizer;
  dynamic organizerName;
  int falseNum;
  int realNum;
  String startTime;
  String endTime;
  String focusesStatus;
  List<String> focusesPortraits;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['eventType'] = eventType;
    map['eventId'] = eventId;
    map['eventName'] = eventName;
    map['coverImage'] = coverImage;
    map['appId'] = appId;
    map['commentNum'] = commentNum;
    map['organizer'] = organizer;
    map['organizerName'] = organizerName;
    map['falseNum'] = falseNum;
    map['realNum'] = realNum;
    map['startTime'] = startTime;
    map['endTime'] = endTime;
    map['focusesStatus'] = focusesStatus;
    map['focusesPortraits'] = focusesPortraits;
    return map;
  }

}