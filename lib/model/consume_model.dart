// To parse this JSON data, do
//
//     final consumeModel = consumeModelFromJson(jsonString);

import 'dart:convert';

ConsumeModel consumeModelFromJson(String str) =>
    ConsumeModel.fromJson(json.decode(str));

String consumeModelToJson(ConsumeModel data) => json.encode(data.toJson());

class ConsumeModel {
  ConsumeModel({
    this.pno,
    this.payType,
    this.createTime,
    this.sno,
    this.appName,
    this.sprice,
  });

  String pno;
  int payType;
  int createTime;
  String sno;
  String appName;
  int sprice;

  factory ConsumeModel.fromJson(Map<String, dynamic> json) => ConsumeModel(
        pno: json["pno"],
        payType: json["payType"],
        createTime: json["createTime"],
        sno: json["sno"],
        appName: json["appName"],
        sprice: json["sprice"],
      );

  Map<String, dynamic> toJson() => {
        "pno": pno,
        "payType": payType,
        "createTime": createTime,
        "sno": sno,
        "appName": appName,
        "sprice": sprice,
      };
}
