class PersonalHomeModel {
  PersonalHomeModel({
    this.social,
    this.sex,
    this.actId,
    this.mobile,
    this.nick,
    this.relate,
    this.online,
    this.apps,
    this.bgPic,
    this.motto,
    this.avatar,
    this.birth,
  });

  PersonalHomeModel.fromJson(dynamic json) {
    social =
        json['social'] != null ? SocialModel.fromJson(json['social']) : null;
    sex = json['sex'];
    actId = json['actId'];
    mobile = json['mobile'];
    nick = json['nick'];
    relate = json['relate'];
    online = json['online'];
    if (json['apps'] != null) {
      apps = [];
      json['apps'].forEach((v) {
        apps.add(AppModel.fromJson(v));
      });
    }
    bgPic = json['bgPic'];
    motto = json['motto'];
    avatar = json['avatar'];
    birth = json['birth'];
  }

  SocialModel social;
  int sex;
  int actId;
  String mobile;
  String nick;
  int relate;
  int online;
  List<AppModel> apps;
  String bgPic;
  String motto;
  String avatar;
  String birth;
}

class AppModel {
  AppModel({
    this.time,
    this.pkg,
    this.appName,
    this.appId,
    this.scover,
    this.appType,
  });

  AppModel.fromJson(dynamic json) {
    time = json['time'];
    pkg = json['pkg'];
    appName = json['appName'];
    appId = json['appId'];
    scover = json['scover'];
    appType = json['appType'];
  }

  int time;
  String pkg;
  String appName;
  int appId;
  String scover;
  int appType;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['time'] = time;
    map['pkg'] = pkg;
    map['appName'] = appName;
    map['appId'] = appId;
    map['scover'] = scover;
    map['appType'] = appType;
    return map;
  }
}

class SocialModel {
  SocialModel({
    this.app,
    this.friend,
    this.mutualfd,
  });

  SocialModel.fromJson(dynamic json) {
    app = json['app'];
    friend = json['friend'];
    mutualfd = json['mutualfd'];
  }

  int app;
  int friend;
  int mutualfd;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['app'] = app;
    map['friend'] = friend;
    map['mutualfd'] = mutualfd;
    return map;
  }
}
