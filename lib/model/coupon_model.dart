// To parse this JSON data, do
//
//     final couponModel = couponModelFromJson(jsonString);

import 'dart:convert';

CouponModel couponModelFromJson(String str) =>
    CouponModel.fromJson(json.decode(str));

String couponModelToJson(CouponModel data) => json.encode(data.toJson());

class CouponModel {
  bool canUse;
  int couponId;
  CouponInfo couponInfo;
  int endTime;
  int startTime;
  int surplus;

  CouponModel({
    this.canUse,
    this.couponId,
    this.couponInfo,
    this.endTime,
    this.startTime,
    this.surplus,
  });

  factory CouponModel.fromJson(Map<String, dynamic> json) => CouponModel(
        canUse: json["canUse"],
        couponId: json["couponId"],
        couponInfo: CouponInfo.fromJson(json["couponInfo"]),
        endTime: json["endTime"],
        startTime: json["startTime"],
        surplus: json["surplus"],
      );

  Map<String, dynamic> toJson() => {
        "canUse": canUse,
        "couponId": couponId,
        "couponInfo": couponInfo.toJson(),
        "endTime": endTime,
        "startTime": startTime,
        "surplus": surplus,
      };
}

class CouponInfo {
  int couponType;
  int createTime;
  String detailDescription;
  int id;
  String name;
  int promEnd;
  int promStart;
  String remark;
  String ruleContent;
  int ruleType;
  String shortDescription;
  int status;
  int updateTime;
  int validityPeriod;

  CouponInfo({
    this.couponType,
    this.createTime,
    this.detailDescription,
    this.id,
    this.name,
    this.promEnd,
    this.promStart,
    this.remark,
    this.ruleContent,
    this.ruleType,
    this.shortDescription,
    this.status,
    this.updateTime,
    this.validityPeriod,
  });

  factory CouponInfo.fromJson(Map<String, dynamic> json) => CouponInfo(
        couponType: json["couponType"],
        createTime: json["createTime"],
        detailDescription: json["detailDescription"],
        id: json["id"],
        name: json["name"],
        promEnd: json["promEnd"],
        promStart: json["promStart"],
        remark: json["remark"],
        ruleContent: json["ruleContent"],
        ruleType: json["ruleType"],
        shortDescription: json["shortDescription"],
        status: json["status"],
        updateTime: json["updateTime"],
        validityPeriod: json["validityPeriod"],
      );

  Map<String, dynamic> toJson() => {
        "couponType": couponType,
        "createTime": createTime,
        "detailDescription": detailDescription,
        "id": id,
        "name": name,
        "promEnd": promEnd,
        "promStart": promStart,
        "remark": remark,
        "ruleContent": ruleContent,
        "ruleType": ruleType,
        "shortDescription": shortDescription,
        "status": status,
        "updateTime": updateTime,
        "validityPeriod": validityPeriod,
      };
}
