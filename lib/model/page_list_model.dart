class PageListModel<T> {
  PageListModel({
    this.current,
    this.size,
    this.totalPage,
    this.totalCount,
    this.content,
  });

  PageListModel.fromJson(dynamic json, T Function(dynamic json) convert) {
    current = json['current'];
    size = json['size'];
    totalPage = json['totalPage'];
    totalCount = json['totalCount'];
    List<dynamic> content = json['content'];
    if (content != null) {
      this.content = content.map((e) => convert(e)).toList(growable: true);
    }
  }

  int current;
  int size;
  int totalPage;
  int totalCount;
  List<T> content;

  bool get hasNextPage => current < totalPage;

  factory PageListModel.empty() {
    return PageListModel.create(1, 0, <T>[], hasNextPage: false);
  }

  void followBy(PageListModel<T> other) {
    current = other.current;
    size = other.size;
    totalPage = other.totalPage;
    totalCount = other.totalCount;
    content.addAll(other.content);
  }

  factory PageListModel.create(int current, int size, List<T> content,
      {bool hasNextPage, int totalCount = 0}) {
    int totalPage;
    bool hasNext = hasNextPage != null ? hasNextPage : content.length >= size;
    if (hasNext) {
      totalPage = current + 1;
    } else {
      totalPage = current;
    }
    return PageListModel(
        current: current,
        size: size,
        totalPage: totalPage,
        totalCount: totalCount,
        content: content);
  }
}
