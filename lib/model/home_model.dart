// To parse this JSON data, do
//
//     final homeList = homeListFrom<PERSON>son(jsonString);

import 'dart:convert';

import 'package:yvr_assistant/model/theme_data_model.dart';

import 'banner_model.dart';

HomeModel homeListFromJson(String str) => HomeModel.fromJson(json.decode(str));

String homeListToJson(HomeModel data) => json.encode(data.toJson());

class HomeModel {
  HomeModel({
    this.themes,
    this.banners,
  });

  List<ThemeDataModel> themes;
  List<BannerModel> banners;

  factory HomeModel.fromJson(Map<String, dynamic> json) => HomeModel(
        themes: List<ThemeDataModel>.from(
            json["themes"].map((x) => ThemeDataModel.fromJson(x))),
        banners: List<BannerModel>.from(
            json["banners"].map((x) => BannerModel.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "themes": List<dynamic>.from(themes.map((x) => x.toJson())),
        "banners": List<dynamic>.from(banners.map((x) => x.toJson())),
      };
}
