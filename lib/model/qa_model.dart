// To parse this JSON data, do
//
//     final qaModel = qaModelFromJson(jsonString);

import 'dart:convert';

QaModel qaModelFromJson(String str) => QaModel.fromJson(json.decode(str));

String qaModelToJson(QaModel data) => json.encode(data.toJson());

class QaModel {
  QaModel({
    this.id,
    this.descp,
  });

  int id;
  String descp;

  factory QaModel.fromJson(Map<String, dynamic> json) => QaModel(
        id: json["id"],
        descp: json["descp"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "descp": descp,
      };
}
