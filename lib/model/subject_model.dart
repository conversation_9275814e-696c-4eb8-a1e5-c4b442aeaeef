import 'package:yvr_assistant/model/apps_model.dart';
import 'package:yvr_assistant/model/summary_info_model.dart';

class SubjectModel {
  SubjectModel({
    this.flag,
    this.name,
    this.id,
    this.pic,
    this.type,
    this.apps,
    this.summaryInfo
  });

  int flag;
  String name;
  int id;
  String pic;
  int type;
  List<AppsModel> apps;
  SummaryInfoModel summaryInfo;

  factory SubjectModel.fromJson(Map<String, dynamic> json) => SubjectModel(
        flag: json["flag"],
        name: json["name"],
        id: json["id"],
        pic: json["pic"],
        type: json["type"],
        summaryInfo: json['summaryInfo'] == null?null:SummaryInfoModel.fromJson(json['summaryInfo']),
        apps: json["apps"] == null
            ? null
            : List<AppsModel>.from(
                json["apps"].map((x) => AppsModel.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "flag": flag,
        "name": name,
        "id": id,
        "pic": pic,
        "type": type,
        "apps": apps == null
            ? null
            : List<dynamic>.from(apps.map((x) => x.toJson())),
      };
}
