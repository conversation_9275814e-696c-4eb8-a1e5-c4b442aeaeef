// To parse this JSON data, do
//
//     final appTypeModel = appTypeModelFromJson(jsonString);

import 'dart:convert';

AppTypeModel appTypeModelFromJson(String str) =>
    AppTypeModel.fromJson(json.decode(str));

String appTypeModelToJson(AppTypeModel data) => json.encode(data.toJson());

class AppTypeModel {
  AppTypeModel({
    this.chinese,
    this.english,
    this.tag,
    this.value,
  });

  String chinese;
  String english;
  int tag;
  int value;

  factory AppTypeModel.fromJson(Map<String, dynamic> json) => AppTypeModel(
        chinese: json["chinese"],
        english: json["english"],
        tag: json["tag"],
        value: json["value"],
      );

  Map<String, dynamic> toJson() => {
        "chinese": chinese,
        "english": english,
        "tag": tag,
        "value": value,
      };
}
