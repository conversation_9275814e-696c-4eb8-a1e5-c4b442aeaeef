// To parse this JSON data, do
//
//     final prodModel = prodModelFromJson(jsonString);

import 'dart:convert';

ProdModel prodModelFromJson(String str) => ProdModel.fromJson(json.decode(str));

String prodModelToJson(ProdModel data) => json.encode(data.toJson());

class ProdModel {
  ProdModel({
    this.scover,
    this.vedios,
    this.promId,
    this.nMode,
    this.forced,
    this.discount,
    this.pkg,
    this.aver,
    this.hasCoupon,
    this.cgy,
    this.downloads,
    this.star1,
    this.vnote,
    this.id,
    this.state,
    this.tag,
    this.prepub,
    this.abbr,
    this.lang,
    this.pics,
    this.brief,
    this.fname,
    this.ver,
    this.incomeType,
    this.count,
    this.star4,
    this.star5,
    this.rcover,
    this.star2,
    this.sprice,
    this.star3,
    this.tdate,
    this.size,
    this.purchased,
    this.authority,
    this.name,
    this.developer,
    this.gMode,
    this.time,
    this.bprice,
    this.md5,
    this.promEndTimeStamp,
    this.promRemainTime,
    this.pTime,
    this.publisher,
    this.currency,
  });

  String scover;
  List<Vedio> vedios;
  int promId;
  String nMode;
  int forced;
  String discount;
  String pkg;
  double aver;
  bool hasCoupon;
  int cgy;
  int downloads;
  int star1;
  String vnote;
  int id;
  int state;
  List<int> tag;
  int prepub;
  String abbr;
  String lang;
  List<String> pics;
  String brief;
  String fname;
  String ver;
  int incomeType;
  int count;
  int star4;
  int star5;
  String rcover;
  int star2;
  int sprice;
  int star3;
  String tdate;
  int size;
  int purchased;
  String authority;
  String name;
  String developer;
  int gMode;
  String time;
  int bprice;
  String md5;
  int promEndTimeStamp;
  int promRemainTime;
  String pTime;
  String publisher;
  int currency;

  factory ProdModel.fromJson(Map<String, dynamic> json) => ProdModel(
      scover: json["scover"],
      vedios: List<Vedio>.from(json["vedios"].map((x) => Vedio.fromJson(x))),
      promId: json["promId"],
      nMode: json["nMode"],
      forced: json["forced"],
      discount: json["discount"],
      pkg: json["pkg"],
      aver: json["aver"].toDouble(),
      hasCoupon: json["hasCoupon"] ?? false,
      cgy: json["cgy"],
      downloads: json["downloads"],
      star1: json["star1"],
      vnote: json["vnote"],
      id: json["id"],
      state: json["state"],
      tag: List<int>.from(json["tag"].map((x) => x)),
      prepub: json["prepub"],
      abbr: json["abbr"],
      lang: json["lang"],
      pics: List<String>.from(json["pics"].map((x) => x)),
      brief: json["brief"],
      fname: json["fname"],
      ver: json["ver"],
      incomeType: json["incomeType"],
      count: json["count"],
      star4: json["star4"],
      star5: json["star5"],
      rcover: json["rcover"],
      star2: json["star2"],
      sprice: json["sprice"],
      star3: json["star3"],
      tdate: json["tdate"],
      size: json["size"],
      purchased: json["purchased"] ?? 0,
      currency: json["currency"] ?? 0,
      authority: json["authority"],
      name: json["name"],
      developer: json["developer"],
      gMode: json["gMode"],
      time: json["time"],
      bprice: json["bprice"],
      md5: json["md5"],
      promEndTimeStamp: json["promEndTimeStamp"],
      promRemainTime: json["promRemainTime"],
      pTime: json['pTime'],
      publisher: json['publisher']);

  Map<String, dynamic> toJson() => {
        "scover": scover,
        "vedios": List<dynamic>.from(vedios.map((x) => x.toJson())),
        "promId": promId,
        "nMode": nMode,
        "forced": forced,
        "discount": discount,
        "pkg": pkg,
        "aver": aver,
        "hasCoupon": hasCoupon,
        "cgy": cgy,
        "downloads": downloads,
        "star1": star1,
        "vnote": vnote,
        "id": id,
        "state": state,
        "tag": List<dynamic>.from(tag.map((x) => x)),
        "prepub": prepub,
        "abbr": abbr,
        "lang": lang,
        "pics": List<dynamic>.from(pics.map((x) => x)),
        "brief": brief,
        "fname": fname,
        "ver": ver,
        "incomeType": incomeType,
        "count": count,
        "star4": star4,
        "star5": star5,
        "rcover": rcover,
        "star2": star2,
        "sprice": sprice,
        "star3": star3,
        "tdate": tdate,
        "size": size,
        "purchased": purchased,
        "authority": authority,
        "name": name,
        "developer": developer,
        "gMode": gMode,
        "time": time,
        "bprice": bprice,
        "md5": md5,
        "promEndTimeStamp": promEndTimeStamp,
        "promRemainTime": promRemainTime,
        "pTime": pTime,
        "publisher": publisher,
        "currency": currency
      };
}

class Vedio {
  Vedio({
    this.vedio,
    this.id,
    this.pic,
  });

  String vedio;
  int id;
  String pic;

  factory Vedio.fromJson(Map<String, dynamic> json) => Vedio(
        vedio: json["vedio"],
        id: json["id"],
        pic: json["pic"],
      );

  Map<String, dynamic> toJson() => {
        "vedio": vedio,
        "id": id,
        "pic": pic,
      };
}
