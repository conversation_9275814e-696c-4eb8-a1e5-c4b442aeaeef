// To parse this JSON data, do
//
//     final eventNotiModel = eventNotiModelFromJson(jsonString);

import 'dart:convert';

EventNotiModel eventNotiModelFromJson(String str) =>
    EventNotiModel.fromJson(json.decode(str));

String eventNotiModelToJson(EventNotiModel data) => json.encode(data.toJson());

class EventNotiModel {
  EventNotiModel({
    this.id,
    this.eventId,
    this.name,
    this.cover,
    this.startTime,
    this.time,
  });

  int id;
  int eventId;
  String name;
  String cover;
  String startTime;
  String time;

  factory EventNotiModel.fromJson(Map<String, dynamic> json) => EventNotiModel(
        id: json["id"],
        eventId: json["eventId"],
        name: json["name"],
        cover: json["cover"],
        startTime: json["startTime"],
        time: json["time"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "eventId": eventId,
        "name": name,
        "cover": cover,
        "startTime": startTime,
        "time": time,
      };
}
