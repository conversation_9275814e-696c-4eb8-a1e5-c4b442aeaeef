// To parse this JSON data, do
//
//     final notiModel = notiModelFromJson(jsonString);

import 'dart:convert';

NotiModel notiModelFromJson(String str) => NotiModel.fromJson(json.decode(str));

String notiModelToJson(NotiModel data) => json.encode(data.toJson());

class NotiModel {
  NotiModel({
    this.tag,
    this.id,
    this.fromId,
    this.name,
    this.cover,
    this.toId,
    this.status,
    this.state,
    this.eventId,
    this.time,
    this.attach,
    this.appId,
    this.appName,
    this.star,
    this.note,
    this.rcover,
    this.scover,
  });

  String tag;
  int id;
  int fromId;
  String name;
  String cover;
  int toId;
  int status;
  int state;
  int eventId;
  String time;
  Attach attach;
  int appId;
  String appName;
  int star;
  String note;
  String rcover;
  String scover;

  factory NotiModel.fromJson(Map<String, dynamic> json) => NotiModel(
        tag: json["tag"],
        id: json["id"],
        fromId: json["fromId"],
        name: json["name"],
        cover: json["cover"],
        toId: json["toId"],
        status: json["status"],
        state: json["state"],
        eventId: json["eventId"],
        time: json["time"],
        attach: Attach.fromJson(json["attach"] ?? Attach().toJson()),
        appId: json["appId"],
        appName: json["appName"],
        star: json["star"],
        note: json["note"],
        rcover: json["rcover"] ?? "",
        scover: json["scover"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "tag": tag,
        "id": id,
        "fromId": fromId,
        "name": name,
        "cover": cover,
        "toId": toId,
        "status": status,
        "state": state,
        "eventId": eventId,
        "time": time,
        "attach": attach.toJson(),
        "appId": appId,
        "appName": appName,
        "star": star,
        "note": note,
        "rcover": rcover,
        "scover": scover,
      };
}

class Attach {
  Attach({
    this.name,
    this.cover,
    this.sex,
  });

  String name;
  String cover;
  int sex;

  factory Attach.fromJson(Map<String, dynamic> json) => Attach(
        name: json["name"],
        cover: json["cover"],
        sex: json["sex"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "cover": cover,
        "sex": sex,
      };
}
