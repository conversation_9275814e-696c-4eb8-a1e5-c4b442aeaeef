// To parse this JSON data, do
//
//     final vrFileModel = vrFileModelFromJson(jsonString);

import 'dart:convert';

VrFileModel vrFileModelFromJson(String str) =>
    VrFileModel.fromJson(json.decode(str));

String vrFileModelToJson(VrFileModel data) => json.encode(data.toJson());

class VrFileModel {
  VrFileModel({
    this.date,
    this.id,
    this.name,
    this.size,
    this.type,
    this.duration,
    this.width,
    this.height,
  });

  String date;
  String id;
  String name;
  String size;
  String duration;
  String title;
  int type;
  int width;
  int height;

  factory VrFileModel.fromJson(Map<String, dynamic> json) => VrFileModel(
        date: json["date"],
        id: json["id"],
        name: json["name"],
        size: json["size"],
        type: json["type"],
        duration: json["duration"],
        width: json["width"] ?? 0,
        height: json["height"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "date": date,
        "id": id,
        "name": name,
        "size": size,
        "type": type,
        "duration": duration,
        "width": width,
        "height": height,
      };
}
