// To parse this JSON data, do
//
//     final punchFormModel = punchFormModelFromJson(jsonString);

import 'dart:convert';

PunchFormModel punchFormModelFromJson(String str) =>
    PunchFormModel.fromJson(json.decode(str));

String punchFormModelToJson(PunchFormModel data) => json.encode(data.toJson());

class PunchFormModel {
  PunchFormModel({
    this.id,
    this.activityId,
    this.userId,
    this.userPhone,
    this.deviceId,
    this.activityStartTime,
    this.activityEndTime,
    this.activityStatus,
    this.rewardStatus,
    this.approvalStatus,
    this.purchaseChannel,
    this.orderPicture,
    this.orderNumber,
    this.payPrice,
    this.invoicePicture,
    this.alipayNumber,
    this.alipayUserName,
    this.frontIdPicture,
    this.backIdPicture,
    this.auditTime,
    this.time,
    this.channelUserId,
    this.orderBuyTime,
    this.receiptTrueName,
  });

  String id;
  int activityId;
  int userId;
  int userPhone;
  String deviceId;
  String activityStartTime;
  String activityEndTime;
  String activityStatus;
  String rewardStatus;
  String approvalStatus;
  String purchaseChannel;
  String orderPicture;
  String orderNumber;
  String payPrice;
  String invoicePicture;
  String alipayNumber;
  String alipayUserName;
  String frontIdPicture;
  String backIdPicture;
  String auditTime;
  String time;
  String channelUserId;
  String orderBuyTime;
  String receiptTrueName;

  factory PunchFormModel.fromJson(Map<String, dynamic> json) => PunchFormModel(
        id: json["id"],
        activityId: json["activityId"],
        userId: json["userId"],
        userPhone: json["userPhone"],
        deviceId: json["deviceId"],
        activityStartTime: json["activityStartTime"],
        activityEndTime: json["activityEndTime"],
        activityStatus: json["activityStatus"],
        rewardStatus: json["rewardStatus"],
        approvalStatus: json["approvalStatus"],
        purchaseChannel: json["purchaseChannel"],
        orderPicture: json["orderPicture"],
        orderNumber: json["orderNumber"],
        payPrice: json["payPrice"],
        invoicePicture: json["invoicePicture"],
        alipayNumber: json["alipayNumber"],
        alipayUserName: json["alipayUserName"],
        frontIdPicture: json["frontIdPicture"],
        backIdPicture: json["backIdPicture"],
        auditTime: json["auditTime"],
        time: json["time"],
        channelUserId: json["channelUserId"],
        orderBuyTime: json["orderBuyTime"],
        receiptTrueName: json["receiptTrueName"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "activityId": activityId,
        "userId": userId,
        "userPhone": userPhone,
        "deviceId": deviceId,
        "activityStartTime": activityStartTime,
        "activityEndTime": activityEndTime,
        "activityStatus": activityStatus,
        "rewardStatus": rewardStatus,
        "approvalStatus": approvalStatus,
        "purchaseChannel": purchaseChannel,
        "orderPicture": orderPicture,
        "orderNumber": orderNumber,
        "payPrice": payPrice,
        "invoicePicture": invoicePicture,
        "alipayNumber": alipayNumber,
        "alipayUserName": alipayUserName,
        "frontIdPicture": frontIdPicture,
        "backIdPicture": backIdPicture,
        "auditTime": auditTime,
        "time": time,
        "channelUserId": channelUserId,
        "orderBuyTime": orderBuyTime,
        "receiptTrueName": receiptTrueName,
      };
}
