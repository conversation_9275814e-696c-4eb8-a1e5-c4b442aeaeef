class BannerModel {
  BannerModel({
    this.link,
    this.name,
    this.id,
    this.pic,
  });

  String link;
  String name;
  int id;
  String pic;

  factory BannerModel.fromJson(Map<String, dynamic> json) => BannerModel(
        link: json["link"],
        name: json["name"],
        id: json["id"],
        pic: json["pic"],
      );

  Map<String, dynamic> toJson() => {
        "link": link,
        "name": name,
        "id": id,
        "pic": pic,
      };
}
