// To parse this JSON data, do
//
//     final devModel = devModelFromJson(jsonString);

import 'dart:convert';

DevModel devModelFromJson(String str) => DevModel.fromJson(json.decode(str));

String devModelToJson(DevModel data) => json.encode(data.toJson());

class DevModel {
  DevModel({
    this.btName,
    this.devId,
    this.linkState,
    this.battery,
    this.sameWiFi,
    this.getWifiIp,
    this.auth,
    this.teen,
  });

  String btName = '';
  String devId = '';
  int linkState = 2;
  int battery = 0;
  bool sameWiFi = true;
  bool getWifiIp = false;
  int auth = 0;
  int teen;

  factory DevModel.fromJson(Map<String, dynamic> json) => DevModel(
        btName: json["btName"],
        devId: json["devId"],
        linkState: json["linkState"] ?? 2,
        battery: json["battery"] ?? 0,
        sameWiFi: json["sameWiFi"] ?? true,
        getWifiIp: json["sameWiFi"] ?? false,
        auth: json["auth"] ?? 0,
        teen: json["teen"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "btName": btName,
        "devId": devId,
        "linkState": linkState,
        "battery": battery,
        "sameWiFi": sameWiFi,
        "getWifiIp": getWifiIp,
        "auth": auth,
        "teen": teen,
      };
}
