class TeenModeModel {
  TeenModeModel({
    this.actId,
    this.devId,
    this.mode,
    this.weekday,
    this.weekend,
    this.daytime,
    this.tdate,
  });

  TeenModeModel.fromJson(dynamic json) {
    actId = json['actId'];
    devId = json['devId'];
    mode = json['mode'];
    weekday = json['weekday'];
    weekend = json['weekend'];
    daytime = json['daytime'];
    tdate = json['tdate'];
  }

  int actId;
  String devId;
  int mode;
  String weekday;
  String weekend;
  String daytime;
  String tdate;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['actId'] = actId;
    map['devId'] = devId;
    map['mode'] = mode;
    map['weekday'] = weekday;
    map['weekend'] = weekend;
    map['daytime'] = daytime;
    map['tdate'] = tdate;
    return map;
  }
}

class TeenVerifyModel {
  TeenVerifyModel({
    this.status,
  });

  TeenVerifyModel.fromJson(dynamic json) {
    status = json['status'];
  }

  int status;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['status'] = status;
    return map;
  }
}
