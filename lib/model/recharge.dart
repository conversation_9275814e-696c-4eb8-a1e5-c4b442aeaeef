// To parse this JSON data, do
//
//     final rechargeModel = rechargeModelFromJson(jsonString);

import 'dart:convert';

RechargeModel rechargeModelFromJson(String str) =>
    RechargeModel.fromJson(json.decode(str));

String rechargeModelToJson(RechargeModel data) => json.encode(data.toJson());

class RechargeModel {
  RechargeModel({
    this.payType,
    this.money,
    this.createTime,
    this.ycount,
    this.actId,
    this.id,
    this.feeRate,
    this.platform,
    this.status,
    this.name,
    this.acquireType,
    this.promId,
    this.startTime,
    this.endTime,
  });

  int payType;
  int money;
  int createTime;
  int ycount;
  int actId;
  String id;
  String feeRate;
  int platform;
  int status;
  String name;
  int acquireType;
  int promId;
  int startTime;
  int endTime;

  factory RechargeModel.from<PERSON>son(Map<String, dynamic> json) => RechargeModel(
        payType: json["payType"],
        money: json["money"],
        createTime: json["createTime"],
        ycount: json["ycount"] ?? 0,
        actId: json["actId"],
        id: json["id"],
        feeRate: json["feeRate"],
        platform: json["platform"],
        status: json["status"],
        name: json["name"],
        acquireType: json["acquireType"],
        promId: json["promId"],
        startTime: json["startTime"],
        endTime: json["endTime"],
      );

  Map<String, dynamic> toJson() => {
        "payType": payType,
        "money": money,
        "createTime": createTime,
        "ycount": ycount,
        "actId": actId,
        "id": id,
        "feeRate": feeRate,
        "platform": platform,
        "status": status,
        "name": name,
        "acquireType": acquireType,
        "promId": promId,
        "startTime": startTime,
        "endTime": promId,
      };
}
