class HallFriendsUsingAppModel {
  HallFriendsUsingAppModel({
    this.scover,
    this.appId,
    this.type,
    this.pkg,
    this.applicationName,
  });

  HallFriendsUsingAppModel.fromJson(dynamic json) {
    scover = json['scover'];
    appId = json['appId'];
    type = json['type'];
    pkg = json['pkg'];
    applicationName = json['applicationName'];
  }

  String scover;
  int appId;
  int type;
  String pkg;
  String applicationName;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['scover'] = scover;
    map['appId'] = appId;
    map['type'] = type;
    map['pkg'] = pkg;
    map['applicationName'] = applicationName;
    return map;
  }
}

class HallFriendsModel {
  HallFriendsModel({
    this.nick,
    this.sex,
    this.actId,
    this.birth,
    this.online,
    this.avatar,
    this.usingApp,
    this.age,
    this.fdType,
  });

  HallFriendsModel.fromJson(dynamic json) {
    nick = json['nick'];
    sex = json['sex'];
    actId = json['actId'];
    birth = json['birth'];
    online = json['online'];
    avatar = json['avatar'];
    usingApp = json['usingApp'] != null
        ? HallFriendsUsingAppModel.fromJson(json['usingApp'])
        : null;
    age = json['age'];
    fdType = json['fdType'];
    fromNick = json['fromNick'];
    fromId = json['fromId'];
  }

  String nick;
  int sex;
  int actId;
  String birth;
  int online;
  String avatar;
  HallFriendsUsingAppModel usingApp;
  int age;
  int fdType;
  String fromNick;
  int fromId;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['nick'] = nick;
    map['sex'] = sex;
    map['actId'] = actId;
    map['birth'] = birth;
    map['online'] = online;
    map['avatar'] = avatar;
    map["fromNick"] = fromNick;
    if (usingApp != null) {
      map['usingApp'] = usingApp.toJson();
    }
    map['age'] = age;
    map['fdType'] = fdType;
    map['fromId'] = fromId;
    return map;
  }
}

class HallAppModel {
  HallAppModel({
    this.userNum,
    this.purchased,
    this.name,
    this.rcover,
    this.id,
    this.avatars,
  });

  HallAppModel.fromJson(dynamic json) {
    userNum = json['userNum'];
    purchased = json['purchased'];
    name = json['name'];
    rcover = json['rcover'];
    id = json['id'];
    avatars = json['avatars'] != null ? json['avatars'].cast<String>() : [];
  }

  int userNum;
  int purchased;
  String name;
  String rcover;
  int id;
  List<String> avatars;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['userNum'] = userNum;
    map['purchased'] = purchased;
    map['name'] = name;
    map['rcover'] = rcover;
    map['id'] = id;
    map['avatars'] = avatars;
    return map;
  }
}

class FriendDataModel {
  FriendDataModel({
    this.users,
  });

  FriendDataModel.fromJson(dynamic json) {
    if (json['users'] != null) {
      users = [];
      json['users'].forEach((v) {
        users.add(HallFriendsModel.fromJson(v));
      });
    }
  }

  List<HallFriendsModel> users;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (users != null) {
      map['users'] = users.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

class AppDataModel {
  AppDataModel({
    this.apps,});

  AppDataModel.fromJson(dynamic json) {
    if (json['apps'] != null) {
      apps = [];
      json['apps'].forEach((v) {
        apps.add(HallAppModel.fromJson(v));
      });
    }
  }
  List<HallAppModel> apps;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (apps != null) {
      map['apps'] = apps.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

class HallDataModel {
  HallDataModel({
    this.recmds,
    this.apps,
  });

  HallDataModel.fromJson(dynamic json) {
    if (json['recmds'] != null) {
      recmds = [];
      json['recmds'].forEach((v) {
        recmds.add(HallFriendsModel.fromJson(v));
      });
    }
    if (json['apps'] != null) {
      apps = [];
      json['apps'].forEach((v) {
        apps.add(HallAppModel.fromJson(v));
      });
    }
  }

  List<HallFriendsModel> recmds;
  List<HallAppModel> apps;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (recmds != null) {
      map['recmds'] = recmds.map((v) => v.toJson()).toList();
    }
    if (apps != null) {
      map['apps'] = apps.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

class AppFriendsUser {
  AppFriendsUser({
    this.nick,
    this.sex,
    this.actId,
    this.birth,
    this.avatar,
    this.duration,
    this.relate,});

  AppFriendsUser.fromJson(dynamic json) {
    nick = json['nick'];
    sex = json['sex'];
    actId = json['actId'];
    birth = json['birth'];
    avatar = json['avatar'];
    duration = json['duration'];
    relate = json['relate'];
  }
  String nick;
  int sex;
  int actId;
  String birth;
  String avatar;
  int duration;
  int relate;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['nick'] = nick;
    map['sex'] = sex;
    map['actId'] = actId;
    map['birth'] = birth;
    map['avatar'] = avatar;
    map['duration'] = duration;
    map['relate'] = relate;
    return map;
  }

}
class AppFriendsDataModel {
  AppFriendsDataModel({
    this.userNum,
    this.users,});

  AppFriendsDataModel.fromJson(dynamic json) {
    userNum = json['userNum'];
    if (json['users'] != null) {
      users = [];
      json['users'].forEach((v) {
        users.add(AppFriendsUser.fromJson(v));
      });
    }
  }
  int userNum;
  List<AppFriendsUser> users;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['userNum'] = userNum;
    if (users != null) {
      map['users'] = users.map((v) => v.toJson()).toList();
    }
    return map;
  }

}