class EventCommentModel {
  EventCommentModel({
    this.id,
    this.eventOfficialId,
    this.eventOfficialName,
    this.appId,
    this.userId,
    this.userName,
    this.userPortrait,
    this.userPhone,
    this.content,
    this.time,
    this.sort,
    this.status,
  });

  EventCommentModel.fromJson(dynamic json) {
    id = json['id'];
    eventOfficialId = json['eventOfficialId'];
    eventOfficialName = json['eventOfficialName'];
    appId = json['appId'];
    userId = json['userId'];
    userName = json['userName'];
    userPortrait = json['userPortrait'];
    userPhone = json['userPhone'];
    content = json['content'];
    time = json['time'];
    sort = json['sort'];
    status = json['status'];
    userStatus = json['userStatus'];
  }

  int id;
  int eventOfficialId;
  String eventOfficialName;
  int appId;
  int userId;
  String userName;
  String userPortrait;
  String userPhone;
  String content;
  String time;
  int sort;
  String status;
  int userStatus;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['eventOfficialId'] = eventOfficialId;
    map['eventOfficialName'] = eventOfficialName;
    map['appId'] = appId;
    map['userId'] = userId;
    map['userName'] = userName;
    map['userPortrait'] = userPortrait;
    map['userPhone'] = userPhone;
    map['content'] = content;
    map['time'] = time;
    map['sort'] = sort;
    map['status'] = status;
    map['userStatus'] = userStatus;
    return map;
  }
}
