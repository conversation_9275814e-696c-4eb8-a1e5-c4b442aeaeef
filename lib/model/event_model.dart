// To parse this JSON data, do
//
//     final eventModel = eventModelFromJson(jsonString);

import 'dart:convert';

class EventDataModel {
  EventDataModel({
    this.events,
    this.currentTime,
  });

  EventDataModel.fromJson(dynamic json) {
    if (json['events'] != null) {
      events = [];
      json['events'].forEach((v) {
        events.add(EventModel.fromJson(v));
      });
    }
    currentTime = json['currentTime'];
  }

  List<EventModel> events;
  String currentTime;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (events != null) {
      map['events'] = events.map((v) => v.toJson()).toList();
    }
    map['currentTime'] = currentTime;
    return map;
  }
}

List<EventModel> eventModelFromJson(String str) =>
    List<EventModel>.from(json.decode(str).map((x) => EventModel.fromJson(x)));

String eventModelToJson(List<EventModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class EventModel {
  EventModel({
    this.id,
    this.appId,
    this.cId,
    this.name,
    this.rcover,
    this.scover,
    this.startTime,
    this.endTime,
    this.cName,
    this.num,
    this.process,
    this.focusesPortraits,
    this.isJonin,
  });

  int id;
  int appId;
  int cId;
  String name;
  String rcover;
  String scover;
  String startTime;
  String endTime;
  String cName;
  int num;
  int process;
  bool isJonin;
  List<String> focusesPortraits;

  factory EventModel.fromJson(Map<String, dynamic> json) => EventModel(
        id: json["id"],
        appId: json["appId"],
        cId: json["cId"],
        name: json["name"],
        rcover: json["rcover"],
        scover: json["scover"],
        startTime: json["startTime"],
        endTime: json["endTime"],
        cName: json["cName"],
        num: json["num"],
        process: json["process"],
        focusesPortraits:
            (json["focusesPortraits"] as List<dynamic>)?.cast<String>(),
        isJonin: json["join"] == 1,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "appId": appId,
        "cId": cId,
        "name": name,
        "rcover": rcover,
        "scover": scover,
        "startTime": startTime,
        "endTime": endTime,
        "cName": cName,
        "num": num,
        "process": process,
        "focusesPortraits": focusesPortraits,
        "join": isJonin ? 1 : 0,
      };
}
