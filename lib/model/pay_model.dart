///支付数据模型
class PayModel {
  int id;
  dynamic bprice;
  dynamic sprice;
  dynamic discount;
  String rcover;
  String name;
  int promId;
  bool isBundle; //是否为捆绑支付
  //以下为捆绑支付才用到的字段，非捆绑支付无需传递
  int subId;
  List<Apps> apps;
  int currency;

  PayModel(
      {this.id = 1,
      this.currency = 0,
      this.bprice,
      this.sprice,
      this.discount,
      this.rcover,
      this.name,
      this.promId,
      this.isBundle = false,
      this.subId});

  PayModel.fromJson(Map<String, dynamic> json) {
    this.id = json['id'] ?? 1;
    this.bprice = json['bprice'];
    this.sprice = json['sprice'];
    this.discount = json['discount'];
    this.rcover = json['rcover'];
    this.name = json['name'];
    this.promId = json['promId'];
    this.isBundle = json['isBundle'] ?? false;
    this.subId = json['subId'];
    this.currency = json['currency'] ?? 0;
    if (json['apps'] != null) {
      apps = <Apps>[];
      json['apps'].forEach((v) {
        apps.add(new Apps.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['bprice'] = this.bprice;
    data['sprice'] = this.sprice;
    data['discount'] = this.discount;
    data['rcover'] = this.rcover;
    data['name'] = this.name;
    data['promId'] = this.promId;
    data['isBundle'] = this.isBundle;
    data['subId'] = this.subId;
    data['currency'] = this.currency;
    if (this.apps != null) {
      data['apps'] = this.apps.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Apps {
  String scover;
  String pkg;
  dynamic aver;
  dynamic cgy;
  int id;
  int state;
  List<int> tag;
  dynamic count;
  String rcover;
  dynamic sprice;
  int purchaseStatus; //0 未购买，1：已购买 2：退款中
  String name;
  dynamic bprice;

  Apps(
      {this.scover,
      this.pkg,
      this.aver,
      this.cgy,
      this.id,
      this.state,
      this.tag,
      this.count,
      this.rcover,
      this.sprice,
      this.purchaseStatus,
      this.name,
      this.bprice});

  Apps.fromJson(Map<String, dynamic> json) {
    scover = json['scover'];
    pkg = json['pkg'];
    aver = json['aver'];
    cgy = json['cgy'];
    id = json['id'];
    state = json['state'];
    tag = json['tag'].cast<int>();
    count = json['count'];
    rcover = json['rcover'];
    sprice = json['sprice'];
    purchaseStatus = json['purchaseStatus'];
    name = json['name'];
    bprice = json['bprice'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['scover'] = this.scover;
    data['pkg'] = this.pkg;
    data['aver'] = this.aver;
    data['cgy'] = this.cgy;
    data['id'] = this.id;
    data['state'] = this.state;
    data['tag'] = this.tag;
    data['count'] = this.count;
    data['rcover'] = this.rcover;
    data['sprice'] = this.sprice;
    data['purchaseStatus'] = this.purchaseStatus;
    data['name'] = this.name;
    data['bprice'] = this.bprice;
    return data;
  }
}
