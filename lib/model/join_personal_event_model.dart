class JoinPersonalEventModel {
  JoinPersonalEventModel({
      this.appName, 
      this.appId, 
      this.process,});

  JoinPersonalEventModel.fromJson(dynamic json) {
    appName = json['appName'];
    appId = json['appId'];
    process = json['process'];
  }
  String appName;
  int appId;
  int process;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['appName'] = appName;
    map['appId'] = appId;
    map['process'] = process;
    return map;
  }

}