// To parse this JSON data, do
//
//     final eventDetlModel = eventDetlModelFromJson(jsonString);

import 'dart:convert';

EventDetlModel eventDetlModelFromJson(String str) =>
    EventDetlModel.fromJson(json.decode(str));

String eventDetlModelToJson(EventDetlModel data) => json.encode(data.toJson());

class EventDetlModel {
  EventDetlModel({
    this.id,
    this.name,
    this.rcover,
    this.scover,
    this.startTime,
    this.endTime,
    this.appId,
    this.detail,
    this.cId,
    this.cName,
    this.auth,
    this.num,
    this.process,
    this.users,
    this.duration,
  });

  int id;
  String name;
  String rcover;
  String scover;
  String startTime;
  String endTime;
  int appId;
  String detail;
  int cId;
  String cName;
  int auth;
  int num;
  int process;
  int duration;
  List<User> users;

  factory EventDetlModel.fromJson(Map<String, dynamic> json) => EventDetlModel(
        id: json["id"],
        name: json["name"],
        rcover: json["rcover"],
        scover: json["scover"],
        startTime: json["startTime"],
        endTime: json["endTime"],
        appId: json["appId"],
        detail: json["detail"],
        cId: json["cId"],
        cName: json["cName"],
        auth: json["auth"],
        num: json["num"],
        duration: json["duration"],
        process: json["process"],
        users: List<User>.from(json["users"].map((x) => User.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "rcover": rcover,
        "scover": scover,
        "startTime": startTime,
        "endTime": endTime,
        "appId": appId,
        "detail": detail,
        "cId": cId,
        "cName": cName,
        "auth": auth,
        "num": num,
        "duration": duration,
        "process": process,
        "users": List<dynamic>.from(users.map((x) => x.toJson())),
      };
}

class User {
  User({
    this.actId,
    this.nick,
    this.sex,
    this.avatar,
    this.online,
    this.relate,
  });

  int actId;
  String nick;
  int sex;
  String avatar;
  int online;
  int relate;

  factory User.fromJson(Map<String, dynamic> json) => User(
        actId: json["actId"],
        nick: json["nick"],
        sex: json["sex"],
        avatar: json["avatar"],
        online: json["online"],
        relate: json["relate"],
      );

  Map<String, dynamic> toJson() => {
        "actId": actId,
        "nick": nick,
        "sex": sex,
        "avatar": avatar,
        "online": online,
        "relate": relate,
      };
}
