{"Dgoumai": "Purchased at {date_0}", "NNNhoujieshu": " End after {num_0}:{num_1}:{num_2}", "NNhoujieshu": " End after {num_0}:{num_1} ", "NYbi": "{num_0} Y Coins", "NYbiyifafangkezaisha": "{num_0} Y Coins have been distributed. You can use them to redeem items in the store. Thank you for your support for YVR!", "Nfenzhong": "{num_0} min", "Nfenzhonghoujieshu": " End after {num_0} minutes", "Nfenzhongqian": "{num_0} minutes ago", "Nmiaohoujieshu": "To end in {num_0}s", "NrencanjiaNrencenjia": "{num_0} users have joined in.", "Nrenpingfen": "Rated by {num_0} users", "Nrenpinglun": "Commented by {num_0} users", "Nrenyibaoming": "{num_0} users have signed up.", "NrenyicanjiaNrenyice": "{num_0} users have joined in.", "NsheNzhe": "{num_0}0% off", "Ntianhoujieshu": "End {num_0} days later", "Nxiaoshi": "{num_0} h", "Nxiaoshihoujieshu": "End after {num_0} hours", "Nxiaoshiqian": "{num_0} hours ago", "Nxing": "{num_0} star", "Nxing_2": "{num_0} stars", "Nzepinglun": "{num_0} comments", "Nzhang": "{num_0}", "NzhangSyouhuquanyifa": "{num_0} coupons for {str_0} have been distributed and you can use them to exchange for items in the store. Thank you for your support and love for <PERSON><PERSON>!", "Nzhangkeyong": "{num_0} vouchers available.", "PayPal": "PayPal", "Sgoumaichenggongkuai": "{str_0} has been purchased. Now you can enjoy it on your VR device.", "Shuodongbucunzai": "{str_0} This activity does not exist.", "SjinriweidakaSjinriw": "{str_0} has not checked in today.", "Syilingjiang": "{str_0} has claimed the reward.", "SyiwanchengdakaSyiwa": "{str_0} has checked in.", "SyuSshigongtonghaoyo": "and {str_0} are mutual friends.", "SzaiSzhongweian": "{str_0} has not been installed in {str_1}. Install it now and try it.", "Szhongdehuifuyinshex": "The comment in {str_0} has been blocked by the system due to suspected violations. Do not make uncivil comments for the sake of a favorable community environment.", "Szhongdehuifuyinshex_1": "The comment in {str_0} has been blocked by the system due to suspected {str_1}. Do not make uncivil comments for the sake of a favorable community environment.", "TipszaiVRyanjingzhon": "Tips: You can add it by logging in to the same account in the VR headset. You can return to the list directly without repeating the operation. You can find the SN number of device by choosing [Settings] > [General] > [About] in the VR headset. If you cannot add the VR headset, check whether relevant permissions are enabled in the Play For Dream.", "VRduanyizhongzhitoub": "Projection terminated in the VR side.", "VRnarongzhiyushangfa": "VR content at the top. \nMobile phone recording at the bottom.", "VRnarongzhiyuzuocens": "VR content on the left. Mobile phone recording on the right.", "VRshebeigoumaiqingqi": "VR device purchase request", "VRshebeijiangzailian": "The VR headset will automatically download this app when it is connected to the network.", "VRshipinchuanshuzhon": "Transmitting the VR video...", "VRweizhu": "More in VR", "VRyanjingSNhaoS": "SN number: {str_0}", "VRyanjingchuyuqingsh": "The VR headset is in teen mode and cannot be added again.", "VRyanjingduankailian": "The VR headset is disconnected. Please connect again.", "VRyanjingjiangtongbu": "This file will also be deleted and cannot be restored. Are you sure you want to continue?", "VRyanjinglianjiechen": "VR headset connected.", "VRyanjingmofalianjie": "The VR headset failed to connect to the Wi-Fi. Please try again.", "VRyanjingnacunbuzuqi": "Insufficient storage of the VR headset. Please free up some space before recording.", "VRyanjingnacunbuzuqi_1": "Insufficient storage of the VR headset. Please free up some space before using the dual recording function.", "VRyanjingtianjiachen": "The VR headset is added.", "VRyanjingtoubingshib": "Failed to initiate projection by VR headset.", "VRyanjingyizhongzhip": "The VR headset has terminated dual recording.", "VRyanjingyizhongzhit": "The VR headset has terminated projection.", "VRyanjingyushoujiwan": "The network of the VR headset and the mobile phone are inconsistent", "VRyingyongyouhuS1VRy": "1. VR app/game: {str_0}", "WiFibuyizhibufengong": "The Wi-Fi is inconsistent", "YVRRefundPolicy": "Play For Dream Refund Policy", "YVRshebeihaoSNhao": "SN number of YVR device", "YVRzaicishanyidixing": "Kindly reminder: Account cancellation is an unrecoverable operation. After the account is canceled, you will no longer be able to use the account or retrieve any content or information you have purchased, browsed or collected (even if you use the same mobile number to register again and use the YVR platform).", "YVRzaicishanyidixing_1": "Kindly reminder: Account cancellation is an unrecoverable operation. Please read this reminder carefully, and proceed only if you are sure you want to cancel the YVR account and fully understand the effect of the operation. We recommend you back up your account information before proceeding and ensure that all services related to the account have been properly handled. Once your account is canceled, we will process or anonymize your personal information according to the relevant terms of {str_0}.", "YVRzhanghao": "YVR account", "Ybi": "Y Coins", "YbiNge": "2. Y Coins: {num_0}", "Ybichongzhi": "Y Coins Recharge", "Ybichongzhixieyi": "YVR Top-up Service Agreement", "YbifafangtongzhiYbif": "Notification of Y Coins Distribution", "Ybihuoqu": "O<PERSON>ain Y Coins", "Ybishouzhimingxi": "Y Coins Transaction Details", "Ybixiaofei": "Consume Y Coins", "Ybiyifafangkezaishan": "Y Coins have been distributed and you can use them to exchange for items in the store. Thank you for your support and love for <PERSON><PERSON>!", "Ybiyuebuzu": "Insufficient Y Coins balance.", "Ybiyuebuzuqingchongz": "Insufficient Y Coins balance", "Ybizhifu": "By <PERSON> Coins", "account_cancellation_agreement_url_dev": "https://apitest.yvrdream.com/yvrdvcenter/#/cancellationagreementen", "account_cancellation_agreement_url_release": "https://developer.yvrdream.com/#/cancellationagreementen", "account_yet": "Already registered? ", "anzhuangyingyong": "Install app", "banben": "Version", "banbengengxin": "Version Update", "baocun": "Save", "baocunchenggong": "Saved.", "baocunzhong": "Saving...", "baocunzhong_1": "Saving", "baohan": "Include:", "baohanyingyong": "Included Apps", "baomi": "Secret", "baoqianmeiyouzhaodao": "Sorry, no VR headset found nearby. Please ensure the Bluetooth of the VR headset is enabled and try again.", "bendexiangcebendixia": "local album", "bianjihuodong": "Edit Activity", "bianjisirenhuodong": "Edit Personal Activity", "bianjiziliao": "Edit Info", "birthday": "Birthday", "bukeyongyouhuquanNzh": "Not applicable coupons: {num_0}", "bushidangnarongzaoch": "Harassment", "buzhichiduobushoujit": "You cannot project multiple mobile phones at the same time.", "buzhichigaiiPhonexin": "This iPhone model is not supported.", "caijian": "Crop", "cancel": "Cancel", "canjiabingwanchengda": "Complete the Daily Check-In Reward activity and you can get 50% of the purchase amount as cashback. For more information, see the activity details.", "canshubuduiwenjianwe": "Incorrect parameter or empty file.", "canshucuowucenshucuo": "Parameter error.", "canyushijiancenyushi": "Accumulated time:", "chakanquanbuzhakanqu": "View All", "chakanxiangqingzhaka": "View Details", "chongxinluzhizhongxi": "Re-record", "chongyaozhongyao": "Important", "chongzhimimazhongzhi": "Reset password.", "chongzhishibai": "Recharge failed.", "chuangjian": "Create", "chuangjianhuodong": "Create Activity", "chuangjiansirenhuodo": "Create Personal Activity", "chuliyuanyuzhouzhong": "Help teens grow healthily in metaverse.", "cimimaweiguanbiqings": "This is a common password for disabling teen mode and setting time lock.", "cizhanghaokenengbeid": "Might be stolen", "confirm": "OK", "confirm_leave": "Are you sure you want to exit?", "congxiangcezhongxuan": "Select from album", "consume_time": "Purchase Time", "cunchu": "Store", "cundaoxiangce": "Save", "cunzaijitaqinquanhan": "Infringement", "cuxiaohuodongfasheng": "The sales promotion has changed. Please refresh and try again.", "daifabudaifeibu": "To be released", "dakafanxiandaqiafanx": "Daily Check-In Reward", "dakahuo50goujikuanxi": "Join in Daily Check-In Reward to get 50% of the purchase amount as cashback", "dakai": "Enable", "dakaiSchaoshiqingcho": "{str_0} opening timed out. Please try again.", "dakailianjieshibai": "Failed to open link", "dakaiyingyong": "Open the app", "dakashujuyouyidingya": "The check-in data may have delay. Ensure that the VR headset is connected to the network.", "dakatianshumeiyoudad": "The number of check-in days does not reach the threshold for daily check-in reward.", "dangVRyanjingzaifuji": "When the VR headset is nearby, enable the Bluetooth of the mobile phone and the VR headset, and ensure that the Bluetooth and location permissions of the Play For Dream are enabled. When the connection fails, tap the Refresh button on the upper part of the screen to reconnect.", "dangqianVRyanjingdia": "The VR headset is projecting the screen to a TV. Please stop projecting and try again.", "dangqianVRyanjinglub": "The VR headset is recording the screen. Please stop recording and try again.", "dangqianVRyanjingtou": "The VR headset is projecting the screen. Please stop projecting and try again.", "dangqianVRyanjingyid": "You have logged in with the same account as the mobile phone. Now you can experience the interaction between the mobile phone and the VR headset.", "dangqianquanbuzhichi": "The current coupon is not supported.", "dangqianshebeichuyuq": "The VR headset is in teen mode. You need to disable the mode in the Device tab of the app before you can try the device.", "dangqianshebeiyilian": "The current device has been connected.", "dangqianwanglaobukey": "The current network is unavailable. Please check your network settings.", "dangqianweibangdingV": "No VR headset is bound.", "dangqianyingyongyixi": "The app has been removed.", "dangqianzaixianrensh": "{num_0} persons are online.", "dangqianzhanghaoyong": "The virtual assets under this account include but not limited to:", "dangqianzhengzaibian": "An activity is being edited. If you exit, the edited content will be lost.", "dangqianzhengzaichua": "An activity is being created. If you exit, all content will be lost.", "datingdaiting": "Hall", "denglu": "Log In", "denglumimaxiugaichen": "The login password has been changed.", "denglumimaxiugaishib": "Failed to change the login password.", "denglushibai": "<PERSON><PERSON> failed.", "dev_add_dev": "Add device", "dev_add_remind": "1. Tap \"Search for Nearby VR Headset\" down below.\n\n2. Tap a VR headset name. \n\n3. After the selected VR headset is successfully added, it will automatically log in to the current account, and you can use projection, screenshot, screen recording and other features.", "dev_ble_lead": "Pairing guide", "please_add_dev": "Please log in to your account in VR first", "dev_can_connect": "Devices available for pairing", "dev_connected": "Connected", "dev_connecting": "Connecting...", "dev_modify": "Change device name", "dev_my_vr": "My VR devices", "dev_nearby": "Nearby", "dev_need_vr": "Connect to VR glasses before you can use the feature.", "dev_not_connected": "Not connected", "dev_open_bluetooth": "Bluetooth not enabled. Failed to find VR glasses.", "dev_proj": "Projection", "dev_proj_desc": "1. If all projection attempts failed, check whether the VR headset is connected to Wi-Fi and try again.", "dev_proj_from": "Projected From", "dev_proj_to": "Projected To", "dev_research": "Search again", "dev_search_ble": "Search for devices nearby", "dev_searching": "Searching for devices nearby...", "dev_sel_dev": "Select device", "dev_setting": "Set", "dev_unavailable_func": "The feature is unavailable.", "diNhangdiNhengdiNxin": "Row {num_0}", "diNhangdiNhengdiNxin_1": "Row {num_0}", "dianjichakanhuodongx": "Click to view activity details.", "dianjidengluzhuce": "Click Log On/Sign Up", "dijiaotijiao": "同意并提交", "dingchangoumaishijia": "Order placement time", "dingchanhaodingdanha": "Order No.:", "dingchanhaodingdanha_1": "Order No.", "direct_login": "Log on directly", "dishitishi": "Prompt", "disuruma": "Obscene and profane content", "do_it_later": "Later", "douyin": "<PERSON><PERSON><PERSON>", "douyinhao": "Do<PERSON>in <PERSON>", "downloadNow": "Download now?", "downloadText": "If you tap Download Now, the VR headset will automatically download this app when it is connected to the network.", "duihuan": "Redeem", "duihuanchenggong": "Redeemed.", "email_address": "Email address", "erweiliaobangchuninw": "II. To help you complete account cancelation, you should undertake to YVR that:", "fabiaofeibiao": "Send", "fabiaoyixianidekanfa": "Share your comments.", "fangchenmikongzhishi": "Anti-addiction by limiting usage period. ", "fangqibaocundangqian": "Do not save the spliced video.", "fangqidangqianpinglu": "Discard this comment.", "fanhuishangyiye": "Previous", "faqituikuanshibaifei": "Failed to initiate a refund.", "faxiancuowuqingzaish": "An error occurred. Please add the error code in the upper part.", "faxiantongxingcuhaoy": "Find friends with similar interests.", "feishiyongshiduanVRy": "During the unavailable period, the VR headset will be locked. You can modify the available period of the current day on the teen guardian tool page.", "female": "Female", "fenzhong": "30 min", "fenzhong_1": "90 min", "forget_pwd": "Forgot password?", "fuwuqiqingqiushibai": "Server request failed.", "fuwuxieyiheyinsizhen": "Service Agreement and Privacy Policy", "gaishebeibucunzai": "This device does not exist.", "gaishebeiyijingshenq": "You have already applied for daily check-in reward. Do not repeat the operation.", "gaixuanzejinyingxian": "This affects only the display mode of the final splicing video.", "gaiyingyongmeiyougou": "This app has not been purchased yet.", "gaiyingyongtuikuanzh": "This app is being refunded and cannot be opened.", "gaizhanghaodehaoyouj": "4. Your friends under this account will not be able to find you through this account.", "gaizhanghaodequanbug": "3. All personal data and historical information of the account (including but not limited to avatar, user name, published content, browsing records, following records, and favorites) will be unrecoverable.", "gaizhanghaojitongguo": "1. This account is registered through the official YVR channel for your own use.", "gaizhanghaojitongton": "1. This account is registered through the official YVR channel for your own use.", "gaizhanghaoleijidede": "5. The levels, points and benefits you have earned under the account will be cleared.", "gaizhanghaonamofachu": "2. There are no unfinished transactions in the account.", "gaizhanghaonamoweich": "2. There are no unfinished transactions in the account.", "gaizhanghaoyishenqin": "This account has been canceled and will be deleted in {str_0}. If you continue to log in, account cancelation will be withdrawn.", "ganggang": "Just now", "ganxienindezhichinwo": "Thanks for your support. We will handle your report as soon as possible.", "hechengshibai": "Synthesis failed. Please record again.", "gender": "Gender", "gengduo": "Expand", "genghuanbeijing": "Change Background", "genghuanshoujihao": "Change Mobile Number", "get_verify_code": "Request verification code", "get_vfcode_failed": "Failed to request the verification code.", "gexinghuanarongtuiji": "Personalized recommendation", "gexingqianming": "Bio", "gongtonghaoyou": "Mutual friend", "gongxininjinridakach": "You have checked in today.", "gongxininnindedakahu": "Congratulations. Your daily check-in reward has been granted based on the information you entered in the Play For Dream. For any doubt, contact the customer service or dial ************.", "goumai": "Purchase", "goumaiS": "Purchase {str_0}", "goumaiyingyong": "Purchase App", "goumaiyingyongqianqi": "Before purchasing an app, go to Device to add a YVR device.", "guanbi": "Disable", "guanbihoujiangmofash": "If disabled, you will not receive personalized recommendations. We recommend you enable it so that you can access more interesting content.", "guanfang": "Official", "guanfanghuodong": "Official Activity", "guanfanghuodongbucun": "The official activity does not exist.", "guanlishiyongshijian": "Manage the available period to prevent gaming addiction.", "guizexiangqing": "Rule Details", "haimeiyouhaoyoukuaiq": "No friend yet. Add some now.", "haimeiyourenhedongta": "No dynamics yet.", "haoyou": "Friend", "haoyouyijingtianjia": "Friend added.", "hechenghengpingshipi": "Form 16:9 video", "hechengshupingshipin": "Form 9:16 video", "hehuhuo": "and", "heimingchanheimingda": "Blacklist", "henbaoqianmeiyouzhao": "Sorry. The app you are searching for does not exist.", "hengbinghengping": "Landscape", "hengbingpinjiehengpi": "Landscape Splicing", "henxinfangqi": "Yes", "home_action_adventure": "Action", "home_all_app": "All apps", "home_all_categories": "All types", "home_all_prices": "All prices", "home_all_types": "All types", "home_app": "App", "home_app_info": "App info", "home_below_100": "50-100", "home_below_50": "Under 50", "home_cartoon_animation": "Cartoon", "home_casual_creativity": "Leisure", "home_cosplay": "Role-play", "home_recommended_sorting": "Recommended", "home_free": "Free", "home_game": "Game", "home_go_appcenter": "Did not find a suitable application, go to the application center to have a look", "home_go_now": "Go now", "home_highest_price": "Most expensive", "home_hot_download": "Popular downloads", "home_lowest_price": "Cheapest", "home_mark": "Rating", "home_media_entertainment": "Multimedia", "home_more_100": "Above 100", "home_multiplayer_game": "Multiplayer", "home_my_app": "My Applications", "home_noti_center": "Notification Center", "home_office_tools": "Office tool", "home_person_com": "{num_0} comment(s)", "home_recently_released": "Latest release", "home_recently_update": "Latest update", "home_search_history": "Search records", "home_search_plhd": "Enter keyword search", "home_social_network": "Social network", "home_star": "Star", "home_stim_shot": "Shooting", "home_thrilling": "Horror", "home_view_all": "View all", "home_view_all_comments": "View all comments", "houduanjiekouxujianr": "The backend interface needs to be compatible with the {str_0} field.", "hulve": "Ignore", "hunheluzhi": "MRC", "huodong": "Activity", "huodongID": "Activity ID:", "huodonganchanghuodon": "Activity Mall", "huodongbucunzai": "This activity does not exist.", "huodongcanyushibaihu": "Failed to join in the activity.", "huodongchuangjianche": "Activity created.", "huodongchuangjianshi": "Failed to create the activity.", "huodongguize": "Activity Rules", "huodongjieshao": "活动介绍", "huodongshijian": "Activity time:", "huodongshuimingxuant": "(Optional) Activity Description", "huodongxiangqing": "Activity Details", "huodongxiugaichenggo": "Activity modified.", "huodongxiugaishibai": "Failed to modify the activity.", "huodongyiguoqi": "This activity has expired.", "huodongyijieshu": "This activity has ended.", "huodongyijieshuqings": "The activity has ended. Please refresh and try again.", "huodongyijingguoqi": "This activity has expired.", "huodongyijingkaishim": "This activity has started and you cannot exit.", "huodongyikaishimofat": "This activity has started and you cannot exit.", "huodongyikaishishanc": "This activity has started and cannot be deleted.", "huodongyixiaxian": "This activity has ended.", "huoquVRduanshipinshi": "Failed to obtain the videos from the VR side.", "huoquYbishujushibai": "Failed to obtain Y Coins data.", "huoquchongzhixinxish": "Failed to obtain recharge information.", "huoqushebeiliebiaosh": "Failed to obtain the device list.", "huoqushijian": "Obtained At:", "input_email": "Please enter email address", "input_phone_num": "Enter the mobile number", "input_pwd": "Enter the password", "input_verify_code": "Enter the verification code", "jiangliyifafangjiang": "<PERSON><PERSON> granted.", "jianyiVRdianliang40y": "We recommend you use the VR headset when the battery level is more than 40%.", "jianyininzaizhuxiaoq": "We recommend you back up your account information before proceeding, and ensure that all services related to the account have been properly handled. Once your account is canceled, we will process or anonymize your personal information according to the relevant terms of the User Privacy Policy.", "jiaru": "Join", "jiarushibaigaiyingyo": "Failed to add this app because it has not been purchased.", "jiazaishibaiqingchon": "Loading failed. Please try again.", "jiazaizhong": "Loading...", "jiebangzhongxiebangz": "Unbinding...", "jiebinghelubingjiepi": "Media", "jiebingyulubingjiepi": "Media", "jieshaoyixianiziji": "Introduce yourself.", "jieshushijian": "End Time", "jijiangshangxianjiqi": "Coming soon", "jingdong": "JD.com", "jingdongyonghuming": "JD.com ID", "jingtoufanzhuan": "Switch", "jinhangzhongjinhengz": "Ongoing", "jinqi": "Recent", "jinrikeshiyongshidua": "Available period today", "jintian": "Today", "jinzhichizhongwendax": "Only Chinese characters, English letters, and digits are supported.", "jitacuowuqitacuowu": "Other error.", "jitajinen19999qitaji": "Other amount 1-9,999", "jitaqita": "Other", "jitongcuowuxitongcuo": "System error.", "jitongtongzhixitongt": "System Notification", "jitongxiaoxixitongxi": "System message", "jubanzhe": "Organizer:", "jubao": "Report", "jubaoshibai": "Reporting failed.", "kaiqihuodongqingqian": "Go to the VR headset to join in the activity.", "kaiqiqingshaonianmos": "Enable teen mode", "kaishi": "Start", "kaishishijian": "Start Time", "keep_secret": "Confidential", "kenengrenshikenengre": "Users you may know", "keshiyongshiduan": "Available period", "keshiyongyouhuquandu": "You can redeem coupons for it.", "keyongshijianduan": "Available period", "keyongyouhuquanNzhan": "Applicable coupons: {num_0}", "keyongyouhuquankeyon": "Valid coupons", "kongzhishichangshouh": "Anti-addiction control helps teens grow healthily in metaverse.", "kuailaibaomingba": "Sign up now.", "kuaiquVRlitiyanSba": "Try {str_0} in VR.", "kuaiquVRlitiyanSba_1": "Try {str_0} in the VR now.", "kunbangbao": "Bundle", "laheiS": "Blacklist {str_0}", "laheiyonghuyiweizhao": "If you blacklist a user, the user cannot: \nadd you as a friend,\ninvite you to a chatroom, or\nfind you using search.", "lajiangaolajiguangga": "Spam advertisement", "lanhelianjieshibaila": "Bluetooth connection failed.", "lanheweikaiqixianggu": "Bluetooth is not enabled. The related functions cannot be used.", "leave": "Exit", "leijidakaNtianleijid": "Accumulated check-in days: {num_0}", "lianjiefuwuqishibaiq": "Failed to connect to the server. Please try again.", "lianjieshebei": "Connect device", "lianjieshibai": "Connection failed.", "lijibaoming": "Sign up Now", "lijigenghuan": "Change Now", "lijigengxin": "Update Now", "lijiqianwang": "Now", "lijixiazai": "Download Now", "lingjiangxinxidijiao": "Submitted.", "lingjiangxinxidijiao_1": "Failed to submit the information.", "lingjiangxinxitianxi": "Page for information required for reward collection", "liuchengmaSqingyongy": "*Flow code: {str_0}. Use the original purchase account to consult the customer service on the corresponding e-commerce platform for final redemption. Once your application is approved, the reward will be granted as soon as possible. Pay attention to the SMS notification.", "liuchengmayifuzhi": "Flow code copied.", "login_network_error": "Network error. Please log in again.", "login_successful": "Logged on successfully.", "luzhijijiangjieshuqi": "The recording is about to end", "luzhinarongguodamofa": "The recorded content is too large to save.", "luzhishibaozhengbeil": "Only capture the center", "male": "Male", "meiyoufugetiaojiande": "No content satisfies the condition.", "meiyoukexuanshijiand": "No period is available.", "meiyouzhaodaofujinde": "No VR headset found nearby.", "mianfeigoumaiwenfeig": "Free", "mianfeihuodewenfeihu": "Free", "mimabuyizhiqingheshi": "The passwords do not match. Please check and enter them again.", "mimacuowu": "Incorrect password.", "mingtian": "Tomorrow", "modify_nickname": "Change nickname", "mofabaocunqingchongs": "Failed to save. Please try again.", "mofabaocunqingfanhui": "Saving failed. Go back to the list to refresh.", "mofagoumaigaiyingyon": "The app cannot be purchased.", "mofahuoqujielubingwe": "Unable to obtain the media file. Please ensure that the mobile phone and the VR headset are connected to the same Wi-Fi and the app permission is enabled, and then try again.", "mofahuoquwifimingche": "Failed to obtain the Wi-Fi name.", "mofatianjiaVRyanjing": "Failed to add the VR headset.", "mofatianjiaciVRyanji": "Failed to add the VR headset. Please go back and try again.", "mofatongbuwanglaowuf": "Failed to synchronize the network.", "mofazuoweikaishishij": "This is not a valid start time. Please select again.", "mokeshiyongshiduanqi": "No period available. Go to the time lock setting.", "msg_accept_friend": "{num_0} accepted your friend request.", "msg_after_half_hour": "This activity will start 30 minutes later.", "msg_delete": "Delete", "msg_delete_confirm": "Deleted messages cannot be recovered. Are you sure you want to delete it?", "msg_evet_cancel": "This activity has been canceled.", "msg_evet_start": "The activity is about to start.", "msg_friend_pass": "Your friend request has been accepted.", "msg_invite_event": "{num_0} invites you to join an activity.", "msg_invite_you": "Your friend invites you to join an activity.", "msg_req_friend": "sent you a friend request.", "nan": "Male", "narongguodajianyizhi": "The content is too large. We recommend you directly copy it to your PC.", "nichenbukeweikonghuo": "The nickname cannot be empty or contain spaces.", "nichenbukeweikongzif": "The nickname cannot be empty or contain spaces.", "nickname": "Nickname", "nihaimeiyougaiyingyo": "You do not have this app yet. Go to purchase it.", "nihaimeiyoulianjiesh": "The device is not connected. Do you want to connect it?", "nimenduzaiwanSnimend": "You are all playing {str_0}", "nindangqianyishizuix": "It is already the latest version.", "nindeVRshebeixiangni": "Your VR headset requests to purchase {str_0}. Would you like to purchase it?", "nindebeijingtupiansh": "Your background image has not been approved", "nindegexingqianmings": "Your bio review failed", "nindegoumaiqudaonind": "Your purchase channel", "nindetouxiangtupians": "May contain disturbing content, please modify and try again.", "nindeyonghunichengsh": "Your nickname review failed", "nindezhanghaochuyuzh": "3. The account is in normal use, has passed the security inspection by YVR and bears no risk of theft, and has never committed any violation of laws and regulations.", "nindezhanghaochuyuzh_1": "3. The account is in normal use, has passed the security inspection by YVR and bears no risk of theft, and has never committed any violation of laws and regulations.", "nindijiaodezifugeshi": "The character format is not supported.", "ninjiangfangqiweidao": "1. You will give up your unexpired or unused virtual currency and props under various game characters, your various identity rights and interests in various products and/or services on the YVR platform, the unexpired online service content you have purchased in various products and/or services, the related assets (such as Y Coins) you have obtained in various products and/or services you have purchased, and other rights and interests that have been generated but not used up yet or expected benefits in the future.", "ninjiangjiechugaizha": "2. You will disassociate this account from other products (such as video or third-party platforms), for example, remove the SSO.", "ninjiangjixuwancheng": "The account cancelation process will proceed and the data of this account will also be deleted once the YVR account is canceled.", "ninjiaruhuodonghouca": "You can invite friends only after you join in an activity.", "ninshangchuandetouxi": "The avatar you uploaded does not meet the requirements.", "ninshangchuandetouxi_1": "The avatar you uploaded does not meet the requirements.", "ninshengyuhuodongtia": "Your remaining activity days cannot satisfy the condition for receiving the reward.", "ninshurudemingchenyi": "The name you entered has exceeded the length limit.", "ninyiwanchengzhifunk": "Payment complete. Now you can enjoy it on your VR headset.", "ninzaiyingyongshangd": "You have given a {num_0}-star rating for {str_0} in the App Store.", "ninzaiyingyongshangd_1": "You have given a {num_0}-star rating for {str_0} in the App Store. Your review has been approved.", "ninzaiyingyongshangd_2": "You have given a {num_0}-star rating for {str_0} in the App Store. Your review has been rejected due to illegal content.", "ninzaiyingyongshangd_3": "You have given a {num_0}-star rating for {str_0} in the App Store. Your review has been rejected due to {str_1} content.", "no_account": "New user?", "nv": "Female", "paizhao": "Take photo", "pingfenyidijiaopingf": "Your rating has been submitted.", "pinglunyidijiaoshenh": "The comment has been submitted for review. You can check the review results in [Notification Center].", "pinjie": "Splice", "pinjieluzhi": "Dual Recording", "pinjieluzhiguochengz": "Ensure that the app is on the frontend during dual recording.", "pinjiemoshi": "Mode", "plhd_no_event": "No activity yet.", "plhd_no_more_data": "No more data.", "plhd_no_msg": "No new messages.", "privacy_policy": "Privacy Policy", "privacy_policy_html_page_name": "protectionen", "prod_alipay": "Alipay", "prod_authority": "Required permission: ", "prod_detail": "Details", "prod_developer": "Developer: ", "prod_download_queue": "Added to the download list.", "prod_experience_vr": "You have paid for the app.\nEnjoy it on your VR device.", "prod_fold": "Collapse", "prod_input_comment": "Enter your review.", "prod_install_alipay": "Install Alipay first.", "prod_install_wechat": "Install WeChat first.", "prod_language": "Language: ", "prod_my_comment": "My review", "prod_need_login": "Please log on to buy.", "prod_network": "Network mode: ", "prod_pay_fail": "Payment failed.", "prod_pay_result": "Payment status", "prod_pay_success": "<PERSON><PERSON> successfully.", "prod_pay_way": "Select payment method", "prod_policy": "Platform policy:", "prod_policy_title": "Platform policy", "prod_publisher": "publisher: ", "prod_ram": "Required ROM: ", "prod_ratings_reviews": "Rating and reviews", "prod_release_date": "Release date: ", "prod_sofa": "No review yet. Be the first reviewer!", "prod_unfold": "Expand", "prod_version": "Version: ", "prod_wechat": "WeChat", "prod_book": "Book", "prod_booked": "Booked", "prod_book_app_succeeded": "Reservation successful, you will be notified when it goes live.", "prod_book_app_failed": "Reservation failed, please try again.", "prod_unbook_app_failed": "Cancellation failed, please try again.", "prod_coming_time": "Coming soon {str_0}", "profile_buy_history": "Purchase records", "profile_cancel_feedback": "Cancel feedback", "profile_change_pwd": "Change password", "profile_check_version": "Check the version", "profile_confirm_password": "Confirm password", "profile_connect_way": "Email address", "profile_contact_information": "Call ************ Customer service online time: 9:00-18:00 on working days", "profile_current_version": "Current version", "profile_desc_problem": "Describe the problem you have encountered. We will handle it in time.", "profile_edit": "Edit profile", "profile_enter_sms": "Enter SMS verification code", "profile_exchange_code": "Enter exchange code", "profile_feedback": "User feedback", "profile_health": "Health and Safety", "profile_homepage": "Homepage", "profile_input_nick": "Enter your nickname.", "profile_is_feedback": "Are you sure you want to cancel the feedback?", "profile_legal_info": "Legal information", "profile_new_password": "New password", "profile_new_ver": "Latest version", "profile_not_update": "Later", "profile_phone_num": "Phone number", "profile_please_redemption": "Enter your redemption code.", "profile_question_type": "Problem type", "profile_redemption_code": "Redemption code", "profile_refunded": "Refunded", "profile_refunding": "Refunding", "profile_request_refund": "Request refund", "profile_sel_feedback": "Select your feedback type.", "profile_set_password": "Set new password", "profile_update_now": "Update now", "profile_ver_size": "Version size", "profile_ver_update": "Version update", "profile_verify_identidy": "Verify identification", "profile_verify_phone_num": "Verify mobile number", "pwd_input_again": "Confirm the password", "pwd_login": "Log on by password", "qianmingbunengchaogu": "The bio cannot exceed 6 lines.", "qianmingbunenghanyou": "The bio cannot contain blank lines and should have no more than 6 lines.", "qiehuanshipin": "Switch Video", "qingduiyingyongSdafe": "Rate the {str_0} app.", "qinggenjuxiafangzhiy": "Fill in the reward collection information following the instructions below. We will review the information you provide as soon as possible.", "qinglianjiezhixiangt": "After connecting to the same network, swipe down to refresh the list.", "qingniwubishenshenyu": "Please carefully read and fully understand all of the provisions in {str_0} and {str_1}. You can view, change, and delete your personal information and manage permissions in My. If you agree, tap Agree to accept our service.", "qingqianwangVRshebei": "Go to the VR headset to manually download the app.", "qingqianwangVRyanjin": "Try it in the VR headset.", "qingqianwangYVRguanw": "Please visit https://yvr.cn", "qingqiehuanshipindep": "Switch the video splicing mode.", "qingqiuchaoshi": "Request timed out.", "qingqiushibai": "Request failed.", "qingquanchengshiyong": "Hold your phone vertically during recording", "qingquebaoVRyanjingl": "Ensure that the VR headset is connected to the network and the screen is on.", "qingquebaoVRzhongyid": "Ensure that you have logged in with the same account.", "qingshangchuanbaohan": "Upload the screenshot of the platform order containing the order No. and actual payment amount.", "qingshangchuanshenfe": "Upload the photos of the front and back of your ID card (must be clear and complete.)", "qingshaonianmoshiqin": "Teen mode", "qingshaonianmoshiyig": "Teen mode is off.", "qingshaonianmoshiyig_1": "The teen mode is disabled. Enable it and retry.", "qingshaonianmoshiyik": "Teen mode enabled.", "qingshaonianshouhugo": "Teen guardian tool", "qingshiyongjitashaix": "Try other filter criteria.", "qingshuruNweishuzi": "Enter a {num_0}-digit number.", "qingshuruS": "Enter {str_0}.", "qingshuruWiFimima": "Enter the Wi-Fi password.", "qingshurufashenggeig": "Enter the verification code that is sent to the mobile number bound to the account.", "qingshurumimayanzhen": "Enter the password to authenticate the parent's identity and enter the teen mode setting.", "qingshuruningoumaiqu": "Enter the platform order No. (must be consistent with that in the uploaded screenshot) of your purchase channel.", "qingshurushoujihao": "Enter a mobile number.", "qingshuruxinshoujiha": "Enter a new mobile number.", "qingshuruyanzhengma": "Enter the verification code.", "qingshuruyouxiaoshou": "Enter a valid mobile number.", "qingwanchengbitianxi": "Complete all required items before submission.", "qingwutuichu": "Do not exit", "qingxiananzhuangweix": "Please install the WeChat app first.", "qingxianbangdingnind": "Please bind your VR headset first.", "qingxiandakaiVRshebe": "Enable the Wi-Fi function of the VR headset first.", "qingxiandakaiVRyanji": "Enable the Wi-Fi function of the VR headset first.", "qingxiandakaifangwen": "Please enable the permission to access the local network.", "qingxiandakaiqingsha": "Enable teen mode first.", "qingxiandenglunindez": "Log in first.", "qingxianguanbiqingsh": "Turn off teen mode first.", "qingxianjiangshoujil": "Connect the mobile phone to Wi-Fi first. If it is connected, enable the Bluetooth and location permissions of the Play For Dream first.", "qingxiankaiqiSquanxi": "Enable {str_0} permission", "qingxiankaiqibendexi": "Please enable the permission to access the local album.", "qingxiankaiqixiangji": "Enable camera permission", "qingxiankaiqixiangji_1": "Enable the camera and microphone permissions first.", "qingxianlianjieVRyan": "Connect the VR headset first.", "qingxiantongyiYVRfuw": "You need to agree to the Service Agreement and the Privacy Policy of YVR.", "qingxianwanchengbiti": "Complete the required items first.", "qingxianyuedubington": "Please read and agree to the terms and conditions of the Account Cancelation Agreement first.", "qingxianyuedubington_1": "Please read and agree to YVR Top-up Service Agreement", "qingxianzaiVRyanjing": "Log in first in the VR headset.", "qingxuanzeshipindepi": "Select a video splicing mode.", "identity_card_front": "Front", "identity_card_back": "Back", "quanbu": "All", "quanbuhuodong": "All activities", "qucanjiaqucenjiaqusa": "Join In Now", "qudenglu": "Log In Now", "queding": "OK", "quedingfangqicaichan": "Give Up Account Assets", "quedingyaoshanchugai": "Are you sure you want to delete this friend?", "quedingyaoshanchugai_1": "Are you sure you want to delete this activity?", "quedingyaotuikuanmat": "Are you sure you want to refund? The refund amount will be returned to your payment account in three working days. You cannot use this app during the refund period.", "quedingzhuxiao": "Yes", "quedingzhuxiaozhangh": "Confirm Account Can<PERSON>", "queren": "OK", "querenmima": "Confirm password", "querenzhuxiaochongya": "Important reminders for account cancelation", "querenzhuxiaojiangwa": "If you confirm the cancelation, your request for account cancelation will be completed, and the system will automatically cancel your YVR account 7 days later. To withdraw the request, log in to this account again within 7 days.", "querenzhuxiaozhangha": "After you confirm account cancelation, your account will be automatically canceled 7 days later. To withdraw the request, log in to this account again within 7 days.", "qugoumai": "Purchase Now", "qukaiqi": "Enable Now", "qulianjie": "Connect", "qushezhi": "Disable Now", "quxiao": "Cancel", "quxiaoluzhi": "Cancel", "quxiazai": "Download Now", "recharge_service_agreement_url_dev": "https://apitest.yvrdream.com/yvrdvcenter/#/rechargeagreementen", "recharge_service_agreement_url_release": "https://developer.yvrdream.com/#/rechargeagreementen", "refundAvailable": "Refund Available: ", "refund_policy_html_page_name": "refundPolicyen", "register_account": "Register", "renxiangweizhu": "More in Camera", "resend": "Resend", "riqishijian": "Date/Time", "riyiersan": "<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>", "rizhidiaoshirizhitia": "Log debugging", "runinrengxuanzejixuz": "If you still choose to cancel your account, you are deemed to automatically give up your virtual assets (including but not limited to the following rights and interests) under the account. You understand and agree that we cannot assist you in restoring the preceding services.", "ruxinxiqueloucuowuze": "If the information is missing or wrong, the application will be deemed invalid.", "ruyouqingshangchuang": "(Optional) Upload the YVR invoice, if any.", "sanninzhuxiaobenYVRz": "III. Your cancelation of this YVR account does not mean your relevant responsibilities under this account will be diminished or exempted before cancelation.", "saomiaolanhexuyaonin": "To search for nearby Bluetooth devices, please enable location permission for Play For Dream in Settings.", "sel_photograph": "Select from album", "service_agreement": "Terms of Service", "service_agreement_html_page_name": "serveren", "shaixuantiaojian": "Filter criterion:", "shanchu": "Yes", "shanchuS": "Delete{str_0}", "shanchuhaoyouchenggo": "Deleted.", "shanchuhuodong": "Delete Activity", "shanchushibai": "Deletion failed.", "shanchuzhong": "Deleting...", "shangchuanshibaishan": "Upload failure.", "shangchuanwenjianwei": "The uploaded file fails the intelligent audit.", "shangchuanzhongshang": "Uploading...", "shangwu": "a.m.", "shaohouchakanshaohou": "View Later", "shaohouqianwang": "Later", "shaohouxiazai": "Maybe Later", "shebeibucunzai": "No device found.", "shenfenyanzheng": "Identity authentication", "shengjishibaiqingcho": "Upgrade failed. Please try again.", "shengyuN0": "(Remaining: {num_0})", "shengyuNxuzhifuN": "(Remaining: {num_0}，; To-be-paid: {num_1})", "shenhebutongguoyuany": "Rejected. Illegal content found.", "shenheyitongguoshenh": ",Approved.", "shenqingzhanghaozhux": "Request Account Cancelation", "shezhichenggong": "Set.", "shezhikeshiyongshidu": "Available to set the usage period, which defaults to 6:00-22:00 each day.", "shezhimima": "Set password", "shichangshizhang": "Duration", "shichangshizhang_1": "Duration:", "shiduanyizhanyongqin": "This time slot has been occupied. Select another one.", "shifoufangqidangqian": "Are you sure you want to discard?", "shifoulijixiazaindia": "Download now? If you tap Download Now, the VR headset will automatically download this app when it is connected to the network.", "shifouquedingfangqid": "Are you sure you want to give up your virtual assets under this account?", "shifouquxiaobaomings": "Do you want to cancel sign-up?", "shijiansuoshezhi": "Time lock setting", "shijifukuan": "Actual payment", "shipingeshicuowu": "Incorrect video format.", "shipinluzhiyizhongdu": "Video recording has been interrupted.", "shipinnuligechengzho": "Synthesizing...", "shipinshili": "Video Example", "shiyonglanhehuliansh": "When Bluetooth is used for interconnection, this name will be displayed on other devices.", "shiyongshuimingshiyo": "Instruction", "shoujianrenzhenshixi": "Real name of the recipient", "shoujinacunbuzuqingq": "Insufficient storage of the mobile phone. Please free up some space before recording.", "shoujiyuVRyanjingwan": "The network of the mobile phone and the VR headset are inconsistent", "shouqi": "Collapse", "shuaxin": "Refresh", "shuaxinguoyupinfanqi": "You are refreshing too often. Please try later.", "shubingpinjieshuping": "Portrait Splicing", "shubingshuping": "Portrait", "shuidianshenmebashuo": "Any comments are welcome.", "shujuqingqiuchaoshij": "Data request timed out. Loading failed.", "shuruhuodongmingchen": "Enter the activity name (no more than 14 characters).", "shurunindingchanzhon": "Enter the actual payment amount (must be consistent with that in the uploaded screenshot) of your order.", "shuruyanzhengma": "Enter the verification code.", "sign_in": "Log on", "sign_out": "Log out", "sign_up": "Register", "social_activity": "Activity", "social_add_friend": "Add friend", "social_add_req": "requested to add you as a friend.", "social_block": "Blacklist", "social_block_list": "Blacklist", "social_creat_event": "Create activity", "social_date": "Date/Time", "social_duration": "Length", "social_event_desc": "Activity description (optional)", "social_friend_req": "New friends", "social_friends": "Friends", "social_ignore": "Ignore", "social_input_name": "Enter the activity name (within 14 characters).", "social_join_in": "Interested", "social_join_in_vr": "Add to VR", "social_joined": "Joined", "social_know_more": "Add friend to learn more.", "social_leave_event": "Quit Activity", "social_leave_event_desc": "If you quit the activity, you will not receive any activity notifications or updates, but you can join the activity again.", "social_minute": "Minutes", "social_my_activity": "My activities", "social_my_friends": "My friends", "social_no_block": "The blacklist is empty.", "social_organizers": "Host", "social_pass": "Pass", "social_people_attend": "{num_0} user(s) joined", "social_popular_activities": "Hot activities", "social_recent_activity": "Recent activities", "social_recommended": "Recommended", "social_sel_app": "Select app", "social_sendreq_yet": "The request is sent.", "social_unfriend": "Delete", "songNYbi": "Extra {num_0} Y Coins", "sousuojieshu": "Search ended.", "sousuonichenheIDhaot": "Search for the nickname and ID to add a friend.", "sousuozaixianyouhuha": "Search for an online game, friend name, ID, or app.", "state_empty": "Nothing Found", "state_error": "Load Failed", "state_load_fail": "Loading failed. <PERSON><PERSON> to try again.", "state_load_more": "Release to load more.", "state_network": "Load Failed, Check network ", "state_refresh": "Refresh", "state_retry": "Retry", "state_unauth": "Not sign in yet", "submit": "Submit", "tabbar_device": "<PERSON><PERSON>", "tabbar_home": "Store", "tabbar_profile": "Me", "tabbar_social": "Community", "take_picture": "Photo", "taobaozhanghuming": "Taobao ID", "tianjia": "Add", "tianjiaVRyanjing": "Add VR headset", "tianjiaVRyanjingbeij": "The request has been rejected. Please go back and reselect the desired VR headset.", "tianjiaVRyanjingqian": "Before adding a VR headset, enable Bluetooth.", "tianjiahaoyou": "Add Friend", "tianjiahaoyoukeyilia": "Add friends to learn more.", "tianjiashebei": "Add", "tianmao": "Tmall", "tianqian": "1 day ago", "tianqian_1": "2 days ago", "tingzhitoubingtingzh": "Stop Projection", "tips": "Note", "toast_already_commented": "You have already reviewed the app.", "toast_app_purchased": "You have bought the app already. Do not pay repeatedly.", "toast_avatar_fail": "Failed to upload the avatar.", "toast_avatar_success": "Failed to upload the avatar.", "toast_birthday": "Select birth date first.", "toast_dev_limit": "The number of devices logged on has reached the limit.", "toast_emoticon_icon": "Review content does not support stickers.", "toast_event_delete": "This activity has been deleted by the creator.", "toast_feedback_desc": "Thanks for your feedback. We will\ncontact you within seven workdays.", "toast_feedback_fail": "Failed to submit feedback.", "toast_gender": "Select a gender first.", "toast_inventory_shortage": "Insufficient app stock.", "toast_login_fail": "An exception occurred.", "toast_msg_not_input": "Required information is missing.", "toast_msg_update_fail": "Failed to modify information.", "toast_need_login": "Log in first.", "toast_no_regist": "The account is not registered yet.", "toast_not_exist": "The mobile number does not exist.", "toast_order_fail": "Failed to place the order.", "toast_pwd_error": "Incorrect password", "toast_redemption_fail": "Redemption failed.", "toast_refund_desc": "Your refund request is accepted. The refund will be sent to the account used to purchase the app in 3 workdays.", "toast_refund_fail": "Refund failed.", "toast_refund_success": "Refunded.", "toast_regist_yet": "The mobile number is already registered.", "toast_unknown_error": "Unknown error.", "toast_update_pwd": "The new and original password cannot be the same.", "toast_userinfo": "User info updated.", "toast_vfcodeJ_send_fail": "Failed to request the verification code.", "toast_vfcode_day": "Your requests for the verification code have exceeded the daily limit.", "toast_vfcode_error": "Incorrect verification code.", "toast_vfcode_frequently": "Too many requests for the verification code.", "tongbuqianqingxianqu": "Before network synchronization, ensure that the Bluetooth and location permissions are enabled. If the Wi-Fi has no password, directly tap OK.", "tongbuwanglaotongbuw": "Synchronize", "tongguo": "Accept", "tongyi": "Agree", "tongzhiVRxiazaishiba": "Download failed. Go to the VR headset to manually download it.", "toubingtouping": "Projection", "tuichuzhuxiao": "Exit", "tuijian": "Recommend", "tuikuan": "Refund", "tuikuanshibai": "Refund failed.", "tuikuanshibaiyichaog": "Refund failed. The refund period has expired.", "tuikuanzhong": "Refunding...", "tupianshangchuanshib": "Failed to upload the image. Please try again.", "tupianyasushibaitupi": "Image compression failed.", "understood": "OK", "unknown": "Unknown", "user_info": "Personal info", "verify_code_login": "Log on by verification code", "vfcode_send": "Verification code sent. Please check.", "wancheng": "Complete", "wangjiliao": "Forgot?", "wanglaolianjieyiduan": "The network is disconnected.", "wanglaoyichangqingsh": "Network error. Please try again later.", "wanglaoyichangqingsh_1": "Network error. Please try again later.", "weibaocunpinjieshipi": "The splicing video is not saved.", "weibaozhengnindezhan": "To ensure the security of your account, the following conditions must be met before the cancelation takes effect:", "weibaozhengxiaoguoqi": "To ensure the recording effect, please hold your phone vertically during recording.", "weichengweihaoyou": "You are not friends yet.", "weichuanruwangyelian": "No webpage link is found. Please check the jump settings.", "weifaweigui": "Illegal content", "weifaxianxiangyaodeV": "If the desired VR headset is not found, tap Search Again.", "weiguanzhugaihuodong": "You have not followed this activity and cannot unfollow it.", "weiguinichenqingchon": "Invalid nickname. Please create a new one and re-submit.", "weikaiqidingweifuwub": "The location service is not enabled. Some functions are unavailable.", "weilianjie": "Not connected.", "weixin": "WeChat", "weixinzhifu": "By WeChat", "weizhaodaoxiangguanh": "No related activity is found.", "weizhi": "Unknown", "weizhicuowu": "Unknown error.", "welcome": "Welcome to Play for Dream", "wenjianbucunzai": "The file does not exist.", "wenxindishiwenxintis": "Kindly reminder", "wodeVRyanjingwodiVRy": "My headset", "wodehaoyouwodihaoyou": "My friend", "wodeheimingdan": "Blacklist", "wodehuodongwodihuodo": "My Activity", "wodeqianbaowodiqianb": "My Wallet", "woyiyuedubingtongyiS": "I have read and agreed to the terms and conditions of the {str_0}", "woyiyuedubingtongyiY": "I have read and agree to YVR{str_0} and {str_1}.", "wozhidaoliao": "OK", "xiangcanjiaxiangcenj": "I'm interested", "xiangce": "Album", "xiangguantiaokuanchu": "according to the relevant terms of the User Privacy Policy.", "xiangguanyingyong": "Related apps", "xiangji": "Camera", "xiangjicuowu": "camera error", "xiangxiangrenshinide": "Introduce yourself to people interested in you.", "xiaofeishijian": "Transaction time:", "xiaofeixiangqing": "Transaction Details", "xiaoshi": "1 h", "xiaoshi_1": "3 h", "xiaoshi_2": "4 h", "xiaoshi_3": "2 h", "xiaoshi_4": "5 h", "xiawu": "p.m.", "xiayibu": "Next", "xiazaiyingyong": "Download App", "xinzengshijianduan": "Add period", "xiugaiqianqingxiangu": "Disable teen mode before modification.", "xiugaishoujihaobunen": "The new mobile number cannot be the same as the old one.", "xuanzeshebeiqushebei": "Select a device and try the app on the device.", "xuanzeshebeixuanzhai": "Select Device", "xuanzeyingyongxuanzh": "Select App", "xuyaodenglucainengch": "You can view downloaded apps only after you log in.", "xuyaodenglucainengch_1": "You can view your app only after you log in.", "xuyaodenglucainengch_2": "You can enter the notification center only after you log in.", "xuyaodenglucainenggo": "You can purchase it only after you log in.", "xuyaodenglucainengju": "You can report it only after you log in.", "yanzhengguchangshenf": "Authenticate parent identity", "yanzhengmabuzhengque": "Incorrect verification code.", "yanzhengmacuowu": "Incorrect verification code.", "yanzhengshenfen": "Verify Identity", "yanzhengshibai": "Authentication failed.", "yaocanjiahuodongnixu": "To join this activity, you need to download {str_0}.", "yaoqinghaoyou": "In<PERSON><PERSON>", "yibaocun": "Saved.", "yibaocun_1": "Saved", "yibaocunzhi": "Saved to", "yibaocunzhixiangce": "Saved to the album.", "yibaoming": "Already signed up", "yicanjiagaihuodongyi": "Joined in this activity", "yicanjiayicenjiayisa": "Already joined in", "yichenggongfachuyaoq": "Invitation sent.", "yichenggongjiaruhuod": "You have joined in this activity.", "yichu": "Delete", "yichuciVRyanjing": " Delete VR Headset", "yichuheimingchanchen": "Unblocked.", "yichuheimingchanyich": "<PERSON><PERSON><PERSON> from Blacklist", "yichuheimingdan": "<PERSON><PERSON><PERSON> from Blacklist", "yichuhouVRjiangzaili": "After this VR headset is deleted, it will automatically log out of \nthe original login account when connected to network. Are you sure you want to proceed?", "yifasongqingqiuyifei": "Request sent.", "yifeichangweihanwome": "I. We regret that we will no longer provide services for you. If you still choose to cancel your account, you are deemed to automatically give up your virtual assets (including but not limited to the following rights and interests) under the account. You understand and agree that we cannot assist you in restoring the preceding services.", "yigoumai": "Purchased", "yiguanbigexinghuanar": "Personalized recommendation has been disabled.", "yijiarugaihuodong": "You have joined in this activity.", "yijiaruheimingchanyi": "Blocked.", "yijieshu": "Already ended", "yijingguanzhugaihuod": "You have already followed this activity and cannot follow it again.", "yilingjiang": "You have received the reward.", "yimogengduowenjianyi": "No more files.", "yimogengduoyingyongy": "No more apps.", "yingyonggoumaichengg": "Purchased.", "yingyongtuiandetongz": "Notification of App Promotion", "yingyongyixiajiahuob": "The app has been removed or does not exist.", "yinsiguanli": "Privacy management", "yiquxiaogechengqingc": "Synthesis canceled. Please record again.", "yituichugaihuodong": "You have exited this activity.", "yituichugaihuodong_1": "You have exited this activity.", "yiwan": "Played", "yiwanNfenzhong": "Time played: {num_0} minutes.", "yiwanNxiaoshi": " Time played: {num_0} hours.", "yiwanchengdakayiwanc": "You have checked in.", "yixiangVRyanjingfaso": "A request for adding the VR headset has been sent. Go to the VR headset and click Agree.", "yonghuyinsizhengce": "User Privacy Policy", "youhuquanNzhang3youh": "3. Coupons: {num_0}", "youhuquanfafangtongz": "Notification of Coupons Distribution", "youhuquanyouhuiquany": "coupons", "youhuquanyouhuiquany_1": "Coupons", "youhushuyouhuishuyou": "Games Owned", "youxiaoqizhi": "Valid until:", "yuan10Ybi": "CNY 1 = 10 Y Coins", "yudiyicishurudemimab": "Passwords do not match.", "yujihaixuNmiao": "{num_0} seconds left", "yulantupian": "Preview Image", "yunxuyijiaruyonghuya": "<PERSON>ow joined users to invite friends", "yvr_protocol": "Registration by mobile number means that you have agreed to YVR", "yvr_protocol2": "Registration by email means that you have agreed to YVR{str_0} and {str_1}", "zaiVRzhongjiaru": "Join in from VR", "zaininshenqingzhuxia": "Before you request account cancelation, please carefully read and agree to this Account Cancellation Agreement.", "zanbu": "No", "zanbugengxin": "Maybe Later", "zanbushiyong": "Later", "zanmogengduodongtaiz": "No more posts.", "zanmohaoyouzanwuhaoy": "No friend found.", "zanmojiebinghelubing": "No screenshot or recording file available.", "zanmojiluzanwujilu": "No record available.", "zanmokaquanzanwukaqu": "No voucher available.", "zanmokeyongzanwukeyo": "No voucher available.", "zanmoshiyongshuiming": "No usage instruction is available.", "zanshimofahuoqushebe": "Unable to obtain the device binding information.", "zanweigoumaiyingyong": "No app has been purchased yet.", "zhangchanzhangdanzha": "Bill", "zhanghaobucunzai": "The account does not exist.", "zhanghaodenglu": "Account login", "zhanghaoyishenqingzh": "The account has been canceled.", "zhanghaoyishixiao": "The account is invalid.", "zhanghaoyizhuxiao": "The account has been canceled.", "zhanghaoyuanquan": "Account & Security", "zhanghaozhuxiaoshiba": "Account cancelation failed.", "zhanghuyuanquan": "Account & Security", "zhankai": "Expand", "zhegeshishipindebiao": "It is the video title.", "zhengzaiYVRdatingzhe": "is in the YVR hall.", "zhengzaibaocun": "Saving...", "zhengzaichangshichon": "Trying to reconnect...", "zhengzaichangshilian": "Trying to connect another device...", "zhengzaidakaiyingyon": "The app is being opened. Do not repeat this operation.", "zhengzaijinhangzhong": "Ongoing", "zhengzaijinhangzhong_1": "An ongoing activity cannot be deleted.", "zhengzailianjieVRyan": "Connecting VR headset...", "zhengzailianjiezhong": "Connecting...", "zhengzaiqiehuanshebe": "Switching device...", "zhengzaiquanlisousuo": "Searching for the VR headset...", "zhengzaishiyongS": "is using {str_0}.", "zhengzaishiyongweizh": "is using an unknown app.", "zhengzaishiyongyingy": "is using {str_0}.", "zhengzaisousuo": "Searching...", "zhengzaiweiVRyanjing": "Connecting the VR headset to Wi-Fi... Please wait...", "zhidaoliao": "OK", "zhifubaozhanghaodesh": "Authenticated real name for the Bank account", "zhifubaozhanghaoyong": "Bank account (for receiving cashback)", "zhifubaozhifu": "By <PERSON><PERSON>y", "zhifuchenggong": "Paid.", "zhifufangshi": "Payment method:", "zhifufangshi_1": "Payment method", "zhifujieguo": "Payment result", "zhifujine": "Payment amount:", "zhifushibai": "Payment failed.", "zhifushijian": "Payment time:", "zhiyouhuodongchuangj": "Only the activity creator has this permission.", "zhouliuzhizhourizhou": "Saturday to Sunday", "zhouyizhizhouwu": "Monday to Friday", "zhuanti": "Topic", "zhuantiyiguoqihuobuc": "The topic has expired or does not exist.", "zhutiyiguoqihuobucun": "The subject has expired or does not exist.", "zhuxiaochenggongshou": "Canceled. Your mobile phone has been unbound with the third party.", "zhuxiaoxieyi": "Account Cancelation Agreement", "zhuxiaoxieyi_1": "Account Cancelation Agreement", "zhuxiaozhanghao": "Cancel account", "zhuyireshenhetongguo": "1.请再次确认信息填写是否有误，审核中无法修改信息。\n2.提交后，将生成返现流程码，请联系原销售渠道员工/客服，进行最终核对。\n3.您所提交的所有信息我们将信息保密处理，不与第三方共享，继续提交将视为您已同意向信息授权给YVR进行合法校验。", "zijichuangjiandehuod": "You cannot exit an activity created by yourself.", "zongyue": "Total balance", "zuiduoyunxushangchua": "A maximum of {num_0} images are allowed.", "zuijinyizhou": "Recent week", "zuirehuodong": "Hot", "zuixinhuodong": "New", "cardsForCN": " ", "yCoinRecord": "Y币记录", "payWayManage": "支付方式管理", "coupon": "优惠券", "toBind": "去绑定", "unbind": "解除绑定", "bindFail": "绑定失败", "unbindFail": "解绑失败", "bindSuccess": "绑定成功", "unbindSuccess": "解绑成功", "unbindAlipay": "确定要解绑支付宝免密支付吗？", "confirmTheContract": "确认签约", "bindAlipay": "确定要开通支付宝免密支付吗？", "alipayPwdFree": "支付宝免密支付", "ComfortRating": "舒适度评级", "Comfort": "舒适度：", "AgeAdvice": "年龄建议：", "SupportedPlatforms": "支持平台：", "ControlMode": "控制方式：", "GameMode": "游戏模式：", "VRController": "VR手柄", "HandTracking": "手势追踪", "Gamepad": "游戏手柄", "Keyboard": "键盘", "Comfortable": "舒适", "Moderate": "适中", "Nervous": "紧张", "Individual": "单人", "ManyPeople": "多人", "Cooperate": "合作", "Confrontation": "对抗", "TheComfortOfVRPlay": "Comfort in VR depends on factors such as game optimization, gameplay mode, and player adaptability. Please refer to the comfort rating to choose a suitable game.", "SuitableForAllPlayers": "Appropriate for most players.", "FitsMostPlayers": "Not appropriate for a few players, and may become comfortable after adaptation.", "SomePlayersAreNotSuitable": "Some players may experience discomfort such as dizziness, so players who are new to VR should exercise caution when trying it out.", "CommonQuestion": "常见问题", "Recommend": "推荐", "Games": "游戏", "Application": "应用", "Explore": "探索", "ExploreArea": "探索专区的应用可能在开发或测试中，可参考兼容性评级和游玩建议进行体验", "NotPossess": "未拥有", "Stores": "线下门店", "Attention": "注意", "PleaseFillInTheIDNumber": "请填写银行卡号实名认证人的身份证号码（根据相关财税规定，请务必正确填写身份证号码，且年满18周岁，信息仅用于校验，我们将对您的信息保密）", "PleaseFillInCorrectly": "请正确填写，信息缺漏、错误，则视为申领无效", "IConfirmThatTheAbove": "我确认以上信息无误。信息一旦提交将不能修改，如信息错误导致未能取得返现由本人承担责任。", "IUnderstandAndAgree": "我知悉并同意为返现活动提供的信息，且知悉以上信息包含个人信息。", "IAgreeThatYVRWill": "我同意YVR使用所提交信息进行合法校验。YVR承诺对提供的信息严格保密。", "IKnownThisVRAcitivity": "我知悉本次参与的VR产品打卡试用活动将会获得不同激励金额，我已阅读并同意{str_0}，根据国家法律规定，YVR将为我申报个人所得税。", "PlatformServiceAgreement": "《平台服务协议》", "PleaseTickTheAboveAgreement": "请先勾选以上协议", "BackToCheck": "返回检查", "FailedToVerifyTheDigits": "身份证号码位数验证失败", "PleaseFillInYourPurchase": "请填写您的购买渠道", "PleaseUploadAPaymentScreenshot": "请上传付款截图（支付宝/微信截图）", "ConnectNearbyDevicesPermission": "扫描蓝牙需要您在玩出梦想应用设置中先开启连接附近的设备权限", "CheckInActivity": "打卡签到活动", "AdditionalContent": "附加内容", "MyPoints": "我的积分", "PointsMall": "积分商城", "CurrentlyAvailablePoints": "当前可用积分", "PointsRules": "积分规则", "PointsMallIsUnderConstruction": "积分商城预计7月上线，敬请期待", "NoPointDetailsYet": "暂无积分明细哦", "NotJoinPunch": "暂未参加任何打卡活动", "point_rule_html_page_name": "pointRuleen", "ProductDetails": "商品详情", "ConfirmOrder": "确认订单", "ShippingAddress": "收货地址", "EditAddress": "编辑收货地址", "AddNewAddress": "新建收货地址", "ExchangeResult": "兑换结果", "ExchangeRecord": "兑换记录", "OrderDetails": "订单详情页", "RelatedActivity": "相关活动", "GiftCard": "礼品卡", "IDCardError": "身份证号码校验未通过", "YVRGoSport": "YVR GO运动", "PrivacySetting": "隐私设置", "PublicScope": "公开范围", "SelectGames": "选择游戏", "NoRatingYet": "暂无评分", "Star_1": "1 star", "Star_2": "3 stars", "Star_3": "2 stars", "Star_4": "5 stars", "Star_5": "4 stars", "StarAll": "All", "DataAccumulation": "暂无评分，数据累积中", "HowDoYouEvaluate": "你如何评价{str_0}？", "WriteReviewAndMoreFeedback": "写评价留下更多反馈吧！", "TheEvaluationIsUnderReview": "评价将在审核通过后展示", "PleaseInstallAppAndExperience": "请先安装应用并体验5分钟再来评价吧！", "Popular": "Popular", "Latest": "Latest", "MajorUpdate": "Major Update", "Today": "Today", "Tomorrow": "Tomorrow", "LoginFailure": "Login failure", "LoginPasswordRules": "8-12 digits, with at least 3 digits in numbers, letters, and symbols", "TheAccountHasBeenLocked": "The account has been locked. Please login again after 5 minutes.", "PasswordsError": "Passwords error，You have {num_0} chances left", "SportsPoster": "运动海报", "RelatedContentHasBeenRemoved": "相关内容已下架", "Share": "分享", "PlayForDream": "PLAY FOR DREAM", "TheEvaluationYouSubmit": "您提交的评价会经过平台审核后展示", "PleaseEnterAtLeastFive": "应用评价至少为5个字", "CompatibilityRating": "兼容性评级", "AllGames": "全部游戏", "AllApps": "全部应用", "Appointment": "预约", "SafePay": "支付安全", "PageNotFound": "请求页面不存在", "IncorrectEmailAddress": "Incorrect email address", "EmailAddressHasBeenRegistered": "Email address registered", "RemoteShooting": "遥控拍摄", "KeepTimeSynchronized": "Please keep the phone and device time synchronized", "xing": "4 stars", "xing_1": "1 star", "xing_2": "3 stars", "xing_3": "2 stars", "xing_4": "5 stars", "mianfeiwenfei": "Free", "home_comment": "Comment", "pingtaizhengce": "Platform Policy", "pinglunnarongweiguip": "Illegal content", "home_earliest_released": "Earliest release", "xuanzezhifufangshixu": "Select payment method", "pwd_rule": "6-12 characters (letters & digits)", "gechengshibaiqingcho": "Synthesis failed. Please record again.", "qingshuru612weidaxia": "Enter a string of 6 to 12 letters, digits, or special characters."}