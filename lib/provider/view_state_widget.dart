import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/svg.dart';
import 'package:yvr_assistant/manager/resource_mananger.dart';
import 'package:yvr_assistant/generated/l10n.dart';
import 'package:yvr_assistant/public_ui/widget/buttons.dart';

import '../styles/app_color.dart';
import 'view_state.dart';

/// 加载中
class ViewStateBusyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return const Center(
        child: const SpinKitCircle(
      color: const Color(0xFF52555C),
      size: 50,
    ));
  }
}

/// 基础Widget
class ViewStateWidget extends StatelessWidget {
  final String title;
  final String message;
  final Widget image;
  final Widget buttonText;
  final String buttonTextData;
  final VoidCallback onPressed;

  ViewStateWidget(
      {Key key,
      this.image,
      this.title,
      this.message,
      this.buttonText,
      @required this.onPressed,
      this.buttonTextData})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    var titleStyle = Theme.of(context)
        .textTheme
        // ignore: deprecated_member_use
        .subtitle1
        .copyWith(color: Color(0xff2C2E33), fontSize: 16);
    var messageStyle =
        titleStyle.copyWith(color: titleStyle.color, fontSize: 14);
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        image ?? Icon(IconFonts.iconNoWifi, size: 56, color: Color(0xff52555C)),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 30),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              Text(
                title ?? YLocal.of(context).state_error,
                style: titleStyle,
              ),
              ConstrainedBox(
                constraints: BoxConstraints(maxHeight: 250, minHeight: 30),
                child: SingleChildScrollView(
                  child: Text(
                    message ?? '',
                    style: messageStyle,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ViewStateButton(
              child: buttonText,
              textData: buttonTextData,
              onPressed: onPressed,
            )
          ],
        ),
      ],
    );
  }
}

class ViewStateErrorWidget extends StatelessWidget {
  final ViewStateError error;
  final String title;
  final String message;
  final Widget image;
  final Widget buttonText;
  final String buttonTextData;
  final VoidCallback onPressed;

  const ViewStateErrorWidget({
    Key key,
    @required this.error,
    this.image,
    this.title,
    this.message,
    this.buttonText,
    this.buttonTextData,
    @required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var defaultImage;
    var defaultTitle;
    var errorMessage = error.message;
    String defaultTextData = YLocal.of(context).state_retry;
    switch (error.errorType) {
      case ErrorType.networkError:
        defaultImage =
            //  Transform.translate(
            //   offset: Offset(-50, 0),
            //   child:
            const Icon(IconFonts.iconNoWifi,
                size: 56, color: Color(0xff52555C));
        // );
        defaultTitle = YLocal.of(context).state_network;
        errorMessage = ''; // 网络异常移除message提示
        break;
      case ErrorType.defaultError:
        // 自定义
        errorMessage = "";
        defaultImage = const Icon(IconFonts.iconNoWifi,
            size: 56, color: Color(0xff52555C));
        defaultTitle = YLocal.of(context).state_error;
        break;
      case ErrorType.detailError:
        // 自定义
        // errorMessage = errorMessage;
        defaultImage = const Icon(
          Icons.error,
          size: 56,
          color: Color(0xff52555C),
        );
        defaultTitle = "";
        break;
    }

    return ViewStateWidget(
      onPressed: this.onPressed,
      image: image ?? defaultImage,
      title: title ?? defaultTitle,
      message: message ?? errorMessage,
      buttonTextData: buttonTextData ?? defaultTextData,
      buttonText: buttonText,
    );
  }
}

///搜索没数据
class ViewStateSearchEmptyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 150),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            "assets/svg/search_empty.svg",
            color: Color(0xff52555C),
            width: 54,
            height: 48,
          ),
          SizedBox(
            height: 28,
          ),
          Text(
            YLocal.of(context).meiyoufugetiaojiande,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 12,
              height: 1,
              color: Color(0xff2C2E33),
            ),
          ),
        ],
      ),
    );
  }
}

/// 页面无数据
class ViewStateEmptyWidget extends StatelessWidget {
  final String message;
  final Widget image;
  final Widget buttonText;
  final VoidCallback onPressed;

  const ViewStateEmptyWidget(
      {Key key,
      this.image,
      this.message,
      this.buttonText,
      @required this.onPressed})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ViewStateWidget(
      onPressed: this.onPressed,
      image: image ??
          const Icon(IconFonts.iconNoDev, size: 56, color: Color(0xff52555C)),
      title: message ?? YLocal.of(context).state_empty,
      buttonText: buttonText,
      buttonTextData: YLocal.of(context).state_refresh,
    );
  }
}

/// 页面未授权
class ViewStateUnAuthWidget extends StatelessWidget {
  final String message;
  final Widget image;
  final Widget buttonText;
  final VoidCallback onPressed;

  const ViewStateUnAuthWidget(
      {Key key,
      this.image,
      this.message,
      this.buttonText,
      @required this.onPressed})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ViewStateWidget(
        onPressed: this.onPressed,
        image: image ?? ViewStateUnAuthImage(),
        title: message ?? YLocal.of(context).state_unauth,
        buttonText: buttonText,
        buttonTextData: YLocal.of(context).sign_in);
  }
}

/// 未授权图片
class ViewStateUnAuthImage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Hero(
      tag: 'loginLogo',
      child: Image.asset(
        'assets/images/r_plhd.png',
        width: 130,
        height: 100,
        fit: BoxFit.fitWidth,
        // ignore: deprecated_member_use
        color: Theme.of(context).accentColor,
        colorBlendMode: BlendMode.srcIn,
      ),
    );
  }
}

/// 公用Button
class ViewStateButton extends StatelessWidget {
  final VoidCallback onPressed;
  final Widget child;
  final String textData;

  const ViewStateButton({@required this.onPressed, this.child, this.textData})
      : assert(child == null || textData == null);

  @override
  Widget build(BuildContext context) {
    // ignore: deprecated_member_use
    return YvrTextButton(
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 18),
      child: child ??
          Text(
            textData ?? YLocal.of(context).state_retry,
            style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 14,
              height: 1,
              letterSpacing: 1,
              color: AppColors.background,
            ),
          ),
      color: Color(0xff4F7FFE),
      onPressed: onPressed,
    );
  }
}
