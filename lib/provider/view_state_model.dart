import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:yvr_assistant/network/http.dart';
import 'package:yvr_assistant/utils/log.dart';

import 'view_state.dart';

class ViewStateModel with ChangeNotifier {
  /// 防止页面销毁后,异步任务才完成,导致报错
  bool _disposed = false;

  /// 当前的页面状态,默认为busy,可在viewModel的构造方法中指定;
  ViewState _viewState;

  /// 根据状态构造
  ///
  /// 子类可以在构造函数指定需要的页面状态
  /// FooModel():super(viewState:ViewState.busy);
  ViewStateModel({ViewState viewState})
      : _viewState = viewState ?? ViewState.idle {
    // Log.d('ViewStateModel---constructor--->$runtimeType');
  }

  ViewState get viewState => _viewState;

  set viewState(ViewState viewState) {
    _viewStateError = null;
    _viewState = viewState;
  }

  ViewStateError _viewStateError;

  ViewStateError get viewStateError => _viewStateError;

  String get errorMessage => _viewStateError?.message;

  /// 以下变量是为了代码书写方便,加入的get方法.严格意义上讲,并不严谨

  bool get busy => viewState == ViewState.busy;

  bool get idle => viewState == ViewState.idle;

  bool get empty => viewState == ViewState.empty;

  bool get error => viewState == ViewState.error;

  void setIdle() {
    viewState = ViewState.idle;
    notifyListeners();
  }

  void setBusy() {
    viewState = ViewState.busy;
    notifyListeners();
  }

  void setEmpty() {
    viewState = ViewState.empty;
    notifyListeners();
  }

  void setError2(String message) {
    viewState = ViewState.error;
    _viewStateError = ViewStateError(ErrorType.detailError, message: message);
    notifyListeners();
  }

  /// 未授权的回调
  void onUnAuthorizedException() {}

  /// [e]分类Error和Exception两种
  void setError(e, stackTrace, {String message}) {
    ErrorType errorType;
    String errorMessage = "";
    if (e is DioError) {
      if (isNetworkError(e)) {
        errorType = ErrorType.networkError;
      } else {
        errorType = ErrorType.defaultError;
      }
      errorMessage = getDioErrorMessage(e) ?? "";
    } else {
      errorType = ErrorType.defaultError;
      errorMessage = e.toString();
    }
    viewState = ViewState.error;
    _viewStateError = ViewStateError(
      errorType,
      message: message,
      errorMessage: errorMessage,
    );
    notifyListeners();
    printErrorStack(e, stackTrace);
  }

  /// [e]分类Error和Exception两种
  void setDetailError(e, stackTrace, {String message}) {
    ErrorType errorType;
    String errorMessage = "";
    if (e is DioError) {
      if (isNetworkError(e)) {
        errorType = ErrorType.networkError;
      } else {
        errorType = ErrorType.detailError;
      }
      errorMessage = getDioErrorMessage(e) ?? "";
    } else {
      errorType = ErrorType.detailError;
      errorMessage = e.toString();
    }

    viewState = ViewState.error;
    _viewStateError = ViewStateError(
      errorType,
      message: message,
      errorMessage: errorMessage,
    );
    notifyListeners();
    printErrorStack(e, stackTrace);
  }

  /// 显示错误消息
  showErrorMessage(context, {String message}) {
    if (viewStateError != null || message != null) {
      if (viewStateError.isNetworkError) {
        // message ??= S.of(context).state_network;
      } else {
        message ??= viewStateError.message;
      }
      Future.microtask(() {});
    }
  }

  @override
  String toString() {
    return 'BaseModel{_viewState: $viewState, _viewStateError: $_viewStateError}';
  }

  @override
  void notifyListeners() {
    if (!_disposed) {
      super.notifyListeners();
    }
  }

  @override
  void dispose() {
    _disposed = true;
    // Log.d('view_state_model dispose -->$runtimeType');
    super.dispose();
  }
}

enum ListRefreshState {
  refreshing,
  loadMore,
}

abstract class ViewStateModel2<T1> extends ViewStateModel {
  T1 model;
  final bool autoInitialize;
  static const int invalidPageNum = 0;

  /// 分页第一页页码
  static const int pageNumFirst = 1;

  /// 分页条目数量
  static const int kPageSize = 20;

  /// 当前页码
  @protected
  int currentPageNum = invalidPageNum;

  bool refreshEnabled;
  bool loadEnabled;

  ListRefreshState refreshState;

  RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  RefreshController get refreshController => _refreshController;

  final PageParams pageParams;

  ViewStateModel2(
      {this.autoInitialize = true,
      this.refreshEnabled = true,
      this.loadEnabled = false,
      this.pageParams = const PageParams(kPageSize, 2)});

  void reset() {
    release();
    viewState = ViewState.busy;
    notifyListeners();
    resetInternal();
  }

  void release() {
    currentPageNum = 0;
    model = null;
  }

  void resetInternal() {
    int current = pageNumFirst;
    loadData(pageParams.pageSize, current).then((value) async {
      Future<void> beforeTask = beforeRefreshSuccess();
      if (beforeTask != null) {
        await beforeTask;
      }
      T1 data = value;
      onRefreshComplete(data);
      model = data;
      currentPageNum = current;
      if (isEmpty) {
        setEmpty();
      } else {
        setIdle();
      }
      if (!hasNextPage(data)) {
        refreshController.refreshCompleted();
        refreshController.loadNoData();
      } else {
        refreshController.refreshCompleted(resetFooterState: true);
      }
    }).onError((e, s) {
      print(s);
      refreshController.refreshFailed();
      refreshController.loadNoData();
      onRefreshError();
      setDetailError(e, s);
    }).whenComplete(() {
      notifyListeners();
    });
  }

  @protected
  Future<void> beforeRefreshSuccess() => null;

  void onRefreshComplete(T1 data) {}

  void onRefreshError() {}

  void onLoadComplete(T1 data) {}

  void onLoadError() {}

  ///只有请求过一次(list!=null)，才允许静默刷新
  bool refreshQuietly({bool force = false}) {
    if (model == null && !force) {
      return false;
    }
    PageToken pageToken = PageToken(this.currentPageNum);
    refreshQuietlyTask(pageToken).then((value) async {
      Future<void> beforeTask = beforeRefreshSuccess();
      if (beforeTask != null) {
        await beforeTask;
      }

      T1 data = value;
      onRefreshComplete(data);
      model = data;
      currentPageNum = pageToken.currentPage;
      if (isEmpty) {
        setEmpty();
      } else {
        setIdle();
      }

      if (!hasNextPage(data)) {
        refreshController.refreshCompleted();
        refreshController.loadNoData();
      } else {
        refreshController.refreshCompleted(resetFooterState: true);
      }
    }).onError((e, s) {
      print(s);
      refreshController.refreshFailed();
      onRefreshError();
    }).whenComplete(() {
      notifyListeners();
    });
    return true;
  }

  void refreshPage() {
    int current = pageNumFirst;
    refreshState = ListRefreshState.refreshing;
    notifyListeners();
    loadData(pageParams.pageSize, current).then((value) async {
      Future<void> beforeTask = beforeRefreshSuccess();
      if (beforeTask != null) {
        await beforeTask;
      }

      T1 data = value;
      onRefreshComplete(data);
      model = data;
      currentPageNum = current;
      if (isEmpty) {
        setEmpty();
      } else {
        setIdle();
      }

      if (!hasNextPage(data)) {
        refreshController.refreshCompleted();
        refreshController.loadNoData();
      } else {
        refreshController.refreshCompleted(resetFooterState: true);
      }
    }).onError((e, s) {
      print(s);
      refreshController.refreshFailed();
      onRefreshError();
    }).whenComplete(() {
      refreshState = null;
      notifyListeners();
    });
  }

  @protected
  bool get isEmpty => model == null;

  /// 上拉加载更多
  void loadMorePage() {}

  @protected
  Future<T1> refreshQuietlyTask(PageToken pageToken) {
    int size = pageToken.currentPage * pageParams.pageSize;
    return loadData(size, pageNumFirst);
  }

  @protected
  bool hasNextPage(T1 data) => false;

  Future<T1> loadData(int pageSize, int pageNum);
}

class PageParams {
  final int pageSize;
  final int maxPageSizeRate;

  const PageParams(this.pageSize, this.maxPageSizeRate);
}

class PageToken {
  int currentPage;

  PageToken(this.currentPage);
}

/// [e]为错误类型 :可能为 Error , Exception ,String
/// [s]为堆栈信息
printErrorStack(e, s) {
  Log.d('''
<-----↓↓↓↓↓↓↓↓↓↓-----error-----↓↓↓↓↓↓↓↓↓↓----->

$e

<-----↑↑↑↑↑↑↑↑↑↑-----error-----↑↑↑↑↑↑↑↑↑↑----->''');
  if (s != null) Log.d('''
  
<-----↓↓↓↓↓↓↓↓↓↓-----trace-----↓↓↓↓↓↓↓↓↓↓----->

$s

<-----↑↑↑↑↑↑↑↑↑↑-----trace-----↑↑↑↑↑↑↑↑↑↑----->
    ''');
}
