import 'package:flutter/widgets.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:yvr_assistant/model/page_list_model.dart';
import 'package:yvr_assistant/provider/view_state_model.dart';
import 'package:yvr_assistant/utils/log.dart';
import 'view_state_list_model.dart';

/// 基于
abstract class ViewStateRefreshListModel<T> extends ViewStateListModel<T> {
  /// 分页第一页页码
  static const int pageNumFirst = 0;

  /// 分页条目数量
  static const int pageSize = 20;

  RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  RefreshController get refreshController => _refreshController;

  /// 当前页码
  int _currentPageNum = pageNumFirst;

  /// 下拉刷新
  ///
  /// [init] 是否是第一次加载
  /// true:  Error时,需要跳转页面
  /// false: Error时,不需要跳转页面,直接给出提示
  Future<List<T>> refresh({bool init = false}) async {
    try {
      _currentPageNum = pageNumFirst;
      var data = await loadData(pageNum: pageNumFirst);
      if (data.isEmpty) {
        refreshController.refreshCompleted(resetFooterState: true);
        list.clear();
        setEmpty();
      } else {
        onCompleted(data);
        list.clear();
        list.addAll(data);
        refreshController.refreshCompleted();
        // 小于分页的数量,禁止上拉加载更多
        if (data.length < pageSize) {
          refreshController.loadNoData();
        } else {
          //防止上次上拉加载更多失败,需要重置状态
          refreshController.loadComplete();
        }
        setIdle();
      }
      return data;
    } catch (e, s) {
      /// 页面已经加载了数据,如果刷新报错,不应该直接跳转错误页面
      /// 而是显示之前的页面数据.给出错误提示
      if (init) list.clear();
      refreshController.refreshFailed();
      setDetailError(e, s);
      return null;
    } finally {
      notifyListeners();
    }
  }

  /// 上拉加载更多
  Future<List<T>> loadMore() async {
    try {
      int nextPage = _currentPageNum + 1;
      var data = await loadData(pageNum: nextPage);
      if (data.isEmpty) {
        refreshController.loadNoData();
      } else {
        onCompleted(data);
        list.addAll(data);
        if (data.length < pageSize) {
          refreshController.loadNoData();
        } else {
          refreshController.loadComplete();
        }
      }
      _currentPageNum = nextPage;
      return data;
    } catch (e, s) {
      refreshController.loadFailed();
      Log.d('error--->\n' + e.toString());
      Log.d('statck--->\n' + s.toString());
      return null;
    } finally {
      notifyListeners();
    }
  }

  // 加载数据
  Future<List<T>> loadData({int pageNum});

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }
}

abstract class ViewStateRefreshListModel2<T1>
    extends ViewStateModel2<PageListModel<T1>> {
  ViewStateRefreshListModel2(
      {bool autoInitialize = true,
      bool refreshEnabled = true,
      bool loadEnabled = true,
      PageParams pageParams = const PageParams(ViewStateModel2.kPageSize, 2)})
      : super(
            autoInitialize: autoInitialize,
            refreshEnabled: refreshEnabled,
            loadEnabled: loadEnabled,
            pageParams: pageParams);

  List<T1> get list => model?.content;

  int get totalListCount {
    return model == null ? 0 : model.totalCount;
  }

  set totalListCount(int value) {
    model.totalCount = value;
  }

  @override
  bool hasNextPage(PageListModel<T1> data) {
    return data.hasNextPage;
  }

  @protected
  bool get isEmpty =>
      model == null || model.content == null || model.content.isEmpty;

  @override
  void loadMorePage() {
    int nextPage = currentPageNum + 1;
    refreshState = ListRefreshState.loadMore;
    notifyListeners();
    loadData(pageParams.pageSize, nextPage).then((value) {
      PageListModel<T1> data = value;
      onLoadComplete(data);
      model.followBy(data);
      currentPageNum = nextPage;
      if (!value.hasNextPage) {
        refreshController.loadNoData();
      } else {
        refreshController.loadComplete();
      }
    }).onError((e, s) {
      print(s);
      refreshController.loadFailed();
      onLoadError();
    }).whenComplete(() {
      refreshState = null;
      notifyListeners();
    });
  }

  @override
  Future<PageListModel<T1>> refreshQuietlyTask(PageToken pageToken) async {
    int size = pageToken.currentPage * pageParams.pageSize;
    int maxPageSize = this.pageParams.maxPageSizeRate * pageParams.pageSize;
    if (size <= maxPageSize) {
      return super.refreshQuietlyTask(pageToken);
    }
    int num = (size + (maxPageSize - 1)) ~/ maxPageSize;
    var result = await Future<List<PageListModel<T1>>>.sync(() async {
      List<PageListModel<T1>> result = List.empty(growable: true);
      for (int i = 0; i < num; ++i) {
        result.add(await loadData(maxPageSize, i + 1));
      }
      return result;
    }).then<PageListModel<T1>>((value) {
      return value.reduce(
        (value, element) {
          element.current = value.current;
          element.size += value.size;
          element.content = value.content.followedBy(element.content).toList();
          return element;
        },
      );
    });

    pageToken.currentPage = num * maxPageSize ~/ pageParams.pageSize;

    return result;
  }

  void clear() {
    model.content.clear();
    totalListCount = 0;
    setEmpty();
  }

  void removeAt(int index) {
    model.content.removeAt(index);
    --totalListCount;
    if (isEmpty) {
      setEmpty();
    } else {
      notifyListeners();
    }
  }

  bool remove(T1 value) {
    if (model.content.remove(value)) {
      --totalListCount;
      if (isEmpty) {
        setEmpty();
      } else {
        notifyListeners();
      }
      return true;
    }
    return false;
  }

  void removeWhere(bool test(T1 element)) {
    model.content.removeWhere((element) {
      if (test(element)) {
        --totalListCount;
        return true;
      }
      return false;
    });
    if (isEmpty) {
      setEmpty();
    } else {
      notifyListeners();
    }
  }

  Future<PageListModel<T1>> loadData(int pageSize, int pageNum);
}
