name: yvr_assistant
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 2.2.3+18

environment:
  flutter: ^2.2.0
  sdk: ">=2.10.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  platform: ^3.1.0
  intl: ^0.17.0
  provider: ^6.0.1

  # Base Component
  # WebView iOS添加键值对 o.flutter.embedded_views_preview = YES
  #  🌏 /Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-2.7.5
  # 高版本(例如3.13.1)中复制 PrivacyInfo.xcprivacy 和 webview_flutter_wkwebview.podspec 文件
  webview_flutter:
  wifi_iot: 0.3.16
  # flutter_nearby_connections: ^1.1.1
  device_info: ^2.0.2
  package_info: ^2.0.2
  # quiver: ^3.0.1
  connectivity_plus: ^2.2.1 #网络连接状态监听
  dio: ^4.0.6
  http: ^0.13.5
  dio_cookie_manager: ^2.0.0
  cookie_jar: ^3.0.1
  shared_preferences: ^2.0.6
  localstorage: ^4.0.0+1 # 本地json对象存储
  adaptive_dialog: ^1.1.0 # 弹出框
  html_unescape: ^1.0.2 # Html Encoder/Decoder

  # 判断list是否相等
  collection: ^1.15.0
  pull_to_refresh: ^2.0.0
  flutter_swiper: ^1.1.6
  cached_network_image: ^3.2.0
  shimmer: ^1.1.1 # 谁用谁闪亮
  flare_flutter: ^2.0.6
  permission_handler: ^8.1.6 # android权限弹窗
  location: ^4.3.0
  url_launcher: ^6.1.7 # 启动第三方app、浏览器
  wakelock: ^0.6.2
  fluwx: 3.12.2 #微信支付
  flukit: ^2.0.0 #实战二组件

  interactiveviewer_gallery: ^0.6.0 #图片浏览
  image_gallery_saver: ^1.7.1
  auto_orientation: ^2.2.1 # 屏幕旋转
  brightness_volume: ^1.0.3 # 亮度和音量调节
  disk_space: ^0.2.1
  video_compress: ^3.1.0

  hive: ^2.2.3 #数据存储
  hive_flutter: ^1.1.0

  # 新增依赖'
  # https://blog.csdn.net/qq_38779672/article/details/122687239
  # camera: ^0.10.0 #自定义相机
  crypto: ^3.0.2 #加密
  # flutter_tab_indicator_styler: ^2.0.0 #分段控制菜单
  fijkplayer: ^0.10.1 #播放rtsp数据流
  # fijkplayer_skin: ^2.2.8 #播放器皮肤已本地使用
  # sharesdk_plugin: ^1.3.4
  # flutter_section_list: ^1.1.1
  table_calendar: ^3.0.5
  text_scroll: 0.0.3 #滚动文本
  logger: ^1.1.0 #彩色打印
  path_provider: ^2.0.11 #获取应用路径
  restart_app: ^1.1.0 #重启应用
  event_bus: ^2.0.0
  flutter_svg: ^0.23.0+1 #减低版本以满足flutter_html依赖
  common_utils: ^2.1.0 #工具库 ⭐️
  flutter_blue_plus: 1.34.5
  badges: ^1.1.6 #消息提醒
  expandable_text: ^2.2.0 #可折叠文本
  expandable: ^5.0.1 #折叠卡片
  image_picker: ^0.8.2 #图片选择
  image_crop: ^0.4.0 #图片裁剪
  percent_indicator: ^2.1.9 #进度条
  image_picker_platform_interface: ^2.1.0
  flutter_screenutil: ^4.0.2+3 #屏幕适配
  flutter_picker: ^2.0.1 #滚动选择
  flutter_easyloading: ^3.0.5 #比oktoast功能齐全
  flutter_keyboard_visibility: ^5.2.0
  keyboard_avoider: ^0.1.2 #解决键盘遮挡问题
  flutter_rating_bar: ^4.0.0 #评星
  list_tile_more_customizable: ^1.3.3 #自定义ListTile
  auto_size_text: ^2.1.0 #根据宽度自适应字号
  menu_button: ^1.4.2+1 #下拉选择
  web_socket_channel: ^2.1.0
  video_player: ^2.1.6
  #  zego_express_engine: 2.21.2           #即构实时音视频: pod update ZegoExpressEngine
  flutter_page_view_indicator: 0.0.4 #页面指示器
  flutter_spinkit: ^5.0.0 #loading效果
  flutter_switch: ^0.3.2
  ios_utsname_ext: ^2.1.0
  flutter_html: ^2.2.1
  flutter_image_compress: ^1.0.0-nullsafety
  photo_view: ^0.14.0
  wechat_assets_picker: 6.3.1
  image_downloader: ^0.31.0
  dotted_border: ^2.0.0+1 #虚线边框
  pin_code_fields: ^7.4.0
  flutter_slidable: ^1.3.2
  easy_rich_text: ^1.1.0
  animations: ^2.0.0
  synchronized: ^3.0.0+3
  get: ^4.6.1
  disk_space_plus: 0.2.3

  notification_listener_service: ^0.3.2 #获取Android通知栏

  flutter_datetime_picker: #日期滚动选择
    git:
      url: https://github.com/espresso3389/flutter_datetime_picker

  # 本地插件引入
  flutter_logan:
    path: plugins/flutter_logan-1.0.2
  websocket_connector:
    path: plugins/websocket_connector
  ffmpeg_kit_flutter_https_gpl:
    path: plugins/ffmpeg_kit_flutter_https_gpl-6.0.3
  camera: # 主项目 compileSdkVersion 大于 32 报错
    path: plugins/camera-0.10.0+3

dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  uses-material-design: true

  assets:
    - assets/svg/
    - assets/images/
    - assets/html/
    - assets/video/

  fonts:
    - family: iconfont
      fonts:
        - asset: assets/fonts/iconfont.ttf
        #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

flutter_intl:
  # 多语言时设置 enabled 为 true; generated->l10n.dart // Locale.fromSubtags(languageCode: 'en'),
  enabled: true # Required. Must be set to true to activate the plugin. Default: false
  class_name: YLocal # Optional. Sets the name for the generated localization class. Default: S
