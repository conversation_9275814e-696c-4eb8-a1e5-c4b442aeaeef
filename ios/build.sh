#!/bin/bash
# 工程名
APP_NAME="Runner"
# 自动选择证书
CODE_SIGN_IDENTITY="iPhone Developer"
# info.plist路径
PLIST_PATH="${APP_NAME}/Info.plist"

DATE="$(date +%Y%m%d)"
IPANAME="YVR_Assistant_${DATE}"
EXPORT_PATH="build/output"
IPA_PATH="${EXPORT_PATH}/ipa"
ARCHIVE_PATH="${EXPORT_PATH}/${IPANAME}.xcarchive"

if [ ! -d "Pods" ]
then
	pod install
fi

if [ ! -d "${IPA_PATH}" ]
then
	mkdir -p ${IPA_PATH}
fi

echo "=================clean================="
xcodebuild clean -workspace "${APP_NAME}.xcworkspace" -scheme "${APP_NAME}"  -configuration Release

echo "=================build================="
xcodebuild archive -workspace "${APP_NAME}.xcworkspace" -scheme "${APP_NAME}" -sdk iphoneos -configuration 'Release' -archivePath "${ARCHIVE_PATH}" \
CODE_SIGN_IDENTITY="${CODE_SIGN_IDENTITY}" \
PROVISIONING_PROFILE=21fd89ef-f3a0-43f6-9af6-d98d57ef9fd4 \
SYMROOT='$(PWD)'
xcodebuild archive -workspace "${APP_NAME}.xcworkspace" -scheme "${APP_NAME}" -sdk iphoneos -configuration 'Release' -archivePath "${ARCHIVE_PATH}" CODE_SIGN_IDENTITY="${CODE_SIGN_IDENTITY}" SYMROOT='$(PWD)'

echo "=================export================="
xcodebuild -exportArchive -archivePath ${ARCHIVE_PATH} -exportPath ${IPA_PATH} -exportOptionsPlist ${PLIST_PATH}

if [ -f "${IPA_PATH}/yvr_assistant.ipa" ]
then
	echo "Xcode build is Successful!"
else
	echo "Xcode build is fail!"
	exit 0
fi
