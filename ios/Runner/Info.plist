<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>ru,en,tr,es</string>
	<key>CFBundleDisplayName</key>
	<string>Play For Dream</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>yvr_assistant</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weixin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wx6cb2aac952d884cc</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>alipay</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>mba</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>qq</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>tencent1110451818</string>
				<string>QQ05FB8B52</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weibo</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wb568898243</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>dingding</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>dingoabcwtuab76wy0kyzo</string>
				<string>dingoax9s2mdekb7a6748n</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>douyin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>awycvl19mldccyso</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>tiktok</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>aw3vqar8qg1oy91q</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>kuaishou</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ks705657770555308030</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>weixin</string>
		<string>wechat</string>
		<string>weixinULAPI</string>
		<string>alipayauth</string>
		<string>alipayshare</string>
		<string>alipay</string>
		<string>https</string>
		<string>http</string>
		<string>sinaweibohd</string>
		<string>sinaweibo</string>
		<string>weibosdk2.5</string>
		<string>weibosdk</string>
		<string>weibo</string>
		<string>sinaweibosso</string>
		<string>mqqopensdkapiV4</string>
		<string>mqzone</string>
		<string>mqqwpa</string>
		<string>wtloginmqq2</string>
		<string>mqzoneopensdkapiV2</string>
		<string>mqqopensdkminiapp</string>
		<string>mqzoneopensdkapi19</string>
		<string>mqzoneopensdkapi</string>
		<string>mqzoneopensdk</string>
		<string>mqqopensdkapiV3</string>
		<string>mqqopensdkapiV2</string>
		<string>mqq</string>
		<string>mqqOpensdkSSoLogin</string>
		<string>mqqapi</string>
		<string>douyinsharesdk</string>
		<string>douyinopensdk</string>
		<string>tiktokopensdk</string>
		<string>tiktoksharesdk</string>
		<string>dingtalk-sso</string>
		<string>dingtalk-open</string>
		<string>dingtalk</string>
		<string>ksnebula</string>
		<string>KwaiSDKMediaV2</string>
		<string>kwai.clip.multi</string>
		<string>KwaiBundleToken</string>
		<string>kwaiopenapi</string>
		<string>kwai</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>MOBAppKey</key>
	<string>361bca85f108e</string>
	<key>MOBAppSecret</key>
	<string>6179c158ced8ad546d0a18ca81350dae</string>
	<key>MOBNetLater</key>
	<integer>2</integer>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSLocalNetworkUsageDescription</key>
	<string>The app will not connect to the devices on your network, it will only detect connectivity with your local gateway.</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>You need to enable Bluetooth to search for nearby YVR devices.</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>You need to enable Bluetooth to search for nearby YVR devices.</string>
	<key>NSCameraUsageDescription</key>
	<string>You need to enable the camera permission to set your profile photo.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>You need to enable the location permission to implement data communication with YVR devices via Bluetooth.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>You need to enable the location permission to implement data communication with YVR devices via Bluetooth.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>You need to enable the location permission to implement data communication with YVR devices via Bluetooth.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>You need to enable the microphone permission to project your screen.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>You need to allow access to your storage to save photos to your album.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>You need to allow access to your album to set your profile photo.</string>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen.storyboard</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
