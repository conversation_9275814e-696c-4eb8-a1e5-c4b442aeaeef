//
//  File.swift
//  Runner
//
//  Created by on 2022/12/13.
//

class BaseViewController: UIViewController {
    
    let appDelegate = UIApplication.shared.delegate as! AppDelegate
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        forceOrientationLandscape()
    }
    
    override func viewDidAppear(_ animated: <PERSON><PERSON>) {
        super.viewDidAppear(animated)
        self.popGestureClose()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        self.popGestureOpen()
    }
    
    // 是否支持自动转屏
    override var shouldAutorotate: Bool {
        return false
    }
    
    // 支持哪些屏幕方向
    override var supportedInterfaceOrientations: UIInterfaceOrientationMask {
        return .landscapeRight
    }
    
    // 默认的屏幕方向（当前ViewController必须是通过模态出来的UIViewController（模态带导航的无效）方式展现出来的，才会调用这个方法）
    override var preferredInterfaceOrientationForPresentation: UIInterfaceOrientation {
        return .landscapeRight
    }
    
    // 状态栏样式
    override var preferredStatusBarStyle: UIStatusBarStyle {
        return .default
    }
    
    // 是否隐藏状态栏
    override var prefersStatusBarHidden: Bool {
        return false
    }
    
    override var preferredScreenEdgesDeferringSystemGestures: UIRectEdge {
        return [.bottom]
    }
    
    ///强制横屏
    func forceOrientationLandscape() {
        appDelegate.orientation = .landscapeRight
        let _ = appDelegate.application(UIApplication.shared, supportedInterfaceOrientationsFor: self.view.window)
        
        UIDevice.current.setValue(UIInterfaceOrientation.landscapeRight.rawValue, forKey: "orientation")
        UIViewController.attemptRotationToDeviceOrientation()
    }
    
    ///强制竖屏
    func forceOrientationPortrait() {
        appDelegate.orientation = .portrait
        let _ = appDelegate.application(UIApplication.shared, supportedInterfaceOrientationsFor: self.view.window)
        
        UIDevice.current.setValue(UIInterfaceOrientation.portrait.rawValue, forKey: "orientation")
        UIViewController.attemptRotationToDeviceOrientation()
    }
}

extension UIViewController {
    
    func popGestureClose() {
        if let ges = self.navigationController?.interactivePopGestureRecognizer?.view?.gestureRecognizers {
            for item in ges {
                item.isEnabled = false
            }
        }
    }
    
    func popGestureOpen() {
        if let ges = self.navigationController?.interactivePopGestureRecognizer?.view?.gestureRecognizers {
            for item in ges {
                item.isEnabled = false
            }
        }
    }
}

