
import UIKit
import Flutter
import AVFoundation

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
    var captureSession: AVCaptureSession?
    
    var orientation: UIInterfaceOrientationMask = .portrait
    
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        guard let contoller = window?.rootViewController as? FlutterViewController else {
            fatalError("contoller is not type FlutterViewController")
        }
        
        application.isIdleTimerDisabled = true
        GeneratedPluginRegistrant.register(with: self)
        let navigation = UINavigationController(rootViewController: contoller)
        navigation.setNavigationBarHidden(true, animated: true)
        window.rootViewController = navigation
        flutterSetup()

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleCaptureSessionInterrupt),
            name: .AVCaptureSessionWasInterrupted,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleCaptureSessionResumed),
            name: .AVCaptureSessionInterruptionEnded,
            object: nil
        )
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    func flutterSetup() {
        let navigation = window?.rootViewController as? UINavigationController
        guard let contoller = navigation?.topViewController as? FlutterViewController else {
            fatalError("contoller is not type FlutterViewController")
        }
        
        let flutterChannel = FlutterMethodChannel(name:"samples.flutter.jumpto.iOS",
                                                  binaryMessenger: contoller.binaryMessenger)
        
        flutterChannel.setMethodCallHandler {  call, result in
            
            if call.method == "iOSChangeOrientation" {
                if let args = call.arguments as? Dictionary<String, Int> {
                    var orientation = args["orientation"] ?? 0
                    self.forceOrientationPortrait(orientation: orientation)
                }
            } else{
                result("Flutter Method Not Implemented")
            }
        }
    }
    
    override
    func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        return orientation
    }
    
    /// 强制屏幕转换
    func forceOrientationPortrait(orientation: Int) {
        switch orientation {
        case 0:
            self.orientation = .portrait
        case 1:
            self.orientation = .landscapeRight
        case 2:
            self.orientation = .all
        default:
            self.orientation = .portrait
            break
        }
        let _ = self.application(UIApplication.shared, supportedInterfaceOrientationsFor: window)
        
        if #available(iOS 16.0, *) {
            UIViewController.attemptRotationToDeviceOrientation()
            let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene
            windowScene?.requestGeometryUpdate(.iOS(interfaceOrientations: self.orientation))
            
        } else {
            
            UIDevice.current.setValue(UIInterfaceOrientation.portrait.rawValue, forKey: "orientation")
            UINavigationController.attemptRotationToDeviceOrientation()
        }
    }

    @objc func handleCaptureSessionInterrupt(notification: Notification) {
        print("🚨 相机录制被中断")                                       
        captureSession?.stopRunning()
    }

    @objc func handleCaptureSessionResumed(notification: Notification) {
        print("✅ 相机恢复")
        captureSession?.startRunning()
    }
}

