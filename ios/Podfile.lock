PODS:
  - auto_orientation (0.0.1):
    - Flutter
  - BIJKPlayer (0.7.16)
  - brightness_volume (0.0.1):
    - Flutter
  - camera_avfoundation (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info (0.0.1):
    - Flutter
  - disk_space (0.0.1):
    - Flutter
  - disk_space_plus (0.0.1):
    - Flutter
  - ffmpeg_kit_flutter_https_gpl (6.0.3):
    - ffmpeg_kit_flutter_https_gpl/ffmpeg_kit_ios_local (= 6.0.3)
    - Flutter
  - ffmpeg_kit_flutter_https_gpl/ffmpeg_kit_ios_local (6.0.3):
    - Flutter
  - fijkplayer (0.10.1):
    - BIJKPlayer (~> 0.7.16)
    - Flutter
  - Flutter (1.0.0)
  - flutter_blue_plus (0.0.1):
    - Flutter
  - flutter_image_compress (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_logan (0.0.1):
    - Flutter
    - flutter_logan/Logan (= 0.0.1)
  - flutter_logan/Logan (0.0.1):
    - Flutter
    - flutter_logan/Logan/mbedtls (= 0.0.1)
  - flutter_logan/Logan/mbedtls (0.0.1):
    - Flutter
  - fluwx (0.0.1):
    - Flutter
    - OpenWeChatSDK (~> 1.9.9)
  - FMDB (2.7.12):
    - FMDB/standard (= 2.7.12)
  - FMDB/Core (2.7.12)
  - FMDB/standard (2.7.12):
    - FMDB/Core
  - image_crop (0.0.1):
    - Flutter
  - image_downloader (0.0.1):
    - Flutter
  - image_gallery_saver (1.5.0):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - location (0.0.1):
    - Flutter
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - "OpenWeChatSDK (1.9.9+1)"
  - package_info (0.0.1):
    - Flutter
  - path_provider_ios (0.0.1):
    - Flutter
  - "permission_handler (5.1.0+2)":
    - Flutter
  - photo_manager (1.0.0):
    - Flutter
    - FlutterMacOS
  - ReachabilitySwift (5.2.4)
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.2):
    - Flutter
    - FMDB (>= 2.7.5)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_compress (0.3.0):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
  - wakelock (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
  - wifi_iot (0.0.1):
    - Flutter

DEPENDENCIES:
  - auto_orientation (from `.symlinks/plugins/auto_orientation/ios`)
  - brightness_volume (from `.symlinks/plugins/brightness_volume/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info (from `.symlinks/plugins/device_info/ios`)
  - disk_space (from `.symlinks/plugins/disk_space/ios`)
  - disk_space_plus (from `.symlinks/plugins/disk_space_plus/ios`)
  - ffmpeg_kit_flutter_https_gpl (from `.symlinks/plugins/ffmpeg_kit_flutter_https_gpl/ios`)
  - fijkplayer (from `.symlinks/plugins/fijkplayer/ios`)
  - Flutter (from `Flutter`)
  - flutter_blue_plus (from `.symlinks/plugins/flutter_blue_plus/ios`)
  - flutter_image_compress (from `.symlinks/plugins/flutter_image_compress/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_logan (from `.symlinks/plugins/flutter_logan/ios`)
  - fluwx (from `.symlinks/plugins/fluwx/ios`)
  - image_crop (from `.symlinks/plugins/image_crop/ios`)
  - image_downloader (from `.symlinks/plugins/image_downloader/ios`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - location (from `.symlinks/plugins/location/ios`)
  - package_info (from `.symlinks/plugins/package_info/ios`)
  - path_provider_ios (from `.symlinks/plugins/path_provider_ios/ios`)
  - permission_handler (from `.symlinks/plugins/permission_handler/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_compress (from `.symlinks/plugins/video_compress/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/ios`)
  - wakelock (from `.symlinks/plugins/wakelock/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)
  - wifi_iot (from `.symlinks/plugins/wifi_iot/ios`)

SPEC REPOS:
  trunk:
    - BIJKPlayer
    - FMDB
    - libwebp
    - Mantle
    - OpenWeChatSDK
    - ReachabilitySwift
    - SDWebImage
    - SDWebImageWebPCoder

EXTERNAL SOURCES:
  auto_orientation:
    :path: ".symlinks/plugins/auto_orientation/ios"
  brightness_volume:
    :path: ".symlinks/plugins/brightness_volume/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info:
    :path: ".symlinks/plugins/device_info/ios"
  disk_space:
    :path: ".symlinks/plugins/disk_space/ios"
  disk_space_plus:
    :path: ".symlinks/plugins/disk_space_plus/ios"
  ffmpeg_kit_flutter_https_gpl:
    :path: ".symlinks/plugins/ffmpeg_kit_flutter_https_gpl/ios"
  fijkplayer:
    :path: ".symlinks/plugins/fijkplayer/ios"
  Flutter:
    :path: Flutter
  flutter_blue_plus:
    :path: ".symlinks/plugins/flutter_blue_plus/ios"
  flutter_image_compress:
    :path: ".symlinks/plugins/flutter_image_compress/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_logan:
    :path: ".symlinks/plugins/flutter_logan/ios"
  fluwx:
    :path: ".symlinks/plugins/fluwx/ios"
  image_crop:
    :path: ".symlinks/plugins/image_crop/ios"
  image_downloader:
    :path: ".symlinks/plugins/image_downloader/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  location:
    :path: ".symlinks/plugins/location/ios"
  package_info:
    :path: ".symlinks/plugins/package_info/ios"
  path_provider_ios:
    :path: ".symlinks/plugins/path_provider_ios/ios"
  permission_handler:
    :path: ".symlinks/plugins/permission_handler/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_compress:
    :path: ".symlinks/plugins/video_compress/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/ios"
  wakelock:
    :path: ".symlinks/plugins/wakelock/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"
  wifi_iot:
    :path: ".symlinks/plugins/wifi_iot/ios"

SPEC CHECKSUMS:
  auto_orientation: 102ed811a5938d52c86520ddd7ecd3a126b5d39d
  BIJKPlayer: 4c5d66e5cb99ae5bade6f22a4fcc031722a81c64
  brightness_volume: e63139ebe38e642937d5cd286b58c9a9890f1725
  camera_avfoundation: 07c77549ea54ad95d8581be86617c094a46280d9
  connectivity_plus: 413a8857dd5d9f1c399a39130850d02fe0feaf7e
  device_info: d7d233b645a32c40dfdc212de5cf646ca482f175
  disk_space: e94d34bbdf77954adfb39e60bde9cc5c7233eda6
  disk_space_plus: faa27633429bb392ecf7342395bf12040ca4ac9b
  ffmpeg_kit_flutter_https_gpl: 4d6fc6a98ae8757193985837dfc1e56aaded1a2d
  fijkplayer: 5ecf78e80cb97eed64cc170b094a4f691d9b9691
  Flutter: 50d75fe2f02b26cc09d224853bb45737f8b3214a
  flutter_blue_plus: 4837da7d00cf5d441fdd6635b3a57f936778ea96
  flutter_image_compress: 5a5e9aee05b6553048b8df1c3bc456d0afaac433
  flutter_keyboard_visibility: 0339d06371254c3eb25eeb90ba8d17dca8f9c069
  flutter_logan: 2e5543b7fbd9fe4b8dd41543d601fd040a35469a
  fluwx: f14acb5301bc227adc092f6052cefac56b2100f2
  FMDB: 728731dd336af3936ce00f91d9d8495f5718a0e6
  image_crop: e0a67085d3ebf3cf46ca46d61c53a082507b0bc3
  image_downloader: 73e190d6c9f286f2649554051348d9cb319cd4b3
  image_gallery_saver: 259eab68fb271cfd57d599904f7acdc7832e7ef2
  image_picker_ios: b786a5dcf033a8336a657191401bfdf12017dabb
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  location: 3a2eed4dd2fab25e7b7baf2a9efefe82b512d740
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  OpenWeChatSDK: ea48e9db20645f78128db9091893910280b8e4b1
  package_info: 873975fc26034f0b863a300ad47e7f1ac6c7ec62
  path_provider_ios: 14f3d2fd28c4fdb42f44e0f751d12861c43cee02
  permission_handler: ccb20a9fad0ee9b1314a52b70b76b473c5f8dab0
  photo_manager: 84fa94fbeb82e607333ea9a13c43b58e0903a463
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  shared_preferences_foundation: 297b3ebca31b34ec92be11acd7fb0ba932c822ca
  sqflite: 6d358c025f5b867b29ed92fc697fd34924e11904
  url_launcher_ios: ae1517e5e344f5544fb090b079e11f399dfbe4d2
  video_compress: fce97e4fb1dfd88175aa07d2ffc8a2f297f87fbe
  video_player_avfoundation: e489aac24ef5cf7af82702979ed16f2a5ef84cff
  wakelock: d0fc7c864128eac40eba1617cb5264d9c940b46f
  webview_flutter_wkwebview: be0f0d33777f1bfd0c9fdcb594786704dbf65f36
  wifi_iot: b5aafd6f9b52f8a357383a1deabab45f31cd602d

PODFILE CHECKSUM: 9a192d3f84442211861cc0b8b43d5c0bcc75e99e

COCOAPODS: 1.15.2
