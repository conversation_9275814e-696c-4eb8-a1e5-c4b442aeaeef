buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    // Conditional for compatibility with AGP <4.2.
    if (project.android.hasProperty("namespace")) {
        namespace 'com.arthenica.ffmpegkit.flutter'
    }

    compileSdkVersion 33

    defaultConfig {
        minSdkVersion 24
        targetSdkVersion 33
        versionCode 603
        versionName "6.0.3"
    }

    buildTypes {
        release {
            minifyEnabled false
        }
    }
    lintOptions {
        disable 'GradleCompatible'
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation 'androidx.annotation:annotation:1.5.0'
    implementation 'com.arthenica:ffmpeg-kit-https-gpl:6.0-2'
}
